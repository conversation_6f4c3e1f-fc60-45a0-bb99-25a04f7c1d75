## Mantis

TestOps For DevCube

![Release](https://img.shields.io/badge/release-v9.15.0-blue)
![Software License](https://img.shields.io/badge/license-ZhonganTech-brightgreen.svg?style=flat-square)


### Setup

```shell
make setup
```

### Run

```shell
make run
```

### API doc

```shell
make doc
```


### 目录结构
|── app/              # Mantis业务逻辑<br>
|  |── app1             # - 模块1<br>
|  |── app2             # - 模块2<br>
|── cmd/              # 应用程序的入口<br>
|── configs/          # 配置对象<br>
|── pkg/              # 公共逻辑代码<br>
|── vendor/           # 依赖的第三方库<br>
|── .gitignore        # Git忽略文件设置<br>
|── config.yml        # 配置文件<br>
|── Dockerfile        # 镜像制作<br>
|── Makefile          # 自动构建<br>
|── README.md         # 说明文件<br>

### 基础组件
- 数据库：postgres
- ORM：gorm
- 缓存：redis
- 轻量级消息队列: hibiken/asynq
- 搜索引擎：elasticsearch
- 全局唯一ID：snowflake
- 客户端：pkg/clients(rest、es、cache...)
