package dto

// CubeResourceDTO 资源对象
type CubeResourceDTO struct {
	Id       int64             `json:"id"`
	Name     string            `json:"name"`
	Code     string            `json:"code"`
	Type     string            `json:"type"`
	Pid      int64             `json:"pid"`
	Props    string            `json:"props"`
	Children []CubeResourceDTO `json:"children"`
}

// CubeResourceOpenResp 查询资源相应对象
type CubeResourceOpenResp struct {
	CubeBaseOpenResp
	Result []CubeResourceDTO `json:"result"`
}

type CubeResourceResp struct {
	CubeBaseCommonResp
	Result []CubeResourceDTO `json:"data"`
}

type CubeBaseOpenResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// cube 通用返回结果
type CubeBaseCommonResp struct {
	ReturnCode string `json:"returnCode"`
	ReturnMsg  string `json:"returnMsg"`
}

const CubeBaseRespOkCode = "000000"

type CubeTagResp struct {
	CubeBaseCommonResp
	Data struct {
		List []struct {
			Colour  string `json:"colour"`
			Id      int64  `json:"id"`
			TagName string `json:"tagName"`
		} `json:"list"`
	} `json:"data"`
}

type CubeTagDTO struct {
	Colour string `json:"colour"`
	Value  int64  `json:"value"`
	Label  string `json:"label"`
}

type CubeProjectResp struct {
	CubeBaseCommonResp
	Data CubeProjectDTO `json:"data"`
}

type CubeProjectPageResp struct {
	CubeBaseCommonResp
	Data CubeProjectPage `json:"data"`
}

type CubeProjectPage struct {
	CurrentPage int64            `json:"currentPage"`
	PageSize    int64            `json:"pageSize"`
	Pages       int64            `json:"pages"`
	Total       int64            `json:"total"`
	List        []CubeProjectDTO `json:"list"`
}

type CubeProjectDTO struct {
	Name      string `json:"name"`
	Id        int64  `json:"id"`
	CompanyId string `json:"companyId"`
	System    []struct {
		Name string `json:"name"`
		Id   any    `json:"id"`
	} `json:"system"`
}
