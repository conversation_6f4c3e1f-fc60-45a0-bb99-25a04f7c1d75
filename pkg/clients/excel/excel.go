package excel

import (
	"bytes"
	"fmt"
	"mime/multipart"
	"slices"

	"github.com/xuri/excelize/v2"
)

// ExcelUtil 封装 Excel 操作的工具结构体
type ExcelUtil struct {
	file      *excelize.File
	filePath  string
	sheetName string
}

// NewExcelUtil 创建新的 ExcelUtil 实例
func NewExcelUtil(sheetName string) (*ExcelUtil, error) {
	f := excelize.NewFile()
	// 重命名默认的Sheet1为指定名称
	err := f.SetSheetName("Sheet1", sheetName)
	if err != nil {
		return nil, fmt.Errorf("重命名工作表失败: %v", err)
	}
	return &ExcelUtil{
		file:      f,
		sheetName: sheetName,
	}, nil
}

// OpenExcelUtil 打开现有 Excel 文件
func OpenExcelUtil(filePath, sheetName string) (*ExcelUtil, error) {
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开 Excel 文件失败: %v", err)
	}
	// 获取工作表列表
	sheets := f.GetSheetList()
	if len(sheets) == 0 {
		return nil, fmt.Errorf("文件中没有工作表")
	}

	// 如果 sheetName 为空，选择第一个工作表
	if sheetName == "" {
		sheetName = sheets[0]
	} else if !slices.Contains(sheets, sheetName) {
		return nil, fmt.Errorf("工作表 %s 不存在", sheetName)
	}
	return &ExcelUtil{
		file:      f,
		filePath:  filePath,
		sheetName: sheetName,
	}, nil
}

// OpenFromMultipartFile 从文件流打开
func OpenFromMultipartFile(fileHeader *multipart.FileHeader, sheetName string) (*ExcelUtil, error) {
	file, err := fileHeader.Open()
	if err != nil {
		return nil, fmt.Errorf("打开上传文件失败: %v", err)
	}
	defer file.Close()

	f, err := excelize.OpenReader(file)
	if err != nil {
		return nil, fmt.Errorf("从上传文件读取 Excel 失败: %v", err)
	}
	// 获取工作表列表
	sheets := f.GetSheetList()
	if len(sheets) == 0 {
		return nil, fmt.Errorf("文件中没有工作表")
	}

	// 如果 sheetName 为空，选择第一个工作表
	if sheetName == "" {
		sheetName = sheets[0]
	} else if !slices.Contains(sheets, sheetName) {
		return nil, fmt.Errorf("工作表 %s 不存在", sheetName)
	}
	return &ExcelUtil{
		file:      f,
		sheetName: sheetName,
	}, nil
}

// WriteCell 写入单元格数据
func (eu *ExcelUtil) WriteCell(cell string, value string) error {
	err := eu.file.SetCellStr(eu.sheetName, cell, value)
	if err != nil {
		return fmt.Errorf("写入单元格 %s 失败: %v", cell, err)
	}
	return nil
}

// WriteCellWithStyle 写入单元格并指定格式
func (eu *ExcelUtil) WriteCellWithStyle(cell string, value string, style *excelize.Style) error {
	sty, err := eu.file.NewStyle(style)
	if err != nil {
		return fmt.Errorf("设置单元格 %s 格式失败: %v", cell, err)
	}
	err = eu.file.SetCellStyle(eu.sheetName, cell, cell, sty)
	if err != nil {
		return fmt.Errorf("设置单元格 %s 格式失败: %v", cell, err)
	}
	err = eu.file.SetCellStr(eu.sheetName, cell, value)
	if err != nil {
		return fmt.Errorf("写入单元格 %s 失败: %v", cell, err)
	}
	return nil
}

// AppendRow 在工作表最后一行追加数据
func (eu *ExcelUtil) AppendRow(values []string) error {
	// 获取当前工作表的行数
	rows, err := eu.file.GetRows(eu.sheetName)
	if err != nil {
		return fmt.Errorf("获取工作表 %s 行数据失败: %v", eu.sheetName, err)
	}

	// 确定新行的行号（现有行数 + 1）
	newRow := len(rows) + 1

	// 从 A 列开始写入数据
	for i, value := range values {
		// 计算列号（A=1, B=2, ...），转换为单元格坐标
		cell, err := excelize.CoordinatesToCellName(i+1, newRow)
		if err != nil {
			return fmt.Errorf("计算单元格坐标失败: %v", err)
		}
		if err := eu.file.SetCellValue(eu.sheetName, cell, value); err != nil {
			return fmt.Errorf("写入单元格 %s 失败: %v", cell, err)
		}
	}
	return nil
}

// ReadCell 读取单元格数据
func (eu *ExcelUtil) ReadCell(cell string) (string, error) {
	value, err := eu.file.GetCellValue(eu.sheetName, cell)
	if err != nil {
		return "", fmt.Errorf("读取单元格 %s 失败: %v", cell, err)
	}
	return value, nil
}

func (eu *ExcelUtil) ReadRows() ([][]string, error) {
	return eu.file.GetRows(eu.sheetName)
}

// ReadRow 读取指定行的所有单元格数据
func (eu *ExcelUtil) ReadRow(row int) ([]string, error) {
	if row < 1 {
		return nil, fmt.Errorf("行号必须大于 0")
	}

	rows, err := eu.file.GetRows(eu.sheetName)
	if err != nil {
		return nil, fmt.Errorf("读取工作表 %s 失败: %v", eu.sheetName, err)
	}

	if row > len(rows) {
		return nil, fmt.Errorf("行号 %d 超出工作表范围", row)
	}

	// 将字符串切片转换为 interface{} 切片
	result := make([]string, len(rows[row-1]))
	copy(result, rows[row-1])
	return result, nil
}

// SetCellStyle 设置单元格格式（示例：加粗、背景色、对齐）
func (eu *ExcelUtil) SetCellStyle(startRange, endRange string, style *excelize.Style) error {
	sty, err := eu.file.NewStyle(style)
	if err != nil {
		return fmt.Errorf("创建样式失败: %v", err)
	}

	err = eu.file.SetCellStyle(eu.sheetName, startRange, endRange, sty)
	if err != nil {
		return fmt.Errorf("设置单元格 %s 到 %s 样式失败: %v", startRange, endRange, err)
	}
	return nil
}

// SetDropdownList 为指定列设置下拉选项，支持略过表头
func (eu *ExcelUtil) SetDropdownList(column string, options []string, skipHeader bool) error {
	if len(options) == 0 {
		return fmt.Errorf("下拉选项列表不能为空")
	}

	// 根据 skipHeader 设置起始行
	startRow := 1
	if skipHeader {
		startRow = 2
	}

	// 设置数据验证范围（如 A1:A1048576 或 A2:A1048576）
	rangeStart := fmt.Sprintf("%s%d", column, startRow)
	rangeEnd := fmt.Sprintf("%s1048576", column)

	dv := excelize.NewDataValidation(true)
	dv.Sqref = fmt.Sprintf("%s:%s", rangeStart, rangeEnd)
	dv.SetDropList(options)

	err := eu.file.AddDataValidation(eu.sheetName, dv)
	if err != nil {
		return fmt.Errorf("为列 %s 设置下拉选项失败: %v", column, err)
	}
	return nil
}

// SetColumnWidth 设置单列或多列的宽度
func (eu *ExcelUtil) SetColumnWidth(startCol, endCol string, width float64) error {
	err := eu.file.SetColWidth(eu.sheetName, startCol, endCol, width)
	if err != nil {
		return fmt.Errorf("设置列宽失败: %v", err)
	}
	return nil
}

// Save 保存 Excel 文件，支持自定义输出路径
func (eu *ExcelUtil) Save(outputPath string) error {
	if outputPath == "" {
		outputPath = eu.filePath
	}
	if outputPath == "" {
		return fmt.Errorf("输出路径为空且未设置默认路径")
	}
	if err := eu.file.SaveAs(outputPath); err != nil {
		return fmt.Errorf("保存 Excel 文件到 %s 失败: %v", outputPath, err)
	}
	return nil
}

// GetFileStream 返回 Excel 文件的字节流
func (eu *ExcelUtil) GetFileStream() ([]byte, error) {
	var buffer bytes.Buffer
	if err := eu.file.Write(&buffer); err != nil {
		return nil, fmt.Errorf("生成 Excel 文件流失败: %v", err)
	}
	return buffer.Bytes(), nil
}

// Close 关闭 Excel 文件
func (eu *ExcelUtil) Close() error {
	if err := eu.file.Close(); err != nil {
		return fmt.Errorf("关闭 Excel 文件失败: %v", err)
	}
	return nil
}
