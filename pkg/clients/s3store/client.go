package s3store

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/duke-git/lancet/v2/xerror"
)

var (
	client     *s3.Client
	bucketName = ""
	duration   = time.Hour * 24 * 7

	// 文件扩展名到Content-Type的映射
	contentTypeMap = map[string]string{
		".pdf":  "application/pdf",
		".bmp":  "image/bmp",
		".gif":  "image/gif",
		".jpeg": "image/jpg",
		".jpg":  "image/jpg",
		".png":  "image/jpg",
		".html": "text/html",
		".txt":  "text/plain",
		".json": "text/plain",
		".vsd":  "application/vnd.visio",
		".pptx": "application/vnd.ms-powerpoint",
		".ppt":  "application/vnd.ms-powerpoint",
		".docx": "application/msword",
		".doc":  "application/msword",
		".xlsx": "application/vnd.ms-excel",
		".xls":  "application/vnd.ms-excel",
		".csv":  "text/csv",
		".xml":  "text/xml",
		".js":   "application/x-javascript",
		".css":  "text/css",
	}
)

// Init 初始化S3客户端
func Init() {
	bucketName = configs.Config.Store.BucketName
	client = s3.NewFromConfig(aws.Config{
		Region:       "us-west-2",
		Credentials:  credentials.NewStaticCredentialsProvider(configs.Config.Store.AccessId, configs.Config.Store.AccessKey, ""),
		BaseEndpoint: aws.String(configs.Config.Store.Endpoint),
	})
}

// InitWithoutConfig 使用自定义配置初始化S3客户端
func InitWithoutConfig(endpoint, accessId, accessKey, cusBucketName string) {
	bucketName = cusBucketName
	client = s3.NewFromConfig(aws.Config{
		Region:       "us-west-2",
		Credentials:  credentials.NewStaticCredentialsProvider(accessId, accessKey, ""),
		BaseEndpoint: aws.String(endpoint),
	})
}

// GetFile 获取文件内容为字节数组
func GetFile(key string) ([]byte, error) {
	object, err := client.GetObject(context.TODO(), &s3.GetObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(key),
	})
	if err != nil {
		return nil, err
	}
	defer object.Body.Close()

	buf := new(bytes.Buffer)
	_, err = io.Copy(buf, object.Body)
	if err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

// GetFileContent 获取文件内容为字符串
func GetFileContent(key string) (string, error) {
	object, err := client.GetObject(context.TODO(), &s3.GetObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(key),
	})
	if err != nil {
		return "", err
	}
	defer object.Body.Close()

	buf := new(bytes.Buffer)
	_, err = io.Copy(buf, object.Body)
	if err != nil {
		return "", err
	}

	return buf.String(), nil
}

// DownloadFile 下载文件到指定路径
func DownloadFile(key string, desPath string) {
	object, err := client.GetObject(context.TODO(), &s3.GetObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(key),
	})
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get object from oss"))
	}

	// 优化文件写入流程
	data, err := io.ReadAll(object.Body)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error reading from S3 object"))
	}
	defer object.Body.Close()

	err = os.WriteFile(desPath, data, 0o644)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in write to file"))
	}
}

// DeleteFile 删除S3中的文件
func DeleteFile(key string) {
	_, err := client.DeleteObject(context.TODO(), &s3.DeleteObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(key),
	})
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in delete object on oss"))
	}
}

// GetPresignedURL 获取预签名URL
func getPresignedURL(key string) (string, error) {
	preSignClient := s3.NewPresignClient(client)
	res, err := preSignClient.PresignGetObject(context.Background(), &s3.GetObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(key),
	}, func(opts *s3.PresignOptions) {
		opts.Expires = duration
	})
	return res.URL, err
}

// UploadFileWithReader 使用读取器上传文件并返回URL
func UploadFileWithReader(reader io.Reader, desPath, contentType string) (string, error) {
	metadata := map[string]string{"Content-Disposition": "inline"}
	ctx := context.Background()

	// 上传文件到S3
	_, err := client.PutObject(ctx, &s3.PutObjectInput{
		Bucket:      aws.String(bucketName),
		Key:         aws.String(desPath),
		Body:        reader,
		Metadata:    metadata,
		ContentType: aws.String(contentType),
	})
	if err != nil {
		return "", err
	}

	// 获取预签名URL
	return getPresignedURL(desPath)
}

// GetContentType 根据文件扩展名获取Content-Type
func GetContentType(filePath string) string {
	if filePath == "" {
		return ""
	}

	ext := strings.ToLower(filepath.Ext(filePath))
	if contentType, exists := contentTypeMap[ext]; exists {
		return contentType
	}
	return ""
}

// UploadFile 上传文件并返回URL，出错时记录日志
func UploadFile(srcPath, desPath string) (string, error) {
	file, err := os.Open(srcPath)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in open file"))
	}
	defer file.Close()

	return UploadFileWithReader(file, desPath, "application/octet-stream")
}

// UploadFileWithContentType 根据文件类型上传文件
func UploadFileWithContentType(srcPath, desPath string) (string, error) {
	file, err := os.Open(srcPath)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in open file"))
	}
	defer file.Close()

	contentType := GetContentType(srcPath)
	if contentType != "" {
		contentType += "; charset=utf-8"
	}

	return UploadFileWithReader(file, desPath, contentType)
}

// UploadMultipartFile 上传多部分表单文件
func UploadMultipartFile(file *multipart.FileHeader, desPath string) (string, error) {
	src, err := file.Open()
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in open file"))
	}
	defer src.Close()

	return UploadFileWithReader(src, desPath, "")
}

// GetStreamFromUrl 从URL获取文件流
func GetStreamFromUrl(url string) io.ReadCloser {
	resp, err := http.Get(url)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get file from url"))
	}

	if resp.StatusCode != 200 {
		errMsg := fmt.Errorf("error status code: %d", resp.StatusCode)
		logger.Logger.Panicf("%+v", xerror.Wrap(errMsg, "error in get file from url"))
	}

	return resp.Body
}

// GetURLByKey 根据键获取URL
func GetURLByKey(key string) (string, error) {
	return getPresignedURL(key)
}

// ObjectExists 检查对象是否存在
func ObjectExists(key string) bool {
	_, err := client.HeadObject(context.Background(), &s3.HeadObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(key),
	})
	return err == nil
}
