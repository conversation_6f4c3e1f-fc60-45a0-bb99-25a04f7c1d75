package tekton

import (
	"context"
	"fmt"
	"path"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/s3store"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver/request"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/k8s"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/cmdb"

	"github.com/tektoncd/pipeline/pkg/apis/pipeline/pod"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	tekton "github.com/tektoncd/pipeline/pkg/client/clientset/versioned"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

var systemClusterId string

type Tekton struct {
	client    tekton.Interface
	k8sClient kubernetes.Interface
}

func NewTekton() (*Tekton, error) {
	if systemClusterId == "" {
		var items []struct {
			Meta cmdb.K8sClusterModel `json:"meta"`
		}
		if err := cmdb.GetDefaultCMDB().GetModelInstances(context.Background(), constants.KubernetesClusterModelType, &items, nil); err != nil {
			logger.Logger.Errorf("get kubernetes clusters failed: %v", err)
		}
		for _, item := range items {
			if item.Meta.K8sClusterMeta().IsSystemCluster {
				systemClusterId = item.Meta.K8sClusterMeta().ClusterId
				break
			}
		}
	}
	client, err := k8s.GetTektonClientset(systemClusterId)
	if err != nil {
		return nil, err
	}
	k8sClient, err := k8s.GetClientset(systemClusterId)
	if err != nil {
		return nil, err
	}
	return &Tekton{
		client:    client,
		k8sClient: k8sClient,
	}, nil
}

func (tk *Tekton) Run(ctx context.Context, req request.RunRequest) error {
	var taskName string
	switch req.Type {
	case constants.SonarTaskType:
		taskName = "sonar-scanner"
	case constants.TrivyTaskType:
		taskName = "trivy-scanner"
	case constants.JacocoTaskType:
		taskName = "jacoco-coverage"
	case constants.UiTaskType:
		taskName = "ui-executor"
	default:
		return fmt.Errorf("unknown task type: %s", req.Type)
	}

	params := []v1beta1.Param{}
	for key, val := range req.Params {
		value := v1beta1.ParamValue{}
		switch t := val.(type) {
		case string:
			value.Type = v1beta1.ParamTypeString
			value.StringVal = val.(string)
		case []string:
			value.Type = v1beta1.ParamTypeString
			value.ArrayVal = val.([]string)
		case map[string]string:
			value.Type = v1beta1.ParamTypeString
			value.ObjectVal = val.(map[string]string)
		default:
			return fmt.Errorf("不支持的参数类型: %v, key: %s, value: %+v", t, key, val)
		}
		params = append(params, v1beta1.Param{
			Name:  key,
			Value: value,
		})
	}

	taskRun := v1beta1.TaskRun{
		ObjectMeta: metav1.ObjectMeta{
			Name:      req.UUID,
			Namespace: configs.Config.Pipeline.Namespace,
			Labels:    req.Labels,
		},
		Spec: v1beta1.TaskRunSpec{
			Params: params,
			TaskRef: &v1beta1.TaskRef{
				Name: taskName,
			},
			Timeout: &metav1.Duration{
				Duration: func() time.Duration {
					timeout := configs.Config.Pipeline.TaskTimeOut
					if timeout == 0 {
						timeout = 24 * 60 * 60 * time.Second
					}
					return timeout
				}(),
			},
			PodTemplate: &pod.Template{
				NodeSelector: configs.Config.Pipeline.TaskNodeSelector,
				HostNetwork:  configs.Config.Pipeline.TaskHostNetwork,
				ImagePullSecrets: []corev1.LocalObjectReference{
					{
						Name: configs.Config.Pipeline.ImagePullSecret,
					},
				},
			},
		},
	}

	if configs.Config.Pipeline.TaskHostNetwork {
		dNSPolicy := corev1.DNSClusterFirstWithHostNet
		taskRun.Spec.PodTemplate.DNSPolicy = &dNSPolicy
	}

	_, err := tk.client.TektonV1beta1().TaskRuns(configs.Config.Pipeline.Namespace).Create(ctx, &taskRun, metav1.CreateOptions{})
	if err != nil {
		logger.Logger.Errorf("create task run error: %v", err)
		return err
	}
	return nil
}

func (tk *Tekton) Cancel(ctx context.Context, taskId string) error {
	taskRun, err := tk.client.TektonV1beta1().TaskRuns(configs.Config.Pipeline.Namespace).Get(ctx, taskId, metav1.GetOptions{})
	if err != nil {
		logger.Logger.Errorf("get task run error: %v", err)
		return err
	}
	taskRun.Spec.Status = v1beta1.TaskRunSpecStatusCancelled
	_, err = tk.client.TektonV1beta1().TaskRuns(configs.Config.Pipeline.Namespace).Update(ctx, taskRun, metav1.UpdateOptions{})
	if err != nil {
		logger.Logger.Errorf("update task run error: %v", err)
		return err
	}
	return nil
}

func (tk *Tekton) GetLog(ctx context.Context, taskId string) (string, error) {
	// 尝试从S3获取日志
	logPath := path.Join(constants.TaskRunLogFilePath, fmt.Sprintf("%s.txt", taskId))
	if content, err := s3store.GetFileContent(logPath); err == nil {
		return content, nil
	} else {
		logger.Logger.Infof("未找到S3日志，尝试从TaskRun获取: %v", err)
	}

	// 获取TaskRun日志
	taskRun, err := tk.client.TektonV1beta1().TaskRuns(configs.Config.Pipeline.Namespace).Get(ctx, taskId, metav1.GetOptions{})
	if err != nil || len(taskRun.Status.Steps) == 0 {
		return "", nil
	}

	body, err := tk.k8sClient.CoreV1().Pods(configs.Config.Pipeline.Namespace).
		GetLogs(taskRun.Status.PodName, &corev1.PodLogOptions{
			Container: taskRun.Status.Steps[0].ContainerName,
		}).DoRaw(ctx)
	if err != nil {
		logger.Logger.Infof("未找到TaskRun日志: %v", err)
		return "", nil
	}

	return string(body), nil
}

func (tk *Tekton) Delete(ctx context.Context, taskId string) error {
	return tk.client.TektonV1beta1().TaskRuns(configs.Config.Pipeline.Namespace).Delete(ctx, taskId, metav1.DeleteOptions{})
}
