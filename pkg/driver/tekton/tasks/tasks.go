package tasks

import (
	"context"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/k8s"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/cmdb"

	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	"github.com/tektoncd/pipeline/pkg/client/clientset/versioned"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// Init 初始化Tekton任务
func Init() {
	// 定义所有Task
	tasks := map[string]v1beta1.Task{
		"trivy-scanner":   trivyScanTask(),
		"sonar-scanner":   sonarTask(),
		"jacoco-coverage": jacocoCoverageTask(),
		"ui-executor":     uiExecutorTask(),
	}

	// 获取K8s集群
	var clusterId string
	var items []struct {
		Meta cmdb.K8sClusterModel `json:"meta"`
	}
	if err := cmdb.GetDefaultCMDB().GetModelInstances(context.Background(), constants.KubernetesClusterModelType, &items, nil); err != nil {
		logger.Logger.Errorf("获取Kubernetes集群失败: %v", err)
		return
	}

	// 获取K8s系统集群ID
	for _, item := range items {
		if item.Meta.K8sClusterMeta().IsSystemCluster {
			clusterId = item.Meta.K8sClusterMeta().ClusterId
			break
		}
	}
	if clusterId == "" {
		logger.Logger.Error("未找到系统集群")
		return
	}

	// 获取Tekton客户端并初始化任务
	tektonClient, err := k8s.GetTektonClientset(clusterId)
	if err != nil {
		logger.Logger.Errorf("获取Tekton客户端失败: %v", err)
		return
	}

	// 创建或更新Task
	namespace := configs.Config.Pipeline.Namespace
	for name, task := range tasks {
		if err := createOrUpdateTask(tektonClient, namespace, task); err != nil {
			logger.Logger.Errorf("初始化Task %s 失败: %v", name, err)
			continue
		}
	}

	loopDeal(tektonClient)
}

// createOrUpdateTask 创建或更新Task
func createOrUpdateTask(client versioned.Interface, namespace string, task v1beta1.Task) error {
	ctx := context.Background()
	oldTask, err := client.TektonV1beta1().Tasks(namespace).Get(ctx, task.Name, metav1.GetOptions{})
	if err != nil {
		// Task不存在，创建新Task
		_, err = client.TektonV1beta1().Tasks(namespace).Create(ctx, &task, metav1.CreateOptions{})
		return err
	}

	// Task已存在，更新Task
	oldTask.Labels = task.Labels
	oldTask.Annotations = task.Annotations
	oldTask.Spec = task.Spec
	_, err = client.TektonV1beta1().Tasks(namespace).Update(ctx, oldTask, metav1.UpdateOptions{})
	return err
}
