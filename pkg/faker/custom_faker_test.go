package faker

import (
	"regexp"
	"strconv"
	"testing"
	"time"
)

// TestGenerateChinesePhoneNumber 测试生成中国手机号函数
func TestGenerateChinesePhoneNumber(t *testing.T) {
	// 测试生成多个手机号
	for range [100]struct{}{} {
		phone := GenerateChinesePhoneNumber()
		t.<PERSON>(phone)
		// 验证长度
		if len(phone) != 11 {
			t.<PERSON><PERSON><PERSON>("手机号长度应为11位，实际为%d位: %s", len(phone), phone)
		}

		// 验证格式（全数字）
		phoneRegex := regexp.MustCompile(`^\d{11}$`)
		matched := phoneRegex.MatchString(phone)
		if !matched {
			t.<PERSON><PERSON><PERSON>("手机号格式不正确，应为11位数字: %s", phone)
		}

		// 验证前缀
		prefix := phone[:3]
		validPrefixes := []string{
			"130", "131", "132", "133", "134", "135", "136", "137", "138", "139",
			"150", "151", "152", "153", "155", "156", "157", "158", "159",
			"180", "181", "182", "183", "184", "185", "186", "187", "188", "189",
			"170", "171", "172", "173", "174", "175", "176", "177", "178", "179",
		}

		isValidPrefix := false
		for _, validPrefix := range validPrefixes {
			if prefix == validPrefix {
				isValidPrefix = true
				break
			}
		}

		if !isValidPrefix {
			t.Errorf("手机号前缀不在有效范围内: %s", prefix)
		}
	}
}

// TestGenerateChineseIDCard 测试生成中国身份证号函数
func TestGenerateChineseIDCard(t *testing.T) {
	currentYear := time.Now().Year()

	// 测试生成多个身份证号
	for range [100]struct{}{} {
		idCard := GenerateChineseIDCard()
		t.Log(idCard)
		// 验证长度
		if len(idCard) != 18 {
			t.Errorf("身份证号长度应为18位，实际为%d位: %s", len(idCard), idCard)
		}

		// 验证格式（前17位为数字，最后一位为数字或X）
		idCardRegex := regexp.MustCompile(`^\d{17}[\dX]$`)
		matched := idCardRegex.MatchString(idCard)
		if !matched {
			t.Errorf("身份证号格式不正确: %s", idCard)
		}

		// 验证地区代码（前6位）
		areaCode := idCard[:6]
		validAreaCodes := []string{
			// 直辖市
			"110101", "110102", "110105", "110106", "110107", "110108", // 北京市
			"120101", "120102", "120103", "120104", "120105", "120106", // 天津市
			"310101", "310104", "310105", "310106", "310107", "310109", // 上海市
			"500101", "500102", "500103", "500104", "500105", "500106", // 重庆市
			// 省会城市及主要城市
			"130101", "130102", "130104", "130105", // 河北省石家庄市
			"140101", "140105", "140106", "140107", // 山西省太原市
			"150101", "150102", "150103", "150104", // 内蒙古呼和浩特市
			"210101", "210102", "210103", "210104", // 辽宁省沈阳市
			"220101", "220102", "220103", "220104", // 吉林省长春市
			"230101", "230102", "230103", "230104", // 黑龙江省哈尔滨市
			"320101", "320102", "320104", "320105", // 江苏省南京市
			"330101", "330102", "330103", "330104", // 浙江省杭州市
			"340101", "340102", "340103", "340104", // 安徽省合肥市
			"350101", "350102", "350103", "350104", // 福建省福州市
			"360101", "360102", "360103", "360104", // 江西省南昌市
			"370101", "370102", "370103", "370104", // 山东省济南市
			"410101", "410102", "410103", "410104", // 河南省郑州市
			"420101", "420102", "420103", "420104", // 湖北省武汉市
			"430101", "430102", "430103", "430104", // 湖南省长沙市
			"440101", "440103", "440104", "440105", // 广东省广州市
			"440301", "440303", "440304", "440305", // 广东省深圳市
			"450101", "450102", "450103", "450105", // 广西南宁市
			"460101", "460105", "460106", "460107", // 海南省海口市
			"510101", "510104", "510105", "510106", // 四川省成都市
			"520101", "520102", "520103", "520111", // 贵州省贵阳市
			"530101", "530102", "530103", "530111", // 云南省昆明市
			"540101", "540102", "540103", "540104", // 西藏拉萨市
			"610101", "610102", "610103", "610104", // 陕西省西安市
			"620101", "620102", "620103", "620104", // 甘肃省兰州市
			"630101", "630102", "630103", "630104", // 青海省西宁市
			"640101", "640104", "640105", "640106", // 宁夏银川市
			"650101", "650102", "650103", "650104", // 新疆乌鲁木齐市
		}

		isValidAreaCode := false
		for _, validAreaCode := range validAreaCodes {
			if areaCode == validAreaCode {
				isValidAreaCode = true
				break
			}
		}

		if !isValidAreaCode {
			t.Errorf("身份证号地区代码不在有效范围内: %s", areaCode)
		}

		// 验证出生日期（第7-14位）
		birthDateStr := idCard[6:14]
		yearStr := birthDateStr[:4]
		monthStr := birthDateStr[4:6]
		dayStr := birthDateStr[6:8]

		year, err := strconv.Atoi(yearStr)
		if err != nil {
			t.Errorf("身份证号出生年份格式错误: %s", yearStr)
		}

		month, err := strconv.Atoi(monthStr)
		if err != nil {
			t.Errorf("身份证号出生月份格式错误: %s", monthStr)
		}

		day, err := strconv.Atoi(dayStr)
		if err != nil {
			t.Errorf("身份证号出生日期格式错误: %s", dayStr)
		}

		// 验证年份范围
		if year < 1950 || year > currentYear {
			t.Errorf("身份证号出生年份超出范围(1950-%d): %d", currentYear, year)
		}

		// 验证月份范围
		if month < 1 || month > 12 {
			t.Errorf("身份证号出生月份超出范围(1-12): %d", month)
		}

		// 验证日期范围
		if day < 1 || day > 31 {
			t.Errorf("身份证号出生日期超出范围(1-31): %d", day)
		}

		// 验证校验码
		first17 := idCard[:17]
		expectedCheckCode := calculateIDCardCheckCode(first17)
		actualCheckCode := string(idCard[17])

		if actualCheckCode != expectedCheckCode {
			t.Errorf("身份证号校验码错误，期望: %s，实际: %s，身份证号: %s", expectedCheckCode, actualCheckCode, idCard)
		}
	}
}
