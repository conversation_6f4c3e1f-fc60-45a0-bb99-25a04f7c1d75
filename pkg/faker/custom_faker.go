package faker

import (
	"fmt"
	"math/rand"
	"strconv"
	"time"
)

// GenerateChinesePhoneNumber 生成随机中国手机号
func GenerateChinesePhoneNumber() string {
	// 中国手机号前缀（主要运营商）
	prefixes := []string{
		"130", "131", "132", "133", "134", "135", "136", "137", "138", "139", // 中国联通/移动
		"150", "151", "152", "153", "155", "156", "157", "158", "159", // 中国移动/联通
		"180", "181", "182", "183", "184", "185", "186", "187", "188", "189", // 中国电信/移动/联通
		"170", "171", "172", "173", "174", "175", "176", "177", "178", "179", // 虚拟运营商
	}
	
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	prefix := prefixes[r.Intn(len(prefixes))]
	
	// 生成后8位数字
	suffix := fmt.Sprintf("%08d", r.Intn(100000000))
	
	return prefix + suffix
}

// GenerateChineseIDCard 生成随机中国身份证号（18位）
func GenerateChineseIDCard() string {
	// 全国各地区代码（前6位）
	areaCodes := []string{
		// 直辖市
		"110101", "110102", "110105", "110106", "110107", "110108", // 北京市
		"120101", "120102", "120103", "120104", "120105", "120106", // 天津市
		"310101", "310104", "310105", "310106", "310107", "310109", // 上海市
		"500101", "500102", "500103", "500104", "500105", "500106", // 重庆市
		// 省会城市及主要城市
		"130101", "130102", "130104", "130105", // 河北省石家庄市
		"140101", "140105", "140106", "140107", // 山西省太原市
		"150101", "150102", "150103", "150104", // 内蒙古呼和浩特市
		"210101", "210102", "210103", "210104", // 辽宁省沈阳市
		"220101", "220102", "220103", "220104", // 吉林省长春市
		"230101", "230102", "230103", "230104", // 黑龙江省哈尔滨市
		"320101", "320102", "320104", "320105", // 江苏省南京市
		"330101", "330102", "330103", "330104", // 浙江省杭州市
		"340101", "340102", "340103", "340104", // 安徽省合肥市
		"350101", "350102", "350103", "350104", // 福建省福州市
		"360101", "360102", "360103", "360104", // 江西省南昌市
		"370101", "370102", "370103", "370104", // 山东省济南市
		"410101", "410102", "410103", "410104", // 河南省郑州市
		"420101", "420102", "420103", "420104", // 湖北省武汉市
		"430101", "430102", "430103", "430104", // 湖南省长沙市
		"440101", "440103", "440104", "440105", // 广东省广州市
		"440301", "440303", "440304", "440305", // 广东省深圳市
		"450101", "450102", "450103", "450105", // 广西南宁市
		"460101", "460105", "460106", "460107", // 海南省海口市
		"510101", "510104", "510105", "510106", // 四川省成都市
		"520101", "520102", "520103", "520111", // 贵州省贵阳市
		"530101", "530102", "530103", "530111", // 云南省昆明市
		"540101", "540102", "540103", "540104", // 西藏拉萨市
		"610101", "610102", "610103", "610104", // 陕西省西安市
		"620101", "620102", "620103", "620104", // 甘肃省兰州市
		"630101", "630102", "630103", "630104", // 青海省西宁市
		"640101", "640104", "640105", "640106", // 宁夏银川市
		"650101", "650102", "650103", "650104", // 新疆乌鲁木齐市
	}
	
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	areaCode := areaCodes[r.Intn(len(areaCodes))]
	
	// 生成出生日期（8位）- 1950年到现在
	currentYear := time.Now().Year()
	year := r.Intn(currentYear-1950+1) + 1950 // 1950-现在
	month := r.Intn(12) + 1   // 1-12
	day := r.Intn(28) + 1     // 1-28 (避免月份天数问题)
	birthDate := fmt.Sprintf("%04d%02d%02d", year, month, day)
	
	// 生成顺序码（3位）
	sequence := fmt.Sprintf("%03d", r.Intn(1000))
	
	// 前17位
	first17 := areaCode + birthDate + sequence
	
	// 计算校验码（第18位）
	checkCode := calculateIDCardCheckCode(first17)
	
	return first17 + checkCode
}

// calculateIDCardCheckCode 计算身份证校验码
func calculateIDCardCheckCode(first17 string) string {
	// 权重因子
	weights := []int{7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2}
	// 校验码对应表
	checkCodes := []string{"1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"}
	
	sum := 0
	for i, char := range first17 {
		digit, _ := strconv.Atoi(string(char))
		sum += digit * weights[i]
	}
	
	return checkCodes[sum%11]
}