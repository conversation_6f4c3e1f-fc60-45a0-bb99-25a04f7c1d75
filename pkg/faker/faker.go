package faker

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/brianvoe/gofakeit/v7"
)

func Mock(input string) (string, error) {
	matches := extractRandomStrings(input)
	faker := gofakeit.New(0)
	output := input
	for _, match := range matches {
		v, err := getFakeData(faker, match.FuncName, match.Args...)
		if err != nil {
			// 如果函数不存在，将其当作普通字符串处理，不进行替换
			continue
		}
		output = strings.ReplaceAll(output, match.FullMatch, fmt.Sprintf("%v", v))
	}
	return output, nil
}

type FuncMatch struct {
	FullMatch string
	FuncName  string
	Args      []string
}

func extractRandomStrings(input string) []FuncMatch {
	// 匹配{{_函数名(参数)}}或{{_函数名}}格式
	re := regexp.MustCompile(`{{_(\w+)(?:\(([^)]*)\))?}}`)
	matches := re.FindAllStringSubmatch(input, -1)

	var results []FuncMatch
	for _, match := range matches {
		funcMatch := FuncMatch{
			FullMatch: match[0],
			FuncName:  match[1],
		}

		// 如果有参数
		if len(match) > 2 && match[2] != "" {
			// 按逗号分割参数
			args := strings.Split(match[2], ",")
			for i, arg := range args {
				args[i] = strings.TrimSpace(arg)
			}
			funcMatch.Args = args
		}

		results = append(results, funcMatch)
	}

	return results
}

// 初始化一个映射表，将key映射到对应的生成函数，按照gofakeit官方文档顺序组织
var fakeDataFuncs = map[string]func(*gofakeit.Faker, ...string) (any, error){
	// File
	"FileExt":  func(f *gofakeit.Faker, args ...string) (any, error) { return f.FileExtension(), nil },
	"MimeType": func(f *gofakeit.Faker, args ...string) (any, error) { return f.FileMimeType(), nil },
	// Product
	"Product":            func(f *gofakeit.Faker, args ...string) (any, error) { return f.ProductName(), nil },
	"ProductDescription": func(f *gofakeit.Faker, args ...string) (any, error) { return f.ProductDescription(), nil },
	"ProductCategory":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.ProductCategory(), nil },
	"ProductFeature":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.ProductFeature(), nil },
	"ProductMaterial":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.ProductMaterial(), nil },
	"ProductUPC":         func(f *gofakeit.Faker, args ...string) (any, error) { return f.ProductUPC(), nil },
	"ProductDimension":   func(f *gofakeit.Faker, args ...string) (any, error) { return f.ProductDimension(), nil },
	"ProductUseCase":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.ProductUseCase(), nil },
	"ProductBenefit":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.ProductBenefit(), nil },
	"ProductSuffix":      func(f *gofakeit.Faker, args ...string) (any, error) { return f.ProductSuffix(), nil },
	"ProductISBN":        func(f *gofakeit.Faker, args ...string) (any, error) { return f.ProductISBN(nil), nil },
	// Person
	"PersonName":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.Name(), nil },
	"NamePrefix":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.NamePrefix(), nil },
	"NameSuffix":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.NameSuffix(), nil },
	"FirstName":      func(f *gofakeit.Faker, args ...string) (any, error) { return f.FirstName(), nil },
	"MiddleName":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.MiddleName(), nil },
	"LastName":       func(f *gofakeit.Faker, args ...string) (any, error) { return f.LastName(), nil },
	"Gender":         func(f *gofakeit.Faker, args ...string) (any, error) { return f.Gender(), nil },
	"SSN":            func(f *gofakeit.Faker, args ...string) (any, error) { return f.SSN(), nil },
	"Hobby":          func(f *gofakeit.Faker, args ...string) (any, error) { return f.Hobby(), nil },
	"Email":          func(f *gofakeit.Faker, args ...string) (any, error) { return f.Email(), nil },
	"Phone":          func(f *gofakeit.Faker, args ...string) (any, error) { return GenerateChinesePhoneNumber(), nil },
	"PhoneFormatted": func(f *gofakeit.Faker, args ...string) (any, error) { return "+86" + GenerateChinesePhoneNumber(), nil },
	"IDCardNumber":   func(f *gofakeit.Faker, s ...string) (any, error) { return GenerateChineseIDCard(), nil },
	// Auth
	"Username": func(f *gofakeit.Faker, args ...string) (any, error) { return f.Username(), nil },
	"Password": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 6 {
			lower, err := strconv.ParseBool(args[0])
			if err != nil {
				return nil, err
			}
			upper, err := strconv.ParseBool(args[1])
			if err != nil {
				return nil, err
			}
			numbers, err := strconv.ParseBool(args[2])
			if err != nil {
				return nil, err
			}
			special, err := strconv.ParseBool(args[3])
			if err != nil {
				return nil, err
			}
			space, err := strconv.ParseBool(args[4])
			if err != nil {
				return nil, err
			}
			length, err := strconv.Atoi(args[5])
			if err != nil {
				return nil, err
			}
			return f.Password(lower, upper, numbers, special, space, length), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	// Address
	"Address":      func(f *gofakeit.Faker, args ...string) (any, error) { return f.Address().Address, nil },
	"City":         func(f *gofakeit.Faker, args ...string) (any, error) { return f.City(), nil },
	"Country":      func(f *gofakeit.Faker, args ...string) (any, error) { return f.Country(), nil },
	"CountryAbr":   func(f *gofakeit.Faker, args ...string) (any, error) { return f.CountryAbr(), nil },
	"State":        func(f *gofakeit.Faker, args ...string) (any, error) { return f.State(), nil },
	"StateAbr":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.StateAbr(), nil },
	"Street":       func(f *gofakeit.Faker, args ...string) (any, error) { return f.Street(), nil },
	"StreetName":   func(f *gofakeit.Faker, args ...string) (any, error) { return f.StreetName(), nil },
	"StreetNumber": func(f *gofakeit.Faker, args ...string) (any, error) { return f.StreetNumber(), nil },
	"StreetPrefix": func(f *gofakeit.Faker, args ...string) (any, error) { return f.StreetPrefix(), nil },
	"StreetSuffix": func(f *gofakeit.Faker, args ...string) (any, error) { return f.StreetSuffix(), nil },
	"Zip":          func(f *gofakeit.Faker, args ...string) (any, error) { return f.Zip(), nil },
	"Latitude":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.Latitude(), nil },
	"Longitude":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.Longitude(), nil },
	// Game
	"Gamertag": func(f *gofakeit.Faker, args ...string) (any, error) { return f.Gamertag(), nil },
	"Dice": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 2 {
			numDice, err := strconv.Atoi(args[0])
			if err != nil {
				return nil, err
			}
			sides, err := strconv.Atoi(args[1])
			if err != nil {
				return nil, err
			}
			return f.Dice(uint(numDice), []uint{uint(sides)}), nil
		}
		return nil, fmt.Errorf("invalid args, %v", args)
	},
	// Beer
	"BeerName":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.BeerName(), nil },
	"BeerStyle":   func(f *gofakeit.Faker, args ...string) (any, error) { return f.BeerStyle(), nil },
	"BeerHop":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.BeerHop(), nil },
	"BeerYeast":   func(f *gofakeit.Faker, args ...string) (any, error) { return f.BeerYeast(), nil },
	"BeerMalt":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.BeerMalt(), nil },
	"BeerIbu":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.BeerIbu(), nil },
	"BeerAlcohol": func(f *gofakeit.Faker, args ...string) (any, error) { return f.BeerAlcohol(), nil },
	"BeerBlg":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.BeerBlg(), nil },
	// Car
	"Car":                 func(f *gofakeit.Faker, args ...string) (any, error) { return f.Car().Brand, nil },
	"CarMaker":            func(f *gofakeit.Faker, args ...string) (any, error) { return f.CarMaker(), nil },
	"CarModel":            func(f *gofakeit.Faker, args ...string) (any, error) { return f.CarModel(), nil },
	"CarType":             func(f *gofakeit.Faker, args ...string) (any, error) { return f.CarType(), nil },
	"CarFuelType":         func(f *gofakeit.Faker, args ...string) (any, error) { return f.CarFuelType(), nil },
	"CarTransmissionType": func(f *gofakeit.Faker, args ...string) (any, error) { return f.CarTransmissionType(), nil },
	// Words
	"Noun":                      func(f *gofakeit.Faker, args ...string) (any, error) { return f.Noun(), nil },
	"NounCommon":                func(f *gofakeit.Faker, args ...string) (any, error) { return f.NounCommon(), nil },
	"NounConcrete":              func(f *gofakeit.Faker, args ...string) (any, error) { return f.NounConcrete(), nil },
	"NounAbstract":              func(f *gofakeit.Faker, args ...string) (any, error) { return f.NounAbstract(), nil },
	"NounCollectivePeople":      func(f *gofakeit.Faker, args ...string) (any, error) { return f.NounCollectivePeople(), nil },
	"NounCollectiveAnimal":      func(f *gofakeit.Faker, args ...string) (any, error) { return f.NounCollectiveAnimal(), nil },
	"NounCollectiveThing":       func(f *gofakeit.Faker, args ...string) (any, error) { return f.NounCollectiveThing(), nil },
	"NounCountable":             func(f *gofakeit.Faker, args ...string) (any, error) { return f.NounCountable(), nil },
	"NounUncountable":           func(f *gofakeit.Faker, args ...string) (any, error) { return f.NounUncountable(), nil },
	"Verb":                      func(f *gofakeit.Faker, args ...string) (any, error) { return f.Verb(), nil },
	"VerbAction":                func(f *gofakeit.Faker, args ...string) (any, error) { return f.VerbAction(), nil },
	"VerbLinking":               func(f *gofakeit.Faker, args ...string) (any, error) { return f.VerbLinking(), nil },
	"VerbHelping":               func(f *gofakeit.Faker, args ...string) (any, error) { return f.VerbHelping(), nil },
	"Adverb":                    func(f *gofakeit.Faker, args ...string) (any, error) { return f.Adverb(), nil },
	"AdverbManner":              func(f *gofakeit.Faker, args ...string) (any, error) { return f.AdverbManner(), nil },
	"AdverbDegree":              func(f *gofakeit.Faker, args ...string) (any, error) { return f.AdverbDegree(), nil },
	"AdverbPlace":               func(f *gofakeit.Faker, args ...string) (any, error) { return f.AdverbPlace(), nil },
	"AdverbTimeDefinite":        func(f *gofakeit.Faker, args ...string) (any, error) { return f.AdverbTimeDefinite(), nil },
	"AdverbFrequencyIndefinite": func(f *gofakeit.Faker, args ...string) (any, error) { return f.AdverbFrequencyIndefinite(), nil },
	"Preposition":               func(f *gofakeit.Faker, args ...string) (any, error) { return f.Preposition(), nil },
	"PrepositionSimple":         func(f *gofakeit.Faker, args ...string) (any, error) { return f.PrepositionSimple(), nil },
	"PrepositionDouble":         func(f *gofakeit.Faker, args ...string) (any, error) { return f.PrepositionDouble(), nil },
	"PrepositionCompound":       func(f *gofakeit.Faker, args ...string) (any, error) { return f.PrepositionCompound(), nil },
	"Adjective":                 func(f *gofakeit.Faker, args ...string) (any, error) { return f.Adjective(), nil },
	"AdjectiveDescriptive":      func(f *gofakeit.Faker, args ...string) (any, error) { return f.AdjectiveDescriptive(), nil },
	"AdjectiveQuantitative":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.AdjectiveQuantitative(), nil },
	"AdjectiveProper":           func(f *gofakeit.Faker, args ...string) (any, error) { return f.AdjectiveProper(), nil },
	"AdjectiveDemonstrative":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.AdjectiveDemonstrative(), nil },
	"AdjectivePossessive":       func(f *gofakeit.Faker, args ...string) (any, error) { return f.AdjectivePossessive(), nil },
	"AdjectiveInterrogative":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.AdjectiveInterrogative(), nil },
	"AdjectiveIndefinite":       func(f *gofakeit.Faker, args ...string) (any, error) { return f.AdjectiveIndefinite(), nil },
	"Pronoun":                   func(f *gofakeit.Faker, args ...string) (any, error) { return f.Pronoun(), nil },
	"PronounPersonal":           func(f *gofakeit.Faker, args ...string) (any, error) { return f.PronounPersonal(), nil },
	"PronounObject":             func(f *gofakeit.Faker, args ...string) (any, error) { return f.PronounObject(), nil },
	"PronounPossessive":         func(f *gofakeit.Faker, args ...string) (any, error) { return f.PronounPossessive(), nil },
	"PronounReflective":         func(f *gofakeit.Faker, args ...string) (any, error) { return f.PronounReflective(), nil },
	"PronounDemonstrative":      func(f *gofakeit.Faker, args ...string) (any, error) { return f.PronounDemonstrative(), nil },
	"PronounInterrogative":      func(f *gofakeit.Faker, args ...string) (any, error) { return f.PronounInterrogative(), nil },
	"PronounRelative":           func(f *gofakeit.Faker, args ...string) (any, error) { return f.PronounRelative(), nil },
	"Connective":                func(f *gofakeit.Faker, args ...string) (any, error) { return f.Connective(), nil },
	"ConnectiveTime":            func(f *gofakeit.Faker, args ...string) (any, error) { return f.ConnectiveTime(), nil },
	"ConnectiveComparative":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.ConnectiveComparative(), nil },
	"ConnectiveComplaint":       func(f *gofakeit.Faker, args ...string) (any, error) { return f.ConnectiveComplaint(), nil },
	"ConnectiveListing":         func(f *gofakeit.Faker, args ...string) (any, error) { return f.ConnectiveListing(), nil },
	"ConnectiveCasual":          func(f *gofakeit.Faker, args ...string) (any, error) { return f.ConnectiveCasual(), nil },
	"ConnectiveExamplify":       func(f *gofakeit.Faker, args ...string) (any, error) { return f.ConnectiveExamplify(), nil },
	"Word":                      func(f *gofakeit.Faker, args ...string) (any, error) { return f.Word(), nil },
	"Sentence": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 1 {
			wordCount, err := strconv.Atoi(args[0])
			if err != nil {
				return nil, err
			}
			return f.Sentence(wordCount), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	"Paragraph": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 4 {
			paragraphCount, err := strconv.Atoi(args[0])
			if err != nil {
				return nil, err
			}
			sentenceCount, err := strconv.Atoi(args[1])
			if err != nil {
				return nil, err
			}
			wordCount, err := strconv.Atoi(args[2])
			if err != nil {
				return nil, err
			}
			separator := args[3]
			return f.Paragraph(paragraphCount, sentenceCount, wordCount, separator), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	"LoremIpsumWord": func(f *gofakeit.Faker, args ...string) (any, error) { return f.LoremIpsumWord(), nil },
	"LoremIpsumSentence": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 1 {
			wordCount, err := strconv.Atoi(args[0])
			if err != nil {
				return nil, err
			}
			return f.LoremIpsumSentence(wordCount), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	"LoremIpsumParagraph": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 4 {
			paragraphCount, err := strconv.Atoi(args[0])
			if err != nil {
				return nil, err
			}
			sentenceCount, err := strconv.Atoi(args[1])
			if err != nil {
				return nil, err
			}
			wordCount, err := strconv.Atoi(args[2])
			if err != nil {
				return nil, err
			}
			separator := args[3]
			return f.LoremIpsumParagraph(paragraphCount, sentenceCount, wordCount, separator), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	"Question": func(f *gofakeit.Faker, args ...string) (any, error) { return f.Question(), nil },
	"Quote":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.Quote(), nil },
	"Phrase":   func(f *gofakeit.Faker, args ...string) (any, error) { return f.Phrase(), nil },
	// Foods
	"Fruit":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.Fruit(), nil },
	"Vegetable": func(f *gofakeit.Faker, args ...string) (any, error) { return f.Vegetable(), nil },
	"Breakfast": func(f *gofakeit.Faker, args ...string) (any, error) { return f.Breakfast(), nil },
	"Lunch":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.Lunch(), nil },
	"Dinner":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.Dinner(), nil },
	"Snack":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.Snack(), nil },
	"Dessert":   func(f *gofakeit.Faker, args ...string) (any, error) { return f.Dessert(), nil },
	// Misc
	"Bool":      func(f *gofakeit.Faker, args ...string) (any, error) { return f.Bool(), nil },
	"UUID":      func(f *gofakeit.Faker, args ...string) (any, error) { return f.UUID(), nil },
	"FlipACoin": func(f *gofakeit.Faker, args ...string) (any, error) { return f.FlipACoin(), nil },
	// Colors
	"Color":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.Color(), nil },
	"HexColor": func(f *gofakeit.Faker, args ...string) (any, error) { return f.HexColor(), nil },
	"RGBColor": func(f *gofakeit.Faker, args ...string) (any, error) {
		res := f.RGBColor()
		return fmt.Sprintf("rgb(%d,%d,%d)", res[0], res[1], res[2]), nil
	},
	"SafeColor":  func(f *gofakeit.Faker, args ...string) (any, error) { return f.SafeColor(), nil },
	"NiceColors": func(f *gofakeit.Faker, args ...string) (any, error) { return f.NiceColors(), nil },
	// Images
	"Image": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 2 {
			width, err := strconv.Atoi(args[0])
			if err != nil {
				return nil, err
			}
			height, err := strconv.Atoi(args[1])
			if err != nil {
				return nil, err
			}
			return f.Image(width, height), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	"ImageJpeg": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 2 {
			width, err := strconv.Atoi(args[0])
			if err != nil {
				return nil, err
			}
			height, err := strconv.Atoi(args[1])
			if err != nil {
				return nil, err
			}
			return f.ImageJpeg(width, height), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	"ImagePng": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 2 {
			width, err := strconv.Atoi(args[0])
			if err != nil {
				return nil, err
			}
			height, err := strconv.Atoi(args[1])
			if err != nil {
				return nil, err
			}
			return f.ImagePng(width, height), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	// Internet
	"URL":                  func(f *gofakeit.Faker, args ...string) (any, error) { return f.URL(), nil },
	"DomainName":           func(f *gofakeit.Faker, args ...string) (any, error) { return f.DomainName(), nil },
	"DomainSuffix":         func(f *gofakeit.Faker, args ...string) (any, error) { return f.DomainSuffix(), nil },
	"IPv4Address":          func(f *gofakeit.Faker, args ...string) (any, error) { return f.IPv4Address(), nil },
	"IPv6Address":          func(f *gofakeit.Faker, args ...string) (any, error) { return f.IPv6Address(), nil },
	"MacAddress":           func(f *gofakeit.Faker, args ...string) (any, error) { return f.MacAddress(), nil },
	"HTTPStatusCode":       func(f *gofakeit.Faker, args ...string) (any, error) { return f.HTTPStatusCode(), nil },
	"HTTPStatusCodeSimple": func(f *gofakeit.Faker, args ...string) (any, error) { return f.HTTPStatusCodeSimple(), nil },
	"LogLevel":             func(f *gofakeit.Faker, args ...string) (any, error) { return f.LogLevel("general"), nil },
	"HTTPMethod":           func(f *gofakeit.Faker, args ...string) (any, error) { return f.HTTPMethod(), nil },
	"HTTPVersion":          func(f *gofakeit.Faker, args ...string) (any, error) { return f.HTTPVersion(), nil },
	"UserAgent":            func(f *gofakeit.Faker, args ...string) (any, error) { return f.UserAgent(), nil },
	"ChromeUserAgent":      func(f *gofakeit.Faker, args ...string) (any, error) { return f.ChromeUserAgent(), nil },
	"FirefoxUserAgent":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.FirefoxUserAgent(), nil },
	"OperaUserAgent":       func(f *gofakeit.Faker, args ...string) (any, error) { return f.OperaUserAgent(), nil },
	"SafariUserAgent":      func(f *gofakeit.Faker, args ...string) (any, error) { return f.SafariUserAgent(), nil },
	"InputName":            func(f *gofakeit.Faker, args ...string) (any, error) { return f.InputName(), nil },
	"Svg":                  func(f *gofakeit.Faker, args ...string) (any, error) { return f.Svg(nil), nil },
	// Time
	"Date":       func(f *gofakeit.Faker, args ...string) (any, error) { return f.Date().Format(time.RFC3339), nil },
	"PastDate":   func(f *gofakeit.Faker, args ...string) (any, error) { return f.PastDate().Format(time.RFC3339), nil },
	"FutureDate": func(f *gofakeit.Faker, args ...string) (any, error) { return f.FutureDate().Format(time.RFC3339), nil },
	"DateRange": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 2 {
			start, err := time.Parse(time.RFC3339, args[0])
			if err != nil {
				return nil, err
			}
			end, err := time.Parse(time.RFC3339, args[1])
			if err != nil {
				return nil, err
			}
			return f.DateRange(start, end).Format(time.RFC3339), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	"NanoSecond":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.NanoSecond(), nil },
	"Second":         func(f *gofakeit.Faker, args ...string) (any, error) { return f.Second(), nil },
	"Minute":         func(f *gofakeit.Faker, args ...string) (any, error) { return f.Minute(), nil },
	"Hour":           func(f *gofakeit.Faker, args ...string) (any, error) { return f.Hour(), nil },
	"Month":          func(f *gofakeit.Faker, args ...string) (any, error) { return f.Month(), nil },
	"MonthString":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.MonthString(), nil },
	"Day":            func(f *gofakeit.Faker, args ...string) (any, error) { return f.Day(), nil },
	"WeekDay":        func(f *gofakeit.Faker, args ...string) (any, error) { return f.WeekDay(), nil },
	"Year":           func(f *gofakeit.Faker, args ...string) (any, error) { return f.Year(), nil },
	"TimeZone":       func(f *gofakeit.Faker, args ...string) (any, error) { return f.TimeZone(), nil },
	"TimeZoneAbv":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.TimeZoneAbv(), nil },
	"TimeZoneFull":   func(f *gofakeit.Faker, args ...string) (any, error) { return f.TimeZoneFull(), nil },
	"TimeZoneOffset": func(f *gofakeit.Faker, args ...string) (any, error) { return f.TimeZoneOffset(), nil },
	"TimeZoneRegion": func(f *gofakeit.Faker, args ...string) (any, error) { return f.TimeZoneRegion(), nil },
	// Payment
	"Price": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 2 {
			min, err := strconv.ParseFloat(args[0], 64)
			if err != nil {
				return nil, err
			}
			max, err := strconv.ParseFloat(args[1], 64)
			if err != nil {
				return nil, err
			}
			return f.Price(min, max), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	"CreditCardCvv": func(f *gofakeit.Faker, args ...string) (any, error) { return f.CreditCardCvv(), nil },
	"CreditCardExp": func(f *gofakeit.Faker, args ...string) (any, error) { return f.CreditCardExp(), nil },
	"CreditCardNumber": func(f *gofakeit.Faker, args ...string) (any, error) {
		return f.CreditCardNumber(nil), nil
	},
	"CreditCardType":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.CreditCardType(), nil },
	"CurrencyLong":      func(f *gofakeit.Faker, args ...string) (any, error) { return f.CurrencyLong(), nil },
	"CurrencyShort":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.CurrencyShort(), nil },
	"AchRouting":        func(f *gofakeit.Faker, args ...string) (any, error) { return f.AchRouting(), nil },
	"AchAccount":        func(f *gofakeit.Faker, args ...string) (any, error) { return f.AchAccount(), nil },
	"BitcoinAddress":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.BitcoinAddress(), nil },
	"BitcoinPrivateKey": func(f *gofakeit.Faker, args ...string) (any, error) { return f.BitcoinPrivateKey(), nil },
	"BankName":          func(f *gofakeit.Faker, args ...string) (any, error) { return f.BankName(), nil },
	"BankType":          func(f *gofakeit.Faker, args ...string) (any, error) { return f.BankType(), nil },
	"Cusip":             func(f *gofakeit.Faker, args ...string) (any, error) { return f.Cusip(), nil },
	"Isin":              func(f *gofakeit.Faker, args ...string) (any, error) { return f.Isin(), nil },
	// Company
	"BS":            func(f *gofakeit.Faker, args ...string) (any, error) { return f.BS(), nil },
	"Blurb":         func(f *gofakeit.Faker, args ...string) (any, error) { return f.Blurb(), nil },
	"BuzzWord":      func(f *gofakeit.Faker, args ...string) (any, error) { return f.BuzzWord(), nil },
	"Company":       func(f *gofakeit.Faker, args ...string) (any, error) { return f.Company(), nil },
	"CompanySuffix": func(f *gofakeit.Faker, args ...string) (any, error) { return f.CompanySuffix(), nil },
	"JobDescriptor": func(f *gofakeit.Faker, args ...string) (any, error) { return f.JobDescriptor(), nil },
	"JobLevel":      func(f *gofakeit.Faker, args ...string) (any, error) { return f.JobLevel(), nil },
	"JobTitle":      func(f *gofakeit.Faker, args ...string) (any, error) { return f.JobTitle(), nil },
	"Slogan":        func(f *gofakeit.Faker, args ...string) (any, error) { return f.Slogan(), nil },
	// App
	"AppName":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.AppName(), nil },
	"AppVersion": func(f *gofakeit.Faker, args ...string) (any, error) { return f.AppVersion(), nil },
	"AppAuthor":  func(f *gofakeit.Faker, args ...string) (any, error) { return f.AppAuthor(), nil },
	// Animal
	"PetName":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.PetName(), nil },
	"Animal":     func(f *gofakeit.Faker, args ...string) (any, error) { return f.Animal(), nil },
	"AnimalType": func(f *gofakeit.Faker, args ...string) (any, error) { return f.AnimalType(), nil },
	"FarmAnimal": func(f *gofakeit.Faker, args ...string) (any, error) { return f.FarmAnimal(), nil },
	"Cat":        func(f *gofakeit.Faker, args ...string) (any, error) { return f.Cat(), nil },
	"Dog":        func(f *gofakeit.Faker, args ...string) (any, error) { return f.Dog(), nil },
	"Bird":       func(f *gofakeit.Faker, args ...string) (any, error) { return f.Bird(), nil },
	// Emoji
	"Emoji":            func(f *gofakeit.Faker, args ...string) (any, error) { return f.Emoji(), nil },
	"EmojiDescription": func(f *gofakeit.Faker, args ...string) (any, error) { return f.EmojiDescription(), nil },
	"EmojiCategory":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.EmojiCategory(), nil },
	"EmojiAlias":       func(f *gofakeit.Faker, args ...string) (any, error) { return f.EmojiAlias(), nil },
	"EmojiTag":         func(f *gofakeit.Faker, args ...string) (any, error) { return f.EmojiTag(), nil },
	// Language
	"Language":             func(f *gofakeit.Faker, args ...string) (any, error) { return f.Language(), nil },
	"LanguageAbbreviation": func(f *gofakeit.Faker, args ...string) (any, error) { return f.LanguageAbbreviation(), nil },
	"ProgrammingLanguage":  func(f *gofakeit.Faker, args ...string) (any, error) { return f.ProgrammingLanguage(), nil },
	// Number
	"IntRange": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 2 {
			min, err := strconv.Atoi(args[0])
			if err != nil {
				return nil, err
			}
			max, err := strconv.Atoi(args[1])
			if err != nil {
				return nil, err
			}
			return f.Number(min, max), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	"Int": func(f *gofakeit.Faker, args ...string) (any, error) { return f.Int(), nil },
	"IntN": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 1 {
			n, err := strconv.Atoi(args[0])
			if err != nil {
				return nil, err
			}
			return f.IntN(n), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	"Int8":  func(f *gofakeit.Faker, args ...string) (any, error) { return f.Int8(), nil },
	"Int16": func(f *gofakeit.Faker, args ...string) (any, error) { return f.Int16(), nil },
	"Int32": func(f *gofakeit.Faker, args ...string) (any, error) { return f.Int32(), nil },
	"Int64": func(f *gofakeit.Faker, args ...string) (any, error) { return f.Int64(), nil },
	"Uint":  func(f *gofakeit.Faker, args ...string) (any, error) { return f.Uint(), nil },
	"UintN": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 1 {
			n, err := strconv.Atoi(args[0])
			if err != nil {
				return nil, err
			}
			if n < 0 {
				return nil, fmt.Errorf("invalid args, %v", args)
			}
			return f.UintN(uint(n)), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	"Uint8":   func(f *gofakeit.Faker, args ...string) (any, error) { return f.Uint8(), nil },
	"Uint16":  func(f *gofakeit.Faker, args ...string) (any, error) { return f.Uint16(), nil },
	"Uint32":  func(f *gofakeit.Faker, args ...string) (any, error) { return f.Uint32(), nil },
	"Uint64":  func(f *gofakeit.Faker, args ...string) (any, error) { return f.Uint64(), nil },
	"Float32": func(f *gofakeit.Faker, args ...string) (any, error) { return f.Float32(), nil },
	"Float32Range": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 2 {
			min, err := strconv.ParseFloat(args[0], 32)
			if err != nil {
				return nil, err
			}
			max, err := strconv.ParseFloat(args[1], 32)
			if err != nil {
				return nil, err
			}
			return f.Float32Range(float32(min), float32(max)), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	"Float64": func(f *gofakeit.Faker, args ...string) (any, error) { return f.Float64(), nil },
	"Float64Range": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 2 {
			min, err := strconv.ParseFloat(args[0], 64)
			if err != nil {
				return nil, err
			}
			max, err := strconv.ParseFloat(args[1], 64)
			if err != nil {
				return nil, err
			}
			return f.Float64Range(min, max), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	// String
	"Digit": func(f *gofakeit.Faker, args ...string) (any, error) { return f.Digit(), nil },
	"DigitN": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 1 {
			n, err := strconv.Atoi(args[0])
			if err != nil {
				return nil, err
			}
			return f.DigitN(uint(n)), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	"Letter": func(f *gofakeit.Faker, args ...string) (any, error) { return f.Letter(), nil },
	"LetterN": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 1 {
			n, err := strconv.Atoi(args[0])
			if err != nil {
				return nil, err
			}
			return f.LetterN(uint(n)), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	"Lexify": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 1 {
			return f.Lexify(args[0]), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	"Numerify": func(f *gofakeit.Faker, args ...string) (any, error) {
		if len(args) == 1 {
			return f.Numerify(args[0]), nil
		} else {
			return nil, fmt.Errorf("invalid args, %v", args)
		}
	},
	// Celebrity
	"CelebrityActor":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.CelebrityActor(), nil },
	"CelebrityBusiness": func(f *gofakeit.Faker, args ...string) (any, error) { return f.CelebrityBusiness(), nil },
	"CelebritySport":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.CelebritySport(), nil },
	// Book&&Movie
	"BookTitle":  func(f *gofakeit.Faker, args ...string) (any, error) { return f.BookTitle(), nil },
	"BookAuthor": func(f *gofakeit.Faker, args ...string) (any, error) { return f.BookAuthor(), nil },
	"BookGenre":  func(f *gofakeit.Faker, args ...string) (any, error) { return f.BookGenre(), nil },
	"MovieName":  func(f *gofakeit.Faker, args ...string) (any, error) { return f.MovieName(), nil },
	"MovieGenre": func(f *gofakeit.Faker, args ...string) (any, error) { return f.MovieGenre(), nil },
	// Error
	"Error":           func(f *gofakeit.Faker, args ...string) (any, error) { return f.Error().Error(), nil },
	"ErrorDatabase":   func(f *gofakeit.Faker, args ...string) (any, error) { return f.ErrorDatabase().Error(), nil },
	"ErrorGRPC":       func(f *gofakeit.Faker, args ...string) (any, error) { return f.ErrorGRPC().Error(), nil },
	"ErrorHTTP":       func(f *gofakeit.Faker, args ...string) (any, error) { return f.ErrorHTTP().Error(), nil },
	"ErrorHTTPClient": func(f *gofakeit.Faker, args ...string) (any, error) { return f.ErrorHTTPClient().Error(), nil },
	"ErrorHTTPServer": func(f *gofakeit.Faker, args ...string) (any, error) { return f.ErrorHTTPServer().Error(), nil },
	"ErrorRuntime":    func(f *gofakeit.Faker, args ...string) (any, error) { return f.ErrorRuntime().Error(), nil },
	// School
	"School": func(f *gofakeit.Faker, args ...string) (any, error) { return f.School(), nil },
	// Song
	"SongName":   func(f *gofakeit.Faker, args ...string) (any, error) { return f.SongName(), nil },
	"SongArtist": func(f *gofakeit.Faker, args ...string) (any, error) { return f.SongArtist(), nil },
	"SongGenre":  func(f *gofakeit.Faker, args ...string) (any, error) { return f.SongGenre(), nil },
}

func getFakeData(faker *gofakeit.Faker, key string, args ...string) (any, error) {
	if fn, ok := fakeDataFuncs[key]; ok {
		return fn(faker, args...)
	}
	return nil, fmt.Errorf("unknown key: %s", key)
}
