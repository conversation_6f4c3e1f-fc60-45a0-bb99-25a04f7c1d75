package faker

import (
	"strings"
	"testing"
)

func TestMock(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string // 用于验证是否包含特定内容
		wantErr  bool
	}{
		{
			name:     "带参数的函数调用",
			input:    "价格: {{_Float64Range(10,100)}}",
			expected: "价格: ",
			wantErr:  false,
		},
		{
			name:     "不带参数但有括号的函数调用",
			input:    "姓名: {{_FirstName()}}",
			expected: "姓名: ",
			wantErr:  false,
		},
		{
			name:     "不带参数也不带括号的函数调用",
			input:    "邮箱: {{_Email}}",
			expected: "邮箱: ",
			wantErr:  false,
		},
		{
			name:     "多个函数调用混合",
			input:    "用户: {{_FirstName}} {{_LastName()}}, 年龄: {{_IntRange(18,65)}}, 邮箱: {{_Email}}",
			expected: "用户: ",
			wantErr:  false,
		},
		{
			name:     "不存在的函数",
			input:    "测试: {{_NonExistentFunction}}",
			expected: "测试: {{_NonExistentFunction}}",
			wantErr:  false,
		},
		{
			name:     "无需替换的字符串",
			input:    "这是一个普通字符串",
			expected: "这是一个普通字符串",
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := Mock(tt.input)
			t.Log(result)
			if (err != nil) != tt.wantErr {
				t.Errorf("Mock() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				// 验证结果包含预期的前缀
				if !strings.HasPrefix(result, tt.expected) {
					t.Errorf("Mock() result = %v, expected to start with %v", result, tt.expected)
				}

				// 验证模板变量已被替换（不应该包含{{_}}格式）
				if strings.Contains(result, "{{_") {
					t.Errorf("Mock() result still contains template variables: %v", result)
				}
			}
		})
	}
}
