package controller

import (
	"context"
	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"

	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/apispec/errors"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/authentication"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/reqlog"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/provider/base"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/authority"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/orm/query"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/terrors"
)

const errCode = "1001"

type Empty struct{}

type Result struct {
	Result interface{} `json:"result"`
}

type Response struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
	TraceId string      `json:"traceId"`
}

// BaseController wraps common methods for controllers to host API
type BaseController struct {
	base.Controller
	User          authority.UserAccount
	routerInfo    *routerinfo.RouteInfo
	ProjectNo     string
	MantisContext *commoncontext.MantisContext
}

func NewBaseController(rw http.ResponseWriter, req *http.Request) (*BaseController, error) {
	baseCtrl := base.Controller{
		Request:        req,
		ResponseWriter: rw,
		Data:           make(map[string]any),
	}
	acc, _ := authentication.FromContext(req.Context())
	ctx := &commoncontext.MantisContext{
		Context: context.Background(),
		DB:      nil,
		Header:  req.Header,
		User: dto.UserInfo{
			AdAccount: acc.Name,
			Name:      acc.UserName,
			Username:  acc.UserName,
			CompanyID: acc.CompanyId,
			Id:        utils.IDString(acc.Id),
		},
	}
	// TODO: cubebase 暂时不支持cookie，暂时去除，等待其支持cookie
	ctx.Header.Del("Cookie")
	routerInfo := routerinfo.FromContext(req.Context())
	c := BaseController{
		Controller:    baseCtrl,
		User:          acc,
		routerInfo:    routerInfo,
		ProjectNo:     req.Header.Get("SpaceId"),
		MantisContext: ctx,
	}
	return &c, nil
}

func (b *BaseController) ResSuccess() {
	res := Response{
		Code:    commonconstants.ResponseSuccessCode,
		Message: "操作成功",
		Data:    nil,
		TraceId: b.Request.Header.Get("Traceid"),
	}
	b.Data["json"] = res
	b.ServeJSON()
}

func (b *BaseController) ResSuccessResult(result interface{}) {
	res := Response{
		Code:    commonconstants.ResponseSuccessCode,
		Message: "操作成功",
		Data:    result,
		TraceId: b.Request.Header.Get("Traceid"),
	}
	b.Data["json"] = res
	b.ServeJSON()
}

func (b *BaseController) ResFail(err error) {
	err = terrors.Unwrap(err)
	res := Response{
		Code:    commonconstants.ResponseErrorCode,
		Message: errors.New(0, errCode, err.Error(), "").WithRequest(b.Request).Message,
		Data:    nil,
	}
	b.Data["json"] = res
	b.ServeJSON()
}

// GetFilterQuery get page filter query
func (b *BaseController) GetFilterQuery() *query.FilterQuery {
	filter := query.FilterQuery{
		IsLike: true,
	}
	if err := b.DecodeJSONReq(&filter); err != nil {
		reqlog.FromContext(b.Context()).Warn("get filter query failed:", err)
	} else {
		if val, ok := filter.FilterVal.(string); ok {
			filter.FilterVal = query.ReplaceSpecialChar(val)
		}
	}

	if filter.PageSize == 0 {
		filter.PageSize = query.DEF_PAGE_SIZE
	} else if filter.PageSize > query.MaxPageSize {
		filter.PageSize = query.MaxPageSize
	}
	if filter.PageIndex == 0 {
		filter.PageIndex = query.DEF_PAGE_INDEX
	}

	return &filter
}

func (b *BaseController) GetUser() string {
	return b.User.Name
}

func (b *BaseController) GetCompany() any {
	return b.User.CompanyId
}

func (b *BaseController) GetProjectNo() string {
	return b.ProjectNo
}

// AdminOrPEPermissionCheck check the permission
func (b *BaseController) AdminOrPEPermissionCheck() error {
	if b.User.CheckUserRole(authority.RoleAdmin) || b.User.CheckUserRole(authority.RolePe) {
		return nil
	}
	return terrors.Trace(errors.New(0, errCode, "you are not admin or PE!", "you are not admin or PE!"))
}
