package ttf

import (
	"embed"
	"fmt"
	"io"
	"sync"
)

//go:embed SimSun-01.ttf
var fontFiles embed.FS

var (
	fontManager *FontManager
	once        sync.Once
)

// FontManager 字体管理器
type FontManager struct {
	mu        sync.Mutex
	fontBytes map[string][]byte // 缓存已提取的字体数据
}

// GetFontManager 获取字体管理器单例
func GetFontManager() *FontManager {
	once.Do(func() {
		fontManager = &FontManager{
			fontBytes: make(map[string][]byte),
		}
	})
	return fontManager
}

// GetSimSunFontData 获取SimSun字体数据
func GetSimSunFontData() ([]byte, error) {
	return GetFontManager().GetFontData("SimSun-01.ttf")
}

// GetFontData 获取字体数据
func (fm *FontManager) GetFontData(fontName string) ([]byte, error) {
	fm.mu.Lock()
	defer fm.mu.Unlock()

	// 检查缓存
	if data, exists := fm.fontBytes[fontName]; exists {
		return data, nil
	}

	// 从嵌入的文件系统中读取字体
	fontData, err := fontFiles.Open(fontName)
	if err != nil {
		return nil, fmt.Errorf("打开字体文件失败: %w", err)
	}
	defer fontData.Close()

	// 读取所有字体数据
	fontBytes, err := io.ReadAll(fontData)
	if err != nil {
		return nil, fmt.Errorf("读取字体数据失败: %w", err)
	}

	// 缓存字体数据
	fm.fontBytes[fontName] = fontBytes
	return fontBytes, nil
}

// GetSimSunFontPath 为了向后兼容，保留获取文件路径的方法
func GetSimSunFontPath() (string, error) {
	return "", fmt.Errorf("此方法已弃用，请使用GetSimSunFontData获取字体数据")
}
