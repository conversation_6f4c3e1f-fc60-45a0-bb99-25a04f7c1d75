package migration

import (
	"errors"
	"fmt"
	"io/fs"
	"net/url"
	"regexp"
	"strings"
	"time"
)

// VersionFileNameFS 是一个包装器，用于处理文件名中的版本号格式转换
type VersionFileNameFS struct {
	fs.FS
}

// Open 实现 fs.FS 接口，处理文件名转换
func (v *VersionFileNameFS) Open(name string) (fs.File, error) {
	// 将转换后的文件名还原为原始文件名
	originalName := v.convertBackToOriginalName(name)
	return v.FS.Open(originalName)
}

// ReadDir 实现 fs.ReadDirFS 接口，返回转换后的文件名列表
func (v *VersionFileNameFS) ReadDir(name string) ([]fs.DirEntry, error) {
	if readDirFS, ok := v.FS.(fs.ReadDirFS); ok {
		entries, err := readDirFS.ReadDir(name)
		if err != nil {
			return nil, err
		}

		// 转换文件名
		var convertedEntries []fs.DirEntry
		for _, entry := range entries {
			convertedName := v.convertFileName(entry.Name())
			convertedEntries = append(convertedEntries, &ConvertedDirEntry{
				DirEntry: entry,
				name:     convertedName,
			})
		}
		return convertedEntries, nil
	}
	return nil, errors.New("filesystem does not support ReadDir")
}

// convertFileName 将文件名从 914_01_xxx.sql 格式转换为 91401_xxx.sql 格式
func (v *VersionFileNameFS) convertFileName(filename string) string {
	// 匹配格式：数字_数字_其他内容.扩展名
	re := regexp.MustCompile(`^(\d+)_(\d+)_(.+)\.(up|down)\.sql$`)
	matches := re.FindStringSubmatch(filename)
	if len(matches) == 5 {
		// matches[1] = 主版本号 (914)
		// matches[2] = 子版本号 (01)
		// matches[3] = 描述部分 (xxx)
		// matches[4] = 方向 (up/down)
		return fmt.Sprintf("%s%s_%s.%s.sql", matches[1], matches[2], matches[3], matches[4])
	}
	return filename
}

// convertBackToOriginalName 将转换后的文件名还原为原始文件名
func (v *VersionFileNameFS) convertBackToOriginalName(filename string) string {
	// 匹配格式：91401_xxx.up.sql -> 914_01_xxx.up.sql
	re := regexp.MustCompile(`^(\d{3})(\d{2})_(.+)\.(up|down)\.sql$`)
	matches := re.FindStringSubmatch(filename)
	if len(matches) == 5 {
		// matches[1] = 主版本号 (914)
		// matches[2] = 子版本号 (01)
		// matches[3] = 描述部分 (xxx)
		// matches[4] = 方向 (up/down)
		return fmt.Sprintf("%s_%s_%s.%s.sql", matches[1], matches[2], matches[3], matches[4])
	}
	return filename
}

// ConvertedDirEntry 包装 fs.DirEntry 以提供转换后的文件名
type ConvertedDirEntry struct {
	fs.DirEntry
	name string
}

func (c *ConvertedDirEntry) Name() string {
	return c.name
}

func (c *ConvertedDirEntry) IsDir() bool {
	return c.DirEntry.IsDir()
}

func (c *ConvertedDirEntry) Type() fs.FileMode {
	return c.DirEntry.Type()
}

func (c *ConvertedDirEntry) Info() (fs.FileInfo, error) {
	info, err := c.DirEntry.Info()
	if err != nil {
		return nil, err
	}
	return &convertedFileInfo{
		FileInfo: info,
		name:     c.name,
	}, nil
}

// convertedFileInfo 包装 fs.FileInfo 以提供转换后的文件名
type convertedFileInfo struct {
	fs.FileInfo
	name string
}

func (c *convertedFileInfo) Name() string {
	return c.name
}

func (c *convertedFileInfo) Size() int64 {
	return c.FileInfo.Size()
}

func (c *convertedFileInfo) Mode() fs.FileMode {
	return c.FileInfo.Mode()
}

func (c *convertedFileInfo) ModTime() time.Time {
	return c.FileInfo.ModTime()
}

func (c *convertedFileInfo) IsDir() bool {
	return c.FileInfo.IsDir()
}

func (c *convertedFileInfo) Sys() interface{} {
	return c.FileInfo.Sys()
}

// convertDSNToURL 将键值对格式的 DSN 转换为 go-migrate 兼容的 URL 格式
// 输入示例：host=************ port=5432 user=postgres dbname=test001 password=Cubeinfo_2024 sslmode=disable
// 输出示例：***************************************************/test001?sslmode=disable
func ConvertDSNToURL(dsn string) (string, error) {
	// 检查 DSN 是否为空
	if dsn == "" {
		return "", errors.New("DSN cannot be empty")
	}

	// 初始化参数映射
	params := make(map[string]string)

	// 按空格分割 DSN 并解析 key=value 对
	pairs := strings.Fields(dsn)
	for _, pair := range pairs {
		parts := strings.SplitN(pair, "=", 2)
		if len(parts) != 2 {
			return "", fmt.Errorf("无效的 DSN 参数格式: %s", pair)
		}
		params[parts[0]] = parts[1]
	}

	// 提取必需参数
	user, ok := params["user"]
	if !ok {
		return "", errors.New("DSN 缺少 user 参数")
	}
	dbname, ok := params["dbname"]
	if !ok {
		return "", errors.New("DSN 缺少 dbname 参数")
	}
	host, ok := params["host"]
	if !ok {
		return "", errors.New("DSN 缺少 host 参数")
	}

	// 提取可选参数
	password, hasPassword := params["password"]
	port := params["port"]
	if port == "" {
		port = "5432" // PostgreSQL 默认端口
	}

	// 构建 URL
	var builder strings.Builder
	builder.WriteString("postgres://")

	// 添加用户名和密码（如果存在）
	builder.WriteString(url.PathEscape(user))
	if hasPassword {
		builder.WriteString(":")
		builder.WriteString(url.PathEscape(password))
	}
	builder.WriteString("@")

	// 添加主机和端口
	builder.WriteString(host)
	builder.WriteString(":")
	builder.WriteString(port)

	// 添加数据库名
	builder.WriteString("/")
	builder.WriteString(url.PathEscape(dbname))

	// 添加查询参数（如 sslmode）
	queryParams := url.Values{}
	for key, value := range params {
		if key != "user" && key != "password" && key != "host" && key != "port" && key != "dbname" {
			queryParams.Add(key, value)
		}
	}
	if len(queryParams) > 0 {
		builder.WriteString("?")
		builder.WriteString(queryParams.Encode())
	}

	return builder.String(), nil
}
