package gormx

import (
	"errors"
	"fmt"
	"strings"

	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
	"gorm.io/gorm"
)

// 使用paramBuilder 务必拼接model方法
// or方法的逻辑为or中的不同paramBuilder的条件会拼接为or，同一个paramBuilder的条件会拼接为and
// 例如 NewParamBuilder().Or(gormx.NewParamBuilder().Eq("id", id).Like("name", "%"+search+"%"), gormx.NewParamBuilder().Like("path", "%"+search+"%"))
// 将拼接为 and (id = id and name like %search%) or (path like %search%)
// or 内部继续拼接or的复杂sql请手写使用raw，不要使用builder

type p struct {
	name  string
	value any
}

type params struct {
	eqMap    []p
	likeMap  []p
	gtMap    []p
	gteMap   []p
	ltMap    []p
	lteMap   []p
	inMap    []p
	notEqMap []p
	notInMap []p
	ilikeMap []p
}

func newParams() *params {
	params := &params{
		eqMap:    make([]p, 0),
		likeMap:  make([]p, 0),
		gtMap:    make([]p, 0),
		gteMap:   make([]p, 0),
		ltMap:    make([]p, 0),
		lteMap:   make([]p, 0),
		inMap:    make([]p, 0),
		notEqMap: make([]p, 0),
		ilikeMap: make([]p, 0),
	}
	return params
}

type ParamBuilder struct {
	model        schema.Tabler
	selectFields string
	params       *params
	orParams     []*ParamBuilder
	orderByAsc   []string
	orderByDesc  []string
	limit        int
	offset       int
}

// NewParamBuilder 新建param builder，注意必须使用model方法指定操作的表
func NewParamBuilder() *ParamBuilder {
	paramBuilder := &ParamBuilder{}
	paramBuilder.model = nil
	paramBuilder.params = newParams()
	paramBuilder.orParams = make([]*ParamBuilder, 0)
	paramBuilder.orderByAsc = make([]string, 0, 5)
	paramBuilder.orderByDesc = make([]string, 0, 5)
	return paramBuilder
}

// Model 指定当前的param builder 作用的表
func (paramBuilder *ParamBuilder) Model(value schema.Tabler) *ParamBuilder {
	if value != nil {
		paramBuilder.model = value
	}
	return paramBuilder
}

// SelectMany 指定当前的param builder 查询的字段
func (paramBuilder *ParamBuilder) SelectMany(field ...string) *ParamBuilder {
	for _, f := range field {
		paramBuilder.selectFields += "," + f
	}
	paramBuilder.selectFields = strings.Trim(paramBuilder.selectFields, ",")
	return paramBuilder
}

// Select 指定当前的param builder 查询的字段
func (paramBuilder *ParamBuilder) Select(field string) *ParamBuilder {
	if paramBuilder.selectFields == "" {
		paramBuilder.selectFields += field
	} else {
		paramBuilder.selectFields += fmt.Sprintf(",%s", field)
	}
	return paramBuilder
}

// Or 逻辑为or中的不同paramBuilder的条件会拼接为or，同一个paramBuilder的条件会拼接为and
// 例如 NewParamBuilder().Or(gormx.NewParamBuilder().Eq("id", id).Like("name", "%"+search+"%"), gormx.NewParamBuilder().Like("path", "%"+search+"%"))
// 将拼接为 and (id = id and name like %search%) or (path like %search%)
// or 内部继续拼接or的复杂sql请手写使用raw，不要使用builder
func (paramBuilder *ParamBuilder) Or(params ...*ParamBuilder) *ParamBuilder {
	paramBuilder.orParams = append(paramBuilder.orParams, params...)
	return paramBuilder
}

// Eq 指定{field}字段等于{value}
func (paramBuilder *ParamBuilder) Eq(field string, value any) *ParamBuilder {
	paramBuilder.params.eqMap = append(paramBuilder.params.eqMap, p{name: field, value: value})
	return paramBuilder
}

// NotEq 指定{field}字段不等于{value}
func (paramBuilder *ParamBuilder) NotEq(field string, value any) *ParamBuilder {
	paramBuilder.params.notEqMap = append(paramBuilder.params.notEqMap, p{name: field, value: value})
	return paramBuilder
}

// Like 指定{field}字段like{value}, %请自己拼接
func (paramBuilder *ParamBuilder) Like(field string, value string) *ParamBuilder {
	paramBuilder.params.likeMap = append(paramBuilder.params.likeMap, p{name: field, value: value})
	return paramBuilder
}

// ILike 指定{field}字段like{value}, %请自己拼接 忽略大小写的模糊查询
func (paramBuilder *ParamBuilder) ILike(field string, value string) *ParamBuilder {
	paramBuilder.params.ilikeMap = append(paramBuilder.params.ilikeMap, p{name: field, value: value})
	return paramBuilder
}

// Gt 指定{field}字段大于{value}
func (paramBuilder *ParamBuilder) Gt(field string, value any) *ParamBuilder {
	paramBuilder.params.gtMap = append(paramBuilder.params.gtMap, p{name: field, value: value})
	return paramBuilder
}

// Lt 指定{field}字段小于{value}
func (paramBuilder *ParamBuilder) Lt(field string, value any) *ParamBuilder {
	paramBuilder.params.ltMap = append(paramBuilder.params.ltMap, p{name: field, value: value})
	return paramBuilder
}

// Gte 指定{field}字段大于等于{value}
func (paramBuilder *ParamBuilder) Gte(field string, value any) *ParamBuilder {
	paramBuilder.params.gteMap = append(paramBuilder.params.gteMap, p{name: field, value: value})
	return paramBuilder
}

// Lte 指定{field}字段小于等于{value}
func (paramBuilder *ParamBuilder) Lte(field string, value any) *ParamBuilder {
	paramBuilder.params.lteMap = append(paramBuilder.params.lteMap, p{name: field, value: value})
	return paramBuilder
}

// In 指定{field}字段存在于{value}中
func (paramBuilder *ParamBuilder) In(field string, value any) *ParamBuilder {
	paramBuilder.params.inMap = append(paramBuilder.params.inMap, p{name: field, value: value})
	return paramBuilder
}

// NotIn 指定{field}字段不存在于{value}中
func (paramBuilder *ParamBuilder) NotIn(field string, value any) *ParamBuilder {
	paramBuilder.params.notInMap = append(paramBuilder.params.notInMap, p{name: field, value: value})
	return paramBuilder
}

// OrderByDesc 指定按照{field}字段倒序排序
func (paramBuilder *ParamBuilder) OrderByDesc(field string) *ParamBuilder {
	paramBuilder.orderByDesc = append(paramBuilder.orderByDesc, field)
	return paramBuilder
}

// OrderByAsc 指定按照{field}字段正序排序
func (paramBuilder *ParamBuilder) OrderByAsc(field string) *ParamBuilder {
	paramBuilder.orderByAsc = append(paramBuilder.orderByAsc, field)
	return paramBuilder
}

// Order 指定按照{field}字段{order}顺序排序
func (paramBuilder *ParamBuilder) Order(field string, order string) *ParamBuilder {
	switch order {
	case "asc":
		paramBuilder.orderByAsc = append(paramBuilder.orderByAsc, field)
	case "desc":
		paramBuilder.orderByDesc = append(paramBuilder.orderByDesc, field)
	default:
		logger.Logger.Panic("error param")
	}
	return paramBuilder
}

// Limit 指定最多{limit}条
func (paramBuilder *ParamBuilder) Limit(limit int) *ParamBuilder {
	paramBuilder.limit = limit
	return paramBuilder
}

func (paramBuilder *ParamBuilder) Offset(offset int) *ParamBuilder {
	paramBuilder.offset = offset
	return paramBuilder
}

type PageResult struct {
	Pages       int64 `json:"pages"`       // 总页数
	Total       int64 `json:"total"`       // 总条数
	PageSize    int64 `json:"pageSize"`    // 一页条数
	CurrentPage int64 `json:"currentPage"` // 当前页
	List        any   `json:"list"`        // 数据
}

type PageRequest struct {
	Page     int64 `schema:"page"`
	PageSize int64 `schema:"pageSize"`
}

func getSqlAndParamFromParams(params *params) (string, []any) {
	dbSql := ""
	values := make([]any, 0)
	operation := "and"
	if len(params.eqMap) != 0 {
		for _, p := range params.eqMap {
			dbSql += fmt.Sprintf(`"%s" = ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.inMap) != 0 {
		for _, p := range params.inMap {
			dbSql += fmt.Sprintf(`"%s" in ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.likeMap) != 0 {
		for _, p := range params.likeMap {
			dbSql += fmt.Sprintf(`"%s" like ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.ilikeMap) != 0 {
		for _, p := range params.ilikeMap {
			dbSql += fmt.Sprintf(`"%s" ilike ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.gtMap) != 0 {
		for _, p := range params.gtMap {
			dbSql += fmt.Sprintf(`"%s" > ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.ltMap) != 0 {
		for _, p := range params.ltMap {
			dbSql += fmt.Sprintf(`"%s" < ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.gteMap) != 0 {
		for _, p := range params.gteMap {
			dbSql += fmt.Sprintf(`"%s" >= ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.lteMap) != 0 {
		for _, p := range params.lteMap {
			dbSql += fmt.Sprintf(`"%s" <= ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.notEqMap) != 0 {
		for _, p := range params.notEqMap {
			dbSql += fmt.Sprintf(`"%s" != ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.notInMap) != 0 {
		for _, p := range params.notInMap {
			dbSql += fmt.Sprintf(`"%s" not in ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	dbSql = dbSql[0 : len(dbSql)-2-len(operation)]
	return dbSql, values
}

func setParamToDbConnection(db *gorm.DB, params *params) {
	dbSql := ""
	values := make([]any, 0)
	operation := "and"
	if len(params.eqMap) != 0 {
		for _, p := range params.eqMap {
			dbSql += fmt.Sprintf(`"%s" = ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.inMap) != 0 {
		for _, p := range params.inMap {
			dbSql += fmt.Sprintf(`"%s" in ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.likeMap) != 0 {
		for _, p := range params.likeMap {
			dbSql += fmt.Sprintf(`"%s" like ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.ilikeMap) != 0 {
		for _, p := range params.ilikeMap {
			dbSql += fmt.Sprintf(`"%s" ilike ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.gtMap) != 0 {
		for _, p := range params.gtMap {
			dbSql += fmt.Sprintf(`"%s" > ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.ltMap) != 0 {
		for _, p := range params.ltMap {
			dbSql += fmt.Sprintf(`"%s" < ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.gteMap) != 0 {
		for _, p := range params.gteMap {
			dbSql += fmt.Sprintf(`"%s" >= ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.lteMap) != 0 {
		for _, p := range params.lteMap {
			dbSql += fmt.Sprintf(`"%s" <= ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.notEqMap) != 0 {
		for _, p := range params.notEqMap {
			dbSql += fmt.Sprintf(`"%s" != ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if len(params.notInMap) != 0 {
		for _, p := range params.notInMap {
			dbSql += fmt.Sprintf(`"%s" not in ?`, p.name) + " " + operation + " "
			values = append(values, p.value)
		}
	}
	if dbSql != "" {
		dbSql = dbSql[0 : len(dbSql)-2-len(operation)]
	}
	db.Where(dbSql, values...)
}

func setOrParamToDbConnection(db *gorm.DB, orParams []*ParamBuilder) {
	if len(orParams) != 0 {
		totalSql := ""
		values := make([]any, 0)
		for _, param := range orParams {
			dbSql, vals := getSqlAndParamFromParams(param.params)
			totalSql += "(" + dbSql + ") or "
			values = append(values, vals...)
		}
		totalSql = totalSql[0 : len(totalSql)-4]
		db.Where(totalSql, values...)
	}
}

// SelectByParamStructX 按照{param}结构体内部的字段作为条件进行查询，查询结果保存到{res}。{res}需要为指针。
func SelectByParamStructX(ctx *commoncontext.MantisContext, param any, res any) {
	db := GetDB(ctx)

	err := db.Where(param).Find(res).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error searching database"))
	}
}

// SelectByParamStruct 按照{param}结构体内部的字段作为条件进行查询，查询结果保存到{res}。{res}需要为指针。
func SelectByParamStruct(ctx *commoncontext.MantisContext, param any, res any) error {
	db := GetDB(ctx)
	err := db.Where(param).Find(res).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {

		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error searching database"))
		return err
	}

	return nil
}

// PageSelectByParamBuilderX 按照分页根据param builder 进行查询，结果保存到{res}，返回的pageresult中也会保存结果。{res}需要为指针
func PageSelectByParamBuilderX(ctx *commoncontext.MantisContext, builder *ParamBuilder, res any, request PageRequest) *PageResult {
	request = checkPage(request)
	db := GetDB(ctx)

	if builder.model != nil {
		db = db.Model(builder.model)
	}
	if builder.selectFields != "" {
		db = db.Select(builder.selectFields)
	}
	setParamToDbConnection(db, builder.params)
	setOrParamToDbConnection(db, builder.orParams)
	if len(builder.orderByAsc) != 0 {
		for _, order := range builder.orderByAsc {
			db = db.Order(order)
		}
	}
	if len(builder.orderByDesc) != 0 {
		for _, order := range builder.orderByDesc {
			db = db.Order(fmt.Sprintf("%s desc", order))
		}
	}
	var total int64
	err := db.Count(&total).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error searching database"))
	}
	offset := (request.Page - 1) * request.PageSize
	db = db.Offset(int(offset))
	db = db.Limit(int(request.PageSize))
	err = db.Find(res).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error searching database"))
	}
	pages := total / request.PageSize
	if total%request.PageSize != 0 {
		pages += 1
	}

	return &PageResult{
		Total:       total,
		Pages:       pages,
		PageSize:    request.PageSize,
		CurrentPage: request.Page,
		List:        res,
	}
}

// PageSelectByParamBuilder 按照分页根据param builder 进行查询，结果保存到{res}，返回的pageresult中也会保存结果。{res}需要为指针。
func PageSelectByParamBuilder(ctx *commoncontext.MantisContext, builder *ParamBuilder, res any, request PageRequest) (*PageResult, error) {
	request = checkPage(request)
	db := GetDB(ctx)
	if builder.model != nil {
		db = db.Model(builder.model)
	}
	if builder.selectFields != "" {
		db = db.Select(builder.selectFields)
	}
	setParamToDbConnection(db, builder.params)
	setOrParamToDbConnection(db, builder.orParams)
	if len(builder.orderByAsc) != 0 {
		for _, order := range builder.orderByAsc {
			db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: order}, Desc: false})
		}
	}
	if len(builder.orderByDesc) != 0 {
		for _, order := range builder.orderByDesc {
			db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: order}, Desc: true})
		}
	}
	var total int64
	err := db.Count(&total).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {

		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error searching database"))
		return nil, err
	}
	offset := (request.Page - 1) * request.PageSize
	db = db.Offset(int(offset))
	db = db.Limit(int(request.PageSize))
	err = db.Find(res).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {

		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error searching database"))
		return nil, err
	}
	pages := total / request.PageSize
	if total%request.PageSize != 0 {
		pages += 1
	}

	return &PageResult{
		Total:       total,
		Pages:       pages,
		PageSize:    request.PageSize,
		CurrentPage: request.Page,
		List:        res,
	}, nil
}

// PageSelectByRaw 原生sql分页支持; 计算总条数的时候没有进行优化设计,直接在原有视图上进行的count
// sql demo: sql= "select * from table where a=?  "
// {res}需要为指针。
// pr 分页参数
// values 需要在sql中填充的参数
func PageSelectByRaw(ctx *commoncontext.MantisContext, sql string, res any, pr PageRequest, values ...any) (*PageResult, error) {
	pr = checkPage(pr)
	db := GetDB(ctx)

	var total int64
	sql1 := fmt.Sprintf("select count(1) from (%s) a", sql)
	err := db.Raw(sql1, values...).Scan(&total).Error
	if err != nil {
		logger.Logger.Errorf("%+v", xerror.Wrap(err, "PageSelectByRaw-查询总数报错"))
		return nil, err
	}
	offset := (pr.Page - 1) * pr.PageSize
	err = db.Raw(fmt.Sprintf("%v offset %d limit %d ", sql, offset, pr.PageSize), values...).Scan(res).Error
	if err != nil {
		logger.Logger.Errorf("%+v", xerror.Wrap(err, "PageSelectByRaw-执行sql报错"))
		return nil, err
	}
	pages := total / pr.PageSize
	if total%pr.PageSize != 0 {
		pages += 1
	}
	return &PageResult{
		Total:       total,
		Pages:       pages,
		PageSize:    pr.PageSize,
		CurrentPage: pr.Page,
		List:        res,
	}, nil
}

func checkPage(request PageRequest) PageRequest {
	if request.Page < 1 {
		logger.Logger.Warn("当前 page 小于 1,将使用默认页码 page=1")
		request.Page = 1
	}
	if request.PageSize < 1 {
		logger.Logger.Warn("当前 pageSize 小于 1,将使用默认页码 pageSize=10")
		request.PageSize = 10
	}
	return PageRequest{Page: request.Page, PageSize: request.PageSize}
}

// SelectByParamBuilderX 按照param builder 进行查询，结果保存到{res}。{res}需要为指针。
func SelectByParamBuilderX(ctx *commoncontext.MantisContext, builder *ParamBuilder, res any) {
	db := GetDB(ctx)

	if builder.model != nil {
		db = db.Model(builder.model)
	}
	if builder.selectFields != "" {
		db = db.Select(builder.selectFields)
	}
	setParamToDbConnection(db, builder.params)
	setOrParamToDbConnection(db, builder.orParams)
	if len(builder.orderByAsc) != 0 {
		for _, order := range builder.orderByAsc {
			db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: order}, Desc: false})
		}
	}
	if len(builder.orderByDesc) != 0 {
		for _, order := range builder.orderByDesc {
			db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: order}, Desc: true})
		}
	}
	if builder.limit != 0 {
		db = db.Limit(builder.limit)
	}
	if builder.offset != 0 {
		db = db.Offset(builder.offset)
	}
	err := db.Find(res).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error searching database"))
	}
}

// SelectByParamBuilder 按照param builder 进行查询，结果保存到{res}。{res}需要为指针。
func SelectByParamBuilder(ctx *commoncontext.MantisContext, builder *ParamBuilder, res any) error {
	db := GetDB(ctx)
	if builder.model != nil {
		db = db.Model(builder.model)
	}
	if builder.selectFields != "" {
		db = db.Select(builder.selectFields)
	}
	setParamToDbConnection(db, builder.params)
	setOrParamToDbConnection(db, builder.orParams)
	if len(builder.orderByAsc) != 0 {
		for _, order := range builder.orderByAsc {
			db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: order}, Desc: false})
		}
	}
	if len(builder.orderByDesc) != 0 {
		for _, order := range builder.orderByDesc {
			db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: order}, Desc: true})
		}
	}
	if builder.limit != 0 {
		db = db.Limit(builder.limit)
	}
	if builder.offset != 0 {
		db = db.Offset(builder.offset)
	}
	err := db.Find(res).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {

		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error searching database"))
		return err
	}

	return nil
}

// SelectFieldByParamBuilderX 按照param builder 进行查询，可以不使用映射为表的结构体作为{res}接受结果。{res}需要为指针。
func SelectFieldByParamBuilderX(ctx *commoncontext.MantisContext, builder *ParamBuilder, res any) {
	db := GetDB(ctx)

	if builder.model != nil {
		db = db.Model(builder.model)
	} else {
		logger.Logger.Panicf("%+v", xerror.New("table not set"))
	}
	if builder.selectFields != "" {
		db = db.Select(builder.selectFields)
	}
	setParamToDbConnection(db, builder.params)
	setOrParamToDbConnection(db, builder.orParams)
	if len(builder.orderByAsc) != 0 {
		for _, order := range builder.orderByAsc {
			db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: order}, Desc: false})
		}
	}
	if len(builder.orderByDesc) != 0 {
		for _, order := range builder.orderByDesc {
			db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: order}, Desc: true})
		}
	}
	if builder.limit != 0 {
		db = db.Limit(builder.limit)
	}
	if builder.offset != 0 {
		db = db.Offset(builder.offset)
	}
	err := db.Scan(res).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error searching database"))
	}
}

// SelectFieldByParamBuilder 按照param builder 进行查询，可以不使用映射为表的结构体作为{res}接受结果。{res}需要为指针。
func SelectFieldByParamBuilder(ctx *commoncontext.MantisContext, builder *ParamBuilder, res any) error {
	db := GetDB(ctx)
	if builder.model != nil {
		db = db.Model(builder.model)
	} else {
		logger.Logger.Panicf("%+v", xerror.New("table not set"))
	}
	if builder.selectFields != "" {
		db = db.Select(builder.selectFields)
	}
	setParamToDbConnection(db, builder.params)
	setOrParamToDbConnection(db, builder.orParams)
	if len(builder.orderByAsc) != 0 {
		for _, order := range builder.orderByAsc {
			db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: order}, Desc: false})
		}
	}
	if len(builder.orderByDesc) != 0 {
		for _, order := range builder.orderByDesc {
			db = db.Order(clause.OrderByColumn{Column: clause.Column{Name: order}, Desc: true})
		}
	}
	if builder.limit != 0 {
		db = db.Limit(builder.limit)
	}
	if builder.offset != 0 {
		db = db.Offset(builder.offset)
	}
	err := db.Scan(res).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {

		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error searching database"))
		return err
	}

	return nil
}

// SelectOneByConditionX 按照{obj}参数内的字段条件进行查询，结果写入{obj}，{obj}必须为映射为表的结构体指针。
func SelectOneByConditionX(ctx *commoncontext.MantisContext, obj any) {
	db := GetDB(ctx)

	err := db.Where(obj).First(obj).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error searching database"))
	}
}

// SelectOneByCondition 按照{obj}参数内的字段条件进行查询，结果写入{obj}，{obj}必须为映射为表的结构体指针。
func SelectOneByCondition(ctx *commoncontext.MantisContext, obj any) error {
	db := GetDB(ctx)
	err := db.Where(obj).First(obj).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {

		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error searching database"))
		return err
	}

	return nil
}

// RawX 根据{sql}进行查询，结果会写入{result}，使用?作为sql中的占位符，变量值写入{values}。{result}需要为指针。
func RawX(ctx *commoncontext.MantisContext, sql string, result any, values ...any) {
	db := GetDB(ctx)

	err := db.Raw(sql, values...).Find(result).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error searching database"))
	}
}

// Raw 根据{sql}进行查询，结果会写入{result}，使用?作为sql中的占位符，变量值写入{values}。{result}需要为指针。
func Raw(ctx *commoncontext.MantisContext, sql string, result any, values ...any) error {
	db := GetDB(ctx)
	err := db.Raw(sql, values...).Find(result).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {

		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error searching database"))
		return err
	}

	return nil
}

// UpdateOneByConditionX 根据{obj}的主键作为条件，{obj}的字段内容作为修改进行修改。修改忽略空值，{obj}必须为映射为表的结构体
func UpdateOneByConditionX(ctx *commoncontext.MantisContext, obj any) int64 {
	db := GetDB(ctx)
	tx := db.Model(obj).Updates(obj)
	err := tx.Error
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error updating database"))
	}

	return tx.RowsAffected
}

// UpdateOneByCondition 根据{obj}的主键作为条件，{obj}的字段内容作为修改进行修改。修改忽略空值，{obj}必须为映射为表的结构体
func UpdateOneByCondition(ctx *commoncontext.MantisContext, obj any) (int64, error) {
	db := GetDB(ctx)
	tx := db.Model(obj).Updates(obj)
	err := tx.Error
	if err != nil {

		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error updating database"))
		return 0, err
	}
	return tx.RowsAffected, nil
}

func UpdateOneAllowZeroByCondition(ctx *commoncontext.MantisContext, obj any) (int64, error) {
	db := GetDB(ctx)
	tx := db.Model(obj).Select("*").Updates(obj)
	err := tx.Error
	if err != nil {

		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error updating database"))
		return 0, err
	}
	return tx.RowsAffected, nil
}

// UpdateBatchByParamBuilderAndMapX 根据param builder作为条件，map中的内容作为修改进行修改。不忽略map中的空值
func UpdateBatchByParamBuilderAndMapX(ctx *commoncontext.MantisContext, builder *ParamBuilder, updates map[string]any) int64 {
	db := GetDB(ctx)
	if builder.model != nil {
		db = db.Model(builder.model)
	} else {
		logger.Logger.Panicf("%+v", xerror.New("table not set"))
	}
	setParamToDbConnection(db, builder.params)
	setOrParamToDbConnection(db, builder.orParams)
	tx := db.Updates(updates)
	err := tx.Error
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error updating database"))
	}

	return tx.RowsAffected
}

// UpdateBatchByParamBuilderAndMap 根据param builder作为条件，map中的内容作为修改进行修改。不忽略map中的空值
func UpdateBatchByParamBuilderAndMap(ctx *commoncontext.MantisContext, builder *ParamBuilder, updates map[string]any) (int64, error) {
	db := GetDB(ctx)
	if builder.model != nil {
		db = db.Table(builder.model.TableName())
	} else {
		err := xerror.New("table not set")
		logger.Logger.Errorf("%+v", err)
		return 0, err
	}
	setParamToDbConnection(db, builder.params)
	setOrParamToDbConnection(db, builder.orParams)
	tx := db.Updates(updates)
	err := tx.Error
	if err != nil {
		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error updating database"))
		return 0, err
	}
	return tx.RowsAffected, nil
}

// UpdateBatchByParamBuilderX 根据param builder作为条件，{obj}中的字段作为修改进行修改，忽略空值。{obj}必须为映射为表的结构体
func UpdateBatchByParamBuilderX(ctx *commoncontext.MantisContext, builder *ParamBuilder, obj any) int64 {
	db := GetDB(ctx)
	db = db.Model(obj)
	setParamToDbConnection(db, builder.params)
	setOrParamToDbConnection(db, builder.orParams)
	tx := db.Updates(obj)
	err := tx.Error
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error updating database"))
	}

	return tx.RowsAffected
}

// UpdateBatchByParamBuilder 根据param builder作为条件，{obj}中的字段作为修改进行修改，忽略空值。{obj}必须为映射为表的结构体
func UpdateBatchByParamBuilder(ctx *commoncontext.MantisContext, builder *ParamBuilder, obj any) (int64, error) {
	db := GetDB(ctx)
	db = db.Model(obj)
	setParamToDbConnection(db, builder.params)
	setOrParamToDbConnection(db, builder.orParams)
	tx := db.Updates(obj)
	err := tx.Error
	if err != nil {
		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error updating database"))
		return 0, err
	}
	return tx.RowsAffected, nil
}

// DeleteOneByIdX 根据{obj}的主键id进行删除。{obj}必须为映射为表的结构体
func DeleteOneByIdX(ctx *commoncontext.MantisContext, obj any) {
	db := GetDB(ctx)
	err := db.Delete(obj).Error
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error deleting database"))
	}
}

// DeleteOneById 根据{obj}的主键id进行删除。{obj}必须为映射为表的结构体
func DeleteOneById(ctx *commoncontext.MantisContext, obj any) error {
	db := GetDB(ctx)
	err := db.Delete(obj).Error
	if err != nil {
		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error deleting database"))
		return err
	}
	return nil
}

// DeleteByParamBuilderX 根据 param builder 作为条件进行删除
func DeleteByParamBuilderX(ctx *commoncontext.MantisContext, builder *ParamBuilder) {
	db := GetDB(ctx)
	setParamToDbConnection(db, builder.params)
	setOrParamToDbConnection(db, builder.orParams)
	if builder.model != nil {
		err := db.Delete(builder.model).Error
		if err != nil {
			logger.Logger.Panicf("%+v", xerror.Wrap(err, "error deleting database"))
		}
	} else {
		logger.Logger.Panicf("%+v", xerror.New("table not set"))
	}
}

// DeleteByParamBuilder 根据 param builder 作为条件进行删除
func DeleteByParamBuilder(ctx *commoncontext.MantisContext, builder *ParamBuilder) error {
	db := GetDB(ctx)
	setParamToDbConnection(db, builder.params)
	setOrParamToDbConnection(db, builder.orParams)
	if builder.model != nil {
		err := db.Delete(builder.model).Error
		if err != nil {

			logger.Logger.Errorf("%+v", xerror.Wrap(err, "error deleting database"))
			return err
		}
	} else {

		err := xerror.New("table not set")
		logger.Logger.Errorf("%+v", err)
		return err
	}

	return nil
}

// DeleteAllRecords 无条件物理删除所有记录
func DeleteAllRecords(ctx *commoncontext.MantisContext, model any) error {
	db := GetDB(ctx)
	err := db.Session(&gorm.Session{AllowGlobalUpdate: true}).Delete(model).Error
	if err != nil {
		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error delete all records"))
		return err
	}
	return nil
}

// DeleteRecords 根据条件物理删除记录
func DeleteRecords(model any, where any, ctx *commoncontext.MantisContext, args ...any) error {
	db := GetDB(ctx)
	err := db.Where(where, args...).Delete(model).Error
	if err != nil {
		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error delete records"))
		return err
	}
	return nil
}

// Deprecated: 使用insert或update，不要使用save。
// InsertUpdateOneX {obj} 必须为指针, 如果调用此方法进行update不会忽略空值，update条件为{obj}主键
func InsertUpdateOneX(ctx *commoncontext.MantisContext, obj any) int64 {
	db := GetDB(ctx)

	tx := db.Save(obj)
	err := tx.Error
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error inserting database"))
	}

	return tx.RowsAffected
}

// Deprecated: 使用insert或update，不要使用save。
// InsertUpdateOne {obj} 必须为指针, 如果调用此方法进行update不会忽略空值，update条件为{obj}主键
func InsertUpdateOne(ctx *commoncontext.MantisContext, obj any) (int64, error) {
	db := GetDB(ctx)

	tx := db.Save(obj)
	err := tx.Error
	if err != nil {

		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error inserting database"))
		return 0, err
	}

	return tx.RowsAffected, nil
}

func InsertOne(ctx *commoncontext.MantisContext, obj any) (int64, error) {
	db := GetDB(ctx)

	tx := db.Create(obj)
	err := tx.Error
	if err != nil {

		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error inserting database"))
		return 0, err
	}

	return tx.RowsAffected, nil
}

// InsertBatchX 批量插入{list}。
func InsertBatchX(ctx *commoncontext.MantisContext, list any) int64 {
	db := GetDB(ctx)

	tx := db.Create(list)
	err := tx.Error
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error inserting database"))
	}

	return tx.RowsAffected
}

// InsertBatch 批量插入{list}。
func InsertBatch(ctx *commoncontext.MantisContext, list any) (int64, error) {
	db := GetDB(ctx)

	tx := db.Create(list)
	err := tx.Error
	if err != nil {

		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error inserting database"))
		return 0, err
	}

	return tx.RowsAffected, nil
}

// ExecX 执行非查询的sql, 使用?作为占位符，变量传入{values}
func ExecX(ctx *commoncontext.MantisContext, sql string, values ...any) int64 {
	db := GetDB(ctx)
	tx := db.Exec(sql, values...)
	err := tx.Error
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error operating database"))
	}

	return tx.RowsAffected
}

// Exec 执行非查询的sql, 使用?作为占位符，变量传入{values}
func Exec(ctx *commoncontext.MantisContext, sql string, values ...any) (int64, error) {
	db := GetDB(ctx)
	tx := db.Exec(sql, values...)
	err := tx.Error
	if err != nil {
		logger.Logger.Errorf("%+v", xerror.Wrap(err, "error operating database"))
		return 0, err
	}

	return tx.RowsAffected, nil
}
