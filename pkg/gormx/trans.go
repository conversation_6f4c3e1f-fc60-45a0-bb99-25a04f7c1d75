package gormx

import (
	"database/sql"

	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
	"gorm.io/gorm"
)

func start(db *gorm.DB, level sql.IsolationLevel) *gorm.DB {
	begin := db.Begin(&sql.TxOptions{Isolation: level})
	logger.Logger.Debugf("start trans conn=%v", begin)
	return begin
}

func commit(db *gorm.DB) {
	if db != nil {
		logger.Logger.Debugf("commit trans conn=%v", db)
		db.Commit()
	} else {
		logger.Logger.Panic("did not start a transaction")
	}
}

func rollback(db *gorm.DB) {
	if db != nil {
		logger.Logger.Debugf("rollback trans, conn=%v", db)
		db.Rollback()
	} else {
		logger.Logger.Panic("did not start a transaction")
	}
}

// TransactionX 开启事务，事务级别为默认级别
func TransactionX(ctx *commoncontext.MantisContext, f func() error) {
	err := Transaction(ctx, f)
	if err != nil {
		logger.Logger.Panicf("%s", xerror.Wrap(err).Error())
	}
}

// Transaction 开启事务，事务级别为默认级别
func Transaction(ctx *commoncontext.MantisContext, f func() error) error {
	return TransactionWithLevel(ctx, f, sql.LevelDefault)
}

// TransactionWithLevelX 开启事务，事务级别为传入的级别
func TransactionWithLevelX(ctx *commoncontext.MantisContext, f func() error, level sql.IsolationLevel) {
	err := TransactionWithLevel(ctx, f, level)
	if err != nil {
		logger.Logger.Panicf("%s", xerror.Wrap(err).Error())
	}
}

// TransactionWithLevel 开启事务，事务级别为传入的级别
func TransactionWithLevel(ctx *commoncontext.MantisContext, f func() error, level sql.IsolationLevel) error {
	db := GetDB(ctx)
	trans := start(db, level)

	ctx.DB = trans
	reductionDB := func() {
		ctx.DB = nil
	}
	err := f()
	if err != nil {
		rollback(trans)
		reductionDB()
		return err
	}
	err1 := recover()
	if err1 != nil {
		rollback(trans)
		reductionDB()
		return xerror.New("%v", err1)
	}
	commit(trans)
	reductionDB()
	return nil
}
