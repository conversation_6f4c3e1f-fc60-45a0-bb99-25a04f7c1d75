package times

import (
	"database/sql/driver"
	"fmt"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
)

type Time time.Time

const (
	timeFormat                 = "2006-01-02 15:04:05"       // 基准时间，所有的format操作都要使用此时间
	ISO8601TimeFormat          = "2006-01-02T15:04:05-0700"  // iso 8601格式时间模版
	ISO8601TimeFormatWithColon = "2006-01-02T15:04:05-07:00" // iso 8601格式时间模版带冒号
)

func (t *Time) UnmarshalJSON(data []byte) (err error) {
	now, err := time.ParseInLocation(`"`+timeFormat+`"`, string(data), time.Local)
	*t = Time(now)
	return
}

func (t Time) MarshalJSON() ([]byte, error) {
	b := make([]byte, 0, len(timeFormat)+2)
	b = append(b, '"')
	b = time.Time(t).AppendFormat(b, timeFormat)
	b = append(b, '"')
	return b, nil
}

func (t *Time) Scan(value any) error {
	*t = Time(value.(time.Time))
	return nil
}

// 实现 driver.Valuer 接口，Value 返回 json value
func (t Time) Value() (driver.Value, error) {
	return time.Time(t), nil
}

func (t Time) Second() int {
	t2 := time.Time(t)
	return t2.Second()
}

func (t Time) ToString() string {
	json, err := t.MarshalJSON()
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in convert time to string"))
	}
	s := string(json)
	return s[1 : len(s)-1]
}

func (t Time) Format(format string) string {
	goTime := time.Time(t)
	return goTime.Format(format)
}

func (t Time) ToUnix() int64 {
	return time.Time(t).UnixMilli()
}

func (t *Time) ToGoTime() time.Time {
	return time.Time(*t)
}

func Now() *Time {
	t := Time(time.Now())
	return &t
}

func ParseStringToTime(str string, format string) *Time {
	time1, _ := time.ParseInLocation(format, str, time.Local)
	t := Time(time1)
	return &t
}

func UnixToTime(data int64) time.Time {
	return time.Unix(data/1000, 0)
}

func UnixToTimes(unix int64) *Time {
	t := Time(time.Unix(unix/1000, 0))
	return &t
}

func ToTimeUtilTime(timeRaw time.Time) Time {
	return Time(timeRaw)
}

func ToTimeUtilTimeP(timeRaw time.Time) *Time {
	t := Time(timeRaw)
	return &t
}

func GetDataFromCommaString(dateStr string, index int32) *Time {
	split := strings.Split(dateStr, ",")
	if index >= int32(len(split)) {
		return nil
	}
	str := split[index]
	if index == 0 {
		str = str + " 00:00:00"
	} else {
		str = str + " 23:59:59"
	}
	return ParseStringToTime(str, timeFormat)
}

func Parse(layout string, value string) *Time {
	parse, err := time.Parse(layout, value)
	if err != nil {
		logger.Logger.Panicf("%v", xerror.Wrap(err, "error in parsing time"))
	}
	res := Time(parse)
	return &res
}

func CalculateDiff(start *Time, end *Time) string {
	s := time.Time(*start)
	e := time.Time(*end)
	d := e.Sub(s)
	hours := d / time.Hour
	d -= hours * time.Hour
	minutes := d / time.Minute
	d -= minutes * time.Minute
	seconds := d / time.Second
	return fmt.Sprintf("%d:%d:%d", hours, minutes, seconds)
}
