# IDE
.DS_Store
.project
*.iml
.settings
.idea
.vscode
.cursor
.trae
.zed
.nvim
.classpath
.envrc
.env
*.versionsBackup
.air.toml
**/__debug_bin*
.zed/

# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
/release

# Test binary, build with `go test -c`
*.test

# Output file
*.out
*.log
/logs
**/coverage
**.exe
*go_build_main_go*
/mantis
/cube-mantis
test_build
**/target
pb-api-generator

# Project-local glide cache
.glide/
/k8sconfig/
/trivy-scanner

# api doc and env
/api-doc
.curl/
.env
.envrc

