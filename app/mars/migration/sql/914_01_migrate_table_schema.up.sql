-- Create table mars_bug
CREATE TABLE mars_bug (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    bug_id text,
    demand_id text,
    test_plan_id bigint,
    case_id bigint,
    "type" varchar(100),
    PRIMARY KEY (id)
);

-- Create table mars_case
CREATE TABLE mars_case (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    node_type varchar(50),
    name text,
    parent_id bigint,
    library_id bigint,
    priority varchar(50),
    remark text,
    precondition text,
    steps jsonb,
    attachments jsonb,
    tags jsonb,
    descendants_total bigint,
    sibling_order bigint,
    PRIMARY KEY (id)
);

COMMENT ON COLUMN mars_case.node_type IS '用例类型，root:用例库 folder:目录，case:用例';

COMMENT ON COLUMN mars_case.priority IS 'P0, P1, P2,P3';

COMMENT ON COLUMN mars_case.steps IS '该用例的所有步骤的json';

COMMENT ON COLUMN mars_case.attachments IS '该用例的附件的json';

-- Create table mars_case_history
CREATE TABLE mars_case_history (
    case_id bigint,
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    node_type varchar(50),
    name text,
    parent_id bigint,
    library_id bigint,
    priority varchar(50),
    remark text,
    precondition text,
    steps jsonb,
    attachments jsonb,
    tags jsonb,
    descendants_total bigint,
    sibling_order bigint,
    PRIMARY KEY (id)
);

COMMENT ON COLUMN mars_case_history.node_type IS '用例类型，root:用例库 folder:目录，case:用例';

COMMENT ON COLUMN mars_case_history.priority IS 'P0, P1, P2,P3';

COMMENT ON COLUMN mars_case_history.steps IS '该用例的所有步骤的json';

COMMENT ON COLUMN mars_case_history.attachments IS '该用例的附件的json';

-- Create table mars_case_library
CREATE TABLE mars_case_library (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    name varchar(100),
    description text,
    space_id varchar(50),
    share boolean,
    PRIMARY KEY (id)
);

-- Create table mars_case_relation
CREATE TABLE mars_case_relation (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    case_id bigint,
    relation_type varchar(50) NOT NULL DEFAULT 'demand'::character varying,
    relation_id varchar(50),
    PRIMARY KEY (id)
);

-- Create table mars_review
CREATE TABLE mars_review (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    name varchar(100),
    description text,
    reviewer jsonb,
    review_status varchar(20),
    end_time timestamp without time zone,
    comment text,
    commentor varchar(200),
    comment_time timestamp without time zone,
    space_id varchar(100),
    PRIMARY KEY (id)
);

-- Create table mars_review_case_relation
CREATE TABLE mars_review_case_relation (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    review_id bigint,
    review_status varchar(100),
    case_id bigint,
    comments jsonb NOT NULL DEFAULT '[]'::jsonb,
    PRIMARY KEY (id)
);

-- Create table mars_test_plan
CREATE TABLE mars_test_plan (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    name varchar(200),
    stage varchar(100),
    env varchar(100),
    begin_time timestamp without time zone,
    end_time timestamp without time zone,
    owner jsonb,
    model jsonb,
    remark text,
    status varchar(100),
    related_iteration text,
    is_all_demand boolean,
    plan_type varchar(50),
    space_id varchar(100),
    PRIMARY KEY (id)
);

-- Create table mars_test_plan_case_exec_history
CREATE TABLE mars_test_plan_case_exec_history (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    case_id varchar(50),
    test_plan_id bigint,
    "type" varchar,
    execute_result varchar(20),
    execute_time timestamp without time zone,
    executor varchar(100),
    result text,
    report_url text,
    PRIMARY KEY (id)
);

-- Create table mars_test_plan_case_relation
CREATE TABLE mars_test_plan_case_relation (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    test_plan_id bigint,
    demand_id varchar(50),
    relation_id varchar(50),
    sort bigint,
    "type" varchar(50),
    last_exec_id bigint,
    executors jsonb,
    PRIMARY KEY (id)
);

-- Create table mars_test_plan_demand_relation
CREATE TABLE mars_test_plan_demand_relation (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    test_plan_id bigint,
    demand_id varchar(50),
    "type" varchar(50),
    PRIMARY KEY (id)
);

-- Create table mars_test_plan_report
CREATE TABLE mars_test_plan_report (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    name varchar(100),
    stage varchar(20),
    test_plan_id bigint,
    env varchar(20),
    begin_time timestamp without time zone,
    end_time timestamp without time zone,
    owner jsonb,
    summary text,
    test_plan_pass_rate bytea,
    view_choice_config jsonb,
    case_map jsonb,
    demand_list jsonb,
    bug_snapshot jsonb,
    space_id varchar(20),
    PRIMARY KEY (id)
);