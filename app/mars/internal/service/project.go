package service

import (
	"math"
	"strconv"

	project2 "git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto/project"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/constants"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/remote/project"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	set "github.com/duke-git/lancet/v2/datastructure/set"
)

type ProjectService struct{}

func (t ProjectService) GetBugDTO(ctx *commoncontext.MantisContext, bugs []models.MarsBug, snapshotId int64, planId int64) (*project2.ProjectBugDTO, error) {
	bugIds := set.New[string]()
	apiCaseIds := make([]int64, 0)
	uiCaseIds := make([]int64, 0)
	caseIds := make([]int64, 0)
	caseIdMap := make(map[string]int64)
	bugTypeMap := make(map[string]string)
	for _, marsbug := range bugs {
		bugIds.Add(marsbug.BugId)
		switch marsbug.Type {
		case constants.TestPlanRelationTypeCase:
			caseIds = append(caseIds, marsbug.CaseId)
		case constants.TestPlanRelationTypeApi:
			apiCaseIds = append(apiCaseIds, marsbug.CaseId)
		case constants.TestPlanRelationTypeUI:
			uiCaseIds = append(uiCaseIds, marsbug.CaseId)
		}
		caseIdMap[marsbug.BugId] = marsbug.CaseId
		bugTypeMap[marsbug.BugId] = marsbug.Type
	}
	projectBugDTO := project2.ProjectBugDTO{}
	if bugIds.Size() == 0 {
		projectBugDTO.ProjectBugStatisticsDTO = dto.ProjectBugStatisticsDTO{}
		projectBugDTO.ProjectBugStatisticsDTO.FixedRate = 0
		return &projectBugDTO, nil
	}
	caseMap := make(map[int64]string)
	if len(caseIds) != 0 {
		cases := make([]models.MarsCase, 0)
		err := gormx.SelectByParamBuilder(ctx,
			gormx.NewParamBuilder().Model(&models.MarsCase{}).In("id", caseIds).Eq("is_deleted", commonconstants.DeleteNo),
			&cases)
		if err != nil {
			return nil, err
		}
		for _, marsCase := range cases {
			caseMap[marsCase.Id] = marsCase.Name
		}
	}
	apiCaseMap := make(map[int64]string)
	// TODO 查询接口测试用例信息
	uiCaseMap := make(map[int64]string)
	// TODO 查询ui测试用例信息
	bugCaseMap := make(map[int64]string)
	for _, marsbug := range bugs {
		switch marsbug.Type {
		case constants.TestPlanRelationTypeCase:
			bugCaseMap[marsbug.Id] = caseMap[marsbug.CaseId]
		case constants.TestPlanRelationTypeApi:
			bugCaseMap[marsbug.Id] = apiCaseMap[marsbug.CaseId]
		case constants.TestPlanRelationTypeUI:
			bugCaseMap[marsbug.Id] = uiCaseMap[marsbug.CaseId]
		}
	}
	tmCommonSelectReq := dto.ProjectCommonSelectReq{
		Value:          []int64{21},
		SearchFieldKey: "projectType.id",
		Ids:            bugIds.ToSlice(),
		Page:           1,
		PageSize:       math.MaxInt32,
	}
	return t.getProjectBugDTO(ctx, snapshotId, planId, bugCaseMap, bugs, tmCommonSelectReq)
}

func (t ProjectService) GetBugList(ctx *commoncontext.MantisContext, pageReq gormx.PageRequest, req dto.ProjectCommonSelectReqInSchema) (*gormx.PageResult, error) {
	projectId := ctx.Header.Get("spaceid")
	tmReq := dto.ProjectCommonSelectReq{
		Search:           req.Search,
		SearchFieldKey:   req.SearchFieldKey,
		SearchFieldValue: req.SearchFieldValue,
		Ids:              req.Ids,
		Value:            req.Value,
		ProjectId:        projectId,
		IterationId:      req.IterationId,
		PriorityLevel:    req.PriorityLevel,
		Code:             req.Code,
		Model:            req.Model,
		Page:             pageReq.Page,
		PageSize:         pageReq.PageSize,
	}
	if tmReq.ProjectId == "" {
		return &gormx.PageResult{}, nil
	}
	tmReq.Value = []int64{21}
	tmReq.SearchFieldKey = "projectType.id"
	projectBug, err := t.getProjectBugDTO(ctx, 0, 0, map[int64]string{}, []models.MarsBug{}, tmReq)
	if err != nil {
		return nil, err
	}
	return &gormx.PageResult{
		List:        projectBug.BugDTOS,
		Total:       projectBug.ProjectBugStatisticsDTO.Total,
		CurrentPage: pageReq.Page,
		PageSize:    pageReq.PageSize,
	}, nil
}

func (t ProjectService) GetIterationList(ctx *commoncontext.MantisContext, req dto.ProjectCommonSelectReq, pageReq gormx.PageRequest) ([]models.ProjectItemDTO, error) {
	req.Page = pageReq.Page
	req.PageSize = pageReq.PageSize
	res, _, err := project.ProjectRemoteApi.GetIterations(ctx, req)
	return res, err
}

func (t ProjectService) GetDemandsByPage(ctx *commoncontext.MantisContext, req dto.ProjectCommonSelectReq, pageReq gormx.PageRequest) ([]models.ProjectDemandDTO, int64, error) {
	req.Page = pageReq.Page
	req.PageSize = pageReq.PageSize
	return project.ProjectRemoteApi.GetDemands(ctx, req)
}

func (t ProjectService) getProjectBugDTO(ctx *commoncontext.MantisContext, snapshotId int64, planId int64,
	bugCaseNameMap map[int64]string, bugs []models.MarsBug,
	tmCommonselectReq dto.ProjectCommonSelectReq,
) (*project2.ProjectBugDTO, error) {
	queryRes, total, err := project.ProjectRemoteApi.GetBugs(ctx, tmCommonselectReq)
	if err != nil {
		return nil, err
	}
	flag := len(bugs) == 0
	projectBugDTOs := make([]models.ProjectBugDTO, 0, len(queryRes))
	bugMap := make(map[string]models.ProjectBugDTO)
	var fix int64 = 0
	for _, item := range queryRes {
		if item.OverState == project.OverStateEnd {
			fix++
		}
		if flag {
			projectBugDTOs = append(projectBugDTOs, item)
		}
		bugMap[strconv.FormatInt(item.Id, 10)] = item
	}
	if !flag {
		for _, marsBug := range bugs {
			projectBug := bugMap[marsBug.BugId]
			if projectBug.Id != 0 {
				projectBug.MarsRecordId = marsBug.Id
				projectBug.Type = marsBug.Type
				projectBug.CaseName = bugCaseNameMap[marsBug.Id]
				projectBug.TypeName = constants.GetTestPLanRelationName(marsBug.Type)
				projectBugDTOs = append(projectBugDTOs, projectBug)
			}
		}
	}
	res := project2.ProjectBugDTO{
		ProjectBugStatisticsDTO: dto.ProjectBugStatisticsDTO{
			Total:     total,
			Fixed:     fix,
			FixedRate: 0,
		},
		BugDTOS: projectBugDTOs,
	}
	if total != 0 {
		res.ProjectBugStatisticsDTO.FixedRate = float32(fix) / float32(total)
	}
	return &res, nil
}

func (t ProjectService) GetPriorityLevel(ctx *commoncontext.MantisContext) ([]models.PriorityLevelDTO, error) {
	return project.ProjectRemoteApi.GetPriorityLevel(ctx)
}

func (t ProjectService) GetProjectInfo(ctx *commoncontext.MantisContext, ids []string, search string) ([]models.ProjectDTO, error) {
	return project.ProjectRemoteApi.GetProjectInfo(ctx, ids, search)
}
