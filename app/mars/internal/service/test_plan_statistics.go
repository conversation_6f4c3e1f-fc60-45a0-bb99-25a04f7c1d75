package service

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	set "github.com/duke-git/lancet/v2/datastructure/set"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/service/download"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/notification"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type TestPlanStatisticsService struct{}

func (TestPlanStatisticsService) GetReportList(ctx *commoncontext.MantisContext, req dto.MarsReportSearchReq, pageReq gormx.PageRequest) (*gormx.PageResult, error) {
	projectId := ctx.Header.Get("spaceid")
	sql := `SELECT id,"name", stage, test_plan_id, env, begin_time, end_time, "owner", summary, test_plan_pass_rate,space_id 
		    FROM mars_test_plan_report where is_deleted='N' and space_id=? `
	params := []any{projectId}
	if req.Stage != "" {
		params = append(params, strings.Split(req.Stage, ","))
		sql += " and stage in (?)"
	}
	if req.Owner != "" {
		// (owner @> '["admin"]' OR owner @> '["x"]')
		ownerList := strings.Split(req.Owner, ",")
		temSql := ""
		for _, owner := range ownerList {
			temSql += " or owner @> '[\"" + owner + "\"]'"
		}
		temSql = strings.Replace(temSql, "or", "", 1)
		sql += fmt.Sprintf("and (%s)", temSql)
	}
	if req.BeginTime != "" {
		sql += " and begin_time >= ? and begin_time <= ?"
		params = append(params, times.GetDataFromCommaString(req.BeginTime, 0), times.GetDataFromCommaString(req.BeginTime, 1))
	}
	if req.EndTime != "" {
		sql += " and end_time >= ? and end_time <= ?"
		params = append(params, times.GetDataFromCommaString(req.EndTime, 0), times.GetDataFromCommaString(req.EndTime, 1))
	}
	if req.Name != "" {
		params = append(params, "%"+req.Name+"%")
		sql += " and name like ?"
	}
	sql += " order by id desc"
	list := make([]models.MarsTestPlanReport, 0)
	page, err := gormx.PageSelectByRaw(ctx, sql, &list, pageReq, params...)
	if err != nil {
		return nil, err
	}
	if len(list) == 0 {
		return page, nil
	}
	testPlanIds := make([]int64, 0)
	for _, report := range list {
		testPlanIds = append(testPlanIds, report.TestPlanId)
	}
	plans := make([]models.MarsTestPlan, 0)
	err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MarsTestPlan{}).
		SelectMany("id", "related_iteration").In("id", testPlanIds), &plans)
	if err != nil {
		logger.Logger.Errorf("获取计划的迭代异常,err = %v", err)
		return nil, err
	}
	planIterationMap := make(map[int64]string)
	iterationIds := set.New[string]()
	for _, p := range plans {
		if p.RelatedIteration != "" {
			iterationIds.Add(p.RelatedIteration)
			planIterationMap[p.Id] = p.RelatedIteration
		}
	}
	if len(iterationIds) > 0 {
		projectId := ctx.Header.Get("spaceid")
		iterations, err := projectService.GetIterationList(ctx,
			dto.ProjectCommonSelectReq{Ids: iterationIds.ToSlice(), ProjectId: projectId},
			gormx.PageRequest{Page: 1, PageSize: 999})
		if err != nil {
			return nil, err
		}
		iterationMap := make(map[string]models.ProjectItemDTO)
		for _, ite := range iterations {
			iterationMap[strconv.FormatInt(ite.Id, 10)] = ite
		}
		for i, report := range list {
			itId, itExist := planIterationMap[report.TestPlanId]
			if !itExist {
				continue
			}
			it, ok := iterationMap[itId]
			if ok {
				list[i].Iteration = &it
			}
		}
	}
	return page, nil
}

func (TestPlanStatisticsService) Delete(ctx *commoncontext.MantisContext, id int64) error {
	_, err := gormx.UpdateBatchByParamBuilderAndMap(ctx,
		gormx.NewParamBuilder().Model(&models.MarsTestPlanReport{}).Eq("id", id),
		map[string]any{
			"modifier":     ctx.User.AdAccount,
			"gmt_modified": times.Now(),
			"is_deleted":   commonconstants.DeleteYes,
		})
	return err
}

func (TestPlanStatisticsService) Update(ctx *commoncontext.MantisContext, id int64, name, summary string) error {
	report := models.MarsTestPlanReport{
		Name:    name,
		Summary: summary,
	}
	report.Id = id
	report.Modifier = ctx.User.AdAccount
	report.GmtModified = times.Now()
	_, err := gormx.UpdateOneByCondition(ctx, &report)
	return err
}

func (t TestPlanStatisticsService) Info(ctx *commoncontext.MantisContext, id int64) (*models.MarsTestPlanReport, error) {
	res := models.MarsTestPlanReport{}
	res.Id = id
	res.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &res)
	if err != nil {
		return nil, err
	}
	envMap, err := getEnvMap(ctx)
	if err != nil {
		return nil, err
	}
	res.EnvName = envMap[res.Env]
	return &res, nil
}

func (TestPlanStatisticsService) SendReportMail(ctx *commoncontext.MantisContext, req dto.MarsEmailReportReq) {
	plan := models.MarsTestPlan{}
	plan.Id = req.TestPlanId
	plan.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByCondition(ctx, &plan)
	goroutine.RunWithoutLimit(func() {
		html := fmt.Sprintf(`<!DOCTYPE html> <html> <head> <meta charset="utf-8"></head> <style>
		.reportLink {
		margin-bottom: 50px;
		}
		</style><body><p class="reportLink">测试计划名称：%s<br/>测试计划完整报告：<a href="%s">%s</a></p>
		 <img src="%s"</body></html>`, plan.Name, req.Url, req.Url, req.File)
		notification.Send(req.EmailUserIds, fmt.Sprintf("测试计划执行报告-%s", plan.Name), html,
			req.NoticeType, "", ctx.User.CompanyID)
	})
}

func (t TestPlanStatisticsService) DownloadReport(ctx *commoncontext.MantisContext, rType string, req dto.DownloadReportReq) (string, []byte, error) {
	if rType != "pdf" {
		return "", nil, errors.New("only pdf is supported")
	}
	if req.Image == "" {
		return "", nil, errors.New("image is required")
	}

	// 修改图片路径为/tmp目录下
	imagePath := "/tmp/cube-mantis/images/" + strconv.FormatInt(snowflake.GenSnowFlakeId(), 10) + ".jpeg"

	err := download.SaveBase64ToImage(req.Image, imagePath)
	if err != nil {
		return "", nil, err
	}
	report := &models.MarsTestPlanReport{}
	if req.Id != 0 {
		report.Id = req.Id
		report.IsDeleted = commonconstants.DeleteNo
		err := gormx.SelectOneByCondition(ctx, report)
		if err != nil {
			return "", nil, err
		}
	}
	if req.Id == 0 && req.PlanId != 0 {
		report, err = TestPlanService{}.ReportView(ctx, req.PlanId)
		if err != nil {
			return "", nil, err
		}
	}
	tableDataList := make([]download.TableData, 0)
	caseMap := report.TestPlanCaseMap
	// 功能用例表格
	if list, ok := caseMap[constants.TestPlanRelationTypeCase]; ok {
		tableData := download.TableData{
			Title:   "功能用例列表",
			Headers: []string{"类型", "ID", "名称", "用例模块", "优先级", "结果", "执行人", "缺陷"},
			Widths:  []float64{20, 34, 25, 35, 15, 12, 25, 12},
		}
		tableData.Data = make([][]string, 0)
		for _, c := range list {
			tableData.Data = append(tableData.Data, []string{
				"功能用例",
				strconv.FormatInt(c.RelationId, 10),
				c.Name,
				c.NodeName,
				c.Priority,
				constants.TestPlanResultType(c.ExecuteResult),
				strings.Join(c.Executors, ","),
				strconv.FormatInt(c.BugSize, 10),
			})
		}
		tableDataList = append(tableDataList, tableData)
	}
	// api 用例列表
	if apiList, ok := caseMap[constants.TestPlanRelationTypeApi]; ok {
		tableData := download.TableData{
			Title:   "接口自动化用例列表",
			Headers: []string{"类型", "ID", "名称", "用例库", "用例集", "结果", "执行人", "缺陷"},
			Widths:  []float64{20, 24, 25, 35, 35, 12, 25, 12},
		}
		tableData.Data = make([][]string, 0)
		for _, c := range apiList {
			tableData.Data = append(tableData.Data, []string{
				"接口用例",
				strconv.FormatInt(c.RelationId, 10),
				c.Name,
				c.Library,
				c.NodeName,
				constants.TestPlanResultType(c.ExecuteResult),
				strings.Join(c.Executors, ","),
				strconv.FormatInt(c.BugSize, 10),
			})
		}
		tableDataList = append(tableDataList, tableData)
	}
	// ui 用例列表
	if uiList, ok := caseMap[constants.TestPlanRelationTypeUI]; ok {
		tableData := download.TableData{
			Title:   "UI 自动化计划列表",
			Headers: []string{"类型", "ID", "名称", "环境", "用例个数", "结果", "执行人", "缺陷"},
			Widths:  []float64{20, 34, 25, 35, 25, 12, 25, 12},
		}
		tableData.Data = make([][]string, 0)
		for _, c := range uiList {
			tableData.Data = append(tableData.Data, []string{
				"UI 计划",
				strconv.FormatInt(c.RelationId, 10),
				c.Name,
				c.EnvName,
				strconv.FormatInt(c.CaseCount, 10),
				constants.TestPlanResultType(c.ExecuteResult),
				strings.Join(c.Executors, ","),
				strconv.FormatInt(c.BugSize, 10),
			})
		}
		tableDataList = append(tableDataList, tableData)
	}
	// 需求列表
	testPlan := models.MarsTestPlan{}
	testPlan.Id = report.TestPlanId
	err = gormx.SelectOneByCondition(ctx, &testPlan)
	if err != nil {
		return "", nil, err
	}
	if testPlan.PlanType == constants.TestPLanTypeDemand {
		tableData := download.TableData{
			Title:   "需求列表",
			Headers: []string{"需求编号", "名称", "需求状态", "需求类型", "优先级", "项目", "迭代"},
			Widths:  []float64{20, 34, 25, 25, 25, 12, 25, 22},
		}
		tableData.Data = make([][]string, 0)
		for _, c := range report.DemandList {
			tableData.Data = append(tableData.Data, []string{
				c.Code,
				c.Name,
				c.OverState,
				c.Type,
				c.Priority,
				c.Project,
				c.Iteration,
			})
		}
		tableDataList = append(tableDataList, tableData)
	}
	// bug 列表
	bugTableData := download.TableData{
		Title:   "缺陷列表",
		Headers: []string{"ID", "名称", "用例名称", "类型", "优先级", "经办人", "状态", "修复时长", "reopen"},
		Widths:  []float64{20, 26, 25, 20, 20, 20, 15, 22, 20},
	}
	bugTableData.Data = make([][]string, 0)
	for _, c := range report.BugSnapshot {
		bugTableData.Data = append(bugTableData.Data, []string{
			strconv.FormatInt(c.Id, 10),
			c.Name,
			c.CaseName,
			c.Type,
			c.PriorityName,
			c.Assignee,
			c.OverState,
			strconv.FormatInt(c.FixTime, 10),
			strconv.FormatInt(c.ReopenNum, 10),
		})
	}
	tableDataList = append(tableDataList, bugTableData)
	pdfBytes, err := download.GenPdf(download.FirstPage{
		Title:    "CUBE-MANTIS 报告",
		SubTitle: report.Name,
		Creator:  ctx.User.AdAccount,
	}, []string{imagePath}, tableDataList)
	if err != nil {
		return "", nil, err
	}
	return report.Name + ".pdf", pdfBytes, nil
}
