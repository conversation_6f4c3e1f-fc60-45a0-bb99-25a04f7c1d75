package service

import (
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/enums"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	commonmodels "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	commonremote "git.zhonganinfo.com/zainfo/cube-mantis/pkg/remote"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	"gorm.io/gorm"
)

type NodeService struct{}

var (
	caseService     CaseService
	marsCaseTagsDao dao.MarsCaseTagsDao
)

func (NodeService) AddNode(ctx *commoncontext.MantisContext, node dto.NodeDto) (*dto.NodeDto, error) {
	marsCase := models.MarsCase{
		Addons:    commonmodels.NewAddonsWithUser(ctx.User),
		Name:      node.Name,
		LibraryId: node.LibraryId,
		ParentId:  node.ParentId,
		NodeType:  node.NodeType,
	}
	// 校验重名
	nodes := make([]models.MarsCase, 0)
	err := gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCase{}).Eq("library_id", marsCase.LibraryId).Eq("parent_id", marsCase.ParentId).Eq("name", marsCase.Name).Eq("is_deleted", commonconstants.DeleteNo),
		&nodes)
	if err != nil {
		return nil, err
	}
	if len(nodes) > 0 {
		return nil, fmt.Errorf("节点名称[%s]已存在", marsCase.Name)
	}
	order, err := marsCaseDao.SelectMaxSiblingOrder(ctx, marsCase.ParentId)
	if err != nil {
		return nil, err
	}
	marsCase.SiblingOrder = order + 1
	_, err = gormx.InsertOne(ctx, &marsCase)
	node.Id = marsCase.Id
	return &node, err
}

// CopyNode 获取此节点下所有的子节点,并复制插入 snowflake.GenSnowFlakeId()
func (NodeService) CopyNode(ctx *commoncontext.MantisContext, id int64) error {
	copyNode := models.MarsCase{}
	copyNode.Id = id
	copyNode.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &copyNode)
	if err != nil {
		return err
	}
	if copyNode.Id == 0 {
		logger.Logger.Panic("未找到复制的节点！")
	}
	// 修改copyNode之后的节点的sibling_order
	err = marsCaseDao.UpdateBrotherSiblingOrder(ctx, copyNode.ParentId, copyNode.SiblingOrder)
	if err != nil {
		return err
	}
	copyNode.SiblingOrder = copyNode.SiblingOrder + 1
	oldId := copyNode.Id
	copyNode.Id = snowflake.GenSnowFlakeId()
	copyNode.Name += constants.CopySuffix
	copyNode.SetTimeNowAndUser(ctx.User.AdAccount)
	// 校验重名
	nodes := make([]models.MarsCase, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCase{}).Eq("library_id", copyNode.LibraryId).Eq("parent_id", copyNode.ParentId).Eq("name", copyNode.Name).Eq("is_deleted", commonconstants.DeleteNo),
		&nodes)
	if err != nil {
		return err
	}
	if len(nodes) > 0 {
		return fmt.Errorf("节点名称[%s]已存在", copyNode.Name)
	}
	// 插入copyNode
	_, err = gormx.InsertOne(ctx, &copyNode)
	if err != nil {
		return err
	}
	// 查出该节点的子节点并统一复制
	srcNodes, err := marsNodeHierarchyDao.SelectChildrenUnderParent(ctx, copyNode.LibraryId, id)
	if err != nil {
		return err
	}
	copyCases := make([]models.MarsCase, 0, len(srcNodes))
	idMapping := make(map[int64]int64)
	idMapping[oldId] = copyNode.Id
	for _, node := range srcNodes {
		dest := node
		dest.Id = snowflake.GenSnowFlakeId()
		dest.SetTimeNowAndUser(ctx.User.AdAccount)
		copyCases = append(copyCases, dest)
		idMapping[node.Id] = dest.Id
	}
	// 修改 parentId
	for i := range copyCases {
		copyCases[i].ParentId = idMapping[copyCases[i].ParentId]
	}
	if len(copyCases) == 0 {
		return nil
	}
	_, err = gormx.InsertBatch(ctx, &copyCases)
	return err
}

func (NodeService) CheckDelete(ctx *commoncontext.MantisContext, id int64) (map[string]interface{}, error) {
	deleteNode := models.MarsCase{}
	deleteNode.Id = id
	gormx.SelectOneByCondition(ctx, &deleteNode)
	// 获取该目录下的所有子节点
	children, err := marsNodeHierarchyDao.SelectChildrenUnderParent(ctx, deleteNode.LibraryId, deleteNode.Id)
	if err != nil {
		return nil, err
	}
	folderIds := make([]int64, 0)
	for _, child := range children {
		folderIds = append(folderIds, child.Id)
	}
	// 查询将要删除的case节点
	deleteCaseIds := make([]int64, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCase{}).Select("id").In("parent_id", folderIds).Eq("is_deleted", commonconstants.DeleteNo),
		&deleteCaseIds)
	if err != nil {
		return nil, err
	}
	return caseService.CheckDelete(ctx, deleteCaseIds)
}

// DeleteNode 树节点删除
func (NodeService) DeleteNode(ctx *commoncontext.MantisContext, id int64) error {
	// 获取该节点下所有的用例节点
	caseIds, err := marsNodeHierarchyDao.RecursionSelectIds(ctx, id, constants.MarsNodeTypeCase)
	if err != nil {
		return err
	}
	// 递归删除该节点下及所有的子节点
	err = marsNodeHierarchyDao.RecursionRemoveById(ctx, id)
	if err != nil {
		return err
	}
	if len(caseIds) != 0 {
		// 删除用例节点所有的关联关系
		_, err = gormx.Exec(ctx, "update mars_case_relation set is_deleted = 'Y' where case_id in ?", caseIds)
		if err != nil {
			return err
		}
		// 删除测试计划关联的用例节点
		rids := make([]string, len(caseIds))
		for i, id := range caseIds {
			rids[i] = strconv.FormatInt(id, 10)
		}
		_, err = gormx.UpdateBatchByParamBuilderAndMap(ctx,
			gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseRelation{}).Eq("type", constants.TestPlanRelationTypeCase).
				In("relation_id", rids),
			map[string]any{"is_deleted": commonconstants.DeleteYes})
		if err != nil {
			return err
		}
	}
	return nil
}

func (NodeService) Update(ctx *commoncontext.MantisContext, node dto.NodeDto) error {
	marsCase := &models.MarsCase{
		Name: node.Name,
	}
	// 校验重名
	nodes := make([]models.MarsCase, 0)
	err := gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCase{}).Eq("library_id", node.LibraryId).Eq("parent_id", node.ParentId).Eq("name", node.Name).NotEq("id", node.Id).Eq("is_deleted", commonconstants.DeleteNo),
		&nodes)
	if err != nil {
		return err
	}
	if len(nodes) > 0 {
		return fmt.Errorf("节点名称[%s]已存在", node.Name)
	}
	marsCase.Id = node.Id
	marsCase.GmtModified = times.Now()
	marsCase.Modifier = ctx.User.AdAccount
	_, err = gormx.UpdateOneByCondition(ctx, &marsCase)
	return err
}

func (NodeService) Move(ctx *commoncontext.MantisContext, moveDTO dto.MarsNodeMoveDTO) error {
	caseNode := models.MarsCase{}
	caseNode.Id = moveDTO.Id
	caseNode.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &caseNode)
	if err != nil {
		return err
	}
	if caseNode.Id == 0 {
		logger.Logger.Infof("未找到要移动的节点！%d", moveDTO.Id)
		return errors.New("未找到要移动的节点！请刷新后重试")
	}

	// 校验目标父节点下是否有重名的节点
	existingNodes := make([]models.MarsCase, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCase{}).
			Eq("library_id", caseNode.LibraryId).
			Eq("parent_id", moveDTO.NewParentId).
			Eq("name", caseNode.Name).
			Eq("is_deleted", commonconstants.DeleteNo),
		&existingNodes)
	if err != nil {
		return err
	}

	// 检查是否有重名节点（排除自身）
	for _, existingNode := range existingNodes {
		if existingNode.Id != moveDTO.Id {
			return fmt.Errorf("目标位置已存在同名节点[%s]", caseNode.Name)
		}
	}

	updateNode := models.MarsCase{}
	updateNode.Id = moveDTO.Id
	updateNode.ParentId = moveDTO.NewParentId
	updateNode.GmtModified = times.Now()
	updateNode.Modifier = ctx.User.AdAccount
	if moveDTO.NewSiblingId == constants.NotSiblingId {
		// 当前目录的最后一个节点
		order, err := marsCaseDao.SelectMaxSiblingOrder(ctx, moveDTO.NewParentId)
		if err != nil {
			return err
		}
		updateNode.SiblingOrder = order + 1
	} else {
		// 插入到新的兄弟节点之前
		order, err := marsCaseDao.SelectSiblingOrder(ctx, moveDTO.NewSiblingId)
		if err != nil {
			return err
		}
		// 更新后续同级节点
		err = marsCaseDao.UpdateBrotherSiblingOrder(ctx, moveDTO.NewParentId, order-1)
		if err != nil {
			return err
		}
		updateNode.SiblingOrder = order
	}
	_, err = gormx.UpdateOneByCondition(ctx, &updateNode)
	return err
}

// GetTree 获取目录树
func (s NodeService) GetTree(ctx *commoncontext.MantisContext, libId int64) (*dto.MarsFolderTree, error) {
	marsList := make([]models.MarsCase, 0)
	err := gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MarsCase{}).Eq("library_id", libId).
		In("node_type", []string{enums.RootNode.Value, enums.FolderNode.Value}).
		Eq("is_deleted", commonconstants.DeleteNo), &marsList)
	if err != nil {
		return nil, err
	}

	// 查询目录和叶子节点数统计map
	parentIdCaseCountMap, err := marsCaseDao.CountCaseByParentIdInLib(ctx, libId)
	if err != nil {
		return nil, err
	}

	// 将 marsList 转成 treeDtoMap
	treeDtoMap := make(map[int64]*dto.MarsFolderTree)

	// 先创建所有节点
	for _, node := range marsList {
		treeDtoMap[node.Id] = &dto.MarsFolderTree{
			Id:               node.Id,
			Name:             node.Name,
			NodeType:         node.NodeType,
			ParentId:         node.ParentId,
			SiblingOrder:     node.SiblingOrder,
			LibraryId:        node.LibraryId,
			DescendantsTotal: parentIdCaseCountMap[node.Id],
			Children:         make([]*dto.MarsFolderTree, 0),
		}
	}

	// 构建树结构
	var rootNode *dto.MarsFolderTree
	for _, node := range marsList {
		if node.ParentId == 0 && node.NodeType == enums.RootNode.Value {
			// 根节点
			rootNode = treeDtoMap[node.Id]
		} else if parent, exists := treeDtoMap[node.ParentId]; exists {
			// 将子节点添加到父节点
			childNode := treeDtoMap[node.Id]
			parent.Children = append(parent.Children, childNode)
		}
	}

	// 构建叶子节点数
	s.buildLeafNodeCount(rootNode, parentIdCaseCountMap)

	// 递归排序所有节点的子节点
	var sortChildren func(node *dto.MarsFolderTree)
	sortChildren = func(node *dto.MarsFolderTree) {
		if len(node.Children) > 0 {
			// 根据 SiblingOrder 排序
			sort.Slice(node.Children, func(i, j int) bool {
				return node.Children[i].SiblingOrder < node.Children[j].SiblingOrder
			})

			// 递归排序子节点
			for i := range node.Children {
				childPtr := node.Children[i]
				sortChildren(childPtr)
			}
		}
	}

	// 排序根节点的子节点
	sortChildren(rootNode)

	return rootNode, nil
}

func (s NodeService) buildLeafNodeCount(root *dto.MarsFolderTree, parentIdCaseCountMap map[int64]int64) int64 {
	if len(root.Children) == 0 {
		root.DescendantsTotal = parentIdCaseCountMap[root.Id]
	} else {
		descendantsTotal := parentIdCaseCountMap[root.Id]
		for _, child := range root.Children {
			descendantsTotal += s.buildLeafNodeCount(child, parentIdCaseCountMap)
		}
		root.DescendantsTotal = descendantsTotal
	}
	return root.DescendantsTotal
}

func (NodeService) GetCaseList(ctx *commoncontext.MantisContext, reqDTO dto.MarsCaseListReqDTO, pageReq gormx.PageRequest) (*gormx.PageResult, error) {
	ids, err := marsCaseDao.SelectIdChildByParentId(ctx, reqDTO.ParentId, []string{enums.CaseNode.Value}, reqDTO.Nesting)
	if err != nil {
		return nil, err
	}
	if len(ids) == 0 {
		logger.Logger.Warnf("父节点parentId={%d}下未找到用例！", reqDTO.ParentId)
		return &gormx.PageResult{}, nil
	}
	list := make([]models.MarsCase, 0)
	rawSql := `select * from mars_case where id in ?`
	params := []any{ids}
	if reqDTO.Name != "" {
		rawSql += ` and name ilike ?`
		params = append(params, "%"+reqDTO.Name+"%")
	}
	if reqDTO.Creator != "" {
		rawSql += ` and creator = ?`
		params = append(params, reqDTO.Creator)
	}
	if len(reqDTO.Priority) != 0 {
		rawSql += ` and priority in ?`
		params = append(params, reqDTO.Priority)
	}
	if reqDTO.GmtCreated != "" {
		rawSql += ` and gmt_created >= ? and gmt_created <= ?`
		params = append(params, reqDTO.GetCreatTimeStart(), reqDTO.GetCreatTimeEnd())
	}
	if len(reqDTO.TagIds) != 0 {
		arrayTags := "array["
		for _, tag := range reqDTO.TagIds {
			arrayTags += fmt.Sprintf(`'["%s"]',`, tag)
		}
		arrayTags = arrayTags[:len(arrayTags)-1]
		arrayTags += "]"
		rawSql += ` and tags @> any (?::jsonb[])`
		params = append(params, gorm.Expr(arrayTags))
	}
	rawSql += ` order by id desc`
	res, err := gormx.PageSelectByRaw(ctx, rawSql, &list, pageReq, params...)
	if err != nil {
		return nil, err
	}
	if len(list) == 0 {
		return res, nil
	}
	// 查询 tags
	tagIds := make([]string, 0)
	for _, c := range list {
		tagIds = append(tagIds, c.TagIds...)
	}
	tags, err := commonremote.CubeBaseRemoteApi{}.GetTagsByIds(ctx, strings.Join(tagIds, ","))
	if err != nil {
		return nil, err
	}
	tagMap := make(map[string]*commondto.CubeTagDTO)
	for _, tag := range tags {
		tagMap[strconv.FormatInt(tag.Value, 10)] = &tag
	}
	for i := range list {
		currentTags := make([]commondto.CubeTagDTO, 0)
		for _, tagId := range list[i].TagIds {
			if tag, ok := tagMap[tagId]; ok && tag != nil {
				currentTags = append(currentTags, *tag)
			}
		}
		list[i].Tags = currentTags
	}
	res.List = list
	return res, nil
}
