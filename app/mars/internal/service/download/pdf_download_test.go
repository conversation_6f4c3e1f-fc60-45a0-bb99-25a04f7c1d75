package download

import (
	"fmt"
	"log"
	"os"
	"testing"
	"time"
)

// 测试

func TestGenPdf(t *testing.T) {
	// 首页数据
	firstPage := FirstPage{
		Title:    "CUBE-MANTIS 报告",
		SubTitle: "测试计划名称",
		Creator:  "张三",
	}
	images := []string{"/Users/<USER>/work/project/go-p/go-test/pdf-test/images/output.jpg"}
	tables := []TableData{
		{
			Title:   "表格数据 1",
			Headers: []string{"类型", "ID", "名称", "用例模块", "优先级", "结果", "执行人", "缺陷"},
			Widths:  []float64{20, 34, 25, 35, 35, 12, 15, 12},
			Data: [][]string{
				{"功能测试", "198925281873989", "这是一个很长的测试用例名称，需要自动换行显示", "内容C/功能测试/这是一个很长的模块路径", "高", "通过", "张三", "10"},
				{"性能测试", "198925281873990", "测试用例 def", "内容C/性能测试/压力测试模块", "中", "失败", "李四", "5"},
				{"接口测试", "198925281873991", "API接口测试用例", "内容C/接口测试/RESTful API", "高", "通过", "王五", "0"},
				{"接口测试", "198925281873992", "API接口测试用例2", "内容C/接口测试/RESTful API", "高", "通过", "王五", "0"},
				{"接口测试", "198925281873993", "API接口测试用例3", "内容C/接口测试/RESTful API", "高", "通过", "王五", "0"},
				{"接口测试", "198925281873994", "API接口测试用例4", "内容C/接口测试/RESTful API", "高", "通过", "王五", "0"},
				{"接口测试", "198925281873995", "API接口测试用例5", "内容C/接口测试/RESTful API", "高", "通过", "王五", "0"},
				{"接口测试", "198925281873996", "API接口测试用例6", "内容C/接口测试/RESTful API", "高", "通过", "王五", "0"},
				{"接口测试", "198925281873997", "API接口测试用例7", "内容C/接口测试/RESTful API", "高", "通过", "王五", "0"},
				{"接口测试", "198925281873998", "API接口测试用例8", "内容C/接口测试/RESTful API", "高", "通过", "王五", "0"},
				{"接口测试", "198925281873999", "API接口测试用例9", "内容C/接口测试/RESTful API", "高", "通过", "王五", "0"},
				{"接口测试", "198925281874000", "API接口测试用例10", "内容C/接口测试/RESTful API", "高", "通过", "王五", "0"},
				{"接口测试", "198925281874001", "API接口测试用例11", "内容C/接口测试/RESTful API", "高", "通过", "王五", "0"},
				{"接口测试", "198925281874002", "API接口测试用例12", "内容C/接口测试/RESTful API", "高", "通过", "王五", "0"},
				{"接口测试", "198925281874003", "API接口测试用例13", "内容C/接口测试/RESTful API", "高", "通过", "王五", "0"},
				{"接口测试", "198925281874004", "API接口测试用例14", "内容C/接口测试/RESTful API", "高", "通过", "王五", "0"},
				{"接口测试", "198925281874005", "API接口测试用例15", "内容C/接口测试/RESTful API", "高", "通过", "王五", "0"},
				{"接口测试", "198925281874006", "API接口测试用例16", "内容C/接口测试/RESTful API", "高", "通过", "王五", "0"},
			},
		},
		{
			Title:   "表格数据 2",
			Headers: []string{"类型", "ID", "名称", "用例模块", "优先级", "结果"},
			Widths:  []float64{25, 40, 35, 70, 20, 20},
			Data: [][]string{
				{"功能测试", "198925281873989", "这是一个很长的测试用例名称，需要自动换行显示", "内容C/功能测试/这是一个很长的模块路径", "高", "通过"},
				{"性能测试", "198925281873990", "测试用例 def", "内容C/性能测试/压力测试模块", "中", "失败"},
				{"接口测试", "198925281873991", "API接口测试用例", "内容C/接口测试/RESTful API", "高", "通过"},
			},
		},
	}
	pdfBytes, _ := GenPdf(firstPage, images, tables)

	// 生成文件名（包含时间戳）
	timestamp := time.Now().Unix()
	filename := fmt.Sprintf("./pdf-out/report_%d.pdf", timestamp)

	// 保存PDF文件
	err := os.WriteFile(filename, pdfBytes, 0o644)
	if err != nil {
		log.Fatal("保存PDF失败:", err)
	}

	fmt.Printf("PDF已成功生成: %s\n", filename)
}
