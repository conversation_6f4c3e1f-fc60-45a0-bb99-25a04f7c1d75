package download

import (
	"bytes"
	"fmt"
	"image"
	_ "image/jpeg"
	_ "image/png"
	"os"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/ttf"
	"github.com/signintech/gopdf"
)

// TableData 表格数据结构
type TableData struct {
	Title   string     // 表格标题
	Headers []string   // 表头列名
	Widths  []float64  // 各列宽度(mm)
	Data    [][]string // 表格数据内容
}

// FirstPage 首页信息
type FirstPage struct {
	Title    string // 标题
	SubTitle string // 副标题
	Creator  string // 创建人
}

// PDF常量定义
const (
	// 页面设置
	pageWidth       = 210.0 // A4宽度(mm)
	pageHeight      = 297.0 // A4高度(mm)
	pageMargin      = 10.0  // 页边距(mm)
	tableLeftMargin = 10.0  // 表格左边距(mm)

	// 位置设置
	titlePositionY    = 40.0  // 标题Y坐标(mm)
	subtitlePositionY = 70.0  // 副标题Y坐标(mm)
	creatorPositionY  = 250.0 // 创建者信息Y坐标(mm)
	timePositionY     = 260.0 // 时间信息Y坐标(mm)
	imageMargin       = 20.0  // 图片上边距(mm)
	minBottomMargin   = 50.0  // 页面底部最小边距(mm)

	// 字体设置
	defaultFontSize  = 12.0 // 默认字体大小
	titleFontSize    = 24.0 // 标题字体大小
	subtitleFontSize = 18.0 // 副标题字体大小
	footerFontSize   = 10.0 // 页脚字体大小

	// 行高和单元格设置
	lineHeight   = 10.0 // 基本行高(mm)
	headerHeight = 10.0 // 表头行高(mm)
	cellPadding  = 2.0  // 单元格内边距(mm)

	// 间距设置
	tableTitleSpacing   = 8.0  // 表格标题与表格间距(mm)
	tableBottomSpacing  = 10.0 // 表格底部间距(mm)
	newPageHeaderMargin = 20.0 // 新页面顶部边距(mm)

	// 颜色设置 - 浅灰色背景 (RGB)
	headerBackgrR = 240 // 表头背景红色分量
	headerBackgrG = 240 // 表头背景绿色分量
	headerBackgrB = 240 // 表头背景蓝色分量

	// 其他设置
	minColumnWidth       = 10.0  // 最小列宽(mm)
	maxCompressionRatio  = 0.3   // 最大列宽压缩比例
	defaultCharWidth     = 5.0   // 默认每个中文字符宽度(mm)
	widthCoefficient     = 1.2   // 中文字符宽度系数
	shortHeaderCoeff     = 1.5   // 短表头宽度系数
	lineWidthNormal      = 0.1   // 正常线宽(mm)
	textVerticalOffset   = 0.3   // 文本垂直偏移系数，用于垂直居中
	textWidthMargin      = 0.95  // 文本宽度预留系数，防止溢出
	imageWidthDefault    = 190.0 // 默认图片宽度(mm)
	maxShortHeaderChars  = 4     // 短表头最大字符数
	shortHeaderTolerance = 1.2   // 短表头宽度容差系数
)

// GenPdf 生成PDF文件
// firstPage: 首页信息
// images: 需要插入的图片路径列表
// tables: 需要绘制的表格数据
// 返回PDF文件的字节数据及可能的错误
func GenPdf(firstPage FirstPage, images []string, tables []TableData) ([]byte, error) {
	// 初始化PDF文档
	pdf := gopdf.GoPdf{}
	pdf.Start(gopdf.Config{
		PageSize: *gopdf.PageSizeA4,
		Unit:     gopdf.UnitMM,
	})

	// 准备字体
	if err := prepareFont(&pdf); err != nil {
		return nil, err
	}

	// 创建首页
	if err := createFirstPage(&pdf, firstPage); err != nil {
		return nil, err
	}

	// 处理图片页面和表格数据
	currentY := handleImagesPage(&pdf, images)
	for _, table := range tables {
		currentY = drawTable(&pdf, table, currentY)
	}

	// 导出PDF数据
	pdfBytes := bytes.NewBuffer(nil)
	if err := pdf.Write(pdfBytes); err != nil {
		return nil, fmt.Errorf("写入PDF数据失败: %w", err)
	}

	return pdfBytes.Bytes(), nil
}

// prepareFont 准备PDF字体
func prepareFont(pdf *gopdf.GoPdf) error {
	// 获取字体数据
	fontData, err := ttf.GetSimSunFontData()
	if err != nil {
		return fmt.Errorf("获取字体数据失败: %w", err)
	}

	// 添加字体到PDF
	if err = pdf.AddTTFFontData("SimSun", fontData); err != nil {
		return fmt.Errorf("添加字体失败: %w", err)
	}

	// 设置默认字体和颜色
	resetPdfState(pdf, "", defaultFontSize)
	return nil
}

// resetPdfState 重置PDF绘图状态(字体和颜色)
func resetPdfState(pdf *gopdf.GoPdf, fontStyle string, fontSize float64) {
	pdf.SetTextColor(0, 0, 0) // 黑色文本
	pdf.SetFont("SimSun", fontStyle, fontSize)
}

// createFirstPage 创建PDF首页
func createFirstPage(pdf *gopdf.GoPdf, firstPage FirstPage) error {
	pdf.AddPage()

	// 绘制标题(居中)
	resetPdfState(pdf, "", titleFontSize)
	titleWidth, _ := pdf.MeasureTextWidth(firstPage.Title)
	pdf.SetX((pageWidth - titleWidth) / 2)
	pdf.SetY(titlePositionY)
	pdf.Cell(nil, firstPage.Title)

	// 绘制副标题(居中)
	resetPdfState(pdf, "", subtitleFontSize)
	subtitleWidth, _ := pdf.MeasureTextWidth(firstPage.SubTitle)
	pdf.SetX((pageWidth - subtitleWidth) / 2)
	pdf.SetY(subtitlePositionY)
	pdf.Cell(nil, firstPage.SubTitle)

	// 绘制页脚信息(右对齐)
	resetPdfState(pdf, "", footerFontSize)
	currentTime := time.Now().Format("2006-01-02 15:04:05")

	// 创建人信息
	creatorText := "创建人：" + firstPage.Creator
	creatorWidth, _ := pdf.MeasureTextWidth(creatorText)
	pdf.SetX(pageWidth - pageMargin - creatorWidth)
	pdf.SetY(creatorPositionY)
	pdf.Cell(nil, creatorText)

	// 下载时间信息
	timeText := "下载时间：" + currentTime
	timeWidth, _ := pdf.MeasureTextWidth(timeText)
	pdf.SetX(pageWidth - pageMargin - timeWidth)
	pdf.SetY(timePositionY)
	pdf.Cell(nil, timeText)

	// 恢复默认字体
	resetPdfState(pdf, "", defaultFontSize)
	return nil
}

// handleImagesPage 处理图片页面，返回结束Y坐标
func handleImagesPage(pdf *gopdf.GoPdf, images []string) float64 {
	// 添加新页面
	pdf.AddPage()

	// 如果没有图片，返回顶部位置
	if len(images) == 0 {
		return newPageHeaderMargin
	}

	// 读取第一张图片
	imgFile, err := os.Open(images[0])
	if err != nil {
		return newPageHeaderMargin
	}
	defer imgFile.Close()

	// 获取图片尺寸并计算比例
	img, _, err := image.DecodeConfig(imgFile)
	if err != nil {
		return newPageHeaderMargin
	}

	// 计算图片在PDF中的尺寸，保持比例
	imgWidth := imageWidthDefault
	imgHeight := imgWidth * float64(img.Height) / float64(img.Width)

	// 插入图片
	err = pdf.Image(images[0], pageMargin, imageMargin, &gopdf.Rect{W: imgWidth, H: imgHeight})
	if err != nil {
		return imageMargin + 20
	}

	// 返回图片结束位置
	return imageMargin + imgHeight + 20
}

// drawTable 绘制表格，返回表格结束后的Y坐标
func drawTable(pdf *gopdf.GoPdf, table TableData, startY float64) float64 {
	// 检查是否需要换页
	if startY > pageHeight-minBottomMargin {
		pdf.AddPage()
		startY = newPageHeaderMargin
	}

	// 绘制表格标题(居中)
	resetPdfState(pdf, "", defaultFontSize)
	titleWidth, _ := pdf.MeasureTextWidth(table.Title)
	pdf.SetX((pageWidth - titleWidth) / 2)
	pdf.SetY(startY)
	pdf.Cell(nil, table.Title)

	// 计算表格位置和调整列宽度
	tableY := startY + tableTitleSpacing
	adjustColumnWidths(&table)

	// 绘制表头和数据行
	drawTableHeader(pdf, table, tableY)
	currentY := tableY + headerHeight
	currentY = drawTableRows(pdf, table, currentY)

	// 返回表格结束位置
	return currentY + tableBottomSpacing
}

// adjustColumnWidths 调整列宽度，确保表头不溢出且总宽度合理
func adjustColumnWidths(table *TableData) {
	// 1. 根据内容智能调整各列宽度
	for i, header := range table.Headers {
		if i >= len(table.Widths) {
			continue
		}

		// 根据表头文本长度动态调整
		headerLength := float64(len([]rune(header))) // 使用rune计算中文字符数

		// 增加中文字符宽度系数，防止表头被拆分
		suggestedWidth := headerLength * defaultCharWidth * widthCoefficient

		// 特殊处理可能换行的表头，确保它们有足够宽度保持在一行
		// 检测可能有问题的表头模式(2-4个字符的短中文词组)
		if headerLength >= 2 && headerLength <= maxShortHeaderChars {
			suggestedWidth = max(suggestedWidth, headerLength*defaultCharWidth*shortHeaderCoeff)
		}

		// 根据表头类型调整宽度
		suggestedWidth = getSpecialColumnWidth(header, suggestedWidth)

		// 更新列宽度，但不小于当前宽度（如果当前宽度更大）
		table.Widths[i] = max(table.Widths[i], suggestedWidth)
	}

	// 2. 确保总宽度不超出页面
	totalWidth := 0.0
	for _, width := range table.Widths {
		totalWidth += width
	}

	// 当总宽度超出页面时进行智能压缩
	maxPageWidth := pageWidth - (pageMargin * 2)
	if totalWidth > maxPageWidth {
		applyColumnCompression(table, totalWidth-maxPageWidth)
	}
}

// getSpecialColumnWidth 根据表头类型返回建议宽度
func getSpecialColumnWidth(header string, defaultWidth float64) float64 {
	switch header {
	case "用例名称", "用例名":
		return max(defaultWidth, 30.0)
	case "优先级", "结果", "缺陷":
		return max(defaultWidth, 20.0)
	case "执行人", "经办人":
		return max(defaultWidth, 30.0)
	case "ID", "标识符":
		return max(defaultWidth, 25.0)
	case "名称", "名字", "标题", "状态", "reopen":
		return max(defaultWidth, 25.0)
	case "描述", "内容":
		return max(defaultWidth, 40.0)
	case "修复时长":
		return max(defaultWidth, 30.0)
	default:
		// 通用表头最小宽度
		return max(defaultWidth, 20.0)
	}
}

// applyColumnCompression 智能压缩列宽度
func applyColumnCompression(table *TableData, excessWidth float64) {
	// 标记重要列和普通列
	importantColumns := make([]bool, len(table.Headers))
	totalNormalWidth := 0.0

	for i, header := range table.Headers {
		switch header {
		case "优先级", "结果", "缺陷", "执行人", "ID", "用例名称", "用例名":
			importantColumns[i] = true
		default:
			importantColumns[i] = false
			totalNormalWidth += table.Widths[i]
		}
	}

	// 仅压缩非重要列
	if totalNormalWidth > 0 {
		reductionRatio := min(excessWidth/totalNormalWidth, maxCompressionRatio)

		for i, isImportant := range importantColumns {
			if !isImportant && i < len(table.Widths) {
				reduction := table.Widths[i] * reductionRatio
				table.Widths[i] -= reduction
				table.Widths[i] = max(table.Widths[i], minColumnWidth) // 确保最小宽度
			}
		}
	}
}

// drawTableHeader 绘制表格表头
func drawTableHeader(pdf *gopdf.GoPdf, table TableData, y float64) {
	// 设置表头使用粗体
	resetPdfState(pdf, "B", defaultFontSize)

	// 绘制表头单元格
	currentX := tableLeftMargin
	for i, header := range table.Headers {
		if i < len(table.Widths) {
			// 绘制表头单元格(居中对齐)
			drawWrappedCell(pdf, currentX, y, table.Widths[i], headerHeight,
				header, true, gopdf.Center, true)
			currentX += table.Widths[i]
		}
	}

	// 恢复普通字体
	resetPdfState(pdf, "", defaultFontSize)
}

// drawTableRows 绘制表格数据行，返回结束Y坐标
func drawTableRows(pdf *gopdf.GoPdf, table TableData, startY float64) float64 {
	resetPdfState(pdf, "", defaultFontSize)
	currentY := startY
	rowCount := len(table.Data)

	for i := 0; i < rowCount; i++ {
		row := table.Data[i]

		// 计算行高
		rowHeight := calculateRowHeight(pdf, row, table.Widths)
		availableHeight := pageHeight - currentY - pageMargin

		// 检查是否需要换页
		if rowHeight > availableHeight && i > 0 {
			pdf.AddPage()
			currentY = newPageHeaderMargin

			// 在新页面重绘表头
			drawTableHeader(pdf, table, currentY)
			currentY += headerHeight
		}

		// 绘制数据行
		resetPdfState(pdf, "", defaultFontSize)
		drawTableRow(pdf, row, table.Widths, currentY, rowHeight)
		currentY += rowHeight

		// 检查剩余空间是否足够下一行
		if i < rowCount-1 && (pageHeight-currentY-pageMargin) < lineHeight {
			pdf.AddPage()
			currentY = newPageHeaderMargin
			drawTableHeader(pdf, table, currentY)
			currentY += headerHeight
		}
	}

	return currentY
}

// calculateRowHeight 计算一行数据所需的高度
func calculateRowHeight(pdf *gopdf.GoPdf, row []string, widths []float64) float64 {
	maxHeight := lineHeight

	for j, cell := range row {
		if j < len(widths) {
			_, cellHeight := wrapText(pdf, cell, widths[j]-(cellPadding*2))
			maxHeight = max(maxHeight, cellHeight)
		}
	}

	return maxHeight
}

// drawTableRow 绘制单行数据
func drawTableRow(pdf *gopdf.GoPdf, row []string, widths []float64, y float64, rowHeight float64) {
	currentX := tableLeftMargin

	for j, cell := range row {
		if j < len(widths) {
			// 数据行使用居中对齐以保持与表头一致
			drawWrappedCell(pdf, currentX, y, widths[j], rowHeight,
				cell, true, gopdf.Center, false)
			currentX += widths[j]
		}
	}
}

// drawWrappedCell 绘制带边框和换行的单元格
func drawWrappedCell(pdf *gopdf.GoPdf, x, y, width, height float64, text string, border bool, align int, isHeader bool) {
	// 计算单元格内边距和文本分行
	effectiveCellPadding := cellPadding
	if isHeader {
		effectiveCellPadding = cellPadding * 1.5 // 表头预留更多内边距
		align = gopdf.Center                     // 确保表头居中对齐
	}

	// 处理文本换行和高度计算
	lines, textHeight := wrapText(pdf, text, width-(effectiveCellPadding*2))
	height = max(height, textHeight)

	// 绘制边框和背景
	if border {
		// 设置统一的线宽
		pdf.SetLineWidth(lineWidthNormal)

		if isHeader {
			// 表头单元格：先填充背景，再绘制边框
			pdf.SetFillColor(headerBackgrR, headerBackgrG, headerBackgrB)
			pdf.Rectangle(x, y, x+width, y+height, "F", 0, 0) // 填充背景
			pdf.Rectangle(x, y, x+width, y+height, "D", 0, 0) // 绘制边框
		} else {
			// 普通单元格：只绘制边框
			pdf.Rectangle(x, y, x+width, y+height, "D", 0, 0)
		}
	}

	// 绘制文本
	drawCellText(pdf, x, y, width, height, lines, align, effectiveCellPadding)
}

// drawCellText 绘制单元格内文本
func drawCellText(pdf *gopdf.GoPdf, x, y, width, height float64, lines []string, align int, padding float64) {
	// 重置文本颜色
	pdf.SetTextColor(0, 0, 0)

	// 计算垂直居中位置
	totalLinesHeight := float64(len(lines)) * lineHeight
	verticalPadding := max(0, (height-totalLinesHeight)/2)
	startY := y + verticalPadding

	// 绘制每一行文本
	for i, line := range lines {
		lineY := startY + float64(i)*lineHeight
		textWidth, _ := pdf.MeasureTextWidth(line)

		// 根据对齐方式计算水平位置
		var textX float64
		switch align {
		case gopdf.Center:
			// 精确居中计算，防止文本过于贴近边框
			textX = max(x+padding, x+(width-textWidth)/2)
		case gopdf.Right:
			textX = x + width - textWidth - padding
		default: // Left
			textX = x + padding
		}

		// 设置位置并绘制文本
		pdf.SetX(textX)
		pdf.SetY(lineY + (lineHeight * textVerticalOffset)) // 垂直微调以实现视觉居中
		pdf.Cell(nil, line)
	}
}

// wrapText 计算文本在指定宽度下的分行显示
func wrapText(pdf *gopdf.GoPdf, text string, maxWidth float64) ([]string, float64) {
	// 处理空文本或短文本
	if text == "" {
		return []string{""}, lineHeight
	}

	textWidth, _ := pdf.MeasureTextWidth(text)
	if textWidth <= maxWidth {
		return []string{text}, lineHeight
	}

	// 分行处理文本(中文优化)
	chars := []rune(text)

	// 特别处理短表头，尝试不拆分
	if len(chars) <= maxShortHeaderChars && textWidth <= maxWidth*shortHeaderTolerance {
		// 短表头略微超出宽度时，也尽量保持为一行
		return []string{text}, lineHeight
	}

	// 有效宽度预留边距
	effectiveMaxWidth := maxWidth * textWidthMargin

	// 表头文本长度为2-4个字符时，尝试防止换行
	if len(chars) >= 2 && len(chars) <= maxShortHeaderChars {
		// 如果是可能的表头，增加容忍度，尽量不换行
		effectiveMaxWidth = maxWidth * 1.0 // 使用全部可用宽度
	}

	var lines []string
	var currentLine string

	for _, char := range chars {
		testLine := currentLine + string(char)
		testWidth, _ := pdf.MeasureTextWidth(testLine)

		if testWidth <= effectiveMaxWidth {
			currentLine = testLine
		} else {
			// 添加已完成的行并开始新行
			if currentLine != "" {
				lines = append(lines, currentLine)
				currentLine = string(char)
			} else {
				// 单个字符就超宽的极端情况
				lines = append(lines, string(char))
			}
		}
	}

	// 添加最后一行
	if currentLine != "" {
		lines = append(lines, currentLine)
	}

	// 计算总高度
	return lines, float64(len(lines)) * lineHeight
}

// max 返回两个浮点数的较大值
func max(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}

// min 返回两个浮点数的较小值
func min(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}
