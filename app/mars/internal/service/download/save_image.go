package download

import (
	"encoding/base64"
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

// SaveBase64ToImage 将base64编码转换为图片并保存到本地
func SaveBase64ToImage(base64String, filename string) error {
	// 移除可能的数据URL前缀
	if strings.HasPrefix(base64String, "data:image/") {
		parts := strings.Split(base64String, ",")
		if len(parts) == 2 {
			base64String = parts[1]
		}
	}

	// 解码base64字符串
	data, err := base64.StdEncoding.DecodeString(base64String)
	if err != nil {
		return fmt.Errorf("base64解码失败: %v", err)
	}

	// 确保目录存在
	if err := os.MkdirAll(filepath.Dir(filename), 0o755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	// 写入文件
	err = os.WriteFile(filename, data, 0o644)
	if err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	return nil
}
