package service

import (
	"bytes"
	"fmt"
	"mime/multipart"
	"sort"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/enums"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/remote/project"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/xmind"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	commonmodels "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type XmindService struct{}

func (s XmindService) Import(ctx *commoncontext.MantisContext, file *multipart.FileHeader, libId int64) (int, error) {
	// 解析xmind
	f, err := file.Open()
	if err != nil {
		return 0, err
	}
	defer f.Close()
	content, err := xmind.ParseXMind(f)
	if err != nil {
		return 0, err
	}
	// 解析content
	if content.RootTopic == nil || len(content.RootTopic.Children.Attached) == 0 {
		return 0, fmt.Errorf("未找到可导入的节点")
	}

	// 获取库的根节点
	var rootId int64
	err = gormx.Raw(ctx, "select id from mars_case where library_id = ? and parent_id = 0 and is_deleted='N'", &rootId, libId)
	if err != nil {
		return 0, err
	}

	var total int
	err = gormx.Transaction(ctx, func() error {
		total, err = s.importTopicsUpsert(ctx, content.RootTopic.Children.Attached, rootId, libId, []string{})
		return err
	})
	return total, err
}

// importTopicsUpsert 基于路径匹配导入或更新节点
func (s XmindService) importTopicsUpsert(ctx *commoncontext.MantisContext, topics []*xmind.Topic, parentId, libId int64, parentPath []string) (int, error) {
	var total int

	for _, topic := range topics {
		prefixStart := strings.Index(topic.Title, "[")
		prefixEnd := strings.Index(topic.Title, "]")
		if prefixEnd == 0 {
			return 0, fmt.Errorf("节点 %s 解析错误: 无可解析的类型前缀", topic.Title)
		}
		prefix := topic.Title[prefixStart : prefixEnd+1]
		nodeName := strings.Replace(topic.Title, prefix, "", 1)
		currentPath := append(parentPath, nodeName)

		switch prefix {
		case "[D]", "[目录]":
			// 查找是否存在匹配路径的目录节点
			existingNode, err := s.findNodeByPath(ctx, libId, currentPath, enums.FolderNode.Value)
			if err != nil {
				return 0, err
			}

			var nodeId int64
			if existingNode != nil {
				// 存在则使用现有节点
				nodeId = existingNode.Id
			} else {
				// 不存在则创建新节点
				maxSort, err := marsCaseDao.SelectMaxSiblingOrder(ctx, parentId)
				if err != nil {
					return 0, err
				}

				newNode := models.MarsCase{
					Addons:       commonmodels.NewAddonsWithUser(ctx.User),
					NodeType:     enums.FolderNode.Value,
					Name:         nodeName,
					ParentId:     parentId,
					LibraryId:    libId,
					SiblingOrder: maxSort + 1,
				}

				_, err = gormx.InsertOne(ctx, &newNode)
				if err != nil {
					return 0, err
				}
				nodeId = newNode.Id
			}

			// 递归处理子节点
			subTotal, err := s.importTopicsUpsert(ctx, topic.Children.Attached, nodeId, libId, currentPath)
			if err != nil {
				return 0, err
			}
			total += subTotal

		case "[C]", "[用例]":
			// 查找是否存在匹配路径的用例节点
			existingNode, err := s.findNodeByPath(ctx, libId, currentPath, enums.CaseNode.Value)
			if err != nil {
				return 0, err
			}

			// 构建用例数据
			caseData := models.MarsCase{
				NodeType:  enums.CaseNode.Value,
				Name:      nodeName,
				ParentId:  parentId,
				LibraryId: libId,
			}

			// 解析优先级
			if len(topic.Markers) != 0 && topic.Markers[0].MarkerId != "" {
				priority, err := s.convertXmindPriority2Mars(topic.Markers[0].MarkerId)
				if err != nil {
					return 0, fmt.Errorf("节点 %s 解析错误: %v", topic.Title, err)
				}
				caseData.Priority = priority
			}

			// 解析用例内容
			err = s.importCaseContents(topic.Children.Attached, &caseData)
			if caseData.Steps == nil {
				caseData.Steps = &models.MarsCaseSteps{
					Type: "step",
				}
			}
			if err != nil {
				return 0, err
			}

			if existingNode != nil {
				// 存在则更新
				caseData.Id = existingNode.Id
				caseData.Addons = existingNode.Addons
				caseData.SiblingOrder = existingNode.SiblingOrder
				caseData.Modifier = ctx.User.AdAccount
				caseData.GmtModified = times.Now()

				_, err = gormx.UpdateOneByCondition(ctx, &caseData)
				if err != nil {
					return 0, err
				}

				// 删除现有的需求关联
				_, err = gormx.UpdateBatchByParamBuilderAndMap(ctx,
					gormx.NewParamBuilder().Model(&models.MarsCaseRelation{}).Eq("case_id", existingNode.Id),
					map[string]any{"is_deleted": commonconstants.DeleteYes, "gmt_modified": times.Now(), "modifier": ctx.User.AdAccount})
				if err != nil {
					return 0, err
				}
			} else {
				// 不存在则创建
				maxSort, err := marsCaseDao.SelectMaxSiblingOrder(ctx, parentId)
				if err != nil {
					return 0, err
				}

				caseData.Addons = commonmodels.NewAddonsWithUser(ctx.User)
				caseData.SiblingOrder = maxSort + 1

				_, err = gormx.InsertOne(ctx, &caseData)
				if err != nil {
					return 0, err
				}
			}
			total++
			// 处理需求关联
			if len(topic.Labels) != 0 {
				demands, _, err := project.ProjectRemoteApi.GetDemands(ctx, dto.ProjectCommonSelectReq{Codes: topic.Labels})
				if err != nil {
					return 0, err
				}
				demandIdMap := make(map[string]int64)
				for _, demand := range demands {
					demandIdMap[demand.Code] = demand.Id
				}
				relationList := make([]models.MarsCaseRelation, 0)
				for _, label := range topic.Labels {
					relationList = append(relationList, models.MarsCaseRelation{
						Addons:     commonmodels.NewAddonsWithUser(ctx.User),
						CaseId:     caseData.Id,
						RelationId: strconv.FormatInt(demandIdMap[label], 10),
					})
				}
				if len(relationList) > 0 {
					_, err = gormx.InsertBatch(ctx, relationList)
					if err != nil {
						return 0, err
					}
				}
			}

		default:
			return 0, fmt.Errorf("错误的节点类型或错误的节点附属关系, 错误节点: %s", topic.Title)
		}
	}

	return total, nil
}

// findNodeByPath 根据路径查找节点
func (s XmindService) findNodeByPath(ctx *commoncontext.MantisContext, libId int64, path []string, nodeType string) (*models.MarsCase, error) {
	if len(path) == 0 {
		return nil, nil
	}

	// 获取库根节点
	var rootId int64
	err := gormx.Raw(ctx, "select id from mars_case where library_id = ? and parent_id = 0 and is_deleted='N'", &rootId, libId)
	if err != nil {
		return nil, err
	}

	currentId := rootId
	// 从根节点开始，逐层查找路径中的每个节点
	for _, nodeName := range path {
		var childId int64
		sql := `select id from mars_case where parent_id = ? and name = ? and is_deleted = 'N' limit 1`
		err := gormx.Raw(ctx, sql, &childId, currentId, nodeName)
		if err != nil {
			return nil, err
		}

		if childId == 0 {
			// 路径中的某个节点不存在
			return nil, nil
		}
		currentId = childId
	}

	// 检查最终节点的类型是否匹配
	var node models.MarsCase
	node.Id = currentId
	node.IsDeleted = commonconstants.DeleteNo
	err = gormx.SelectOneByCondition(ctx, &node)
	if err != nil {
		return nil, err
	}

	if node.NodeType == nodeType {
		return &node, nil
	}

	return nil, nil
}

func (s XmindService) importCaseContents(topics []*xmind.Topic, marsCase *models.MarsCase) error {
	for _, topic := range topics {
		prefixStart := strings.Index(topic.Title, "[")
		prefixEnd := strings.Index(topic.Title, "]")
		if prefixEnd == 0 {
			return fmt.Errorf("节点 %s 解析错误: 无可解析的类型前缀", topic.Title)
		}
		prefix := topic.Title[prefixStart : prefixEnd+1]
		switch prefix {
		case "[S]", "[步骤]":
			if marsCase.Steps == nil {
				marsCase.Steps = &models.MarsCaseSteps{
					Steps: make([]models.MarsCaseStep, 0),
					Type:  "step",
				}
			}
			step := models.MarsCaseStep{}
			step.Description = strings.Replace(topic.Title, prefix, "", 1)
			if len(topic.Children.Attached) != 0 {
				if len(topic.Children.Attached) > 1 {
					return fmt.Errorf("步骤下存在多余的预期结果, 步骤节点: %s", topic.Title)
				}
				eTopic := topic.Children.Attached[0]
				ePrefixStart := strings.Index(eTopic.Title, "[")
				ePrefixEnd := strings.Index(eTopic.Title, "]")
				if ePrefixEnd == 0 {
					return fmt.Errorf("节点 %s 解析错误: 无可解析的类型前缀", eTopic.Title)
				}
				ePrefix := eTopic.Title[ePrefixStart : ePrefixEnd+1]
				if ePrefix != "[E]" && ePrefix != "[预期]" {
					return fmt.Errorf("错误的节点类型或错误的节点附属关系, 错误节点: %s", eTopic.Title)
				}
				step.ExpectedResults = strings.Replace(eTopic.Title, ePrefix, "", 1)
			}
			marsCase.Steps.Steps = append(marsCase.Steps.Steps, step)
		case "[P]", "[前置]":
			if marsCase.PreCondition != "" {
				return fmt.Errorf("用例下存在多余的前置条件, 多余节点: %s", topic.Title)
			}
			marsCase.PreCondition = strings.Replace(topic.Title, prefix, "", 1)
		case "[R]", "[备注]":
			if marsCase.Remark != "" {
				return fmt.Errorf("用例下存在多余的备注, 多余节点: %s", topic.Title)
			}
			marsCase.Remark = strings.Replace(topic.Title, prefix, "", 1)
		default:
			return fmt.Errorf("错误的节点类型或错误的节点附属关系, 错误节点: %s", topic.Title)
		}
	}
	return nil
}

func (s XmindService) Export(ctx *commoncontext.MantisContext, parentId int64) (*bytes.Buffer, error) {
	// 查询从库根节点到parentId的路径树
	tree, err := s.buildPathTreeFromRootToParent(ctx, parentId)
	if err != nil {
		return nil, err
	}
	if tree == nil {
		return nil, fmt.Errorf("未查询到parentId: %d", parentId)
	}
	rootTopic, err := s.convertCaseTree2XmindTree(tree)
	if err != nil {
		return nil, err
	}
	return xmind.GenerateXMind(&xmind.XMindContent{
		RootTopic: rootTopic,
	})
}

func (s XmindService) convertCaseTree2XmindTree(node *models.MarsCase) (*xmind.Topic, error) {
	switch node.NodeType {
	case enums.CaseNode.Value: // convert case
		current := &xmind.Topic{
			Title: fmt.Sprintf("[C]%s", node.Name),
		}
		if len(node.DemandCodes) != 0 {
			current.Labels = append(current.Labels, node.DemandCodes...)
		}
		xmindPriority, err := s.convertMarsPriority2Xmind(node.Priority)
		if err != nil {
			return nil, err
		}
		current.Markers = []xmind.Marker{{MarkerId: xmindPriority}}
		current.Children.Attached = make([]*xmind.Topic, 0)
		// convert case precondition
		if node.PreCondition != "" {
			current.Children.Attached = append(current.Children.Attached, &xmind.Topic{
				Title: fmt.Sprintf("[P]%s", node.PreCondition),
			})
		}
		// convert case remark
		if node.Remark != "" {
			current.Children.Attached = append(current.Children.Attached, &xmind.Topic{
				Title: fmt.Sprintf("[R]%s", node.Remark),
			})
		}
		// convert case step
		if node.Steps != nil && node.Steps.Type == "step" && len(node.Steps.Steps) != 0 {
			for _, step := range node.Steps.Steps {
				if step.Description != "" {
					stepTopic := &xmind.Topic{
						Title: fmt.Sprintf("[S]%s", step.Description),
					}
					if step.ExpectedResults != "" {
						stepTopic.Children.Attached = []*xmind.Topic{{Title: fmt.Sprintf("[E]%s", step.ExpectedResults)}}
					}
					current.Children.Attached = append(current.Children.Attached, stepTopic)
				}
			}
		}
		return current, nil
	case enums.FolderNode.Value, enums.RootNode.Value: // convert folder
		current := &xmind.Topic{
			Title: fmt.Sprintf("[D]%s", node.Name),
		}
		if len(node.Children) != 0 {
			current.Children.Attached = make([]*xmind.Topic, 0)
			for _, child := range node.Children {
				childTopic, err := s.convertCaseTree2XmindTree(child)
				if err != nil {
					return nil, err
				}
				current.Children.Attached = append(current.Children.Attached, childTopic)
			}
		}
		return current, nil
	default:
		return nil, fmt.Errorf("不支持的节点类型: %s", node.NodeType)
	}
}

func (XmindService) convertXmindPriority2Mars(xmindPriority string) (string, error) {
	switch xmindPriority {
	case "priority-1":
		return "P0", nil
	case "priority-2":
		return "P1", nil
	case "priority-3":
		return "P2", nil
	case "priority-4":
		return "P3", nil
	default:
		return "", fmt.Errorf("不能解析的优先级: %s", xmindPriority)
	}
}

func (XmindService) convertMarsPriority2Xmind(priority string) (string, error) {
	switch priority {
	case "P0":
		return "priority-1", nil
	case "P1":
		return "priority-2", nil
	case "P2":
		return "priority-3", nil
	case "P3":
		return "priority-4", nil
	default:
		return "", fmt.Errorf("不能解析的优先级: %s", priority)
	}
}

// buildPathTreeFromRootToParent 构建从库根节点到指定parentId的路径树
// 这个方法会包含从根节点到目标节点的路径，以及目标节点下的完整子树
func (s XmindService) buildPathTreeFromRootToParent(ctx *commoncontext.MantisContext, parentId int64) (*models.MarsCase, error) {
	// 首先获取目标节点信息
	targetNode := models.MarsCase{}
	targetNode.Id = parentId
	targetNode.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &targetNode)
	if err != nil {
		return nil, err
	}

	// 获取库的根节点
	var rootId int64
	err = gormx.Raw(ctx, "select id from mars_case where library_id = ? and parent_id = 0 and is_deleted='N'", &rootId, targetNode.LibraryId)
	if err != nil {
		return nil, err
	}

	// 构建从根节点到目标节点的路径
	pathNodeIds, err := s.getPathFromRootToTarget(ctx, rootId, parentId)
	if err != nil {
		return nil, err
	}

	// 将路径节点ID转换为集合，便于快速查找
	pathNodeSet := make(map[int64]bool)
	for _, nodeId := range pathNodeIds {
		pathNodeSet[nodeId] = true
	}

	// 1. 查询路径上的所有节点（不包括子树）
	pathNodes := make([]models.MarsCase, 0)
	if len(pathNodeIds) > 0 {
		err = gormx.SelectByParamBuilder(ctx,
			gormx.NewParamBuilder().Model(&models.MarsCase{}).In("id", pathNodeIds).Eq("is_deleted", commonconstants.DeleteNo),
			&pathNodes,
		)
		if err != nil {
			return nil, err
		}
	}

	// 2. 查询parentId下的完整子树
	var targetSubTree []models.MarsCase
	sql := `with recursive sub as (
		select * from mars_case where id = ? and is_deleted = 'N'
		union
		select e.* from (select * from mars_case where is_deleted = 'N') e
		inner join sub s on s.id = e.parent_id
	) select * from sub`
	err = gormx.Raw(ctx, sql, &targetSubTree, parentId)
	if err != nil {
		return nil, err
	}

	// 3. 合并所有节点并去重
	nodeMap := make(map[int64]*models.MarsCase)
	allNodeIds := make([]int64, 0)

	// 添加路径节点
	for _, node := range pathNodes {
		nodeMap[node.Id] = &node
		allNodeIds = append(allNodeIds, node.Id)
	}

	// 添加目标子树节点
	for _, node := range targetSubTree {
		if _, exists := nodeMap[node.Id]; !exists {
			nodeMap[node.Id] = &node
			allNodeIds = append(allNodeIds, node.Id)
		}
	}

	// 4. 查询需求关联信息
	relations := make([]models.MarsCaseRelation, 0)
	if len(allNodeIds) > 0 {
		err = gormx.SelectByParamBuilder(ctx,
			gormx.NewParamBuilder().Model(&models.MarsCaseRelation{}).In("case_id", allNodeIds).Eq("is_deleted", commonconstants.DeleteNo),
			&relations,
		)
		if err != nil {
			return nil, err
		}
	}

	demandIds := make([]string, 0)
	for _, relation := range relations {
		demandIds = append(demandIds, relation.RelationId)
	}

	if len(demandIds) != 0 {
		demands, _, err := project.ProjectRemoteApi.GetDemands(ctx, dto.ProjectCommonSelectReq{Ids: demandIds})
		if err != nil {
			return nil, err
		}
		demandCodeMap := make(map[string]string)
		for _, demand := range demands {
			demandCodeMap[strconv.FormatInt(demand.Id, 10)] = demand.Code
		}
		for _, relation := range relations {
			node := nodeMap[relation.CaseId]
			if node != nil {
				if node.DemandCodes == nil {
					node.DemandCodes = make([]string, 0)
				}
				node.DemandCodes = append(node.DemandCodes, demandCodeMap[relation.RelationId])
			}
		}
	}

	// 5. 构建树结构，但只连接符合条件的父子关系
	for _, node := range nodeMap {
		parent := nodeMap[node.ParentId]
		if parent != nil {
			if parent.Children == nil {
				parent.Children = make([]*models.MarsCase, 0)
			}
			parent.Children = append(parent.Children, node)
		}
	}

	// 6. 递归排序所有节点的子节点
	var sortChildren func(node *models.MarsCase)
	sortChildren = func(node *models.MarsCase) {
		if len(node.Children) > 0 {
			// 根据 SiblingOrder 排序
			sort.Slice(node.Children, func(i, j int) bool {
				return node.Children[i].SiblingOrder < node.Children[j].SiblingOrder
			})

			// 递归排序子节点
			for i := range node.Children {
				childPtr := node.Children[i]
				sortChildren(childPtr)
			}
		}
	}

	root := nodeMap[rootId]
	if root != nil {
		sortChildren(root)
	}
	return root, nil
}

// getPathFromRootToTarget 获取从根节点到目标节点的路径（包括所有祖先节点）
func (s XmindService) getPathFromRootToTarget(ctx *commoncontext.MantisContext, rootId, targetId int64) ([]int64, error) {
	if rootId == targetId {
		return []int64{rootId}, nil
	}

	// 使用递归查询找到从目标节点到根节点的路径
	sql := `
	WITH RECURSIVE path_to_root AS (
		-- 基础案例：从目标节点开始
		SELECT id, parent_id, 0 as level
		FROM mars_case 
		WHERE id = ? AND is_deleted = 'N'
		
		UNION ALL
		
		-- 递归案例：向上查找父节点
		SELECT p.id, p.parent_id, ptr.level + 1
		FROM mars_case p
		INNER JOIN path_to_root ptr ON p.id = ptr.parent_id
		WHERE p.is_deleted = 'N'
	)
	SELECT id FROM path_to_root 
	ORDER BY level DESC  -- 从根节点到目标节点的顺序
	`

	var pathIds []int64
	err := gormx.Raw(ctx, sql, &pathIds, targetId)
	if err != nil {
		return nil, err
	}

	return pathIds, nil
}
