package service

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/cmdb"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/services/client"
)

// 校验功能用例
func dealCaseInvalid(ctx *commoncontext.MantisContext, planId int64, model string) {
	if model != constants.TestPlanRelationTypeCase {
		return
	}
	type TempRes struct {
		Id     int64 `json:"id"`
		CaseId int64 `json:"caseid" gorm:"column:caseid"`
	}
	tr := make([]TempRes, 0)
	sql := `select t1.id,t2.id as caseid from 
			(select id,relation_id::NUMERIC from mars_test_plan_case_relation where test_plan_id=? and "type"=?
			and is_deleted ='N') t1 left join mars_case t2 on t1.relation_id =t2.id`
	err := gormx.Raw(ctx, sql, &tr, planId, constants.TestPlanRelationTypeCase)
	if err != nil {
		return
	}
	if len(tr) == 0 {
		return
	}
	delRels := make([]int64, 0)
	for _, r := range tr {
		if r.CaseId == 0 {
			delRels = append(delRels, r.Id)
		}
	}
	if len(delRels) > 0 {
		logger.Logger.Infof("delete test plan planId=%d,relation %v", planId, delRels)
		sql := `update mars_test_plan_case_relation set gmt_modified=?,modifier=?,is_deleted=? where id in(?)`
		gormx.ExecX(ctx, sql, times.Now(), ctx.User.AdAccount, commonconstants.DeleteYes, delRels)
	}
}

func checkPlanExist(ctx *commoncontext.MantisContext, planId int64) (*models.MarsTestPlan, error) {
	testPlan := models.MarsTestPlan{}
	testPlan.Id = planId
	testPlan.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &testPlan)
	if err != nil {
		return nil, err
	}
	if testPlan.Id == 0 {
		return nil, errors.New("测试计划不存在")
	}
	return &testPlan, nil
}

// 检查测试计划状态
func checkPlanStatus(ctx *commoncontext.MantisContext, planId int64) (*models.MarsTestPlan, error) {
	testPlan := models.MarsTestPlan{}
	testPlan.Id = planId
	testPlan.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &testPlan)
	if err != nil {
		return nil, err
	}
	if testPlan.Id == 0 {
		return nil, errors.New("测试计划不存在")
	}
	if testPlan.Status == constants.TestPLanStatusNotStart || testPlan.Status == constants.TestPLanStatusFinished {
		return nil, errors.New("测试计划未开始或已关闭，不允许修改执行结果")
	}
	return &testPlan, nil
}

func getEnvMap(ctx context.Context) (map[string]string, error) {
	mantisCtx, ok := ctx.(*commoncontext.MantisContext)
	if !ok {
		return nil, fmt.Errorf("error transfer context to mantis context")
	}
	startTime := time.Now()
	attrList := []string{}
	param := client.NewParam().WithQueries("top", fmt.Sprintf("%v", cmdb.DefaultInstance9999Top))
	if len(attrList) > 0 {
		param.Query().Add("attrs", strings.Join(attrList, "|"))
	}
	list, err := cmdb.GetDefaultCMDB().GetBaseEnvList(ctx, mantisCtx.User.CompanyID)
	if err != nil {
		return nil, err
	}
	elapsedTime := time.Since(startTime).Milliseconds() // 计算耗时
	logger.Logger.Infof("get envs length: %v,cost: %d ms", len(list), elapsedTime)
	res := make(map[string]string)
	for _, e := range list {
		res[string(e.InstanceBizID)] = e.InstanceBizName
	}
	return res, nil
}
