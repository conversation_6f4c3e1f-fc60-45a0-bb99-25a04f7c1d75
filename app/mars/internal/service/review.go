package service

import (
	"errors"
	"fmt"
	"math"
	"slices"
	"sort"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/enums"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	commonmodels "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	set "github.com/duke-git/lancet/v2/datastructure/set"
	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
)

type ReviewService struct{}

var marsReviewDao dao.MarsReviewDao

func (ReviewService) Search(ctx *commoncontext.MantisContext, req dto.MarsReviewSearchReqDTO, pageReq gormx.PageRequest) (*gormx.PageResult, error) {
	sql := `select * from "mars_review" where "space_id" = ?`
	params := []any{req.SpaceId}
	if req.Name != "" {
		sql += ` and "name" like ?`
		params = append(params, "%"+req.Name+"%")
	}
	if len(req.ReviewStatus) != 0 {
		sql += ` and "review_status" in ?`
		params = append(params, req.ReviewStatus)
	}
	if len(req.Reviewer) != 0 {
		reviewers := strings.Split(req.Reviewer, ",")
		arrayReviewers := "array["
		for _, reviewer := range reviewers {
			arrayReviewers += fmt.Sprintf(`'["%s"]',`, reviewer)
		}
		arrayReviewers = arrayReviewers[:len(arrayReviewers)-1]
		arrayReviewers += "]"
		sql += ` and "reviewer" @> any (?::jsonb[])`
		params = append(params, gorm.Expr(arrayReviewers))
	}
	if len(req.Creator) != 0 {
		sql += ` and "creator" in ?`
		params = append(params, req.Creator)
	}
	if req.EndTime != "" {
		sql += ` and "end_time" >= ? and "end_time" <= ?`
		params = append(params, times.GetDataFromCommaString(req.EndTime, 0), times.GetDataFromCommaString(req.EndTime, 1))
	}
	sql += ` and "is_deleted" = 'N' order by gmt_created desc`
	reviews := make([]models.MarsReview, 0)
	res, err := gormx.PageSelectByRaw(ctx, sql, &reviews, pageReq, params...)
	if err != nil {
		return nil, err
	}
	reviewIds := set.New[int64]()
	for _, review := range reviews {
		reviewIds.Add(review.Id)
	}
	reviewCaseCountMap, err := marsReviewDao.SelectCaseCountGroupByReview(ctx, reviewIds.ToSlice())
	if err != nil {
		return nil, err
	}
	list := make([]dto.MarsReviewDTO, 0)
	for _, review := range reviews {
		reviewDto := dto.MarsReviewDTO{
			MarsReview: review,
		}
		reviewDto.CaseSize = reviewCaseCountMap[review.Id]
		list = append(list, reviewDto)
	}
	res.List = list
	return res, nil
}

func (ReviewService) Insert(ctx *commoncontext.MantisContext, review *models.MarsReview) (*models.MarsReview, error) {
	review.ReviewStatus = enums.ReviewNotStart.Value
	review.Addons = commonmodels.NewAddonsWithUser(ctx.User)
	if review.ReviewStatus == "" {
		review.ReviewStatus = enums.NotReview.Value
	}
	_, err := gormx.InsertOne(ctx, review)
	return review, err
}

func (ReviewService) Info(ctx *commoncontext.MantisContext, id int64) (*dto.MarsReviewDTO, error) {
	review := models.MarsReview{}
	review.Id = id
	review.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &review)
	if err != nil {
		return nil, err
	}
	res := dto.MarsReviewDTO{}
	res.MarsReview = review
	reviewCaseCount, err := marsReviewDao.SelectCaseCountGroupByReview(ctx, []int64{id})
	if err != nil {
		return nil, err
	}
	res.CaseSize = reviewCaseCount[res.Id]
	return &res, nil
}

func (ReviewService) Update(ctx *commoncontext.MantisContext, review *models.MarsReview) error {
	review.GmtModified = times.Now()
	review.Modifier = ctx.User.AdAccount
	if review.ReviewStatus == "" {
		review.ReviewStatus = enums.NotReview.Value
	}
	_, err := gormx.UpdateOneByCondition(ctx, &review)
	return err
}

// Delete 试用事务删除评审及关联的所有数据
func (ReviewService) Delete(ctx *commoncontext.MantisContext, id int64) error {
	user := ctx.User
	return gormx.Transaction(ctx, func() error {
		// 删除评审
		review := models.MarsReview{}
		review.Id = id
		review.IsDeleted = commonconstants.DeleteYes
		_, err := gormx.UpdateOneByCondition(ctx, &review)
		if err != nil {
			return err
		}

		// 删除评审关联的用例
		_, err = gormx.UpdateBatchByParamBuilderAndMap(ctx,
			gormx.NewParamBuilder().Model(&models.MarsReviewCaseRelation{}).Eq("review_id", id),
			map[string]any{
				"is_deleted":   commonconstants.DeleteYes,
				"gmt_modified": times.Now(),
				"modifier":     user.AdAccount,
			})
		if err != nil {
			return err
		}
		return nil
	})
}

func (ReviewService) RelateCase(ctx *commoncontext.MantisContext, reviewCaseReq dto.MarsReviewCaseReq) error {
	// 检查状态
	review := models.MarsReview{}
	review.Id = reviewCaseReq.ReviewId
	review.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &review)
	if err != nil {
		return err
	}
	if review.ReviewStatus == enums.ReviewFinished.Value {
		return errors.New("评审已结束")
	}
	// 去除重复的新增
	repeatedRelations := make([]models.MarsReviewCaseRelation, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsReviewCaseRelation{}).Eq("review_id", reviewCaseReq.ReviewId).In("case_id", reviewCaseReq.CaseIds).Eq("is_deleted", commonconstants.DeleteNo),
		&repeatedRelations)
	if err != nil {
		return err
	}
	repeatedCaseIds := make([]int64, 0)
	for _, relation := range repeatedRelations {
		repeatedCaseIds = append(repeatedCaseIds, relation.CaseId)
	}
	insertCaseIds := slice.Difference(reviewCaseReq.CaseIds, repeatedCaseIds)
	reviewCaseRelations := make([]models.MarsReviewCaseRelation, 0)
	if len(insertCaseIds) != 0 {
		for _, caseId := range insertCaseIds {
			reviewCaseRelations = append(reviewCaseRelations, models.MarsReviewCaseRelation{
				Addons:       commonmodels.NewAddonsWithUser(ctx.User),
				ReviewId:     reviewCaseReq.ReviewId,
				CaseId:       caseId,
				ReviewStatus: enums.NotReview.Value,
			})
		}
		_, err = gormx.InsertBatch(ctx, &reviewCaseRelations)
		if err != nil {
			return err
		}
	}
	return nil
}

func (ReviewService) CaseListByDemand(ctx *commoncontext.MantisContext, reviewId int64) ([]dto.MarsReviewDemandCaseList, error) {
	marsReviewCases := make([]models.MarsReviewCaseRelation, 0)
	err := gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsReviewCaseRelation{}).Eq("review_id", reviewId).Eq("is_deleted", commonconstants.DeleteNo),
		&marsReviewCases)
	if err != nil {
		return nil, err
	}
	caseIdList := make([]int64, 0)
	caseReviewMap := make(map[int64]models.MarsReviewCaseRelation)
	for _, marsReviewCase := range marsReviewCases {
		caseIdList = append(caseIdList, marsReviewCase.CaseId)
		caseReviewMap[marsReviewCase.CaseId] = marsReviewCase
	}
	caseList := make([]models.MarsCase, 0)
	err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MarsCase{}).In("id", caseIdList).Eq("is_deleted", commonconstants.DeleteNo), &caseList)
	if err != nil {
		return nil, err
	}
	caseMap := make(map[int64]models.MarsCase)
	libraryIds := make([]int64, 0)
	for _, c := range caseList {
		caseMap[c.Id] = c
		libraryIds = append(libraryIds, c.LibraryId)
	}
	caseRelations := make([]models.MarsCaseRelation, 0)
	err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MarsCaseRelation{}).In("case_id", caseIdList).Eq("is_deleted", commonconstants.DeleteNo), &caseRelations)
	if err != nil {
		return nil, err
	}
	libraries := make([]models.MarsCaseLibrary, 0)
	err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MarsCaseLibrary{}).In("id", libraryIds), &libraries)
	if err != nil {
		return nil, err
	}
	libraryMap := make(map[int64]models.MarsCaseLibrary)
	for _, lib := range libraries {
		libraryMap[lib.Id] = lib
	}
	demandedCaseSet := set.New[int64]()
	demandCaseMap := make(map[string][]int64)
	for _, caseRelation := range caseRelations {
		demandedCaseSet.Add(caseRelation.CaseId)
		_, ok := demandCaseMap[caseRelation.RelationId]
		if !ok {
			demandCaseMap[caseRelation.RelationId] = make([]int64, 0)
		}
		demandCaseMap[caseRelation.RelationId] = append(demandCaseMap[caseRelation.RelationId], caseRelation.CaseId)
	}
	demandIds := make([]string, 0)
	for k := range demandCaseMap {
		demandIds = append(demandIds, k)
	}
	res := make([]dto.MarsReviewDemandCaseList, 0)
	if len(demandIds) != 0 {
		demands, _, err := projectService.GetDemandsByPage(ctx, dto.ProjectCommonSelectReq{Ids: demandIds}, gormx.PageRequest{Page: 1, PageSize: math.MaxInt32})
		if err != nil {
			return nil, err
		}
		for _, demand := range demands {
			current := dto.MarsReviewDemandCaseList{
				Demand: struct {
					Name string "json:\"name\""
					Id   string "json:\"id\""
					Code string "json:\"code\""
				}{
					Id:   strconv.FormatInt(demand.Id, 10),
					Name: demand.Name,
					Code: demand.Code,
				},
				CaseList: make([]struct {
					Id           int64  "json:\"id\""
					Code         string "json:\"code\""
					Name         string "json:\"name\""
					Library      string "json:\"library\""
					ReviewStatus string "json:\"reviewStatus\""
				}, 0),
			}
			caseIds := demandCaseMap[strconv.FormatInt(demand.Id, 10)]
			cases := make([]models.MarsCase, 0)
			for _, caseId := range caseIds {
				cases = append(cases, caseMap[caseId])
			}
			sort.Slice(cases, func(i, j int) bool {
				return cases[i].GmtCreated.ToGoTime().Before(cases[j].GmtCreated.ToGoTime())
			})
			for _, c := range cases {
				current.CaseList = append(current.CaseList, struct {
					Id           int64  "json:\"id\""
					Code         string "json:\"code\""
					Name         string "json:\"name\""
					Library      string "json:\"library\""
					ReviewStatus string "json:\"reviewStatus\""
				}{
					Id:           c.Id,
					Code:         fmt.Sprintf("%d-%d", demand.Id, c.Id),
					Name:         c.Name,
					Library:      libraryMap[c.LibraryId].Name,
					ReviewStatus: caseReviewMap[c.Id].ReviewStatus,
				})
			}
			res = append(res, current)
		}
	}
	// 找到未关联需求的用例
	noDemandCaseIds := slice.Difference(caseIdList, demandedCaseSet.ToSlice())
	if len(noDemandCaseIds) != 0 {
		current := dto.MarsReviewDemandCaseList{
			Demand: struct {
				Name string "json:\"name\""
				Id   string "json:\"id\""
				Code string "json:\"code\""
			}{
				Id:   "0",
				Name: "未关联需求用例",
				Code: "0",
			},
			CaseList: make([]struct {
				Id           int64  "json:\"id\""
				Code         string "json:\"code\""
				Name         string "json:\"name\""
				Library      string "json:\"library\""
				ReviewStatus string "json:\"reviewStatus\""
			}, 0),
		}
		for _, caseId := range noDemandCaseIds {
			c := caseMap[caseId]
			current.CaseList = append(current.CaseList, struct {
				Id           int64  "json:\"id\""
				Code         string "json:\"code\""
				Name         string "json:\"name\""
				Library      string "json:\"library\""
				ReviewStatus string "json:\"reviewStatus\""
			}{
				Id:           c.Id,
				Code:         fmt.Sprintf("%d-%d", 0, c.Id),
				Name:         c.Name,
				Library:      libraryMap[c.LibraryId].Name,
				ReviewStatus: caseReviewMap[c.Id].ReviewStatus,
			})
		}
		res = append(res, current)
	}
	return res, nil
}

func (ReviewService) GetCaseComments(ctx *commoncontext.MantisContext, reviewId, caseId int64) ([]models.MarsReviewCaseComment, error) {
	caseReview := models.MarsReviewCaseRelation{}
	caseReview.ReviewId = reviewId
	caseReview.CaseId = caseId
	caseReview.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &caseReview)
	if err != nil {
		return nil, err
	}
	return caseReview.Comments, nil
}

func (ReviewService) RemoveCase(ctx *commoncontext.MantisContext, reviewId int64, ids []int64) error {
	// 检查状态
	review := models.MarsReview{}
	review.Id = reviewId
	review.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &review)
	if err != nil {
		return err
	}
	if review.ReviewStatus == enums.ReviewFinished.Value {
		return errors.New("评审已结束")
	}
	_, err = gormx.UpdateBatchByParamBuilderAndMap(ctx,
		gormx.NewParamBuilder().Model(&models.MarsReviewCaseRelation{}).In("case_id", ids).Eq("review_id", reviewId),
		map[string]any{
			"is_deleted":   commonconstants.DeleteYes,
			"gmt_modified": times.Now(),
			"modifier":     ctx.User.AdAccount,
		})
	return err
}

func (ReviewService) StartReview(ctx *commoncontext.MantisContext, req dto.MarsReviewStatusUpdateReq) error {
	review := models.MarsReview{}
	review.Id = req.Id
	review.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &review)
	if err != nil {
		return err
	}
	if review.Creator != ctx.User.AdAccount {
		return errors.New("非评审创建者禁止开始评审")
	}
	review.ReviewStatus = enums.ReviewRunning.Value
	_, err = gormx.UpdateOneByCondition(ctx, &review)
	return err
}

func (ReviewService) FinishReview(ctx *commoncontext.MantisContext, req dto.MarsReviewStatusUpdateReq) error {
	review := models.MarsReview{}
	review.Id = req.Id
	review.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &review)
	if err != nil {
		return err
	}
	if !slices.Contains(review.Reviewer, ctx.User.AdAccount) {
		return errors.New("非评审人禁止结束评审")
	}
	review.ReviewStatus = enums.ReviewFinished.Value
	_, err = gormx.UpdateOneByCondition(ctx, &review)
	return err
}

func (ReviewService) SaveReviewComment(ctx *commoncontext.MantisContext, req dto.MarsReviewCommentDTO) error {
	review := models.MarsReview{}
	review.Id = req.ReviewId
	review.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &review)
	if err != nil {
		return err
	}
	if !slices.Contains(review.Reviewer, ctx.User.AdAccount) {
		return errors.New("非评审人禁止提交总评")
	}
	review.Comment = req.Comment
	review.Commentor = ctx.User.AdAccount
	review.CommentTime = times.Now()
	_, err = gormx.UpdateOneByCondition(ctx, &review)
	return err
}

func (ReviewService) GetReviewComment(ctx *commoncontext.MantisContext, id int64) (*dto.MarsReviewCommentDTO, error) {
	review := models.MarsReview{}
	review.Id = id
	review.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &review)
	if err != nil {
		return nil, err
	}
	res := dto.MarsReviewCommentDTO{
		ReviewId:    review.Id,
		Comment:     review.Comment,
		Commentor:   review.Commentor,
		CommentTime: review.CommentTime,
	}
	return &res, nil
}

func (ReviewService) SaveReviewCaseComment(ctx *commoncontext.MantisContext, comment dto.MarsReviewCaseComment) error {
	review := models.MarsReview{}
	review.Id = comment.ReviewId
	review.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &review)
	if err != nil {
		return err
	}
	if review.ReviewStatus != enums.ReviewRunning.Value {
		return errors.New("评审未开始或已结束")
	}
	if !slices.Contains(review.Reviewer, ctx.User.AdAccount) {
		return errors.New("非评审人禁止评审")
	}
	relation := models.MarsReviewCaseRelation{}
	relation.ReviewId = comment.ReviewId
	relation.CaseId = comment.CaseId
	relation.IsDeleted = commonconstants.DeleteNo
	err = gormx.SelectOneByCondition(ctx, &relation)
	if err != nil {
		return err
	}
	relation.ReviewStatus = comment.Comment.ReviewStatus
	comment.Comment.Commentor = ctx.User.AdAccount
	comment.Comment.CommentTime = times.Now()
	relation.Comments = append(relation.Comments, comment.Comment)
	_, err = gormx.UpdateOneByCondition(ctx, &relation)
	return err
}
