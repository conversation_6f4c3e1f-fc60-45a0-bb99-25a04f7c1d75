package service

import (
	"encoding/json"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto/ui"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type UICallbackService struct{}

// DealPlanCallback 处理计划回调
func (UICallbackService) DealPlanCallback(ctx *context.MantisContext, hisId int64, req ui.MarsUiPlanExecResult) error {
	status := constants.CaseResultTypeFail
	if req.Success {
		status = constants.CaseResultTypePass
	}
	jsonStr, err := json.Marshal(req)
	if err != nil {
		return err
	}
	his := models.MarsTestPlanCaseExecHistory{
		Result:        string(jsonStr),
		ExecuteResult: status,
		ReportUrl:     req.ReportUrl,
	}
	his.Id = hisId
	his.GmtModified = times.Now()
	_, err = gormx.UpdateOneByCondition(ctx, &his)
	if err != nil {
		return err
	}
	return nil
}
