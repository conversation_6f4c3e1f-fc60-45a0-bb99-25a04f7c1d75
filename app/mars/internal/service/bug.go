package service

import (
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto/project"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	commonmodels "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	"github.com/duke-git/lancet/v2/slice"
)

var projectService ProjectService

type BugService struct{}

func (BugService) AddBug(ctx *commoncontext.MantisContext, marsBug project.MarsBugReq) error {
	if marsBug.RelateDemand == "" {
		marsBug.RelateDemand = constants.DefaultDemandID
	}
	bugIds := make([]string, len(marsBug.BugIds))
	for i, bugId := range marsBug.BugIds {
		bugIds[i] = strconv.FormatInt(bugId, 10)
	}
	bugIdsExist := make([]string, 0)
	paramBuilder := gormx.NewParamBuilder().Model(&models.MarsBug{}).Select("bug_id")
	if marsBug.CaseId != 0 {
		paramBuilder.Eq("case_id", marsBug.CaseId)
	}
	if marsBug.TestPlanId != 0 {
		paramBuilder.Eq("test_plan_id", marsBug.TestPlanId)
	}
	if marsBug.Type != "" {
		paramBuilder.Eq("type", marsBug.Type)
	}
	if marsBug.RelateDemand != "" {
		paramBuilder.Eq("demand_id", marsBug.RelateDemand)
	}
	paramBuilder.Eq("is_deleted", commonconstants.DeleteNo)
	err := gormx.SelectByParamBuilder(ctx, paramBuilder,
		&bugIdsExist)
	if err != nil {
		return err
	}
	bugIds = slice.Difference(bugIds, bugIdsExist)
	bugsAdd := make([]models.MarsBug, 0)
	for _, bugId := range bugIds {
		bugsAdd = append(bugsAdd, models.MarsBug{
			CaseId:     marsBug.CaseId,
			Type:       marsBug.Type,
			DemandId:   marsBug.RelateDemand,
			TestPlanId: marsBug.TestPlanId,
			BugId:      bugId,
			Addons:     commonmodels.NewAddonsWithUser(ctx.User),
		})
	}
	if len(bugsAdd) != 0 {
		_, err = gormx.InsertBatch(ctx, bugsAdd)
		if err != nil {
			return err
		}
	}
	return nil
}

func (BugService) BugList(ctx *commoncontext.MantisContext, req project.MarsBugListReq) (*project.ProjectBugDTO, error) {
	paramBuilder := gormx.NewParamBuilder().Model(&models.MarsBug{})
	if req.TestPlanId != 0 {
		paramBuilder.Eq("test_plan_id", req.TestPlanId)
	}
	if req.RelateDemand != "" {
		paramBuilder.Eq("demand_id", req.RelateDemand)
	}
	if req.CaseId != 0 {
		paramBuilder.Eq("case_id", req.CaseId)
	}
	if req.Type != "" {
		paramBuilder.Eq("type", req.Type)
	}
	paramBuilder.Eq("is_deleted", commonconstants.DeleteNo)
	marsBugs := make([]models.MarsBug, 0)
	err := gormx.SelectByParamBuilder(ctx, paramBuilder, &marsBugs)
	if err != nil {
		return nil, err
	}
	return projectService.GetBugDTO(ctx, marsBugs, 0, req.TestPlanId)
}

func (BugService) Remove(ctx *commoncontext.MantisContext, req project.MarsBugRemoveReq) error {
	_, err := gormx.UpdateBatchByParamBuilderAndMap(ctx,
		gormx.NewParamBuilder().Model(&models.MarsBug{}).In("id", req.RecordIds).
			Eq("test_plan_id", req.TestPlanId).Eq("is_deleted", commonconstants.DeleteNo),
		map[string]any{
			"gmt_modified": times.Now(),
			"modifier":     ctx.User.AdAccount,
			"is_deleted":   commonconstants.DeleteYes,
		})
	return err
}
