package service

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/enums"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	set "github.com/duke-git/lancet/v2/datastructure/set"
	"strconv"
)

type CaseLibraryService struct{}

var (
	marsNodeHierarchyDao dao.MarsNodeHierarchyDao
	marsCaseDao          dao.MarsCaseDao
	nodeService          NodeService
)

func (CaseLibraryService) Insert(ctx *commoncontext.MantisContext, library *models.MarsCaseLibrary) (*models.MarsCaseLibrary, error) {
	library.SpaceId = ctx.Header.Get("spaceid")
	// 新增
	library.Creator = ctx.User.AdAccount
	library.Modifier = ctx.User.AdAccount
	library.GmtCreated = times.Now()
	library.GmtModified = times.Now()
	library.IsDeleted = commonconstants.DeleteNo
	_, err := gormx.InsertOne(ctx, &library)
	if err != nil {
		return nil, err
	}
	// 在case 表增加根节点
	nodeService.AddNode(ctx, dto.NodeDto{
		LibraryId: library.Id,
		Name:      library.Name,
		NodeType:  enums.RootNode.Value,
	})
	return library, nil
}

func (CaseLibraryService) Update(ctx *commoncontext.MantisContext, library *models.MarsCaseLibrary) (*models.MarsCaseLibrary, error) {
	library.Modifier = ctx.User.AdAccount
	library.GmtModified = times.Now()
	_, err := gormx.UpdateOneByCondition(ctx, &library)
	return library, err
}

func (CaseLibraryService) Copy(ctx *commoncontext.MantisContext, id int64) (int64, error) {
	// 复制用例库
	lib := models.MarsCaseLibrary{}
	lib.Id = id
	lib.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &lib)
	if err != nil {
		return 0, nil
	}
	lib.Id = snowflake.GenSnowFlakeId()
	lib.SetTimeNowAndUser(ctx.User.AdAccount)
	_, err = gormx.InsertOne(ctx, &lib)
	if err != nil {
		return 0, nil
	}
	newLibId := lib.Id
	// 查询被复制库的跟节点 id
	srcRoot := models.MarsCase{}
	err = gormx.Raw(ctx, "select * from mars_case where library_id = ? and node_type = 'root' and is_deleted='N'", &srcRoot, id)
	if err != nil {
		return 0, err
	}
	// 查出该节点的子节点并统一复制
	srcNodes, err := marsNodeHierarchyDao.SelectChildrenUnderParent(ctx, id, srcRoot.Id)
	if err != nil {
		return 0, err
	}
	srcNodes = append(srcNodes, srcRoot)
	copyCases := make([]models.MarsCase, 0, len(srcNodes))
	idMapping := make(map[int64]int64)
	for _, node := range srcNodes {
		// 深拷贝
		dest := node
		dest.Id = snowflake.GenSnowFlakeId()
		dest.SetTimeNowAndUser(ctx.User.AdAccount)
		dest.LibraryId = newLibId
		copyCases = append(copyCases, dest)
		idMapping[node.Id] = dest.Id
	}
	// 修改 parentId
	for i := range copyCases {
		copyCases[i].ParentId = idMapping[copyCases[i].ParentId]
	}
	if len(copyCases) != 0 {
		_, err = gormx.InsertBatch(ctx, &copyCases)
		if err != nil {
			return 0, err
		}
	}
	return newLibId, nil
}

func (CaseLibraryService) DeleteCaseLibrary(ctx *commoncontext.MantisContext, id int64) error {
	lib := models.MarsCaseLibrary{}
	lib.Id = id
	lib.GmtModified = times.Now()
	lib.Modifier = ctx.User.AdAccount
	lib.IsDeleted = commonconstants.DeleteYes
	_, err := gormx.UpdateOneByCondition(ctx, &lib)
	if err != nil {
		return err
	}
	goroutine.RunX(func() error {
		// 查询跟节点 id
		var rootId int64
		err := gormx.Raw(ctx, "select id from mars_case where library_id = ? and parent_id = 0 and is_deleted='N'", &rootId, id)
		if err != nil {
			return err
		}
		// 获取所有的用例节点 id
		caseIds, err := marsNodeHierarchyDao.RecursionSelectIds(ctx, rootId, constants.MarsNodeTypeCase)
		if err != nil {
			return err
		}
		// 删除所有的用例
		_, err = gormx.Exec(ctx, "update mars_case set is_deleted = 'Y',modifier = ?,gmt_modified = ? where library_id = ?",
			ctx.User.AdAccount, times.Now(), id)
		if err != nil {
			return err
		}
		// 删除所有的关联关系
		_, err = gormx.Exec(ctx, "update mars_case_relation set is_deleted = 'Y',modifier = ?,gmt_modified = ? where case_id in ?",
			nil, ctx.User.AdAccount, times.Now(), caseIds)
		if err != nil {
			return err
		}
		// 删除所有的测试计划绑定的用例
		_, err = gormx.UpdateBatchByParamBuilder(ctx,
			gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseRelation{}).Eq("type", constants.TestPlanRelationTypeCase).In("relation_id", caseIds),
			map[string]any{"is_deleted": "Y", "modifier": ctx.User.AdAccount, "gmt_modified": times.Now()})
		return err
	})
	return nil
}

func (CaseLibraryService) SearchCaseLibraryList(ctx *commoncontext.MantisContext, spaceId string, req dto.CaseLibrarySearchReqDto, pageReq gormx.PageRequest) (*gormx.PageResult, error) {
	paramBuilder := gormx.NewParamBuilder().Model(&models.MarsCaseLibrary{})
	if req.Name != "" {
		paramBuilder.Like("name", "%"+req.Name+"%")
	}
	if req.Creator != "" {
		paramBuilder.Eq("creator", req.Creator)
	}
	if req.GmtCreated != "" {
		paramBuilder.Gte("gmt_created", req.GetCreatTimeStart()).Lte("gmt_created", req.GetCreatTimeEnd())
	}
	if req.GmtModified != "" {
		paramBuilder.Gte("gmt_modified", req.GetModifyTimeStart()).Lte("gmt_modified", req.GetModifyTimeEnd())
	}
	if req.Share != nil {
		paramBuilder.Eq("share", req.Share)
	} else {
		paramBuilder.Eq("space_id", spaceId)
	}
	paramBuilder.Eq("is_deleted", commonconstants.DeleteNo).OrderByDesc("gmt_modified")
	libs := make([]models.MarsCaseLibrary, 0)
	pageResult, err := gormx.PageSelectByParamBuilder(ctx, paramBuilder, &libs, pageReq)
	if err != nil {
		return nil, err
	}
	libIds := make([]int64, 0, len(libs))
	spaceIds := set.New[string]()
	for _, lib := range libs {
		libIds = append(libIds, lib.Id)
		spaceIds.Add(lib.SpaceId)
	}
	projectNameMap := make(map[string]string)
	if req.Share != nil && *req.Share && len(spaceIds) > 0 {
		// 共享用例库需要查询项目名称
		infos, err := projectService.GetProjectInfo(ctx, spaceIds.ToSlice(), "")
		if err != nil {
			return nil, err
		}
		for _, p := range infos {
			projectNameMap[strconv.FormatInt(p.Id, 10)] = p.Name
		}
	}
	countMarsCasesByLibId, err := marsCaseDao.CountMarsCasesByLibId(ctx, libIds)
	if err != nil {
		return nil, err
	}
	libCountMap := make(map[int64]int64)
	for _, countDTO := range countMarsCasesByLibId {
		libCountMap[countDTO.LibraryId] = countDTO.Count
	}
	for i, lib := range libs {
		libs[i].Count = libCountMap[lib.Id]
		libs[i].ProjectName = projectNameMap[lib.SpaceId]
	}
	pageResult.List = libs
	return pageResult, nil
}

func (CaseLibraryService) SearchLibrary(ctx *commoncontext.MantisContext, spaceId, name string) ([]models.MarsCaseLibrary, error) {
	paramBuilder := gormx.NewParamBuilder().Model(&models.MarsCaseLibrary{}).Eq("space_id", spaceId)
	if name != "" {
		paramBuilder.Like("name", "%"+name+"%")
	}
	paramBuilder.Eq("is_deleted", commonconstants.DeleteNo).OrderByDesc("id")
	libs := make([]models.MarsCaseLibrary, 0)
	err := gormx.SelectByParamBuilder(ctx, paramBuilder, &libs)
	if err != nil {
		return nil, err
	}
	return libs, nil
}
