package service

import (
	"fmt"
	"log"
	"strconv"
	"testing"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	set "github.com/duke-git/lancet/v2/datastructure/set"
	"github.com/duke-git/lancet/v2/slice"
)

type Rs struct {
	DemandId   string
	RelationId string
}

func Test002(t *testing.T) {
	a := []int{1, 2, 3}
	b := []int{1, 2, 4}
	x1 := slice.Difference(a, b)
	x2 := slice.Difference(b, a)
	fmt.Printf("%+v", x1)
	fmt.Printf("%+v", x2)
}

func TestC01(t *testing.T) {
	strings := make([]string, 20)
	strings[10] = "-1"
	fmt.Printf("%+v", len(strings))
	now := times.Now()
	fmt.Print(now.ToString())
}

func TestC03(t *testing.T) {
	relationList := []Rs{
		{
			DemandId:   "1",
			RelationId: "100",
		},
		{
			DemandId:   "1",
			RelationId: "200",
		},
		{
			DemandId:   "1",
			RelationId: "100",
		},
		{
			DemandId:   "2",
			RelationId: "300",
		},
	}
	demandIdCaseIdsMap := make(map[string]set.Set[int64])
	for _, relation := range relationList {
		caseIds, ok := demandIdCaseIdsMap[relation.DemandId]
		cId, err := strconv.ParseInt(relation.RelationId, 10, 64)
		if err != nil {
			log.Fatal("parse relationId error", err)
		}
		if ok {
			caseIds.Add(cId)
		} else {
			demandIdCaseIdsMap[relation.DemandId] = set.New[int64]()
			demandIdCaseIdsMap[relation.DemandId].Add(cId)
		}
	}
	fmt.Printf("%+v", demandIdCaseIdsMap)
}
