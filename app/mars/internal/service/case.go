package service

import (
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto/outer"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/enums"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	commonmodels "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	commonremote "git.zhonganinfo.com/zainfo/cube-mantis/pkg/remote"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	set "github.com/duke-git/lancet/v2/datastructure/set"
	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
)

type CaseService struct{}

var marsCaseTestPlanRelationDao dao.MarsTestPlanCaseRelationDao

func (c CaseService) Insert(ctx *commoncontext.MantisContext, caseReq *models.MarsCase) (*models.MarsCase, error) {
	// 校验重名
	nodes := make([]models.MarsCase, 0)
	err := gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCase{}).Eq("library_id", caseReq.LibraryId).Eq("parent_id", caseReq.ParentId).Eq("name", caseReq.Name).Eq("is_deleted", commonconstants.DeleteNo),
		&nodes)
	if err != nil {
		return nil, err
	}
	if len(nodes) > 0 {
		return nil, errors.New("用例名称已存在")
	}
	caseReq.Addons = commonmodels.NewAddonsWithUser(ctx.User)
	caseReq.NodeType = enums.CaseNode.Value
	tagIds := make([]string, 0)
	for _, tag := range caseReq.Tags {
		tagIds = append(tagIds, strconv.FormatInt(tag.Value, 10))
	}
	caseReq.TagIds = tagIds
	max, err := marsCaseDao.SelectMaxSiblingOrder(ctx, caseReq.ParentId)
	if err != nil {
		return nil, err
	}
	caseReq.SiblingOrder = max + 1
	_, err = gormx.InsertOne(ctx, caseReq)
	if len(caseReq.RelateDemands) != 0 {
		insertRelationList := make([]models.MarsCaseRelation, 0)
		for _, demand := range caseReq.RelateDemands {
			insertRelationList = append(insertRelationList, models.MarsCaseRelation{
				Addons:     commonmodels.NewAddonsWithUser(ctx.User),
				RelationId: strconv.FormatInt(demand.Id, 10),
				CaseId:     caseReq.Id,
			})
		}
		_, err = gormx.InsertBatch(ctx, insertRelationList)
		if err != nil {
			return nil, err
		}
	}
	return caseReq, err
}

func (c CaseService) BatchInsert(ctx *commoncontext.MantisContext, cases []*models.MarsCase) error {
	for _, cs := range cases {
		_, err := c.Insert(ctx, cs)
		if err != nil {
			return err
		}
	}
	return nil
}

func (c CaseService) Update(ctx *commoncontext.MantisContext, caseReq *models.MarsCase) (*models.MarsCase, error) {
	id := caseReq.Id
	oldMarsCase := models.MarsCase{Addons: commonmodels.Addons{Id: id}}
	// 校验重名
	nodes := make([]models.MarsCase, 0)
	err := gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCase{}).Eq("library_id", caseReq.LibraryId).Eq("parent_id", caseReq.ParentId).Eq("name", caseReq.Name).NotEq("id", id).Eq("is_deleted", commonconstants.DeleteNo),
		&nodes)
	if err != nil {
		return nil, err
	}
	if len(nodes) > 0 {
		return nil, errors.New("用例名称已存在")
	}
	err = gormx.SelectOneByCondition(ctx, &oldMarsCase)
	if err != nil {
		return nil, err
	}
	oldMarsCase.Id = 0
	his := models.MarsCaseHistory{
		CaseId:   id,
		MarsCase: oldMarsCase,
	}
	his.SetTimeNowAndUser(ctx.User.AdAccount)
	_, err = gormx.InsertOne(ctx, &his)
	if err != nil {
		return nil, err
	}
	caseReq.Modifier = ctx.User.AdAccount
	caseReq.GmtModified = times.Now()
	tagIds := make([]string, 0)
	for _, tag := range caseReq.Tags {
		tagIds = append(tagIds, strconv.FormatInt(tag.Value, 10))
	}
	caseReq.TagIds = tagIds
	_, err = gormx.UpdateBatchByParamBuilderAndMap(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCaseRelation{}).Eq("case_id", caseReq.Id).Eq("is_deleted", commonconstants.DeleteNo),
		map[string]any{"is_deleted": commonconstants.DeleteYes})
	if err != nil {
		return nil, err
	}
	if len(caseReq.RelateDemands) != 0 {
		insertRelationList := make([]models.MarsCaseRelation, 0)
		for _, demand := range caseReq.RelateDemands {
			insertRelationList = append(insertRelationList, models.MarsCaseRelation{
				Addons:     commonmodels.NewAddonsWithUser(ctx.User),
				RelationId: strconv.FormatInt(demand.Id, 10),
				CaseId:     caseReq.Id,
			})
		}
		_, err = gormx.InsertBatch(ctx, insertRelationList)
		if err != nil {
			return nil, err
		}
	}
	_, err = gormx.UpdateOneAllowZeroByCondition(ctx, &caseReq)
	return caseReq, err
}

// Copy 只复制用例本身，不复制任何关联信息
func (c CaseService) Copy(ctx *commoncontext.MantisContext, caseId int64) error {
	marsCase := models.MarsCase{}
	marsCase.Id = caseId
	marsCase.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &marsCase)
	if err != nil {
		return err
	}
	marsCase.Addons = commonmodels.NewAddonsWithUser(ctx.User)
	marsCase.Name = marsCase.Name + constants.CopySuffix
	// 校验重名
	nodes := make([]models.MarsCase, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCase{}).Eq("library_id", marsCase.LibraryId).Eq("parent_id", marsCase.ParentId).Eq("name", marsCase.Name).Eq("is_deleted", commonconstants.DeleteNo),
		&nodes)
	if err != nil {
		return err
	}
	if len(nodes) > 0 {
		return fmt.Errorf("用例名称[%s]已存在", marsCase.Name)
	}
	_, err = gormx.InsertOne(ctx, &marsCase)
	return err
}

func (c CaseService) MoveCase(ctx *commoncontext.MantisContext, reqDTO dto.MarsCaseMoveCopyReqDTO) error {
	// 先查询要移动的用例信息
	moveCases := make([]models.MarsCase, 0)
	err := gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCase{}).
			In("id", reqDTO.CaseIds).Eq("is_deleted", commonconstants.DeleteNo),
		&moveCases)
	if err != nil {
		return err
	}

	// 批量校验目标父节点下是否有重名的用例
	// 一次性查询目标父节点下的所有用例
	existingCases := make([]models.MarsCase, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCase{}).
			Eq("library_id", reqDTO.TargetLibraryId).
			Eq("parent_id", reqDTO.NodeId).
			Eq("is_deleted", commonconstants.DeleteNo),
		&existingCases)
	if err != nil {
		return err
	}

	// 构建现有用例的名称映射
	existingCaseMap := make(map[string]int64)
	for _, existingCase := range existingCases {
		existingCaseMap[existingCase.Name] = existingCase.Id
	}

	// 检查是否有重名用例（排除要移动的用例本身）
	for _, moveCase := range moveCases {
		if existingId, exists := existingCaseMap[moveCase.Name]; exists && existingId != moveCase.Id {
			return fmt.Errorf("目标位置已存在同名用例[%s]", moveCase.Name)
		}
	}

	// 查询最大sort
	maxSort, err := marsCaseDao.SelectMaxSiblingOrder(ctx, reqDTO.NodeId)
	if err != nil {
		return err
	}
	// 更新 mars_case表
	_, err = gormx.UpdateBatchByParamBuilderAndMap(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCase{}).In("id", reqDTO.CaseIds).Eq("is_deleted", commonconstants.DeleteNo),
		map[string]any{
			"modifier":      ctx.User.AdAccount,
			"gmt_modified":  times.Now(),
			"library_id":    reqDTO.TargetLibraryId,
			"parent_id":     reqDTO.NodeId,
			"sibling_order": gorm.Expr("sibling_order + ?", maxSort),
		})
	return err
}

func (c CaseService) BatchCopy(ctx *commoncontext.MantisContext, reqDTO dto.MarsCaseMoveCopyReqDTO) error {
	cases := make([]models.MarsCase, 0)
	err := gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCase{}).
			In("id", reqDTO.CaseIds).Eq("is_deleted", commonconstants.DeleteNo),
		&cases)
	if err != nil {
		return err
	}

	// 批量校验目标父节点下是否有重名的用例
	// 一次性查询目标父节点下的所有用例
	existingCases := make([]models.MarsCase, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCase{}).
			Eq("library_id", reqDTO.TargetLibraryId).
			Eq("parent_id", reqDTO.NodeId).
			Eq("is_deleted", commonconstants.DeleteNo),
		&existingCases)
	if err != nil {
		return err
	}

	// 构建现有用例的名称集合
	existingCaseNames := make(map[string]bool)
	for _, existingCase := range existingCases {
		existingCaseNames[existingCase.Name] = true
	}

	// 检查是否有重名用例
	for _, copyCase := range cases {
		copyName := copyCase.Name
		if existingCaseNames[copyName] {
			return fmt.Errorf("目标位置已存在同名用例[%s]", copyName)
		}
	}

	caseIdMap := make(map[int64]models.MarsCase)
	for _, marsCase := range cases {
		caseIdMap[marsCase.Id] = marsCase
	}
	insertCaseList := make([]models.MarsCase, 0)
	for _, caseId := range reqDTO.CaseIds {
		marsCase := caseIdMap[caseId]
		marsCase.Addons = commonmodels.NewAddonsWithUser(ctx.User)
		marsCase.LibraryId = reqDTO.TargetLibraryId
		marsCase.ParentId = reqDTO.NodeId
		insertCaseList = append(insertCaseList, marsCase)
	}
	if len(insertCaseList) != 0 {
		_, err = gormx.InsertBatch(ctx, insertCaseList)
		if err != nil {
			return err
		}
	}
	return nil
}

// FastCreate 在计划界面快速创建用例
func (c CaseService) FastCreate(ctx *commoncontext.MantisContext, caseDTO dto.TestPlanCaseDto) error {
	marsCase, err := c.Insert(ctx, &caseDTO.MarsCase)
	if caseDTO.DemandId == "" {
		caseDTO.DemandId = constants.DefaultDemandID
	}
	if err != nil {
		return err
	}
	// 初始化历史记录
	his := models.MarsTestPlanCaseExecHistory{
		Addons:        commonmodels.NewAddonsWithUser(ctx.User),
		TestPlanId:    caseDTO.TestPlanId,
		CaseId:        strconv.FormatInt(marsCase.Id, 10),
		Type:          constants.TestPlanRelationTypeCase,
		ExecuteResult: constants.CaseResultTypeUntest,
	}
	_, err = gormx.InsertOne(ctx, &his)
	if err != nil {
		return err
	}
	// 设置关联关系
	caseTestPlanRelation := models.MarsTestPlanCaseRelation{}
	caseTestPlanRelation.Addons = commonmodels.NewAddonsWithUser(ctx.User)
	caseTestPlanRelation.RelationId = strconv.FormatInt(marsCase.Id, 10)
	caseTestPlanRelation.TestPlanId = caseDTO.TestPlanId
	caseTestPlanRelation.Type = constants.TestPlanRelationTypeCase
	caseTestPlanRelation.DemandId = caseDTO.DemandId
	caseTestPlanRelation.Sort, err = marsCaseTestPlanRelationDao.SelectMaxSortByTestPlanId(ctx, caseDTO.TestPlanId, constants.TestPlanRelationTypeCase, caseDTO.DemandId)
	if err != nil {
		return err
	}
	caseTestPlanRelation.Sort++
	caseTestPlanRelation.LastExecId = his.Id
	_, err = gormx.InsertOne(ctx, &caseTestPlanRelation)
	return err
}

// SearchInLib 用例库下搜索用例,返回列表
func (c CaseService) SearchInLib(ctx *commoncontext.MantisContext, name string, libId int64, pageInfo gormx.PageRequest) (*gormx.PageResult, error) {
	searchBuilder := gormx.NewParamBuilder().Model(&models.MarsCase{}).SelectMany("id", "name", "priority", "preCondition", "steps").
		Eq("library_id", libId).
		Eq("node_type", constants.MarsNodeTypeCase).
		Eq("is_deleted", commonconstants.DeleteNo)
	if name != "" {
		searchBuilder.Like("name", "%"+name+"%")
	}
	caseList := make([]models.MarsCase, 0)
	page, err := gormx.PageSelectByParamBuilder(ctx, searchBuilder, &caseList, pageInfo)
	dataList := make([]map[string]any, 0)
	for _, v := range caseList {
		dataList = append(dataList, map[string]any{
			"id":           v.Id,
			"name":         v.Name,
			"priority":     v.Priority,
			"steps":        v.Steps,
			"preCondition": v.PreCondition,
		})
	}
	page.List = dataList
	return page, err
}

func (CaseService) Info(ctx *commoncontext.MantisContext, id int64) (*models.MarsCase, error) {
	marsCase := models.MarsCase{}
	marsCase.Id = id
	marsCase.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &marsCase)
	if err != nil {
		return nil, err
	}
	// 查询tags
	if len(marsCase.TagIds) != 0 {
		tags, err := commonremote.CubeBaseRemoteApi{}.GetTagsByIds(ctx, strings.Join(marsCase.TagIds, ","))
		if err != nil {
			return nil, err
		}
		marsCase.Tags = tags
	}
	// 查询评审状态
	caseReviews := make([]models.MarsReviewCaseRelation, 0)
	err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MarsReviewCaseRelation{}).Eq("case_id", marsCase.Id).Eq("is_deleted", commonconstants.DeleteNo).OrderByDesc("gmt_modified"), &caseReviews)
	if err != nil {
		return nil, err
	}
	if len(caseReviews) != 0 {
		marsCase.ReviewHistory = make([]models.ReviewHistory, 0)
		reviewIds := make([]int64, 0)
		for _, review := range caseReviews {
			reviewIds = append(reviewIds, review.ReviewId)
		}
		reviews := make([]models.MarsReview, 0)
		err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MarsReview{}).In("id", reviewIds), &reviews)
		if err != nil {
			return nil, err
		}
		reviewIdNameMap := make(map[int64]string)
		for _, review := range reviews {
			reviewIdNameMap[review.Id] = review.Name
		}
		for _, review := range caseReviews {
			his := models.ReviewHistory{
				Id:           review.ReviewId,
				Name:         reviewIdNameMap[review.ReviewId],
				ReviewStatus: review.ReviewStatus,
			}
			if len(review.Comments) != 0 {
				his.Reviewer = review.Comments[0].Commentor
				his.ReviewTime = review.Comments[0].CommentTime
			}
			marsCase.ReviewHistory = append(marsCase.ReviewHistory, his)
		}
	}
	// 查询关联需求
	caseRelations := make([]models.MarsCaseRelation, 0)
	err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MarsCaseRelation{}).Eq("case_id", id).Eq("is_deleted", commonconstants.DeleteNo), &caseRelations)
	if err != nil {
		return nil, err
	}
	if len(caseRelations) != 0 {
		demandIds := make([]string, 0)
		for _, relation := range caseRelations {
			demandIds = append(demandIds, relation.RelationId)
		}
		demands, _, err := projectService.GetDemandsByPage(ctx, dto.ProjectCommonSelectReq{Ids: demandIds}, gormx.PageRequest{Page: 1, PageSize: int64(len(demandIds))})
		if err != nil {
			return nil, err
		}
		marsCase.RelateDemands = demands
	}
	return &marsCase, err
}

func (CaseService) HistoryList(ctx *commoncontext.MantisContext, caseId int64) ([]models.MarsCaseHistory, error) {
	res := make([]models.MarsCaseHistory, 0)
	err := gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCaseHistory{}).Select("id, gmt_created, creator").
			Eq("case_id", caseId).Eq("is_deleted", commonconstants.DeleteNo).OrderByDesc("gmt_created"),
		&res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (CaseService) HistoryDetail(ctx *commoncontext.MantisContext, id int64) (*models.MarsCase, error) {
	marsCaseHistory := models.MarsCaseHistory{}
	marsCaseHistory.Id = id
	marsCaseHistory.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &marsCaseHistory)
	if err != nil {
		return nil, err
	}
	// 查询tags
	if len(marsCaseHistory.TagIds) != 0 {
		tags, err := commonremote.CubeBaseRemoteApi{}.GetTagsByIds(ctx, strings.Join(marsCaseHistory.TagIds, ","))
		if err != nil {
			return nil, err
		}
		marsCaseHistory.Tags = tags
	}
	return &marsCaseHistory.MarsCase, nil
}

func (CaseService) CheckDelete(ctx *commoncontext.MantisContext, caseIds []int64) (map[string]interface{}, error) {
	res := make(map[string]any)
	// 查询用例关联的评审
	reviewCaseRelations := make([]models.MarsReviewCaseRelation, 0)
	err := gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsReviewCaseRelation{}).In("case_id", caseIds).Eq("is_deleted", commonconstants.DeleteNo),
		&reviewCaseRelations)
	if err != nil {
		return nil, err
	}
	reviewIds := set.New[int64]()
	for _, relation := range reviewCaseRelations {
		reviewIds.Add(relation.ReviewId)
	}
	if reviewIds.Size() != 0 {
		reviews := make([]models.MarsReview, 0, reviewIds.Size())
		err = gormx.SelectByParamBuilder(ctx,
			gormx.NewParamBuilder().Model(&models.MarsReview{}).In("id", reviewIds.ToSlice()).Eq("is_deleted", commonconstants.DeleteNo),
			&reviews)
		if err != nil {
			return nil, err
		}
		res["reviews"] = reviews
	}
	// 查询用例关联的测试计划
	caseIdStrs := make([]string, 0, len(caseIds))
	for _, caseId := range caseIds {
		caseIdStrs = append(caseIdStrs, strconv.FormatInt(caseId, 10))
	}
	caseTestPlanRelations := make([]models.MarsTestPlanCaseRelation, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseRelation{}).In("relation_id", caseIdStrs).Eq("is_deleted", commonconstants.DeleteNo),
		&caseTestPlanRelations)
	if err != nil {
		return nil, err
	}
	testPlanIds := set.New[int64]()
	for _, relation := range caseTestPlanRelations {
		testPlanIds.Add(relation.TestPlanId)
	}
	if testPlanIds.Size() != 0 {
		testPlans := make([]models.MarsTestPlan, 0, testPlanIds.Size())
		err = gormx.SelectByParamBuilder(ctx,
			gormx.NewParamBuilder().Model(&models.MarsTestPlan{}).In("id", testPlanIds.ToSlice()).Eq("is_deleted", commonconstants.DeleteNo),
			&testPlans)
		if err != nil {
			return nil, err
		}
		res["testPlans"] = testPlans
	}
	return res, nil
}

func (CaseService) Delete(ctx *commoncontext.MantisContext, id int64) error {
	// 删除用例节点
	_, err := gormx.Exec(ctx, `update mars_case set is_deleted = 'Y' where id = ?`, id)
	if err != nil {
		return err
	}
	// 删除用例节点所有的关联关系
	_, err = gormx.Exec(ctx, "update mars_case_relation set is_deleted = 'Y' where case_id = ?", id)
	if err != nil {
		return err
	}
	// 删除评审关联的节点
	_, err = gormx.UpdateBatchByParamBuilderAndMap(ctx, gormx.NewParamBuilder().Model(&models.MarsReviewCaseRelation{}).
		Eq("case_id", id), map[string]any{"is_deleted": commonconstants.DeleteYes})
	if err != nil {
		return err
	}
	// 删除测试计划关联的用例节点
	// _, err = gormx.UpdateBatchByParamBuilderAndMap(ctx, gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseRelation{}).
	// 	Eq("case_id", id).Eq("type", constants.TestPlanRelationTypeCase), map[string]any{"is_deleted": commonconstants.DeleteYes})
	return err
}

func (CaseService) BatchDelete(ctx *commoncontext.MantisContext, ids []int64) error {
	// 删除用例节点
	_, err := gormx.Exec(ctx, `update mars_case set is_deleted = 'Y' where id in ?`, ids)
	if err != nil {
		return err
	}
	// 删除用例节点所有的关联关系
	_, err = gormx.Exec(ctx, "update mars_case_relation set is_deleted = 'Y' where case_id in ?", ids)
	if err != nil {
		return err
	}
	// 删除评审关联的节点
	_, err = gormx.UpdateBatchByParamBuilderAndMap(ctx, gormx.NewParamBuilder().Model(&models.MarsReviewCaseRelation{}).
		In("case_id", ids), map[string]any{"is_deleted": commonconstants.DeleteYes})
	if err != nil {
		return err
	}
	// 删除测试计划关联的用例节点
	idStrs := make([]string, 0)
	for _, id := range ids {
		idStrs = append(idStrs, strconv.FormatInt(id, 10))
	}
	_, err = gormx.UpdateBatchByParamBuilderAndMap(ctx, gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseRelation{}).
		In("relation_id", idStrs).Eq("type", constants.TestPlanRelationTypeCase), map[string]any{"is_deleted": commonconstants.DeleteYes})
	return err
}

func (CaseService) GetRelatedDemands(ctx *commoncontext.MantisContext, caseId int64) (any, error) {
	var relationList []models.MarsCaseRelation
	gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MarsCaseRelation{}).
		Eq("case_id", caseId).
		Eq("is_deleted", commonconstants.DeleteNo), &relationList)
	if len(relationList) == 0 {
		return nil, nil
	}
	ids := make([]string, 0)
	for i, v := range relationList {
		ids[i] = v.RelationId
	}
	list, total, err := projectService.GetDemandsByPage(ctx, dto.ProjectCommonSelectReq{Ids: ids}, gormx.PageRequest{Page: 1, PageSize: math.MaxInt64})
	if err != nil {
		return nil, err
	}
	logger.Logger.Infof("demand total=%d", total)
	return list, nil
}

func (CaseService) RelateCase(ctx *commoncontext.MantisContext, demandId string, caseIds []int64) error {
	relatedCaseIds := make([]int64, 0)
	err := gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCaseRelation{}).Select("case_id").
			Eq("relation_id", demandId).Eq("is_deleted", commonconstants.DeleteNo),
		&relatedCaseIds)
	if err != nil {
		return err
	}
	addRelateCaseIds := slice.Difference(caseIds, relatedCaseIds)
	addRelations := make([]models.MarsCaseRelation, 0)
	for _, caseId := range addRelateCaseIds {
		addRelations = append(addRelations, models.MarsCaseRelation{
			Addons:     commonmodels.NewAddonsWithUser(ctx.User),
			CaseId:     caseId,
			RelationId: demandId,
		})
	}
	if len(addRelations) > 0 {
		_, err = gormx.InsertBatch(ctx, &addRelations)
	}
	return err
}

func (CaseService) CancelRelatedDemand(ctx *commoncontext.MantisContext, ids []int64, demandId string) error {
	_, err := gormx.UpdateBatchByParamBuilderAndMap(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCaseRelation{}).Eq("relation_id", demandId).In("case_id", ids).
			Eq("is_deleted", commonconstants.DeleteNo),
		map[string]any{
			"is_deleted":   commonconstants.DeleteYes,
			"modifier":     ctx.User.AdAccount,
			"gmt_modified": times.Now(),
		})
	return err
}

// CaseListByDemand 根据需求ID查询用例列表
func (CaseService) CaseListByDemand(ctx *commoncontext.MantisContext, demandId int64, pageReq gormx.PageRequest) (*gormx.PageResult, error) {
	// 1. 查询需求关联的用例ID
	caseRelations := make([]models.MarsCaseRelation, 0)
	err := gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCaseRelation{}).
			Eq("relation_id", strconv.FormatInt(demandId, 10)).
			Eq("relation_type", constants.TestPLanTypeDemand).
			Eq("is_deleted", commonconstants.DeleteNo),
		&caseRelations)
	if err != nil {
		return nil, err
	}

	if len(caseRelations) == 0 {
		return &gormx.PageResult{
			Total:       0,
			Pages:       0,
			PageSize:    pageReq.PageSize,
			CurrentPage: pageReq.Page,
			List:        make([]dto.CaseListDTO, 0),
		}, nil
	}

	// 2 提取用例ID列表
	caseIds := make([]int64, 0, len(caseRelations))
	for _, relation := range caseRelations {
		caseIds = append(caseIds, relation.CaseId)
	}

	// 3分页查询用例详情
	cases := make([]models.MarsCase, 0)
	paramBuilder := gormx.NewParamBuilder().Model(&models.MarsCase{}).
		In("id", caseIds).
		Eq("is_deleted", commonconstants.DeleteNo).
		OrderByDesc("id")

	pageResult, err := gormx.PageSelectByParamBuilder(ctx, paramBuilder, &cases, pageReq)
	if err != nil {
		return nil, err
	}

	// 4. 查询用例库信息
	libraryIds := make([]int64, 0)
	parentIds := make([]int64, 0)
	for _, c := range cases {
		libraryIds = append(libraryIds, c.LibraryId)
		parentIds = append(parentIds, c.ParentId)
	}
	libraries := make([]models.MarsCaseLibrary, 0)
	if len(libraryIds) > 0 {
		err = gormx.SelectByParamBuilder(ctx,
			gormx.NewParamBuilder().Model(&models.MarsCaseLibrary{}).In("id", libraryIds),
			&libraries)
		if err != nil {
			return nil, err
		}
	}
	nodeMap := make(map[int64]string)
	if len(parentIds) > 0 {
		parentNode := make([]models.MarsCase, 0)
		err = gormx.SelectByParamBuilder(ctx,
			gormx.NewParamBuilder().Model(&models.MarsCase{}).SelectMany("id, name").In("id", parentIds),
			&parentNode)
		if err != nil {
			return nil, err
		}
		for _, node := range parentNode {
			nodeMap[node.Id] = node.Name
		}
	}

	// 5. 构建库名称映射
	libraryMap := make(map[int64]string)
	for _, lib := range libraries {
		libraryMap[lib.Id] = lib.Name
	}

	// 6. 组装DTO
	caseListDTOs := make([]dto.CaseListDTO, 0, len(cases))
	for _, c := range cases {
		caseDTO := dto.CaseListDTO{
			Id:          c.Id,
			Name:        c.Name,
			LibraryName: libraryMap[c.LibraryId],
			NodePath:    nodeMap[c.ParentId],
		}
		caseListDTOs = append(caseListDTOs, caseDTO)
	}

	// 7. 更新分页结果
	pageResult.List = caseListDTOs
	return pageResult, nil
}

func (c CaseService) AddBatchAndRelateDemand(ctx *commoncontext.MantisContext, req outer.AddBatchAndRelateDemandReq) error {
	logger.Logger.Infof("add batch and relate demand req= %+v", req)
	if len(req.MarsCaseGptDTOS) < 1 {
		return errors.New("用例为空,请重新生成")
	}
	user := ctx.User
	caseList := make([]models.MarsCase, len(req.MarsCaseGptDTOS))
	for i, ca := range req.MarsCaseGptDTOS {
		caseList[i] = models.MarsCase{
			Addons:       commonmodels.NewAddonsWithUser(user),
			Name:         ca.Name,
			LibraryId:    req.LibraryID,
			ParentId:     req.ParentNodeID,
			NodeType:     constants.MarsNodeTypeCase,
			Priority:     ca.Priority,
			PreCondition: ca.Precondition,
			Steps:        ca.CaseStepReqDTOs,
		}
	}
	rels := make([]models.MarsCaseRelation, 0)
	for _, ca := range caseList {
		_, err := c.Insert(ctx, &ca)
		if err != nil {
			return err
		}
		if len(req.RelateDemandIDs) > 0 {
			for _, demandID := range req.RelateDemandIDs {
				rels = append(rels, models.MarsCaseRelation{
					Addons:       commonmodels.NewAddonsWithUser(user),
					CaseId:       ca.Id,
					RelationId:   strconv.FormatInt(demandID, 10),
					RelationType: constants.TestPLanTypeDemand,
				})
			}
		}
	}
	if len(rels) > 0 {
		logger.Logger.Infof("插入用例与需求的关联关系，共计 %d 条", len(rels))
		_, err := gormx.InsertBatch(ctx, rels)
		if err != nil {
			return err
		}
	}
	return nil
}
