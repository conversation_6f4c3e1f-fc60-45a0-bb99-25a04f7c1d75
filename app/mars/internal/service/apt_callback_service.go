package service

import (
	"errors"
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/remote"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type AptCallbackService struct{}

func (AptCallbackService) ModifyCaseResult(ctx *context.MantisContext, req remote.AptCaseExecCallback) error {
	logger.Logger.Infof("apt回调信息,req=%+v", req)
	if req.ExecuteTag == "" {
		return errors.New("executeTag is empty")
	}
	if len(req.CaseExecResults) == 0 {
		return errors.New("caseExecResults is empty")
	}
	successCaseId := make([]string, 0)
	failCaseId := make([]string, 0)
	for _, r := range req.CaseExecResults {
		if r.ExecuteResult == 22 {
			successCaseId = append(successCaseId, strconv.FormatInt(r.RelatedCaseId, 10))
		} else if r.ExecuteResult == 23 {
			failCaseId = append(failCaseId, strconv.FormatInt(r.RelatedCaseId, 10))
		}
	}
	logger.Logger.Infof("成功用例id:%v,失败用例id:%v", successCaseId, failCaseId)
	if len(successCaseId) > 0 {
		_, err := gormx.UpdateBatchByParamBuilderAndMap(ctx, gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseExecHistory{}).
			Eq("result", req.ExecuteTag).In("case_id", successCaseId).Eq("type", constants.TestPlanRelationTypeApi),
			map[string]any{
				"execute_result": constants.CaseResultTypePass,
				"gmt_modified":   times.Now(),
			})
		if err != nil {
			logger.Logger.Errorf("更新用例执行结果失败(成功用例),err=%v", err)
			return err
		}
	}
	if len(failCaseId) > 0 {
		_, err := gormx.UpdateBatchByParamBuilderAndMap(ctx, gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseExecHistory{}).
			Eq("result", req.ExecuteTag).In("case_id", failCaseId).Eq("type", constants.TestPlanRelationTypeApi),
			map[string]any{
				"execute_result": constants.CaseResultTypeFail,
				"gmt_modified":   times.Now(),
			})
		if err != nil {
			logger.Logger.Errorf("更新用例执行结果失败(成功用例),err=%v", err)
			return err
		}
	}
	return nil
}
