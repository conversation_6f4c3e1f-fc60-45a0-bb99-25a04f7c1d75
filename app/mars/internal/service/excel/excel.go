package service

import (
	"embed"
	"errors"
	"fmt"
	"mime/multipart"
	"sort"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/enums"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/remote/project"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/excel"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	commonmodels "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	"github.com/xuri/excelize/v2"
)

//go:embed template.xlsx
var ExcelTemplateFS embed.FS

var expectedHeads = []string{"模块", "用例名称", "前置条件", "优先级", "关联需求编号", "测试描述类型", "测试描述内容", "测试预期", "备注"}

type ExcelService struct{}

func (s ExcelService) Import(ctx *commoncontext.MantisContext, fileHeader *multipart.FileHeader, libId int64) (int, error) {
	eu, err := excel.OpenFromMultipartFile(fileHeader, "")
	if err != nil {
		return 0, err
	}
	rows, err := eu.ReadRows()
	if err != nil {
		return 0, err
	}
	// 判断第一行的表头
	if len(rows) == 0 {
		return 0, errors.New("空excel")
	}
	heads := rows[0]
	if len(heads) < len(expectedHeads) {
		return 0, fmt.Errorf("不合法的表头: %v", heads)
	}
	for i, expected := range expectedHeads {
		if !strings.Contains(heads[i], expected) {
			return 0, fmt.Errorf("不合法的表头: %v", heads)
		}
	}
	rows = rows[1:]
	var total int
	err = gormx.Transaction(ctx, func() error {
		total, err = s.importCasesUpsert(ctx, rows, libId)
		return err
	})
	return total, err
}

// importCasesUpsert 根据路径匹配实现upsert策略导入用例
func (s ExcelService) importCasesUpsert(ctx *commoncontext.MantisContext, rows [][]string, libId int64) (int, error) {
	// 获取库的根节点
	var rootId int64
	err := gormx.Raw(ctx, "select id from mars_case where library_id = ? and parent_id = 0 and is_deleted='N'", &rootId, libId)
	if err != nil {
		return 0, err
	}

	// 需要处理需求关联的用例ID和需求编号
	caseRelations := make(map[int64][]string)
	var processedCount int

	for i, row := range rows {
		newRow := make([]string, len(expectedHeads))
		copy(newRow, row)
		row = newRow

		// 解析路径和构建完整路径
		pathParts := strings.Split(strings.Trim(row[0], "/"), "/")
		if len(pathParts) == 1 && pathParts[0] == "" {
			pathParts = []string{}
		}

		// 构建完整路径（包括用例名称）
		fullPath := append(pathParts, row[1])

		// 查找或创建路径上的所有节点
		currentParentId := rootId
		for j, pathPart := range fullPath {
			isLastPart := j == len(fullPath)-1
			var nodeType string
			if isLastPart {
				nodeType = enums.CaseNode.Value
				processedCount++
			} else {
				nodeType = enums.FolderNode.Value
			}

			// 查找当前层级的节点
			existingNode, err := s.findNodeByPath(ctx, libId, fullPath[:j+1])
			if err != nil {
				return 0, err
			}

			if existingNode != nil {
				// 节点存在
				if isLastPart {
					// 更新用例节点
					err = s.updateCaseNode(ctx, existingNode, row)
					if err != nil {
						return 0, fmt.Errorf("第%d行更新用例失败: %s", i+1, err.Error())
					}

					// 处理需求关联
					if strings.TrimSpace(row[4]) != "" {
						demandCodes := s.parseDemandCodes(row[4])
						caseRelations[existingNode.Id] = demandCodes
					}
				}
				currentParentId = existingNode.Id
			} else {
				// 节点不存在，创建新节点
				maxOrder, err := dao.MarsCaseDao{}.SelectMaxSiblingOrder(ctx, currentParentId)
				if err != nil {
					return 0, err
				}

				newNode := models.MarsCase{
					Addons:       commonmodels.NewAddonsWithUser(ctx.User),
					Name:         pathPart,
					NodeType:     nodeType,
					LibraryId:    libId,
					ParentId:     currentParentId,
					SiblingOrder: maxOrder + 1,
				}

				if isLastPart {
					// 设置用例特有字段
					err = s.setCaseFields(&newNode, row, i+1)
					if err != nil {
						return 0, err
					}
				}

				_, err = gormx.InsertOne(ctx, &newNode)
				if err != nil {
					return 0, fmt.Errorf("第%d行创建节点失败: %s", i+1, err.Error())
				}

				if isLastPart {
					// 处理需求关联
					if strings.TrimSpace(row[4]) != "" {
						demandCodes := s.parseDemandCodes(row[4])
						caseRelations[newNode.Id] = demandCodes
					}
				}
				currentParentId = newNode.Id
			}
		}
	}

	// 处理需求关联
	err = s.processCaseRelations(ctx, caseRelations)
	if err != nil {
		return 0, err
	}

	return processedCount, nil
}

// findNodeByPath 根据完整路径查找节点
func (s ExcelService) findNodeByPath(ctx *commoncontext.MantisContext, libId int64, pathParts []string) (*models.MarsCase, error) {
	if len(pathParts) == 0 {
		return nil, nil
	}

	// 获取库根节点
	var rootId int64
	err := gormx.Raw(ctx, "select id from mars_case where library_id = ? and parent_id = 0 and is_deleted='N'", &rootId, libId)
	if err != nil {
		return nil, err
	}

	currentParentId := rootId
	for i, pathPart := range pathParts {
		var node models.MarsCase
		node.ParentId = currentParentId
		node.Name = pathPart
		node.LibraryId = libId
		node.IsDeleted = commonconstants.DeleteNo
		err := gormx.SelectOneByCondition(ctx, &node)
		if err != nil {
			return nil, err
		}

		if node.Id == 0 {
			return nil, nil
		}

		if i == len(pathParts)-1 {
			// 找到目标节点
			return &node, nil
		}
		currentParentId = node.Id
	}

	return nil, nil
}

// updateCaseNode 更新用例节点
func (s ExcelService) updateCaseNode(ctx *commoncontext.MantisContext, node *models.MarsCase, row []string) error {
	updateMap := map[string]any{
		"precondition": row[2],
		"priority":     row[3],
		"remark":       row[8],
		"gmt_modified": times.Now(),
		"modifier":     ctx.User.AdAccount,
	}

	// 处理测试步骤
	steps, err := s.parseSteps(row)
	if err != nil {
		return err
	}
	updateMap["steps"] = steps

	_, err = gormx.UpdateBatchByParamBuilderAndMap(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCase{}).Eq("id", node.Id),
		updateMap,
	)
	return err
}

// setCaseFields 设置用例字段
func (s ExcelService) setCaseFields(node *models.MarsCase, row []string, rowIndex int) error {
	node.PreCondition = row[2]
	node.Priority = row[3]
	node.Remark = row[8]

	steps, err := s.parseSteps(row)
	if err != nil {
		return fmt.Errorf("解析第%d行失败: %s", rowIndex, err.Error())
	}
	node.Steps = steps
	return nil
}

// parseSteps 解析测试步骤
func (s ExcelService) parseSteps(row []string) (*models.MarsCaseSteps, error) {
	switch row[5] {
	case "步骤描述":
		steps := &models.MarsCaseSteps{
			Type:  "step",
			Steps: make([]models.MarsCaseStep, 0),
		}
		if row[6] == "" {
			return steps, nil
		}
		descriptions := strings.Split(row[6], "\n")
		expectations := strings.Split(row[7], "\n")
		for i, description := range descriptions {
			if len(description) < 4 {
				return nil, fmt.Errorf("不合法的测试描述内容: %s", row[6])
			}
			currentStep := models.MarsCaseStep{
				Description: description[3:],
			}
			if i < len(expectations) {
				expectation := expectations[i]
				if len(expectation) < 3 {
					return nil, fmt.Errorf("不合法的测试预期: %s", row[7])
				}
				if len(expectation) == 3 {
					currentStep.ExpectedResults = ""
				} else {
					currentStep.ExpectedResults = expectation[3:]
				}
			}
			steps.Steps = append(steps.Steps, currentStep)
		}
		return steps, nil
	case "文本描述":
		steps := &models.MarsCaseSteps{
			Type: "text",
			Text: &models.MarsCaseStepText{
				Description:     row[6],
				ExpectedResults: row[7],
			},
		}
		return steps, nil
	default:
		return nil, fmt.Errorf("不支持的测试描述类型: %s", row[5])
	}
}

// parseDemandCodes 解析需求编号
func (s ExcelService) parseDemandCodes(demandStr string) []string {
	lines := strings.Split(demandStr, "\n")
	codes := make([]string, 0)
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			// 提取[数字]后面的内容
			if idx := strings.Index(line, "]"); idx != -1 && idx < len(line)-1 {
				code := strings.TrimSpace(line[idx+1:])
				if code != "" {
					codes = append(codes, code)
				}
			} else {
				codes = append(codes, line)
			}
		}
	}
	return codes
}

// processCaseRelations 处理用例需求关联
func (s ExcelService) processCaseRelations(ctx *commoncontext.MantisContext, caseRelations map[int64][]string) error {
	if len(caseRelations) == 0 {
		return nil
	}

	// 根据code获取需求id
	codes := make([]string, 0)
	for _, demandCodes := range caseRelations {
		codes = append(codes, demandCodes...)
	}
	demands, _, err := project.ProjectRemoteApi.GetDemands(ctx, dto.ProjectCommonSelectReq{Codes: codes})
	if err != nil {
		return err
	}
	demandIdMap := make(map[string]int64)
	for _, demand := range demands {
		demandIdMap[demand.Code] = demand.Id
	}
	// 为每个用例创建需求关联（简化处理，直接存储需求编号作为关联ID）
	for caseId, demandCodes := range caseRelations {
		// 先删除已有关联
		_, err := gormx.UpdateBatchByParamBuilderAndMap(ctx,
			gormx.NewParamBuilder().Model(&models.MarsCaseRelation{}).Eq("case_id", caseId),
			map[string]any{"is_deleted": commonconstants.DeleteYes, "gmt_modified": times.Now(), "modifier": ctx.User.AdAccount},
		)
		if err != nil {
			return err
		}

		// 创建新关联（这里简化处理，需求编号直接作为关联ID存储）
		relations := make([]models.MarsCaseRelation, 0)
		for _, code := range demandCodes {
			if strings.TrimSpace(code) != "" {
				relation := models.MarsCaseRelation{
					Addons:     commonmodels.NewAddonsWithUser(ctx.User),
					CaseId:     caseId,
					RelationId: strconv.FormatInt(demandIdMap[code], 10),
				}
				relations = append(relations, relation)
			}
		}

		if len(relations) > 0 {
			_, err = gormx.InsertBatch(ctx, relations)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func (s ExcelService) Export(ctx *commoncontext.MantisContext, parentId int64) ([]byte, error) {
	// 构建从库根节点开始的路径映射和用例列表
	folderMap, caseList, err := s.buildFolderMapFromRoot(ctx, parentId)
	if err != nil {
		return nil, err
	}
	eu, err := excel.NewExcelUtil("用例导出")
	if err != nil {
		return nil, err
	}
	defer eu.Close()

	// 记录每列的最大文本长度，用于自动调整列宽
	columnWidths := make([]int, len(expectedHeads))

	// 计算文本显示长度的辅助函数
	calculateTextWidth := func(text string) int {
		length := 0
		for _, r := range text {
			// 中文字符按2个字符宽度计算，英文按1个字符宽度计算
			if r > 127 {
				length += 2
			} else {
				length += 1
			}
		}
		return length
	}

	// 写入表头并计算表头列宽
	eu.AppendRow(expectedHeads)
	for i, head := range expectedHeads {
		width := calculateTextWidth(head)
		if width > columnWidths[i] {
			columnWidths[i] = width
		}
	}

	// 设置下拉框选项
	eu.SetDropdownList("D", []string{"P0", "P1", "P2", "P3"}, true)
	eu.SetDropdownList("F", []string{"步骤描述", "文本描述"}, true)
	// 设置可换行
	wrapStyle := &excelize.Style{
		Alignment: &excelize.Alignment{
			Vertical: "center",
			WrapText: true,
		},
	}
	// 写入用例
	for i, c := range caseList {
		row := i + 2
		folderName := folderMap[c.ParentId]

		// 写入数据并更新列宽
		rowData := []string{
			folderName,
			c.Name,
			c.PreCondition,
			c.Priority,
			"", // 关联需求，后面单独处理
			"", // 测试描述类型，后面单独处理
			"", // 测试描述内容，后面单独处理
			"", // 测试预期，后面单独处理
			c.Remark,
		}

		// 处理关联需求
		demands := ""
		for i, demandCode := range c.DemandCodes {
			demands += fmt.Sprintf("[%d]%s\n", i+1, demandCode)
		}
		if len(demands) != 0 {
			demands = demands[:len(demands)-1]
		}
		rowData[4] = demands

		// 处理测试描述
		if c.Steps.Type == "step" {
			rowData[5] = "步骤描述"
			steps := ""
			expectedResults := ""
			for i, step := range c.Steps.Steps {
				steps += fmt.Sprintf("[%d]%s\n", i+1, step.Description)
				expectedResults += fmt.Sprintf("[%d]%s\n", i+1, step.ExpectedResults)
			}
			if len(steps) != 0 {
				steps = steps[:len(steps)-1]
			}
			if len(expectedResults) != 0 {
				expectedResults = expectedResults[:len(expectedResults)-1]
			}
			rowData[6] = steps
			rowData[7] = expectedResults
		} else {
			rowData[5] = "文本描述"
			rowData[6] = c.Steps.Text.Description
			rowData[7] = c.Steps.Text.ExpectedResults
		}

		// 更新列宽统计
		for j, cellValue := range rowData {
			if j < len(columnWidths) {
				width := calculateTextWidth(cellValue)
				if width > columnWidths[j] {
					columnWidths[j] = width
				}
			}
		}

		// 写入单元格
		eu.WriteCell(fmt.Sprintf("A%d", row), folderName)
		eu.WriteCell(fmt.Sprintf("B%d", row), c.Name)
		eu.WriteCell(fmt.Sprintf("C%d", row), c.PreCondition)
		eu.WriteCell(fmt.Sprintf("D%d", row), c.Priority)
		eu.WriteCellWithStyle(fmt.Sprintf("E%d", row), demands, wrapStyle)
		eu.WriteCell(fmt.Sprintf("F%d", row), rowData[5])
		eu.WriteCellWithStyle(fmt.Sprintf("G%d", row), rowData[6], wrapStyle)
		eu.WriteCellWithStyle(fmt.Sprintf("H%d", row), rowData[7], wrapStyle)
		eu.WriteCell(fmt.Sprintf("I%d", row), c.Remark)
	}

	// 根据内容长度自动设置列宽
	columns := []string{"A", "B", "C", "D", "E", "F", "G", "H", "I"}
	for i, col := range columns {
		if i < len(columnWidths) {
			// 设置最小列宽为10
			width := float64(columnWidths[i])
			if width < 10 {
				width = 10
			}
			eu.SetColumnWidth(col, col, width)
		}
	}

	return eu.GetFileStream()
}

// buildFolderMapFromRoot 构建从库根节点开始的路径映射和用例列表
func (s ExcelService) buildFolderMapFromRoot(ctx *commoncontext.MantisContext, parentId int64) (map[int64]string, []*models.MarsCase, error) {
	// 首先获取目标节点信息
	targetNode := models.MarsCase{}
	targetNode.Id = parentId
	targetNode.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &targetNode)
	if err != nil {
		return nil, nil, err
	}

	// 获取库的根节点
	var rootId int64
	err = gormx.Raw(ctx, "select id from mars_case where library_id = ? and parent_id = 0 and is_deleted='N'", &rootId, targetNode.LibraryId)
	if err != nil {
		return nil, nil, err
	}

	// 构建从根节点到目标节点的路径
	pathNodeIds, err := s.getPathFromRootToTarget(ctx, rootId, parentId)
	if err != nil {
		return nil, nil, err
	}

	// 查询路径上的所有节点
	pathNodes := make([]models.MarsCase, 0)
	if len(pathNodeIds) > 0 {
		err = gormx.SelectByParamBuilder(ctx,
			gormx.NewParamBuilder().Model(&models.MarsCase{}).In("id", pathNodeIds).Eq("is_deleted", commonconstants.DeleteNo),
			&pathNodes,
		)
		if err != nil {
			return nil, nil, err
		}
	}

	// 查询parentId下的完整子树
	var targetSubTree []models.MarsCase
	sql := `with recursive sub as (
		select * from mars_case where id = ? and is_deleted = 'N'
		union
		select e.* from (select * from mars_case where is_deleted = 'N') e
		inner join sub s on s.id = e.parent_id
	) select * from sub`
	err = gormx.Raw(ctx, sql, &targetSubTree, parentId)
	if err != nil {
		return nil, nil, err
	}

	// 合并所有节点
	nodeMap := make(map[int64]*models.MarsCase)
	allNodeIds := make([]int64, 0)

	// 添加路径节点
	for _, node := range pathNodes {
		nodeMap[node.Id] = &node
		allNodeIds = append(allNodeIds, node.Id)
	}

	// 添加目标子树节点
	for _, node := range targetSubTree {
		if _, exists := nodeMap[node.Id]; !exists {
			nodeMap[node.Id] = &node
			allNodeIds = append(allNodeIds, node.Id)
		}
	}

	// 查询需求关联信息
	relations := make([]models.MarsCaseRelation, 0)
	if len(allNodeIds) > 0 {
		err = gormx.SelectByParamBuilder(ctx,
			gormx.NewParamBuilder().Model(&models.MarsCaseRelation{}).In("case_id", allNodeIds).Eq("is_deleted", commonconstants.DeleteNo),
			&relations,
		)
		if err != nil {
			return nil, nil, err
		}
	}

	demandIds := make([]string, 0)
	for _, relation := range relations {
		demandIds = append(demandIds, relation.RelationId)
	}

	if len(demandIds) != 0 {
		demands, _, err := project.ProjectRemoteApi.GetDemands(ctx, dto.ProjectCommonSelectReq{Ids: demandIds})
		if err != nil {
			return nil, nil, err
		}
		demandCodeMap := make(map[string]string)
		for _, demand := range demands {
			demandCodeMap[strconv.FormatInt(demand.Id, 10)] = demand.Code
		}
		for _, relation := range relations {
			node := nodeMap[relation.CaseId]
			if node != nil {
				if node.DemandCodes == nil {
					node.DemandCodes = make([]string, 0)
				}
				node.DemandCodes = append(node.DemandCodes, demandCodeMap[relation.RelationId])
			}
		}
	}

	// 构建父子关系映射
	parentChildMap := make(map[int64][]int64)
	for _, node := range nodeMap {
		if node.ParentId != 0 {
			parentChildMap[node.ParentId] = append(parentChildMap[node.ParentId], node.Id)
		}
	}

	// 构建路径映射
	folderMap := make(map[int64]string)
	var buildPath func(nodeId int64) string
	buildPath = func(nodeId int64) string {
		if path, exists := folderMap[nodeId]; exists {
			return path
		}

		node := nodeMap[nodeId]
		if node == nil {
			return ""
		}

		if node.ParentId == 0 {
			// 根节点，路径为空
			folderMap[nodeId] = ""
			return ""
		}

		parentPath := buildPath(node.ParentId)
		var currentPath string
		if parentPath == "" {
			currentPath = node.Name
		} else {
			currentPath = parentPath + "/" + node.Name
		}
		folderMap[nodeId] = currentPath
		return currentPath
	}

	// 为所有节点构建路径
	for nodeId := range nodeMap {
		buildPath(nodeId)
	}

	// 收集所有用例节点
	caseList := make([]*models.MarsCase, 0)
	for _, node := range nodeMap {
		if node.NodeType == enums.CaseNode.Value {
			caseList = append(caseList, node)
		}
	}

	// 按照路径和名称排序用例
	sort.Slice(caseList, func(i, j int) bool {
		pathI := folderMap[caseList[i].ParentId]
		pathJ := folderMap[caseList[j].ParentId]
		if pathI == pathJ {
			return caseList[i].Name < caseList[j].Name
		}
		return pathI < pathJ
	})

	return folderMap, caseList, nil
}

// getPathFromRootToTarget 获取从根节点到目标节点的路径（包括所有祖先节点）
func (s ExcelService) getPathFromRootToTarget(ctx *commoncontext.MantisContext, rootId, targetId int64) ([]int64, error) {
	if rootId == targetId {
		return []int64{rootId}, nil
	}

	// 使用递归查询找到从目标节点到根节点的路径
	sql := `
	WITH RECURSIVE path_to_root AS (
		-- 基础案例：从目标节点开始
		SELECT id, parent_id, 0 as level
		FROM mars_case 
		WHERE id = ? AND is_deleted = 'N'
		
		UNION ALL
		
		-- 递归案例：向上查找父节点
		SELECT p.id, p.parent_id, ptr.level + 1
		FROM mars_case p
		INNER JOIN path_to_root ptr ON p.id = ptr.parent_id
		WHERE p.is_deleted = 'N'
	)
	SELECT id FROM path_to_root 
	ORDER BY level DESC  -- 从根节点到目标节点的顺序
	`

	var pathIds []int64
	err := gormx.Raw(ctx, sql, &pathIds, targetId)
	if err != nil {
		return nil, err
	}

	return pathIds, nil
}
