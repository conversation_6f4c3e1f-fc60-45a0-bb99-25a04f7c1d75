package service

import (
	"cmp"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"slices"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto/project"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto/report"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto/ui"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/remote"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/openapi"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	commonmodels "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	set "github.com/duke-git/lancet/v2/datastructure/set"
	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
)

type TestPlanService struct{}

var (
	aptRemoteApi                remote.AptRemoteApi
	marsBugDao                  dao.MarsBugDao
	uiPlanOpenApi               openapi.UiPLanOpenapi
	marsTestPlanCaseRelationDao dao.MarsTestPlanCaseRelationDao
	marsCaseRelationDao         dao.MarsCaseRelationDao
	bugService                  BugService
)

func (s TestPlanService) GetTestPlanList(ctx *commoncontext.MantisContext, req dto.MarsTestPlanListReq, pageReq gormx.PageRequest,
	spaceId string,
) (*gormx.PageResult, error) {
	sql := "select * from mars_test_plan where space_id=? and is_deleted='N'"
	params := []any{spaceId}
	if req.PlanType != "" {
		sql += " and plan_type=?"
		params = append(params, req.PlanType)
	}
	if req.Owner != "" {
		// (owner @> '["admin"]' OR owner @> '["x"]')
		temSql := ""
		for _, owner := range strings.Split(req.Owner, ",") {
			temSql += " or owner @> '[\"" + owner + "\"]'"
		}
		temSql = strings.Replace(temSql, "or", "", 1)
		sql += fmt.Sprintf("and (%s)", temSql)
	}
	if req.BeginTime != "" {
		sql += " and begin_time >= ? and begin_time <= ?"
		params = append(params, times.GetDataFromCommaString(req.BeginTime, 0), times.GetDataFromCommaString(req.BeginTime, 1))
	}
	if req.EndTime != "" {
		sql += " and end_time >= ? and end_time <= ?"
		params = append(params, times.GetDataFromCommaString(req.EndTime, 0), times.GetDataFromCommaString(req.EndTime, 1))
	}
	if req.Status != "" {
		sql += " and status in (?)"
		params = append(params, strings.Split(req.Status, ","))
	}
	if req.Stage != "" {
		sql += " and stage in (?)"
		params = append(params, strings.Split(req.Stage, ","))
	}
	if req.Creator != "" {
		sql += " and creator in (?)"
		params = append(params, req.Creator)
	}
	if req.Iteration != "" {
		sql += " and related_iteration = ?"
		params = append(params, req.Iteration)
	}
	if req.Name != "" {
		sql += " and name like ?"
		params = append(params, "%"+req.Name+"%")
	}
	sql += " order by gmt_modified desc"
	testPlans := make([]models.MarsTestPlanDTO, 0)
	res, err := gormx.PageSelectByRaw(ctx, sql, &testPlans, pageReq, params...)
	if err != nil {
		return nil, err
	}
	iterationIds := set.New[string]()
	testPlanIds := make([]int64, 0)
	for _, testPlan := range testPlans {
		if testPlan.RelatedIteration != "" {
			iterationIds.Add(testPlan.RelatedIteration)
		}
		testPlanIds = append(testPlanIds, testPlan.Id)
	}
	if len(iterationIds) > 0 {
		projectId := ctx.Header.Get("spaceid")
		iterations, err := projectService.GetIterationList(ctx,
			dto.ProjectCommonSelectReq{Ids: iterationIds.ToSlice(), ProjectId: projectId},
			gormx.PageRequest{Page: 1, PageSize: 999})
		if err != nil {
			return nil, err
		}
		iterationMap := make(map[string]models.ProjectItemDTO)
		for _, ite := range iterations {
			iterationMap[strconv.FormatInt(ite.Id, 10)] = ite
		}
		for i, plan := range testPlans {
			it, ok := iterationMap[plan.RelatedIteration]
			if ok {
				testPlans[i].Iteration = &it
			}
		}
	}
	if len(testPlanIds) > 0 {
		tasks := make([]func(), len(testPlanIds))
		var rateMap sync.Map
		for i, id := range testPlanIds {
			tasks[i] = func() {
				rateDto, err := s.GetPlanPassRate(ctx, id)
				if err != nil {
					logger.Logger.Error(err)
					return
				}
				rateMap.Store(id, *rateDto)
			}
		}
		err := goroutine.RunTasks(tasks)
		if err != nil {
			return nil, err
		}
		for i, plan := range testPlans {
			r, ok := rateMap.Load(plan.Id)
			if ok {
				testPlans[i].PassRate = r.(report.MaraTestPlanPassRateDTO).PassRate
			}
		}
	}
	return res, nil
}

func (TestPlanService) Insert(ctx *commoncontext.MantisContext, plan models.MarsTestPlanDTO) (*models.MarsTestPlan, error) {
	if !constants.CheckTestPlanType(plan.PlanType) {
		return nil, errors.New("type illegal, must in [common,demand]")
	}
	projectId := ctx.Header.Get("spaceid")
	plan.Addons = commonmodels.NewAddonsWithUser(ctx.User)
	plan.Model = []string{constants.TestPlanRelationTypeCase}
	plan.Status = constants.TestPLanStatusNotStart
	plan.SpaceId = projectId
	if plan.Iteration != nil {
		plan.RelatedIteration = strconv.FormatInt(plan.Iteration.Id, 10)
	}
	_, err := gormx.InsertOne(ctx, &plan)
	if err != nil {
		return nil, err
	}
	if plan.IsAllDemand {
		demands, _, err := projectService.GetDemandsByPage(ctx, dto.ProjectCommonSelectReq{
			ProjectId:   projectId,
			IterationId: plan.RelatedIteration,
		}, gormx.PageRequest{Page: 1, PageSize: math.MaxInt32})
		if err != nil {
			return nil, err
		}
		demandRelation := make([]models.MarsTestPlanDemandRelation, 0)
		demandIds := make([]string, len(demands))
		for i, d := range demands {
			demandRelation = append(demandRelation, models.MarsTestPlanDemandRelation{
				Addons:     commonmodels.NewAddonsWithUser(ctx.User),
				TestPlanId: plan.Id,
				DemandId:   strconv.FormatInt(d.Id, 10),
				Type:       constants.TestPlanRelationTypeCase,
			})
			demandIds[i] = strconv.FormatInt(d.Id, 10)
		}
		// 获取需求关联的用例
		caseRelations := make([]models.MarsCaseRelation, 0)
		err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MarsCaseRelation{}).
			In("relation_id", demandIds).Eq("is_deleted", commonconstants.DeleteNo),
			&caseRelations)
		if err != nil {
			return nil, err
		}
		marsCaseTestPlanRelations := make([]models.MarsTestPlanCaseRelation, 0)
		execHisList := make([]models.MarsTestPlanCaseExecHistory, 0)
		for i, caseRelation := range caseRelations {
			rel, execHis := initTestPlanCaseRelationAndExecHis(plan.Id, int64(i+1), caseRelation.RelationId,
				strconv.FormatInt(caseRelation.CaseId, 10), ctx.User)
			execHisList = append(execHisList, execHis)
			marsCaseTestPlanRelations = append(marsCaseTestPlanRelations, rel)
		}
		if len(demandRelation) > 0 {
			_, err = gormx.InsertBatch(ctx, &demandRelation)
			if err != nil {
				return nil, err
			}
		}
		if len(marsCaseTestPlanRelations) > 0 {
			_, err = gormx.InsertBatch(ctx, &marsCaseTestPlanRelations)
			if err != nil {
				return nil, err
			}
		}
		if len(execHisList) > 0 {
			_, err = gormx.InsertBatch(ctx, &execHisList)
			if err != nil {
				return nil, err
			}

		}
	}
	return &plan.MarsTestPlan, nil
}

// Copy 复制测试计划
func (TestPlanService) Copy(ctx *commoncontext.MantisContext, id int64) (int64, error) {
	testPlan := models.MarsTestPlan{}
	testPlan.Id = id
	err := gormx.SelectOneByCondition(ctx, &testPlan)
	if err != nil {
		return 0, err
	}
	testPlan.Addons = commonmodels.NewAddonsWithUser(ctx.User)
	_, err = gormx.InsertOne(ctx, &testPlan)
	if err != nil {
		return 0, err
	}
	// 如果是需求测试计划，就复制需求
	if testPlan.PlanType == constants.TestPLanTypeDemand {
		demandRels := make([]models.MarsTestPlanDemandRelation, 0)
		err = gormx.SelectByParamBuilder(ctx,
			gormx.NewParamBuilder().Model(&models.MarsTestPlanDemandRelation{}).Eq("test_plan_id", id).Eq("is_deleted", commonconstants.DeleteNo),
			&demandRels)
		if err != nil {
			return 0, err
		}
		for i := range demandRels {
			demandRels[i].Addons = commonmodels.NewAddonsWithUser(ctx.User)
			demandRels[i].TestPlanId = testPlan.Id
		}
		if len(demandRels) > 0 {
			_, err = gormx.InsertBatch(ctx, &demandRels)
			if err != nil {
				return 0, err
			}
		}
	}
	// 复制用例关联关系
	caseRelations := make([]models.MarsTestPlanCaseRelation, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseRelation{}).Eq("test_plan_id", id).Eq("is_deleted", commonconstants.DeleteNo),
		&caseRelations)
	if err != nil {
		return 0, err
	}
	execHisList := make([]models.MarsTestPlanCaseExecHistory, 0)
	for i, rel := range caseRelations {
		caseRelations[i].Addons = commonmodels.NewAddonsWithUser(ctx.User)
		caseRelations[i].TestPlanId = testPlan.Id
		hisId := snowflake.GenSnowFlakeId()
		caseRelations[i].LastExecId = hisId
		if rel.Type == constants.TestPlanRelationTypeCase {
			// 功能用例需要初始化执行结果
			his := models.MarsTestPlanCaseExecHistory{
				Addons:        commonmodels.NewAddonsWithUser(ctx.User),
				TestPlanId:    testPlan.Id,
				CaseId:        rel.RelationId,
				Type:          constants.TestPlanRelationTypeCase,
				ExecuteResult: constants.CaseResultTypeUntest,
			}
			his.Id = hisId
			execHisList = append(execHisList, his)
		} else {
			caseRelations[i].LastExecId = 0
		}
	}
	if len(caseRelations) > 0 {
		_, err = gormx.InsertBatch(ctx, &caseRelations)
		if err != nil {
			return 0, err
		}
	}
	if len(execHisList) > 0 {
		_, err = gormx.InsertBatch(ctx, &execHisList)
		if err != nil {
			return 0, err
		}
	}
	return testPlan.Id, err
}

func (TestPlanService) GetPlanDemand(ctx *commoncontext.MantisContext, planId int64, req dto.MarsDemandReq) (*gormx.PageResult, error) {
	// 查询 tm 迭代下的需求
	testPlan := models.MarsTestPlan{}
	testPlan.Id = planId
	testPlan.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &testPlan)
	if err != nil {
		return nil, err
	}
	projectId := ctx.Header.Get("spaceid")
	tmReq := dto.ProjectCommonSelectReq{
		ProjectId:   projectId,
		IterationId: testPlan.RelatedIteration,
	}
	if req.Search != "" {
		tmReq.Search = req.Search
	}
	if req.Code != "" {
		tmReq.Code = req.Code
	}
	if req.PriorityLevel != 0 {
		tmReq.PriorityLevel = []int64{req.PriorityLevel}
	}
	tmDemands, total, err := projectService.GetDemandsByPage(ctx, tmReq, gormx.PageRequest{Page: req.Page, PageSize: req.PageSize})
	if err != nil {
		return nil, err
	}
	demandIds := make([]string, len(tmDemands))
	for i, d := range tmDemands {
		demandIds[i] = strconv.FormatInt(d.Id, 10)
	}
	// 功能用例中需求用用例的用射
	existDemandCaseMap := make(map[string][]models.MarsCaseRelationExpand)
	caseExpandList, err := marsCaseRelationDao.GetCaseRelationExpand(ctx, demandIds, constants.TestPLanTypeDemand)
	if err != nil {
		return nil, err
	}
	for _, c := range caseExpandList {
		v, ok := existDemandCaseMap[c.RelationId]
		if ok {
			existDemandCaseMap[c.RelationId] = append(v, c)
		} else {
			existDemandCaseMap[c.RelationId] = []models.MarsCaseRelationExpand{c}
		}
	}
	// 获取所有需求关联的用例
	existDemandIds := make([]string, 0)
	err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MarsTestPlanDemandRelation{}).Select("demand_id").
		Eq("test_plan_id", planId).Eq("is_deleted", commonconstants.DeleteNo).
		Eq("type", req.Model), &existDemandIds)
	if err != nil {
		return nil, err
	}
	// 测试计划中需求与用例id的映射
	testPlanDemandCaseMap := make(map[string][]string)
	if len(existDemandIds) > 0 {
		// 获取计划已经关联的用例
		testPlanDemandCase := make([]models.MarsTestPlanCaseRelation, 0)
		err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseRelation{}).
			Eq("test_plan_id", planId).Eq("type", req.Model).
			In("demand_id", existDemandIds).
			Eq("is_deleted", commonconstants.DeleteNo), &testPlanDemandCase)
		if err != nil {
			return nil, err
		}
		for _, c := range testPlanDemandCase {
			v, ok := testPlanDemandCaseMap[c.DemandId]
			if ok {
				testPlanDemandCaseMap[c.DemandId] = append(v, c.RelationId)
			} else {
				testPlanDemandCaseMap[c.DemandId] = []string{c.RelationId}
			}
		}
	}
	res := make([]ui.MarsUiDemandExpand, len(tmDemands))
	for i, demand := range tmDemands {
		caseList, ok := existDemandCaseMap[strconv.FormatInt(demand.Id, 10)]
		planCaseList, planCaseOk := testPlanDemandCaseMap[strconv.FormatInt(demand.Id, 10)]
		totalCase := make([]ui.SelectCase, 0)
		selectedCase := make([]ui.SelectCase, 0)
		if ok {
			// 有用例
			for _, c := range caseList {
				sc := ui.SelectCase{
					CaseId:   strconv.FormatInt(c.CaseId, 10),
					CaseName: c.Name,
					Priority: c.Priority,
				}
				totalCase = append(totalCase, sc)
				// 如果计划下的需求有用例，并且包含此用例
				if planCaseOk && slices.Contains(planCaseList, strconv.FormatInt(c.CaseId, 10)) {
					selectedCase = append(selectedCase, sc)
				}
			}
		}
		d := ui.MarsUiDemandExpand{
			ProjectDemandDTO: demand,
			Selected:         planCaseOk,
			TotalCase:        totalCase,
			SelectedCase:     selectedCase,
		}
		res[i] = d
	}
	return &gormx.PageResult{
		Total:       total,
		PageSize:    req.PageSize,
		CurrentPage: req.Page,
		List:        res,
		Pages:       total/req.PageSize + 1,
	}, nil
}

// SyncDemand 同步需求及需求下的用例
func (TestPlanService) SyncDemand(ctx *commoncontext.MantisContext, id int64, demandIds []string) error {
	plan, err := checkPlanExist(ctx, id)
	if err != nil {
		return err
	}
	if plan.PlanType != constants.TestPLanTypeDemand {
		return errors.New("非需求测试计划,不允许同步需求")
	}
	if len(demandIds) == 0 {
		return errors.New("请选择需求")
	}
	// 判断需求下是否新增了用例
	caseRel := make([]models.MarsCaseRelation, 0)
	err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MarsCaseRelation{}).
		In("relation_id", demandIds).Eq("is_deleted", commonconstants.DeleteNo).
		Eq("relation_type", constants.TestPLanTypeDemand),
		&caseRel)
	if err != nil {
		return err
	}
	testPlanCaseRel := make([]models.MarsTestPlanCaseRelation, 0)
	err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseRelation{}).
		In("demand_id", demandIds).Eq("test_plan_id", id).Eq("is_deleted", commonconstants.DeleteNo).
		Eq("type", constants.TestPlanRelationTypeCase),
		&testPlanCaseRel)
	if err != nil {
		return err
	}
	// 需求与用例的映射
	demandCaseMap := make(map[string][]string)
	for _, rel := range caseRel {
		list, ok := demandCaseMap[rel.RelationId]
		if ok {
			demandCaseMap[rel.RelationId] = append(list, strconv.FormatInt(rel.CaseId, 10))
		} else {
			demandCaseMap[rel.RelationId] = []string{strconv.FormatInt(rel.CaseId, 10)}
		}
	}
	// 测试计划下需求与用例的映射
	testPlanDemandCaseMap := make(map[string][]string)
	for _, rel := range testPlanCaseRel {
		list, ok := testPlanDemandCaseMap[rel.DemandId]
		if ok {
			testPlanDemandCaseMap[rel.DemandId] = append(list, rel.RelationId)
		} else {
			testPlanDemandCaseMap[rel.DemandId] = []string{rel.RelationId}
		}
	}
	// 将需求下的新增用例筛选出来
	addMarsCaseTestPlanRelations := make([]models.MarsTestPlanCaseRelation, 0)
	addExecHisList := make([]models.MarsTestPlanCaseExecHistory, 0)
	for k, v := range demandCaseMap {
		existCaseList, ok := testPlanDemandCaseMap[k]
		if !ok {
			for i, caseId := range v {
				// 新增的用例
				rel, execHis := initTestPlanCaseRelationAndExecHis(plan.Id, int64(i+1), k, caseId, ctx.User)
				addExecHisList = append(addExecHisList, execHis)
				addMarsCaseTestPlanRelations = append(addMarsCaseTestPlanRelations, rel)
			}
		} else {
			// 获取最大的 sort
			maxSort := int64(0)
			for i, caseId := range v {
				if !slices.Contains(existCaseList, caseId) {
					if maxSort < 1 {
						existSort, err := marsTestPlanCaseRelationDao.SelectMaxSortByTestPlanId(ctx, plan.Id, constants.TestPlanRelationTypeCase, k)
						if err != nil {
							return err
						}
						maxSort = maxSort + existSort + 1
					}
					rel, execHis := initTestPlanCaseRelationAndExecHis(plan.Id, maxSort+int64(i), k, caseId, ctx.User)
					addExecHisList = append(addExecHisList, execHis)
					addMarsCaseTestPlanRelations = append(addMarsCaseTestPlanRelations, rel)
				}
			}
		}
	}
	if len(addMarsCaseTestPlanRelations) > 0 {
		logger.Logger.Infof("测试计划 %d 新增 %d 个用例关联关系", id, len(addMarsCaseTestPlanRelations))
		_, err := gormx.InsertBatch(ctx, &addMarsCaseTestPlanRelations)
		if err != nil {
			return err
		}
	}
	if len(addExecHisList) > 0 {
		logger.Logger.Infof("测试计划 %d 新增 %d 个用例关联关系的执行记录", id, len(addExecHisList))
		_, err := gormx.InsertBatch(ctx, &addExecHisList)
		if err != nil {
			return err
		}
	}
	return nil
}

// 初始化功能用例和执行历史
func initTestPlanCaseRelationAndExecHis(planId, sort int64, demandId string, caseId string, user commondto.UserInfo) (models.MarsTestPlanCaseRelation, models.MarsTestPlanCaseExecHistory) {
	hisId := snowflake.GenSnowFlakeId()
	caseRel := models.MarsTestPlanCaseRelation{
		Addons:     commonmodels.NewAddonsWithUser(user),
		TestPlanId: planId,
		DemandId:   demandId,
		RelationId: caseId,
		Sort:       sort,
		Type:       constants.TestPlanRelationTypeCase,
		LastExecId: hisId,
	}
	his := models.MarsTestPlanCaseExecHistory{
		Addons:        commonmodels.NewAddonsWithUser(user),
		TestPlanId:    planId,
		CaseId:        caseId,
		Type:          constants.TestPlanRelationTypeCase,
		ExecuteResult: constants.CaseResultTypeUntest,
	}
	his.Id = hisId
	return caseRel, his
}

func (s TestPlanService) Info(ctx *commoncontext.MantisContext, id int64) (*models.MarsTestPlanDTO, error) {
	projectId := ctx.Header.Get("spaceid")
	plan := models.MarsTestPlanDTO{}
	plan.Id = id
	plan.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &plan)
	if err != nil {
		return nil, err
	}
	// 需求测试计划需要查出迭代信息
	if plan.PlanType == constants.TestPLanTypeDemand && plan.RelatedIteration != "" {
		iterations, err := projectService.GetIterationList(ctx,
			dto.ProjectCommonSelectReq{Ids: []string{plan.RelatedIteration}, ProjectId: projectId},
			gormx.PageRequest{Page: 1, PageSize: math.MaxInt32})
		if err != nil {
			return nil, err
		}
		if len(iterations) != 0 {
			plan.Iteration = &iterations[0]
		}
	}
	rate, err := s.GetPlanPassRate(ctx, plan.Id)
	if err != nil {
		return nil, err
	}
	plan.PassRate = rate.PassRate
	plan.CaseRateDto = rate
	return &plan, nil
}

func (s TestPlanService) Delete(ctx *commoncontext.MantisContext, id int64) error {
	// 删除主表
	plan := models.MarsTestPlan{}
	plan.Id = id
	plan.IsDeleted = commonconstants.DeleteYes
	_, err := gormx.UpdateOneByCondition(ctx, &plan)
	if err != nil {
		return err
	}
	// 异步移除关联关系
	goroutine.Run(func() {
		updateMap := map[string]any{
			"is_deleted":   commonconstants.DeleteYes,
			"gmt_modified": times.Now(),
			"modifier":     ctx.User.AdAccount,
		}
		// 删除计划关联的用例
		caseRelCount := gormx.UpdateBatchByParamBuilderAndMapX(ctx, gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseRelation{}).
			Eq("test_plan_id", id).Eq("is_deleted", commonconstants.DeleteNo), updateMap)
		logger.Logger.Infof("测试计划 %d ,删除 %d 条计划关联的用例: ", id, caseRelCount)
		// 删除计划关联的需求
		demandRelCount := gormx.UpdateBatchByParamBuilderAndMapX(ctx, gormx.NewParamBuilder().Model(&models.MarsTestPlanDemandRelation{}).
			Eq("test_plan_id", id).Eq("is_deleted", commonconstants.DeleteNo), updateMap)
		logger.Logger.Infof("测试计划 %d ,删除 %d 条计划关联的用例: ", id, demandRelCount)
		// 删除计划关联的 bug
		bugRelCount := gormx.UpdateBatchByParamBuilderAndMapX(ctx, gormx.NewParamBuilder().Model(&models.MarsBug{}).
			Eq("test_plan_id", id).Eq("is_deleted", commonconstants.DeleteNo), updateMap)
		logger.Logger.Infof("测试计划 %d ,删除 %d 条计划关联的用例: ", id, bugRelCount)
	})
	return err
}

// UpdateStatus 修改测试计划状态
func (TestPlanService) UpdateStatus(ctx *commoncontext.MantisContext, id int64, status string) error {
	if !constants.CheckTestPlanStatus(status) {
		return errors.New("status illegal, must in [notStart, running, finished]")
	}
	plan := models.MarsTestPlan{}
	plan.Id = id
	plan.Status = status
	plan.Modifier = ctx.User.AdAccount
	plan.GmtModified = times.Now()
	_, err := gormx.UpdateOneByCondition(ctx, &plan)
	return err
}

func (TestPlanService) Update(ctx *commoncontext.MantisContext, plan models.MarsTestPlanDTO) error {
	plan.Modifier = ctx.User.AdAccount
	plan.GmtModified = times.Now()
	if plan.PlanType == constants.TestPLanTypeDemand && plan.Iteration != nil {
		plan.RelatedIteration = strconv.FormatInt(plan.Iteration.Id, 10)
	}
	_, err := gormx.UpdateOneByCondition(ctx, &plan)
	return err
}

func (s TestPlanService) RelateCase(ctx *commoncontext.MantisContext, req dto.MarsTestPlanRelateCaseReq) error {
	if req.DemandId == "" {
		req.DemandId = constants.DefaultDemandID
	}
	if len(req.Ids) < 1 {
		logger.Logger.Infof("测试计划 %d 关联用例为空", req.TestPlanId)
		return nil
	}
	// 关联mars用例本身
	maxCaseSort, err := marsCaseTestPlanRelationDao.SelectMaxSortByTestPlanId(ctx, req.TestPlanId, req.Type, req.DemandId)
	if err != nil {
		return err
	}
	// 去除重复的用例
	existRIds := make([]string, 0)
	err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseRelation{}).Select("relation_id").
		Eq("test_plan_id", req.TestPlanId).Eq("demand_id", req.DemandId).
		Eq("type", req.Type).Eq("is_deleted", commonconstants.DeleteNo), &existRIds)
	if err != nil {
		return err
	}
	notExistIds := make([]string, 0)
	if len(existRIds) > 0 {
		for _, id := range req.Ids {
			if !slices.Contains(existRIds, id) {
				notExistIds = append(notExistIds, id)
			}
		}
	} else {
		notExistIds = req.Ids
	}
	marsCaseTestPlanRelations := make([]models.MarsTestPlanCaseRelation, 0)
	for i, caseId := range notExistIds {
		marsCaseTestPlanRelations = append(marsCaseTestPlanRelations, models.MarsTestPlanCaseRelation{
			Addons:     commonmodels.NewAddonsWithUser(ctx.User),
			RelationId: caseId,
			TestPlanId: req.TestPlanId,
			Sort:       maxCaseSort + int64(i) + 1,
			Type:       req.Type,
			DemandId:   req.DemandId,
		})
	}
	execHisList := make([]models.MarsTestPlanCaseExecHistory, 0)
	// 如果关联的用例是功能用例，需要初始化执行结果
	if req.Type == constants.TestPlanRelationTypeCase {
		for i, caseRel := range marsCaseTestPlanRelations {
			hisId := snowflake.GenSnowFlakeId()
			marsCaseTestPlanRelations[i].LastExecId = hisId
			his := models.MarsTestPlanCaseExecHistory{
				Addons:        commonmodels.NewAddonsWithUser(ctx.User),
				TestPlanId:    req.TestPlanId,
				CaseId:        caseRel.RelationId,
				Type:          constants.TestPlanRelationTypeCase,
				ExecuteResult: constants.CaseResultTypeUntest,
			}
			his.Id = hisId
			execHisList = append(execHisList, his)
		}
	}
	if len(execHisList) > 0 {
		_, err = gormx.InsertBatch(ctx, execHisList)
		if err != nil {
			return err
		}
	}
	if len(marsCaseTestPlanRelations) > 0 {
		_, err = gormx.InsertBatch(ctx, marsCaseTestPlanRelations)
		if err != nil {
			return err
		}
	}
	return err
}

func (t TestPlanService) GetPlanPassRate(ctx *commoncontext.MantisContext, id int64) (*report.MaraTestPlanPassRateDTO, error) {
	testPlan := models.MarsTestPlan{}
	testPlan.Id = id
	testPlan.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &testPlan)
	if err != nil {
		return nil, err
	}
	if testPlan.Id == 0 {
		return nil, errors.New("计划已被删除,请刷新后重试")
	}
	res := report.MaraTestPlanPassRateDTO{}
	d := time.Until(testPlan.EndTime.ToGoTime())
	if d < 0 {
		res.RemainderTime = "已延期"
	} else {
		days := int64(d.Hours() / 24)
		hours := int64(d.Hours()) % 24
		minutes := int(d.Minutes()) % 60
		res.RemainderTime = fmt.Sprintf("%d天%d小时%d分钟", days, hours, minutes)
	}
	planModels := testPlan.Model
	logger.Logger.Infof("测试计划 %d, 只计算: %v 模块", id, planModels)
	// 统计 bug 数量
	bugCount, err := marsBugDao.CountBugsByTestPlanAndModels(ctx, id, planModels)
	if err != nil {
		logger.Logger.Errorf("测试计划 %d, 查询 bug 数量失败: %v", id, err)
		return nil, err
	}
	res.TotalBug = bugCount
	// 获取计划下指定模块的关联关系及对应的执行历史
	caseRels, err := marsCaseTestPlanRelationDao.SelectRowsAndExecHis(ctx, id, planModels)
	if err != nil {
		return nil, err
	}
	// 获取所有的 UI 计划,查出对应的用例数量
	relUiPlanIds := make([]string, 0)
	for _, v := range caseRels {
		if constants.TestPlanRelationTypeUI == v.Type {
			relUiPlanIds = append(relUiPlanIds, v.RelationId)
		}
	}
	uiPlanList, err := uiPlanOpenApi.PlanList(ctx, relUiPlanIds)
	if err != nil {
		logger.Logger.Errorf("mars 查询 UI 计划失败 %d, err: %v", id, err)
		return nil, err
	}
	uiCaseCount := make(map[string]int64)
	for _, plan := range uiPlanList {
		uiCaseCount[plan.Id] = plan.CaseCount
	}
	// 计算所有的数据
	for _, v := range caseRels {
		if v.Type == constants.TestPlanRelationTypeUI {
			setUICount(v, uiCaseCount, &res)
		} else {
			// 直接绑定的是用例
			setGenCount(v, &res)
		}
	}
	res.Cal()
	// 对没有数据的模块数值初始值
	if res.ModelRateMap == nil {
		res.ModelRateMap = make(map[string]*report.MaraTestPlanPassRateDTO)
	}
	for _, m := range testPlan.Model {
		if _, ok := res.ModelRateMap[m]; !ok {
			res.ModelRateMap[m] = &report.MaraTestPlanPassRateDTO{}
		}
		res.ModelRateMap[m].Cal()
	}
	return &res, nil
}

func setUICount(v dto.MarsTestPlanCaseRelationAndHis, uiCaseCount map[string]int64, res *report.MaraTestPlanPassRateDTO) {
	c, ok := uiCaseCount[v.RelationId]
	if !ok {
		// todo ui 计划已经被删除,所有数据不在计算
		return
	}
	res.TotalCase += c
	if v.LastExecId == 0 {
		setModel(v.Type, c, 0, map[string]int64{
			constants.CaseResultTypePass: 0,
		}, res)
		return
	}
	var uiPlanExecResult ui.MarsUiPlanExecResult
	err := json.Unmarshal([]byte(v.Result), &uiPlanExecResult)
	if err != nil {
		logger.Logger.Errorf("返序列会 ui 执行结果报错！result: %s,err: %v", v.Result, err)
		return
	}
	res.TotalPass += uiPlanExecResult.CasePassCount
	res.TotalFail += uiPlanExecResult.CaseFailCount
	res.TotalExecuteCase += uiPlanExecResult.CaseTotalCount
	setModel(v.Type, c, uiPlanExecResult.CaseTotalCount, map[string]int64{
		constants.CaseResultTypePass: uiPlanExecResult.CasePassCount,
	}, res)
}

func setGenCount(v dto.MarsTestPlanCaseRelationAndHis, res *report.MaraTestPlanPassRateDTO) {
	res.TotalCase++
	if v.LastExecId == 0 {
		setModel(v.Type, 1, 0, map[string]int64{
			constants.CaseResultTypePass: 0,
		}, res)
		return
	}
	exec := int64(1)
	addStatus := make(map[string]int64)
	switch v.ExecuteResult {
	case constants.CaseResultTypeUntest:
		exec = 0
	case constants.CaseResultTypePass:
		addStatus[constants.CaseResultTypePass] = 1
		res.TotalPass++
		res.TotalExecuteCase++
	case constants.CaseResultTypeFail:
		addStatus[constants.CaseResultTypeFail] = 1
		res.TotalFail++
		res.TotalExecuteCase++
	case constants.CaseResultTypeBlock:
		addStatus[constants.CaseResultTypeBlock] = 1
		res.TotalBlock++
		res.TotalExecuteCase++
	case constants.CaseResultTypeAgain:
		addStatus[constants.CaseResultTypeAgain] = 1
		res.TotalAgain++
		res.TotalExecuteCase++
	}
	setModel(v.Type, 1, exec, addStatus, res)
}

func setModel(modelType string, addTotal, addExec int64, addStatus map[string]int64, res *report.MaraTestPlanPassRateDTO) {
	rateDto := res.GetModelRate(modelType)
	rateDto.TotalCase += addTotal
	rateDto.TotalExecuteCase += addExec
	for k, v := range addStatus {
		switch k {
		case constants.CaseResultTypePass:
			rateDto.TotalPass += v
		case constants.CaseResultTypeFail:
			rateDto.TotalFail += v
		case constants.CaseResultTypeBlock:
			rateDto.TotalBlock += v
		case constants.CaseResultTypeAgain:
			rateDto.TotalAgain += v
		}
	}
}

func (TestPlanService) GetModel(ctx *commoncontext.MantisContext, id int64) ([]commondto.CodeEnumDTO, error) {
	testPlan := models.MarsTestPlan{}
	testPlan.Id = id
	err := gormx.SelectOneByCondition(ctx, &testPlan)
	if err != nil {
		return nil, err
	}
	res := make([]commondto.CodeEnumDTO, 0)
	for _, model := range testPlan.Model {
		res = append(res, commondto.CodeEnumDTO{
			Label: constants.GetTestPLanRelationName(model),
			Value: model,
		})
	}
	return res, nil
}

func (TestPlanService) UpdateModel(ctx *commoncontext.MantisContext, id int64, mos []string) error {
	testPlan := models.MarsTestPlan{}
	testPlan.Id = id
	testPlan.Model = mos
	_, err := gormx.UpdateOneByCondition(ctx, &testPlan)
	return err
}

func (TestPlanService) CaseList(ctx *commoncontext.MantisContext, req dto.MarsTestPlanCaseListReq, pageReq gormx.PageRequest) (*gormx.PageResult, error) {
	dealCaseInvalid(ctx, req.TestPlanId, req.Type)
	testPLan := models.MarsTestPlan{}
	testPLan.Id = req.TestPlanId
	testPLan.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &testPLan)
	if err != nil {
		return nil, err
	}
	if testPLan.Id == 0 {
		return nil, errors.New("test plan id can not be empty")
	}
	if testPLan.PlanType == constants.TestPLanTypeCommon {
		// 普通测试计划
		_, pageInfo, err := searchCommonCase(ctx, req, pageReq)
		if err != nil {
			return nil, err
		}
		return pageInfo, nil
	}
	// 需求测试计划
	// 获取关联的需求(需要分页)
	demandIds := make([]string, 0)
	pageRes, err := gormx.PageSelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsTestPlanDemandRelation{}).Select("demand_id").
			Eq("test_plan_id", req.TestPlanId).Eq("type", req.Type).
			Eq("is_deleted", commonconstants.DeleteNo), &demandIds, pageReq)
	if err != nil {
		return nil, err
	}
	if len(demandIds) == 0 {
		logger.Logger.Infof("no deman in test plan id %v", req.TestPlanId)
		return pageRes, nil
	}
	// 从 cube base 查询需求信息
	demandDTOs, _, err := projectService.GetDemandsByPage(ctx, dto.ProjectCommonSelectReq{
		Ids: demandIds,
	}, gormx.PageRequest{Page: 1, PageSize: 999})
	if err != nil {
		return nil, err
	}
	// 获取需求关联的用例
	caseRelations, err := searchRelations(ctx, req.TestPlanId, req.Executor, req.Type, demandIds)
	if err != nil {
		return nil, err
	}
	// 需求在没有任何用例
	if len(caseRelations) == 0 {
		logger.Logger.Infof("no case in demand list %v", demandIds)
		list := tmDemandToMarsDemand(demandDTOs, nil)
		return &gormx.PageResult{Total: int64(len(list)), List: list, PageSize: pageReq.PageSize, CurrentPage: pageReq.Page}, nil
	}
	caseIds := make([]int64, 0)
	lastExecIds := make([]int64, 0)
	// 需求与(绑定用例 id)的映射 1对多
	demandRIdMap := make(map[string][]int64)
	for _, relation := range caseRelations {
		cid, _ := strconv.ParseInt(relation.RelationId, 10, 64)
		caseIds = append(caseIds, cid)
		lastExecIds = append(lastExecIds, relation.LastExecId)
		r, ok := demandRIdMap[relation.DemandId]
		if ok {
			// 需求存在
			demandRIdMap[relation.DemandId] = append(r, relation.Id)
		} else {
			// 需求不存在
			demandRIdMap[relation.DemandId] = []int64{relation.Id}
		}
	}
	// 查询需求在所有的用例
	marsTestPlanCasesAll, _, err := searchCasesByParams(ctx, req, nil, caseIds, lastExecIds, caseRelations)
	if err != nil {
		return nil, err
	}
	// 设置节点值(lib,node name,bug count)
	err = setNodeNameAndBugCount(ctx, req.TestPlanId, req.Type, marsTestPlanCasesAll)
	if err != nil {
		return nil, err
	}
	// 关系 id 与对象的映射
	rIdMapping := make(map[int64]models.MarsTestPlanCaseDTO)
	for _, marsTestPlanCase := range marsTestPlanCasesAll {
		rIdMapping[marsTestPlanCase.Id] = marsTestPlanCase
	}
	// 需求与用例的映射
	demandCaseListMapping := make(map[string][]models.MarsTestPlanCaseDTO)
	for demand, rIdList := range demandRIdMap {
		for _, rId := range rIdList {
			marsTestPlanCase, caseExist := rIdMapping[rId]
			if !caseExist {
				continue
			}
			dtoList, ok := demandCaseListMapping[demand]
			if ok {
				// 需求存在
				demandCaseListMapping[demand] = append(dtoList, marsTestPlanCase)
			} else {
				demandCaseListMapping[demand] = []models.MarsTestPlanCaseDTO{marsTestPlanCase}
			}
		}
	}
	// 对分组后的数据进行排序
	for _, caseList := range demandCaseListMapping {
		slices.SortFunc(caseList, func(a, b models.MarsTestPlanCaseDTO) int {
			return int(a.Sort - b.Sort)
		})
	}
	list := tmDemandToMarsDemand(demandDTOs, demandCaseListMapping)
	pageRes.List = list
	return pageRes, nil
}

func searchCommonCase(ctx *commoncontext.MantisContext, req dto.MarsTestPlanCaseListReq, pageReq gormx.PageRequest) ([]models.MarsTestPlanCaseDTO, *gormx.PageResult, error) {
	// 普通测试计划
	// 查询 test plan 关联的 case id（此处不分页）
	caseTestPlanRelations, err := searchRelations(ctx, req.TestPlanId, req.Executor, req.Type, nil)
	if err != nil {
		return nil, nil, err
	}
	if len(caseTestPlanRelations) == 0 {
		logger.Logger.Info("no case in test plan")
		return nil, &gormx.PageResult{}, nil
	}
	caseIds := make([]int64, 0)
	lastExecIds := make([]int64, 0)
	for _, relation := range caseTestPlanRelations {
		cid, _ := strconv.ParseInt(relation.RelationId, 10, 64)
		caseIds = append(caseIds, cid)
		lastExecIds = append(lastExecIds, relation.LastExecId)
	}
	marsTestPlanCasesAll, total, err := searchCasesByParams(ctx, req, &pageReq, caseIds, lastExecIds, caseTestPlanRelations)
	if err != nil {
		return nil, nil, err
	}
	// 根据sort排序并且按照pageReq截取
	res := &gormx.PageResult{
		Total:       total,
		Pages:       int64(len(marsTestPlanCasesAll)) / pageReq.PageSize,
		PageSize:    pageReq.PageSize,
		CurrentPage: pageReq.Page,
		List:        []models.MarsTestPlanCaseDTO{},
	}
	slice.SortBy(marsTestPlanCasesAll, func(a, b models.MarsTestPlanCaseDTO) bool {
		return a.Sort < b.Sort
	})
	start := (pageReq.Page - 1) * pageReq.PageSize
	end := start + pageReq.PageSize
	var marsTestPlanCases []models.MarsTestPlanCaseDTO
	if start > int64(len(marsTestPlanCasesAll)) {
	} else if end > int64(len(marsTestPlanCasesAll)) {
		marsTestPlanCases = marsTestPlanCasesAll[start:]
	} else {
		marsTestPlanCases = marsTestPlanCasesAll[start:end]
	}
	err = setNodeNameAndBugCount(ctx, req.TestPlanId, req.Type, marsTestPlanCases)
	if err != nil {
		return nil, nil, err
	}
	res.List = marsTestPlanCases
	return marsTestPlanCases, res, nil
}

// 查询lib, node 和 bug count 并设置到 testPlanCasesDto 中
func setNodeNameAndBugCount(ctx *commoncontext.MantisContext, testPlanId int64, modelType string, marsTestPlanCases []models.MarsTestPlanCaseDTO) error {
	libIds := set.New[int64]()
	nodeIds := set.New[int64]()
	finalCaseIds := set.New[int64]()
	for _, mcase := range marsTestPlanCases {
		libIds.Add(mcase.LibraryId)
		nodeIds.Add(mcase.ParentNodeId)
		finalCaseIds.Add(mcase.RelationId)
	}
	switch modelType {
	case constants.TestPlanRelationTypeCase:
		// 查询lib, node 和 bug count
		libs := make([]models.MarsCaseLibrary, 0)
		err := gormx.SelectByParamBuilder(ctx,
			gormx.NewParamBuilder().Model(&models.MarsCaseLibrary{}).In("id", libIds.ToSlice()).Eq("is_deleted", commonconstants.DeleteNo),
			&libs)
		if err != nil {
			return err
		}
		libMap := make(map[int64]string)
		for _, lib := range libs {
			libMap[lib.Id] = lib.Name
		}
		nodes := make([]models.MarsCase, 0)
		err = gormx.SelectByParamBuilder(ctx,
			gormx.NewParamBuilder().Model(&models.MarsCase{}).In("id", nodeIds.ToSlice()).Eq("is_deleted", commonconstants.DeleteNo),
			&nodes)
		if err != nil {
			return err
		}
		nodeMap := make(map[int64]string)
		for _, node := range nodes {
			nodeMap[node.Id] = node.Name
		}
		bugCountMap, err := marsBugDao.CountBugsByCaseIds(ctx, testPlanId, 0, finalCaseIds.ToSlice(), constants.TestPlanRelationTypeCase)
		if err != nil {
			return err
		}
		for i, mCase := range marsTestPlanCases {
			marsTestPlanCases[i].Library = libMap[mCase.LibraryId]
			marsTestPlanCases[i].NodeName = nodeMap[mCase.ParentNodeId]
			marsTestPlanCases[i].BugSize = bugCountMap[mCase.RelationId]
		}
	case constants.TestPlanRelationTypeUI:
		// 不需要处理
	case constants.TestPlanRelationTypeApi:
		// 不需要处理
	}
	return nil
}

// 按条件查询用例及结果
func searchCasesByParams(ctx *commoncontext.MantisContext, req dto.MarsTestPlanCaseListReq, pageReq *gormx.PageRequest,
	caseIds, lastExecIds []int64, relations []models.MarsTestPlanCaseRelation,
) ([]models.MarsTestPlanCaseDTO, int64, error) {
	execHisList := make([]models.MarsTestPlanCaseExecHistory, 0)
	// 查询执行结果
	if len(lastExecIds) > 0 {
		hisBuilder := gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseExecHistory{}).In("id", lastExecIds)
		if req.ExecuteResult != "" {
			hisBuilder.In("execute_result", strings.Split(req.ExecuteResult, ","))
		}
		err := gormx.SelectByParamBuilder(ctx, hisBuilder, &execHisList)
		if err != nil {
			return nil, 0, err
		}
	}
	// 用例与最近执行记录的映射关系
	execHisMap := make(map[int64]models.MarsTestPlanCaseExecHistory)
	for _, his := range execHisList {
		execHisMap[his.Id] = his
	}
	casesAll := make([]models.MarsCase, 0)
	switch req.Type {
	case constants.TestPlanRelationTypeCase:
		// 根据条件查询 cases
		params := make([]any, 0)
		sql := `select * from mars_case where id in ?`
		params = append(params, caseIds)
		if req.CaseName != "" {
			sql += ` and name like ? `
			params = append(params, "%"+req.CaseName+"%")
		}
		if req.Priority != "" {
			sql += ` and priority in ?`
			params = append(params, strings.Split(req.Priority, ","))
		}
		if req.TagIds != "" {
			tids := strings.Split(req.TagIds, ",")
			arrayTags := "array["
			for _, tagId := range tids {
				arrayTags += fmt.Sprintf("'%s',", tagId)
			}
			arrayTags = arrayTags[:len(arrayTags)-1]
			arrayTags += "]"
			sql += ` and tags @> any (select jsonb_build_array(val) from unnest(?) as val)`
			params = append(params, gorm.Expr(arrayTags))
		}
		sql += ` and is_deleted = 'N'`
		total := int64(0)
		if pageReq == nil {
			err := gormx.Raw(ctx, sql, &casesAll, params...)
			if err != nil {
				return nil, 0, err
			}
			total = int64(len(casesAll))
		} else {
			pageRes, err := gormx.PageSelectByRaw(ctx, sql, &casesAll, *pageReq, params...)
			if err != nil {
				return nil, 0, err
			}
			total = pageRes.Total
		}
		// 将 case 转为 map
		idCaseMap := make(map[string]models.MarsCase)
		for _, caseModel := range casesAll {
			idCaseMap[strconv.FormatInt(caseModel.Id, 10)] = caseModel
		}
		// 组装res
		marsTestPlanCasesAll := make([]models.MarsTestPlanCaseDTO, 0)
		for _, relation := range relations {
			caseModel, ok := idCaseMap[relation.RelationId]
			if !ok {
				continue
			}
			execRes, okRes := execHisMap[relation.LastExecId]
			if req.ExecuteResult != "" {
				// 没有筛选结果
				if !okRes {
					continue
				}
			}
			planCaseDto := models.MarsTestPlanCaseDTO{
				Id:           relation.Id,
				RelationId:   caseModel.Id,
				Name:         caseModel.Name,
				LibraryId:    caseModel.LibraryId,
				ParentNodeId: caseModel.ParentId,
				CaseType:     constants.TestPlanRelationTypeCase,
				Priority:     caseModel.Priority,
				Sort:         relation.Sort,
				Executors:    relation.Executors,
			}
			if okRes {
				planCaseDto.ExecuteResult = execRes.ExecuteResult
				planCaseDto.ReportUrl = execRes.ReportUrl
			}
			marsTestPlanCasesAll = append(marsTestPlanCasesAll, planCaseDto)
		}
		return marsTestPlanCasesAll, total, nil
	case constants.TestPlanRelationTypeUI:
		uiPlanIds := make([]string, len(relations))
		for i, relation := range relations {
			uiPlanIds[i] = relation.RelationId
		}
		uiPlans, err := uiPlanOpenApi.PlanList(ctx, uiPlanIds)
		if err != nil {
			return nil, 0, err
		}
		uiPlanMap := make(map[string]ui.VenusPlanDto)
		envs := set.New[string]()
		for _, plan := range uiPlans {
			uiPlanMap[plan.Id] = ui.VenusPlanDto{
				Id:        plan.Id,
				Name:      plan.Name,
				CaseCount: plan.CaseCount,
				Env:       plan.Env,
			}
			envs.Add(plan.Env)
		}
		envMap, err := getEnvMap(ctx)
		if err != nil {
			return nil, 0, err
		}
		// 组装res
		marsTestPlanCasesAll := make([]models.MarsTestPlanCaseDTO, 0)
		for _, relation := range relations {
			p, ok := uiPlanMap[relation.RelationId]
			if !ok {
				continue
			}
			execRes, okRes := execHisMap[relation.LastExecId]
			if req.ExecuteResult != "" && (!slices.Contains(strings.Split(req.ExecuteResult, ","),
				constants.CaseResultTypeUntest)) {
				// 没有筛选结果
				if !okRes {
					continue
				}
			}
			parseInt, err := strconv.ParseInt(p.Id, 10, 64)
			if err != nil {
				return nil, 0, err
			}
			planCaseDto := models.MarsTestPlanCaseDTO{
				Id:         relation.Id,
				RelationId: parseInt,
				Name:       p.Name,
				CaseCount:  p.CaseCount,
				CaseType:   constants.TestPlanRelationTypeUI,
				Sort:       relation.Sort,
				Executors:  relation.Executors,
				EnvName:    envMap[p.Env],
			}
			if okRes {
				planCaseDto.ExecuteResult = execRes.ExecuteResult
				planCaseDto.Executors = []string{execRes.Executor}
				planCaseDto.ReportUrl = execRes.ReportUrl
			} else {
				planCaseDto.ExecuteResult = constants.CaseResultTypeUntest
			}
			marsTestPlanCasesAll = append(marsTestPlanCasesAll, planCaseDto)
		}
		return marsTestPlanCasesAll, int64(len(relations)), nil
	case constants.TestPlanRelationTypeApi:
		aptCases, err := aptRemoteApi.GetCaseListByIds(ctx, caseIds)
		if err != nil {
			return nil, 0, err
		}
		// 将 case 转为 map
		idCaseMap := make(map[string]remote.AptCaseInfo)
		for _, caseModel := range aptCases {
			idCaseMap[strconv.FormatInt(caseModel.CaseId, 10)] = caseModel
		}
		// 组装res
		marsTestPlanCasesAll := make([]models.MarsTestPlanCaseDTO, 0)
		for _, relation := range relations {
			caseModel, ok := idCaseMap[relation.RelationId]
			if !ok {
				continue
			}
			execRes, okRes := execHisMap[relation.LastExecId]
			if req.ExecuteResult != "" && (!slices.Contains(strings.Split(req.ExecuteResult, ","),
				constants.CaseResultTypeUntest)) {
				// 没有筛选结果
				if !okRes {
					continue
				}
			}
			planCaseDto := models.MarsTestPlanCaseDTO{
				Id:           relation.Id,
				RelationId:   caseModel.CaseId,
				Name:         caseModel.CaseName,
				Library:      caseModel.LibraryName,
				NodeName:     caseModel.SuiteName,
				VersionName:  caseModel.VersionName,
				ParentNodeId: caseModel.SuiteId,
				CaseType:     constants.TestPlanRelationTypeApi,
				Sort:         relation.Sort,
				Executors:    relation.Executors,
			}
			if okRes {
				planCaseDto.ExecuteResult = execRes.ExecuteResult
				planCaseDto.Executors = []string{execRes.Executor}
				planCaseDto.ReportUrl = execRes.ReportUrl
			} else {
				planCaseDto.ExecuteResult = constants.CaseResultTypeUntest
			}
			marsTestPlanCasesAll = append(marsTestPlanCasesAll, planCaseDto)
		}
		return marsTestPlanCasesAll, int64(len(relations)), nil

	}
	return nil, 0, nil
}

// team 需求转 mars 需求
func tmDemandToMarsDemand(demandDTOs []models.ProjectDemandDTO, demandCaseListMapping map[string][]models.MarsTestPlanCaseDTO) []dto.MarsTestPlanDemandDTO {
	marsDemands := make([]dto.MarsTestPlanDemandDTO, 0)
	if demandCaseListMapping == nil {
		demandCaseListMapping = make(map[string][]models.MarsTestPlanCaseDTO)
	}
	for _, demand := range demandDTOs {
		caseList := demandCaseListMapping[strconv.FormatInt(demand.Id, 10)]
		d := dto.MarsTestPlanDemandDTO{
			Id:                strconv.FormatInt(demand.Id, 10),
			Code:              demand.Code,
			Name:              demand.Name,
			PriorityLevelName: demand.Priority,
			State:             demand.OverState,
			Assignee:          demand.Assignee,
			TotalCases:        int64(len(caseList)),
			SelectedCases:     caseList,
		}
		marsDemands = append(marsDemands, d)
	}
	return marsDemands
}

// 查询测试计划关联的 relation
func searchRelations(ctx *commoncontext.MantisContext, testPlanId int64, executor, relationType string, demandIds []string) ([]models.MarsTestPlanCaseRelation, error) {
	// 查询 test plan 关联的 case id
	caseTestPlanRelations := make([]models.MarsTestPlanCaseRelation, 0)
	sql := `select * from mars_test_plan_case_relation where test_plan_id =? and type =? and is_deleted = 'N'`
	params := []any{testPlanId, relationType}
	if executor != "" {
		// name @> '["jack"]'::jsonb;
		sql += ` and (executors @> '?'::jsonb)`
		params = append(params, gorm.Expr(fmt.Sprintf(`["%s"]`, executor)))
	}
	var err error
	if len(demandIds) > 0 {
		sql += ` and demand_id in ?`
		params = append(params, demandIds)
		err = gormx.Raw(ctx, sql, &caseTestPlanRelations, params...)
	} else {
		err = gormx.Raw(ctx, sql, &caseTestPlanRelations, params...)
	}
	if err != nil {
		return nil, err
	}
	return caseTestPlanRelations, nil
}

// AssignExecutors 分配执行者
func (TestPlanService) AssignExecutors(ctx *commoncontext.MantisContext, req dto.MarsTestPlanCaseBatchUpdateReq) error {
	updateMap := map[string]any{
		"gmt_modified": times.Now(),
		"modifier":     ctx.User.AdAccount,
		"executors":    req.AssignExecutors,
	}
	_, err := gormx.UpdateBatchByParamBuilderAndMap(ctx,
		gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseRelation{}).
			In("id", req.Ids).Eq("is_deleted", commonconstants.DeleteNo),
		updateMap)
	return err
}

// UpdateResult 修改执行结果
// 非执行人不允许修改直接结果
func (TestPlanService) UpdateResult(ctx *commoncontext.MantisContext, req dto.MarsTestPlanCaseBatchUpdateReq) error {
	_, err := checkPlanStatus(ctx, req.TestPlanId)
	if err != nil {
		return err
	}
	// id 与执行 id 的映射
	lastExecIds := make([]int64, 0)
	caseRelations := make([]models.MarsTestPlanCaseRelation, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseRelation{}).
			In("id", req.Ids).Eq("is_deleted", commonconstants.DeleteNo), &caseRelations)
	if err != nil {
		return err
	}
	for _, relation := range caseRelations {
		lastExecIds = append(lastExecIds, relation.LastExecId)
	}
	// 修改执行结果
	updateMap := map[string]any{
		"gmt_modified":   times.Now(),
		"modifier":       ctx.User.AdAccount,
		"executor":       ctx.User.AdAccount,
		"execute_result": req.ExecuteResult,
		"execute_time":   times.Now(),
	}
	_, err = gormx.UpdateBatchByParamBuilderAndMap(ctx,
		gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseExecHistory{}).
			In("id", lastExecIds).Eq("is_deleted", commonconstants.DeleteNo),
		updateMap)
	return err
}

// DeleteCasesBatch 批量删除用例
func (TestPlanService) DeleteCasesBatch(ctx *commoncontext.MantisContext, testPLanId int64, rids []int64) error {
	relationList := make([]models.MarsTestPlanCaseRelation, 0)
	err := gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseRelation{}).
		In("id", rids).Eq("is_deleted", commonconstants.DeleteNo), &relationList)
	if err != nil {
		return err
	}
	if len(relationList) == 0 {
		return errors.New("没有找到要删除的用例")
	}
	modelType := relationList[0].Type
	demandIdCaseIdsMap := make(map[string]set.Set[int64])
	for _, relation := range relationList {
		caseIds, ok := demandIdCaseIdsMap[relation.DemandId]
		cId, err := strconv.ParseInt(relation.RelationId, 10, 64)
		if err != nil {
			logger.Logger.Error(err)
			return err
		}
		if ok {
			caseIds.Add(cId)
		} else {
			demandIdCaseIdsMap[relation.DemandId] = set.New[int64]()
			demandIdCaseIdsMap[relation.DemandId].Add(cId)
		}
	}
	updateMap := map[string]any{
		"is_deleted":   commonconstants.DeleteYes,
		"gmt_modified": times.Now(),
		"modifier":     ctx.User.AdAccount,
	}
	err = gormx.Transaction(ctx, func() error { // 开启事务
		// 删除关联关系
		_, err := gormx.UpdateBatchByParamBuilderAndMap(ctx,
			gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseRelation{}).In("id", rids), updateMap)
		if err != nil {
			return err
		}
		//  删除 bug
		for demandId, caseIds := range demandIdCaseIdsMap {
			_, err = gormx.UpdateBatchByParamBuilderAndMap(ctx,
				gormx.NewParamBuilder().Model(&models.MarsBug{}).Eq("test_plan_id", testPLanId).
					In("case_id", caseIds.ToSlice()).Eq("type", modelType).
					Eq("demand_id", demandId).
					Eq("is_deleted", commonconstants.DeleteNo),
				map[string]any{
					"is_deleted":   commonconstants.DeleteYes,
					"gmt_modified": times.Now(),
					"modifier":     ctx.User.AdAccount,
				})
			if err != nil {
				return err
			}
		}
		return nil
	})
	return err
}

// DeleteDemandBatch 批量删除需求
func (s TestPlanService) DeleteDemandBatch(ctx *commoncontext.MantisContext, planId int64, demandIds []string, modelType string) error {
	updateMap := map[string]any{
		"is_deleted":   commonconstants.DeleteYes,
		"gmt_modified": times.Now(),
		"modifier":     ctx.User.AdAccount,
	}
	_, err := gormx.UpdateBatchByParamBuilderAndMap(ctx,
		gormx.NewParamBuilder().Model(&models.MarsTestPlanDemandRelation{}).Eq("test_plan_id", planId).
			In("demand_id", demandIds).Eq("type", modelType), updateMap)
	if err != nil {
		return err
	}
	relationIds := make([]int64, 0)
	err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseRelation{}).Select("id").
		Eq("test_plan_id", planId).Eq("type", modelType).In("demand_id", demandIds), &relationIds)
	if err != nil {
		return err
	}
	goroutine.Run(func() {
		err = s.DeleteCasesBatch(ctx, planId, relationIds)
		if err != nil {
			logger.Logger.Error(err)
		}
	})
	return err
}

func (TestPlanService) MoveCase(ctx *commoncontext.MantisContext, planId int64, req dto.MarsTestPlanCaseMoveReq) error {
	if req.DemandId == "" {
		req.DemandId = constants.DefaultDemandID
	}
	moveNode := models.MarsTestPlanCaseRelation{}
	moveNode.Id = req.Id
	moveNode.Modifier = ctx.User.AdAccount
	moveNode.GmtModified = times.Now()
	if req.SiblingId < 1 {
		// 移动到最后一个节点
		maxSort, err := marsCaseTestPlanRelationDao.SelectMaxSortByTestPlanId(ctx, planId, req.Type, req.DemandId)
		if err != nil {
			return err
		}
		moveNode.Sort = maxSort + 1
		_, err = gormx.UpdateOneByCondition(ctx, &moveNode)
		return err
	}
	// 移动到指定节点后面
	siblingNode := models.MarsTestPlanCaseRelation{}
	siblingNode.Id = req.SiblingId
	err := gormx.SelectOneByCondition(ctx, &siblingNode)
	if err != nil {
		return err
	}
	err = marsCaseTestPlanRelationDao.UpdateSortUpByTestPlanId(ctx, planId, req.Type, req.DemandId, siblingNode.Sort)
	if err != nil {
		return err
	}
	moveNode.Sort = siblingNode.Sort
	_, err = gormx.UpdateOneByCondition(ctx, &moveNode)
	return err
}

func (s TestPlanService) RelateDemand(ctx *commoncontext.MantisContext, demands []dto.MarsTestPlanDemandReqDTO, testPlanId int64, modelType string) error {
	existDemandIds := make([]string, 0)
	err := gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsTestPlanDemandRelation{}).Select("demand_id").Eq("test_plan_id", testPlanId).
			Eq("type", modelType).Eq("is_deleted", commonconstants.DeleteNo),
		&existDemandIds)
	if err != nil {
		return err
	}
	// 计划绑定需求
	addRelation := make([]models.MarsTestPlanDemandRelation, 0)
	for _, r := range demands {
		if slices.Contains(existDemandIds, strconv.FormatInt(r.Id, 10)) {
			continue
		}
		addRelation = append(addRelation, models.MarsTestPlanDemandRelation{
			Addons:     commonmodels.NewAddonsWithUser(ctx.User),
			TestPlanId: testPlanId,
			DemandId:   strconv.FormatInt(r.Id, 10),
			Type:       modelType,
		})
	}
	if len(addRelation) > 0 {
		_, err = gormx.InsertBatch(ctx, &addRelation)
		if err != nil {
			return err
		}
	}
	if constants.TestPlanRelationTypeCase == modelType {
		// 绑定需求下的用例
		demandCaseMap := make(map[string][]string)
		for _, r := range demands {
			if len(r.SelectedCase) > 0 {
				cids := make([]string, len(r.SelectedCase))
				for i, c := range r.SelectedCase {
					cids[i] = c.CaseId
				}
				demandCaseMap[strconv.FormatInt(r.Id, 10)] = cids
			}
		}
		for k, v := range demandCaseMap {
			err := s.RelateCase(ctx, dto.MarsTestPlanRelateCaseReq{
				TestPlanId: testPlanId,
				DemandId:   k,
				Type:       modelType,
				Ids:        v,
			})
			if err != nil {
				return err
			}
		}
	}
	return err
}

func (TestPlanService) GetFunctionCaseInfo(ctx *commoncontext.MantisContext, planId, relationId int64) (*dto.TestPlanCase, error) {
	caseRelation := models.MarsTestPlanCaseRelation{}
	caseRelation.Id = relationId
	caseRelation.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &caseRelation)
	if err != nil {
		return nil, err
	}
	caseId, err := strconv.ParseInt(caseRelation.RelationId, 10, 64)
	if err != nil {
		return nil, err
	}
	caseInfo, err := caseService.Info(ctx, caseId)
	if err != nil {
		logger.Logger.Errorf("查询 case 信息异常,err=%v", err)
		return nil, err
	}
	execHis := models.MarsTestPlanCaseExecHistory{}
	execHis.Id = caseRelation.LastExecId
	err = gormx.SelectOneByCondition(ctx, &execHis)
	if err != nil {
		logger.Logger.Errorf("查询用例执行历史异常,err=%v", err)
		return nil, err
	}
	// 获取步骤结果映射
	execResult := models.MarsCaseResultInfo{}
	stepIdResultMap := make(map[int64]models.MarsCaseStepExecResult)
	if execHis.Result != "" {
		err := json.Unmarshal([]byte(execHis.Result), &execResult)
		if err != nil {
			return nil, err
		}
		if execResult.TextActualResult != nil {
			stepIdResultMap[execResult.TextActualResult.StepId] = *execResult.TextActualResult
		}
		if len(execResult.ListActualResult) > 0 {
			for _, stepResult := range execResult.ListActualResult {
				stepIdResultMap[stepResult.StepId] = stepResult
			}
		}
	}
	if caseInfo.Steps != nil {
		steps := caseInfo.Steps
		if steps.Type == "text" {
			stepId := steps.Text.Id
			r, ok := stepIdResultMap[stepId]
			if ok {
				steps.Text.ActualResults = r.Content
			}
		} else {
			for i, step := range steps.Steps {
				r, ok := stepIdResultMap[step.Id]
				if ok {
					steps.Steps[i].ActualResults = r.Content
					steps.Steps[i].Status = r.Status
				}
			}
		}
	}
	res := dto.TestPlanCase{
		MarsCase: *caseInfo,
		Status:   execHis.ExecuteResult,
		TestData: execResult.TestData,
	}
	return &res, nil
}

func (TestPlanService) UpdateFunctionCaseInfo(ctx *commoncontext.MantisContext, planId, relationId int64, planCase *dto.TestPlanCase) error {
	_, err := checkPlanStatus(ctx, planId)
	if err != nil {
		return err
	}
	caseRelation := models.MarsTestPlanCaseRelation{}
	caseRelation.Id = relationId
	caseRelation.IsDeleted = commonconstants.DeleteNo
	err = gormx.SelectOneByCondition(ctx, &caseRelation)
	if err != nil {
		return err
	}
	user := ctx.User.AdAccount
	resultInfo := models.MarsCaseResultInfo{}
	resultInfo.TestData = planCase.TestData
	if planCase.Steps != nil {
		steps := planCase.Steps
		if steps.Type == "text" {
			stepId := steps.Text.Id
			resultInfo.TextActualResult = &models.MarsCaseStepExecResult{
				StepId:  stepId,
				Content: planCase.Steps.Text.ActualResults,
			}
		} else {
			resultInfo.ListActualResult = make([]models.MarsCaseStepExecResult, 0)
			for _, step := range steps.Steps {
				resultInfo.ListActualResult = append(resultInfo.ListActualResult, models.MarsCaseStepExecResult{
					StepId:  step.Id,
					Status:  step.Status,
					Content: step.ActualResults,
				})
			}
		}
	}
	jsonResult, err := json.Marshal(resultInfo)
	if err != nil {
		return err
	}
	update := models.MarsTestPlanCaseExecHistory{}
	update.Id = caseRelation.LastExecId
	update.ExecuteResult = planCase.Status
	update.Result = string(jsonResult)
	update.Modifier = user
	update.Executor = user
	update.ExecuteTime = times.Now()
	update.GmtModified = times.Now()
	_, err = gormx.UpdateOneByCondition(ctx, &update)
	return err
}

func (TestPlanService) ExecuteCase(ctx *commoncontext.MantisContext, planId int64, req dto.MarsCaseExecReq) error {
	logger.Logger.Infof("用例执行...planid=%d,execute-case-req: %+v", planId, req)
	// 获取关联的用例
	relations := make([]models.MarsTestPlanCaseRelation, 0)
	builder := gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseRelation{}).Select("id,relation_id").
		Eq("test_plan_id", planId).Eq("type", req.Type).Eq("is_deleted", commonconstants.DeleteNo)
	if len(req.Ids) > 0 {
		builder.In("id", req.Ids)
	}
	err := gormx.SelectByParamBuilder(ctx, builder, &relations)
	if err != nil {
		return err
	}
	if len(relations) < 1 {
		return errors.New("没有关联的用例,请先关联用例再执行")
	}
	apiTag := ""
	uiHisIdReportMap := make(map[int64]string)
	switch req.Type {
	case constants.TestPlanRelationTypeUI:
		for i, rel := range relations {
			hisId := snowflake.GenSnowFlakeId()
			reportUrl, err := uiPlanOpenApi.ExecUiPlan(ctx, "", rel.RelationId, "",
				[]string{fmt.Sprintf(constants.UiCallBackPath, hisId)})
			if err != nil {
				logger.Logger.Errorf("调用 ui 执行计划失败! 执行用例异常,ui plan id=%s,err=%v", rel.RelationId, err)
				return err
			}
			relations[i].LastExecId = hisId
			uiHisIdReportMap[hisId] = reportUrl
		}
	case constants.TestPlanRelationTypeApi:
		machineId, err := aptRemoteApi.GetDefaultMachine(ctx)
		if err != nil {
			logger.Logger.Errorf("获取默认的执行机异常,err=%v", err)
			return err
		}
		apiIds := make([]int64, 0)
		for _, rel := range relations {
			parseInt, err := strconv.ParseInt(rel.RelationId, 10, 64)
			if err != nil {
				logger.Logger.Errorf("error in exec api , parse string to int64,err=%v", err)
				return err
			}
			apiIds = append(apiIds, parseInt)
		}
		spaceId := ctx.Header.Get("spaceid")
		params := remote.AptCaseExecParam{
			SpaceId: spaceId,
			User:    ctx.User,
			CaseIds: apiIds,
			SiteId:  machineId,
			EnvId:   req.Env,
			PlanId:  planId,
		}
		execRes, err := aptRemoteApi.CaseExec(ctx, params)
		if err != nil {
			return err
		}
		apiTag = execRes.ExecuteTag
		logger.Logger.Infof("执行结果:%+v", execRes)
	default:
		return errors.New("not support type")
	}
	execHisList := make([]models.MarsTestPlanCaseExecHistory, 0)
	idMap := make(map[int64]int64)
	for _, rel := range relations {
		his := models.MarsTestPlanCaseExecHistory{
			Addons:        commonmodels.NewAddonsWithUser(ctx.User),
			TestPlanId:    planId,
			CaseId:        rel.RelationId,
			Type:          req.Type,
			ExecuteResult: constants.CaseResultTypeBlock,
			Executor:      ctx.User.AdAccount,
			ExecuteTime:   times.Now(),
		}
		if constants.TestPlanRelationTypeUI == req.Type {
			his.Id = rel.LastExecId
			his.ReportUrl = uiHisIdReportMap[his.Id]
		} else {
			// api 自己生成
			his.Id = snowflake.GenSnowFlakeId()
			his.Result = apiTag
			his.ReportUrl = fmt.Sprintf("%s/magic/debug?executeTag=%s", configs.Config.Domain.Cube, apiTag)
		}
		execHisList = append(execHisList, his)
		idMap[rel.Id] = his.Id
	}
	if len(execHisList) > 0 {
		_, err = gormx.InsertBatch(ctx, &execHisList)
		if err != nil {
			return err
		}
	}
	if len(idMap) > 0 {
		err = marsTestPlanCaseRelationDao.UpdateBatchLastExecId(ctx, idMap)
		if err != nil {
			return err
		}
	}
	return nil
}

// HistoryReport 查看历史报告
func (TestPlanService) HistoryReport(ctx *commoncontext.MantisContext, planId int64, modelType string, relationId string,
	pageReq gormx.PageRequest,
) (*gormx.PageResult, error) {
	if modelType == constants.TestPlanRelationTypeUI {
		hisList := make([]models.MarsTestPlanCaseExecHistory, 0)
		builder := gormx.NewParamBuilder().Model(&models.MarsTestPlanCaseExecHistory{}).
			Eq("test_plan_id", planId).Eq("type", modelType).
			Eq("is_deleted", commonconstants.DeleteNo).OrderByDesc("id")
		if relationId != "" {
			builder.Eq("case_id", relationId)
		}
		page, err := gormx.PageSelectByParamBuilder(ctx, builder, &hisList, pageReq)
		for i, his := range hisList {
			if his.Result != "" {
				uiRes := ui.MarsUiPlanExecResult{}
				err = json.Unmarshal([]byte(his.Result), &uiRes)
				if err != nil {
					logger.Logger.Errorf("历史报告转化失败!err=%v", err)
				} else {
					hisList[i].ResultInfo = uiRes
					hisList[i].Result = ""
				}
			}
		}
		if err != nil {
			return nil, err
		}
		return page, nil
	} else if modelType == constants.TestPlanRelationTypeApi {
		type ApiCountDto struct {
			Tag         string      `json:"tag" gorm:"column:tag"`
			PassCount   int64       `json:"passCount" gorm:"column:pass_count"`
			FailCount   int64       `json:"failCount" gorm:"column:fail_count"`
			OtherCount  int64       `json:"otherCount" gorm:"column:other_count"`
			TotalCount  int64       `json:"totalNum" gorm:"column:total_count"`
			ReportUrl   string      `json:"reportUrl" gorm:"column:report_url"`
			Executor    string      `json:"executor" gorm:"column:executor"`
			PassRate    string      `json:"passedPercent" gorm:"-"`
			Type        string      `json:"type" gorm:"-"`
			ExecuteTime *times.Time `json:"executeTime" gorm:"column:execute_time;type:timestamp"`
		}
		list := make([]ApiCountDto, 0)
		// 使用开窗函数，按result分组，取execute_time最大的一条数据
		sql := `WITH ranked_data AS (
					SELECT 
						id,
						executor,
						report_url,
						execute_time,
						result AS tag,
						execute_result,
						COUNT(*) FILTER (WHERE execute_result = 'pass') OVER (PARTITION BY result, report_url) AS pass_count,
						COUNT(*) FILTER (WHERE execute_result = 'fail') OVER (PARTITION BY result, report_url) AS fail_count,
						COUNT(*) FILTER (WHERE execute_result NOT IN ('pass', 'fail')) OVER (PARTITION BY result, report_url) AS other_count,
						COUNT(*) OVER (PARTITION BY result) AS total_count,
						ROW_NUMBER() OVER (PARTITION BY result ORDER BY id DESC) AS rn
					FROM mars_test_plan_case_exec_history
					WHERE test_plan_id = ? 
					  AND type = ?
				)
				SELECT 
					id,
					executor,
					report_url,
					execute_time,
					tag,
					pass_count,
					fail_count,
					other_count,
					total_count
				FROM ranked_data
				WHERE rn = 1
				ORDER BY id DESC`
		page, err := gormx.PageSelectByRaw(ctx, sql, &list, pageReq, planId, modelType)
		if err != nil {
			return nil, err
		}
		for i, item := range list {
			list[i].Type = "接口用例"
			if item.TotalCount == 0 {
				list[i].PassRate = "0.00%"
				continue
			}
			list[i].PassRate = fmt.Sprintf("%.2f%%", float64(item.PassCount)/float64(item.TotalCount)*100)
		}
		return page, nil
	} else {
		return nil, errors.New("not support model type")
	}
}

func (TestPlanService) ProjectDemand(ctx *commoncontext.MantisContext, planId int64) ([]models.ProjectDemandDTO, error) {
	demandIds := make([]string, 0)
	err := gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsTestPlanDemandRelation{}).Select("demand_id").
			Eq("test_plan_id", planId).
			Eq("is_deleted", commonconstants.DeleteNo), &demandIds)
	if err != nil {
		return nil, err
	}
	if len(demandIds) == 0 {
		logger.Logger.Infof("no deman in test plan id %v", planId)
		return nil, nil
	}
	// 从 cube base 查询需求信息
	demandDTOs, _, err := projectService.GetDemandsByPage(ctx, dto.ProjectCommonSelectReq{
		Ids: demandIds,
	}, gormx.PageRequest{Page: 1, PageSize: 999})
	return demandDTOs, err
}

func (t TestPlanService) ReportView(ctx *commoncontext.MantisContext, planId int64) (*models.MarsTestPlanReport, error) {
	plan := models.MarsTestPlan{}
	plan.Id = planId
	plan.IsDeleted = commonconstants.DeleteNo
	err := gormx.SelectOneByCondition(ctx, &plan)
	if err != nil {
		return nil, err
	}
	envMap, err := getEnvMap(ctx)
	if err != nil {
		return nil, err
	}
	res := models.MarsTestPlanReport{
		Name:       plan.Name,
		Owner:      plan.Owner,
		Stage:      plan.Stage,
		Env:        plan.Env,
		BeginTime:  plan.BeginTime,
		EndTime:    plan.EndTime,
		TestPlanId: planId,
		EnvName:    envMap[plan.Env],
	}
	res.Creator = plan.Creator
	res.GmtCreated = plan.GmtCreated
	// 设置图标枚举
	viewChoiceConfigSlice := models.ViewChoiceConfigSlice{}
	viewChoiceConfigSlice = append(viewChoiceConfigSlice, constants.ViewChoiceConfigsMap[constants.Public]...)
	if plan.PlanType == constants.TestPLanTypeDemand {
		viewChoiceConfigSlice = append(viewChoiceConfigSlice, constants.ViewChoiceConfigsMap[constants.TestPLanTypeDemand]...)
	}
	for _, model := range plan.Model {
		viewChoiceConfigSlice = append(viewChoiceConfigSlice, constants.ViewChoiceConfigsMap[model]...)
	}
	slices.SortFunc(viewChoiceConfigSlice, func(a, b models.ViewChoiceConfig) int {
		return cmp.Compare(a.Sort, b.Sort)
	})
	res.ViewChoiceConfig = viewChoiceConfigSlice
	// 设置图标信息
	rate, err := t.GetPlanPassRate(ctx, plan.Id)
	if err != nil {
		logger.Logger.Errorf("ReportView...计划=%d,获取通过率失败!err=%v", planId, err)
		return nil, err
	}
	res.TestPlanPassRate = *rate
	err = t.setReportList(ctx, plan, &res)
	if err != nil {
		return nil, err
	}
	return &res, nil
}

func (t TestPlanService) setReportList(ctx *commoncontext.MantisContext, plan models.MarsTestPlan, res *models.MarsTestPlanReport) error {
	// 设置需求
	if plan.PlanType == constants.TestPLanTypeDemand {
		demand, err := t.ProjectDemand(ctx, plan.Id)
		if err != nil {
			logger.Logger.Errorf("ReportView...计划=%d,获取需求失败!err=%v", plan.Id, err)
			return err
		}
		res.DemandList = demand
	}
	caseMap := models.TestPlanCaseMapMapDTO{}
	// 设置用例列表
	for _, model := range plan.Model {
		list, _, err := searchCommonCase(ctx, dto.MarsTestPlanCaseListReq{
			TestPlanId: plan.Id,
			Type:       model,
		}, gormx.PageRequest{Page: 1, PageSize: 999})
		if err != nil {
			logger.Logger.Errorf("ReportView...计划=%d,获取用例列表失败,model=%s,err=%v", plan.Id, model, err)
			return err
		}
		caseMap[model] = list
	}
	res.TestPlanCaseMap = caseMap
	// 设置 bug 列表
	bugList, err := bugService.BugList(ctx, project.MarsBugListReq{
		TestPlanId: plan.Id,
	})
	if err != nil {
		logger.Logger.Errorf("ReportView...计划=%d,获取缺陷列表失败,err=%v", plan.Id, err)
		return nil
	}
	res.BugSnapshot = bugList.BugDTOS
	return nil
}

// SaveReportView 保存报表
func (t TestPlanService) SaveReportView(ctx *commoncontext.MantisContext, report models.MarsTestPlanReport) (int64, error) {
	plan, err := checkPlanExist(ctx, report.TestPlanId)
	if err != nil {
		return 0, err
	}
	if plan.Status != constants.TestPLanStatusFinished {
		return 0, errors.New("计划状态未结束，请先结束计划再保存报告")
	}
	user := ctx.User.AdAccount
	spaceId := ctx.Header.Get("spaceid")
	report.GmtModified = times.Now()
	report.Modifier = user
	report.SpaceId = spaceId
	err = t.setReportList(ctx, *plan, &report)
	if err != nil {
		return 0, err
	}
	_, err = gormx.InsertOne(ctx, &report)
	if err != nil {
		return 0, err
	}
	return report.Id, nil
}
