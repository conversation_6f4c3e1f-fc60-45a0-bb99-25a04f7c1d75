package service

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	set "github.com/duke-git/lancet/v2/datastructure/set"
)

type OpenapiService struct{}

func (OpenapiService) GetCaseListByDemand(ctx *commoncontext.MantisContext, demandId int64, pageReq gormx.PageRequest) (*gormx.PageResult, error) {
	caseIds := make([]int64, 0)
	gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCaseRelation{}).Select("case_id").Eq("relation_id", demandId).Eq("is_deleted", commonconstants.DeleteNo),
		&caseIds)
	cases := make([]models.MarsCase, 0)
	res, err := gormx.PageSelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCase{}).In("id", caseIds).Eq("is_deleted", commonconstants.DeleteNo),
		&cases, pageReq)
	if err != nil {
		return nil, err
	}
	libraryIds := set.New[int64]()
	folderIds := set.New[int64]()
	for _, marsCase := range cases {
		libraryIds.Add(marsCase.LibraryId)
		folderIds.Add(marsCase.ParentId)
	}
	libraries := make([]models.MarsCaseLibrary, 0)
	gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCaseLibrary{}).In("id", libraryIds.ToSlice()).Eq("is_deleted", commonconstants.DeleteNo),
		&libraries)
	libraryIdNameMap := make(map[int64]string)
	for _, lib := range libraries {
		libraryIdNameMap[lib.Id] = lib.Name
	}
	folders := make([]models.MarsCase, 0)
	gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCase{}).In("id", folderIds.ToSlice()).Eq("is_deleted", commonconstants.DeleteNo),
		&folders)
	folderIdNameMap := make(map[int64]string)
	for _, folder := range folders {
		folderIdNameMap[folder.Id] = folder.Name
	}
	list := make([]dto.TMMarsCaseDTO, 0)
	for _, marsCase := range cases {
		list = append(list, dto.TMMarsCaseDTO{
			Id:       marsCase.Id,
			Name:     marsCase.Name,
			Library:  libraryIdNameMap[marsCase.LibraryId],
			NodePath: folderIdNameMap[marsCase.ParentId],
		})
	}
	res.List = list
	return res, nil
}
