package xmind

import (
	"archive/zip"
	"bytes"
	"embed"
	"encoding/json"
	"fmt"
	"io"

	"github.com/google/uuid"
)

//go:embed template.xmind
var TemplateFS embed.FS

type Marker struct {
	MarkerId string `json:"markerId"`
}

type Children struct {
	Attached []*Topic `json:"attached"`
	Detached []*Topic `json:"detached"`
}

// Topic 表示脑图中的一个节点
type Topic struct {
	Id             string  `json:"id"`
	Class          string  `json:"class"`
	StructureClass string  `json:"structureClasss"` // org.xmind.ui.map.clockwises
	Title          string  `json:"title"`
	CustomWidth    float64 `json:"customWidth"`
	Position       *struct {
		X float64 `json:"x"`
		Y float64 `json:"y"`
	} `json:"position"`
	Style *struct {
		Properties *struct {
			FoFontSize      string `json:"fo:font-size"`
			FoColor         string `json:"fo:color"`
			SvgFill         string `json:"svg:fill"`
			BorderLineWidth string `json:"border-line-width"`
		} `json:"properties"`
	} `json:"style"`
	Markers  []Marker `json:"markers"`
	Labels   []string `json:"labels"`
	Children Children `json:"children"` // 子节点
}

// XMindContent 表示 content.json 的根结构
type XMindContent struct {
	Id        string `json:"id"`
	Class     string `json:"class"`
	Title     string `json:"title"`
	RootTopic *Topic `json:"rootTopic"`
}

// ParseXMind 从文件流解析 XMind 文件
func ParseXMind(reader io.Reader) (*XMindContent, error) {
	// 读取流到缓冲区以确定大小
	data, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("读取输入流失败: %v", err)
	}
	bufReader := bytes.NewReader(data)

	// 创建 ZIP 读取器
	zipReader, err := zip.NewReader(bufReader, int64(len(data)))
	if err != nil {
		return nil, fmt.Errorf("创建 ZIP 读取器失败: %v", err)
	}

	// 查找 content.xml
	var contentFile *zip.File
	for _, f := range zipReader.File {
		if f.Name == "content.json" {
			contentFile = f
			break
		}
	}

	if contentFile == nil {
		return nil, fmt.Errorf("未找到 content.json 文件")
	}

	// 读取 content.json
	rc, err := contentFile.Open()
	if err != nil {
		return nil, fmt.Errorf("打开 content.json 失败: %v", err)
	}
	defer rc.Close()
	rcc, err := io.ReadAll(rc)
	if err != nil {
		return nil, fmt.Errorf("解析 content.json 失败: %v", err)
	}

	// 解析 josn
	var contents []XMindContent
	if err := json.Unmarshal(rcc, &contents); err != nil {
		return nil, fmt.Errorf("解析 content.json 失败: %v", err)
	}
	if len(contents) == 0 {
		return nil, nil
	}

	return &contents[0], nil
}

// GenerateXMind 根据 XMindContent 生成 XMind 文件流
func GenerateXMind(content *XMindContent) (*bytes.Buffer, error) {
	// 为content 生成一些必要的东西
	err := dealContent(content)
	if err != nil {
		return nil, err
	}
	// 创建缓冲区存储 ZIP 文件
	var buf bytes.Buffer
	// 创建 ZIP 写入器
	zipWriter := zip.NewWriter(&buf)
	defer zipWriter.Close()

	contents := []XMindContent{*content}
	// 生成并添加 content.xml
	contentJson, err := json.Marshal(contents)
	if err != nil {
		return nil, fmt.Errorf("序列化 content.json 失败: %v", err)
	}

	// 写入 content.xml 到 ZIP
	contentWriter, err := zipWriter.Create("content.json")
	if err != nil {
		return nil, fmt.Errorf("创建 content.json 失败: %v", err)
	}
	if _, err := contentWriter.Write(contentJson); err != nil {
		return nil, fmt.Errorf("写入 content.json 失败: %v", err)
	}

	// 生成并添加 manifest.xml
	manifestJson := `{"file-entries":{"content.json":{},"metadata.json":{},"Thumbnails/thumbnail.png":{}}}`
	manifestWriter, err := zipWriter.Create("manifest.json")
	if err != nil {
		return nil, fmt.Errorf("创建 manifest.json 失败: %v", err)
	}
	if _, err := manifestWriter.Write([]byte(manifestJson)); err != nil {
		return nil, fmt.Errorf("写入 manifest.json 失败: %v", err)
	}

	// 生成并添加 metadata.json
	metadataJson := fmt.Sprintf(`{"creator":{"name":"devcube","version":"22.08.2194.202208310741"},"activeSheetId":"%s"}`, content.Id)
	metadataWriter, err := zipWriter.Create("metadata.json")
	if err != nil {
		return nil, fmt.Errorf("创建 metadata.json 失败: %v", err)
	}
	if _, err := metadataWriter.Write([]byte(metadataJson)); err != nil {
		return nil, fmt.Errorf("写入 metadata.json 失败: %v", err)
	}

	// 确保 ZIP 写入完成
	if err := zipWriter.Flush(); err != nil {
		return nil, fmt.Errorf("刷新 ZIP 写入器失败: %v", err)
	}

	return &buf, nil
}

func dealContent(content *XMindContent) error {
	content.Id = uuid.NewString()
	content.Class = "sheet"
	content.Title = "devcube测试用例导出"
	return dealTopic(content.RootTopic, "root")
}

func dealTopic(topic *Topic, topicType string) (err error) {
	topic.Id = uuid.NewString()
	topic.Class = "topic"
	switch topicType {
	case "root":
		topic.StructureClass = "org.xmind.ui.map.clockwise"
		topic.Children.Detached = []*Topic{
			{Title: "【模板说明】：\n* Xmind自带【标记】的优先级1，2，3，4分别对应P0，P1，P2，P3\n* 用例节点【笔记】中可以关联需求编号（多个需求编号以逗号分隔）\n* 节点类型说明：\n   [D]/[目录] - 目录\n   [C]/[用例] - 用例\n   [S]/[步骤] - 步骤\n   [E]/[预期] - 预期\n   [P]/[前置] - 前置条件\n   [R]/[备注] - 备注"},
		}
	case "attached":
	case "detached":
		topic.CustomWidth = 473.13191105769204
		topic.Position = &struct {
			X float64 "json:\"x\""
			Y float64 "json:\"y\""
		}{
			X: 145.66285118689908,
			Y: -396.6153846153845,
		}
		topic.Style = &struct {
			Properties *struct {
				FoFontSize      string "json:\"fo:font-size\""
				FoColor         string "json:\"fo:color\""
				SvgFill         string "json:\"svg:fill\""
				BorderLineWidth string "json:\"border-line-width\""
			} "json:\"properties\""
		}{
			Properties: &struct {
				FoFontSize      string "json:\"fo:font-size\""
				FoColor         string "json:\"fo:color\""
				SvgFill         string "json:\"svg:fill\""
				BorderLineWidth string "json:\"border-line-width\""
			}{
				FoFontSize:      "14pt",
				FoColor:         "#FFFFF",
				SvgFill:         "#15831c",
				BorderLineWidth: "0pt",
			},
		}
	default:
		return fmt.Errorf("unkown topicType: %s", topicType)
	}
	for _, attached := range topic.Children.Attached {
		err = dealTopic(attached, "attached")
	}
	for _, detached := range topic.Children.Detached {
		err = dealTopic(detached, "detached")
	}
	return err
}
