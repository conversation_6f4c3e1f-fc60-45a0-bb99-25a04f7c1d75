package xmind

import (
	"fmt"
	"os"
	"testing"
)

func Test_ParseXmind(t *testing.T) {
	filePath := "example.xmind"
	file, err := os.Open(filePath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "错误: %v\n", err)
		os.Exit(1)
	}
	defer file.Close()
	content, err := ParseXMind(file)
	if err != nil {
		fmt.Fprintf(os.Stderr, "错误: %v\n", err)
		os.Exit(1)
	}

	// 打印脑图结构
	// for _, sheet := range content.Sheets {
	// 	fmt.Printf("工作表: %s (ID: %s)\n", sheet.Title, sheet.ID)
	// 	PrintTopic(sheet.Topic, "  ")
	// }

	// 生成 XMind 文件流
	buf, err := GenerateXMind(content)
	if err != nil {
		fmt.Fprintf(os.Stderr, "生成错误: %v\n", err)
		os.Exit(1)
	}

	// 示例：将文件流写入磁盘（可选）
	outputPath := "output.xmind"
	if err := os.WriteFile(outputPath, buf.Bytes(), 0o644); err != nil {
		fmt.Fprintf(os.Stderr, "写入文件错误: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("成功生成 XMind 文件流，写入: %s (%d bytes)\n", outputPath, buf.Len())
}
