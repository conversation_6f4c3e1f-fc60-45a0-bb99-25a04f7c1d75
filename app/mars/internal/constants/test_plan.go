package constants

const (
	DefaultDemandID = "-1"
)

const (
	TestPlanRelationTypeUI   = "ui"
	TestPlanRelationTypeApi  = "api"
	TestPlanRelationTypeCase = "case"
)

const (
	// TestPLanStatusNotStart 未开始
	TestPLanStatusNotStart = "notStart"
	// TestPLanStatusRunning 进行中
	TestPLanStatusRunning = "running"
	// TestPLanStatusFinished 已完成
	TestPLanStatusFinished = "finished"
)

const (
	// TestPLanTypeCommon 普通计划
	TestPLanTypeCommon = "common"
	// TestPLanTypeDemand 需求计划
	TestPLanTypeDemand = "demand"
)

const (
	// TestPlanStageFunctionTesting 功能测试
	TestPlanStageFunctionTesting = "functionTesting"
	// TestPlanStageUnitTesting 单元测试
	TestPlanStageUnitTesting = "unitTesting"
	// TestPlanStageSmokeTesting 冒烟测试
	TestPlanStageSmokeTesting = "smokeTesting"
	// TestPlanStageIntegrationTesting 集成测试
	TestPlanStageIntegrationTesting = "integrationTesting"
	// TestPlanStageRegressionTesting 回归测试
	TestPlanStageRegressionTesting = "regressionTesting"
	// TestPlanStageAcceptanceTesting 验收测试
	TestPlanStageAcceptanceTesting = "acceptanceTesting"
)

const (
	// 未测
	CaseResultTypeUntest = "untest"
	// 通过
	CaseResultTypePass = "pass"
	// 失败
	CaseResultTypeFail = "fail"
	// 受阻
	CaseResultTypeBlock = "block"
	// 重测
	CaseResultTypeAgain = "again"
)

const UiCallBackPath = "http://localhost:8080/mars/openapi/v1/ui/planCallback/%d"

func TestPlanResultType(result string) string {
	switch result {
	case CaseResultTypePass:
		return "通过"
	case CaseResultTypeFail:
		return "失败"
	case CaseResultTypeBlock:
		return "受阻"
	case CaseResultTypeAgain:
		return "重测"
	case CaseResultTypeUntest:
		return "未测"
	default:
		return ""
	}
}

func CheckTestPlanType(planType string) bool {
	return planType == TestPLanTypeCommon || planType == TestPLanTypeDemand
}

func CheckTestPlanStatus(status string) bool {
	return status == TestPLanStatusNotStart || status == TestPLanStatusRunning || status == TestPLanStatusFinished
}

func GetTestPLanRelationName(code string) string {
	switch code {
	case TestPlanRelationTypeUI:
		return "UI用例"
	case TestPlanRelationTypeApi:
		return "接口用例"
	case TestPlanRelationTypeCase:
		return "功能用例"
	default:
		return ""
	}
}
