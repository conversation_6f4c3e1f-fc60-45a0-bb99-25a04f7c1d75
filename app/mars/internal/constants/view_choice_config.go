package constants

import "git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"

var Public = "public"

var ViewChoiceConfigsMap = map[string][]models.ViewChoiceConfig{
	Public: {
		{
			FieldKey:  "reportBasicInformation",
			Label:     "基本信息",
			Value:     true,
			Width:     "100%",
			ClassType: "",
			Sort:      1,
		},
		{
			FieldKey:  "reportSummary",
			Label:     "报告总结",
			Value:     true,
			Width:     "100%",
			ClassType: "",
			Sort:      2,
		},
		{
			FieldKey:  "DefectStateDistributionPie",
			Label:     "缺陷状态分布",
			Value:     true,
			Width:     "calc(50% - 16px)",
			ClassType: "echarts",
			Group:     "bug",
			ChartType: "pie",
			Sort:      9,
		},
		{
			FieldKey:  "DefectDiscoveryContributionBar",
			Label:     "缺陷发现贡献",
			Value:     true,
			Width:     "calc(50% - 16px)",
			ClassType: "echarts",
			Group:     "bug",
			ChartType: "column",
			Sort:      10,
		},
		{
			FieldKey:  "bugReportTable",
			Label:     "缺陷列表",
			Value:     true,
			Width:     "100%",
			TableType: "bug",
			ClassType: "table",
			Group:     "bug",
			ChartType: "list",
			Sort:      15,
		},
	},
	TestPLanTypeDemand: {
		{
			FieldKey:  "demandReportTable",
			Label:     "需求列表",
			Value:     true,
			Width:     "100%",
			TableType: "demand",
			ClassType: "table",
			Group:     "other",
			ChartType: "list",
			Sort:      14,
		},
	},
	TestPlanRelationTypeCase: {
		{
			FieldKey:  "funcCaseTestExecutionPie",
			Label:     "功能用例测试执行",
			Value:     true,
			Width:     "calc(50% - 16px)",
			Type:      TestPlanRelationTypeCase,
			ClassType: "echarts",
			Group:     "testExecution",
			ChartType: "pie",
			Sort:      3,
		},
		{
			FieldKey:  "funcCaseTestResultsPie",
			Label:     "功能用例测试结果",
			Value:     true,
			Width:     "calc(50% - 16px)",
			Type:      TestPlanRelationTypeCase,
			ClassType: "echarts",
			Group:     "testResult",
			ChartType: "pie",
			Sort:      4,
		},
		{
			FieldKey:  "marsReportTable",
			Label:     "功能用例列表",
			Value:     true,
			Width:     "100%",
			Type:      TestPlanRelationTypeCase,
			TableType: "mars",
			ClassType: "table",
			Group:     "caseList",
			ChartType: "list",
			Sort:      11,
		},
	},
	TestPlanRelationTypeApi: {
		{
			FieldKey:  "jupiterTestExecutionPie",
			Label:     "接口自动化测试执行",
			Value:     true,
			Width:     "calc(50% - 16px)",
			Type:      TestPlanRelationTypeApi,
			ClassType: "echarts",
			Group:     "testExecution",
			ChartType: "pie",
			Sort:      5,
		},
		{
			FieldKey:  "jupiterTestResultsPie",
			Label:     "接口自动化测试结果",
			Value:     true,
			Width:     "calc(50% - 16px)",
			Type:      TestPlanRelationTypeApi,
			ClassType: "echarts",
			Group:     "testResult",
			ChartType: "pie",
			Sort:      6,
		},
		{
			FieldKey:  "jupiterReportTable",
			Label:     "接口自动化用例列表",
			Value:     true,
			Width:     "100%",
			Type:      TestPlanRelationTypeApi,
			TableType: "jupiter",
			ClassType: "table",
			Group:     "caseList",
			ChartType: "list",
			Sort:      12,
		},
	},
	TestPlanRelationTypeUI: {
		{
			FieldKey:  "venusTestExecutionPie",
			Label:     "UI自动化测试执行",
			Value:     true,
			Width:     "calc(50% - 16px)",
			Type:      TestPlanRelationTypeUI,
			ClassType: "echarts",
			Group:     "testExecution",
			ChartType: "pie",
			Sort:      7,
		},
		{
			FieldKey:  "venusTestResultsPie",
			Label:     "UI自动化测试结果",
			Value:     true,
			Width:     "calc(50% - 16px)",
			Type:      TestPlanRelationTypeUI,
			ClassType: "echarts",
			Group:     "testResult",
			ChartType: "pie",
			Sort:      8,
		},
		{
			FieldKey:  "venusReportTable",
			Label:     "UI自动化计划列表",
			Value:     true,
			Width:     "100%",
			Type:      TestPlanRelationTypeUI,
			TableType: "venus",
			ClassType: "table",
			Group:     "caseList",
			ChartType: "list",
			Sort:      13,
		},
	},
}
