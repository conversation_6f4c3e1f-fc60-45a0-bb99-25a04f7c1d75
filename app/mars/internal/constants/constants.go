package constants

const (
	CasePriorityPO = "P0"
	CasePriorityP1 = "P1"
	CasePriorityP2 = "P2"
	CasePriorityP3 = "P3"

	CopySuffix = "(副本)"

	MovePositionPre  = -1
	MovePositionIn   = 0
	MovePositionPost = 1

	MarsCaseExecuteRecord = "mars_case_execute_record"
	MarsCaseSnapshot      = "mars_case_snapshot"
	MarsDemandSnapshot    = "mars_demand_snapshot"
	MarsBugSnapshot       = "mars_bug_snapshot"

	MarsCaseLibraryShare = 1
	// MarsNodeTypeCase  用例节点
	MarsNodeTypeCase = "case"
	// MarsNodeTypeFolder  用例目录节点
	MarsNodeTypeFolder = "folder"
	MarsNodeTypeLib    = "lib"
	// NotSiblingId 兄弟节点不存在
	NotSiblingId = -1
)
