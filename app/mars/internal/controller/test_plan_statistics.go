package controller

import (
	"fmt"
	"net/url"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type MarsTestPlanStatisticsIdInPathReq struct {
	Id   int64  `path:"id"`
	Type string `path:"type"`
}

type TestPlanStatisticsController struct {
	*controller.BaseController
}

var (
	DefaultTestPlanStatisticsController TestPlanStatisticsController
	testPlanStatisticsService           service.TestPlanStatisticsService
)

func (c *TestPlanStatisticsController) GetTestPlanList(req dto.MarsReportSearchReq, pageReq gormx.PageRequest) {
	res, err := testPlanStatisticsService.GetReportList(c.<PERSON>xt, req, pageReq)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *TestPlanStatisticsController) Delete(req MarsTestPlanStatisticsIdInPathReq) {
	err := testPlanStatisticsService.Delete(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

type UpdateReportReq struct {
	Name    string `json:"name"`
	Summary string `json:"summary"`
}

func (c *TestPlanStatisticsController) Update(path MarsTestPlanStatisticsIdInPathReq, req UpdateReportReq) {
	err := testPlanStatisticsService.Update(c.MantisContext, path.Id, req.Name, req.Summary)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *TestPlanStatisticsController) Info(req MarsTestPlanStatisticsIdInPathReq) {
	res, err := testPlanStatisticsService.Info(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *TestPlanStatisticsController) SendReportEmail(req dto.MarsEmailReportReq) {
	testPlanStatisticsService.SendReportMail(c.MantisContext, req)
	c.ResSuccess()
}

func (c *TestPlanStatisticsController) DownloadReport(path MarsTestPlanStatisticsIdInPathReq, req dto.DownloadReportReq) {
	fileName, data, err := testPlanStatisticsService.DownloadReport(c.MantisContext, path.Type, req)
	if err != nil {
		c.ResFail(err)
		return
	}
	contentType := "application/octet-stream"
	c.ResponseWriter.Header().Set("Content-Type", contentType)
	fileName = url.QueryEscape(fileName)
	c.ResponseWriter.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	c.ResponseWriter.Header().Set("Content-Transfer-Encoding", "binary")
	c.ResponseWriter.Write(data)
}
