package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type ReviewIdInPathReq struct {
	Id int64 `path:"id"`
}

type ReviewAndCaseIdInSchemaReq struct {
	ReviewId int64 `schema:"reviewId"`
	CaseId   int64 `schema:"caseId"`
}

type ReviewController struct {
	*controller.BaseController
}

var (
	DefaultReviewController ReviewController
	reviewService           service.ReviewService
)

func (c *ReviewController) List(req dto.MarsReviewSearchReqDTO, pageReq gormx.PageRequest) {
	req.SpaceId = c.Request.Header.Get("spaceid")
	res, err := reviewService.Search(c.Mantis<PERSON>ontext, req, pageReq)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *ReviewController) Insert(review *models.MarsReview) {
	review.SpaceId = c.Request.Header.Get("spaceid")
	res, err := reviewService.Insert(c.MantisContext, review)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *ReviewController) Info(req ReviewIdInPathReq) {
	res, err := reviewService.Info(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *ReviewController) Update(review *models.MarsReview) {
	err := reviewService.Update(c.MantisContext, review)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *ReviewController) Delete(req ReviewIdInPathReq) {
	err := reviewService.Delete(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *ReviewController) Start(req dto.MarsReviewStatusUpdateReq) {
	err := reviewService.StartReview(c.MantisContext, req)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *ReviewController) Finish(req dto.MarsReviewStatusUpdateReq) {
	err := reviewService.FinishReview(c.MantisContext, req)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *ReviewController) RelateCase(req dto.MarsReviewCaseReq) {
	err := reviewService.RelateCase(c.MantisContext, req)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *ReviewController) CaseListByDemand(req ReviewAndCaseIdInSchemaReq) {
	res, err := reviewService.CaseListByDemand(c.MantisContext, req.ReviewId)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *ReviewController) GetCaseComments(req *ReviewAndCaseIdInSchemaReq) {
	res, err := reviewService.GetCaseComments(c.MantisContext, req.ReviewId, req.CaseId)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *ReviewController) RemoveCase(req dto.MarsReviewCaseReq) {
	err := reviewService.RemoveCase(c.MantisContext, req.ReviewId, req.CaseIds)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *ReviewController) SaveReviewComment(req dto.MarsReviewCommentDTO) {
	err := reviewService.SaveReviewComment(c.MantisContext, req)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *ReviewController) GetReviewComment(req ReviewIdInPathReq) {
	res, err := reviewService.GetReviewComment(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *ReviewController) SaveReviewCaseComment(comment dto.MarsReviewCaseComment) {
	err := reviewService.SaveReviewCaseComment(c.MantisContext, comment)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}
