package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type MarsTestPlanIdInPathReq struct {
	Id         int64 `path:"id"`
	RelationId int64 `path:"relationId"`
}

type MarsTestPlanStatusReq struct {
	Status string `json:"status"`
}

type MarsTestPlanUpdateModelsReq struct {
	Models []string `json:"models"`
}

type MarsTestPlanRemoveDemandReq struct {
	Id       int64  `path:"id"`
	DemandId string `path:"demandId"`
}

type MarsTestPlanRemoveReq struct {
	RelationIds []int64  `json:"ids"`
	Type        string   `json:"type"` // case/ui/api
	DemandIds   []string `json:"demandIds"`
}

type MarsTestPlanRelateDemandReq struct {
	TestPlanId int64                          `json:"testPlanId"`
	Demands    []dto.MarsTestPlanDemandReqDTO `json:"demands"`
	Type       string                         `json:"type"` // case/ui/api
}

type TestPlanController struct {
	*controller.BaseController
}

var (
	DefaultTestPlanController TestPlanController
	testPlanService           service.TestPlanService
)

func (c *TestPlanController) GetTestPlanList(req dto.MarsTestPlanListReq, pageReq gormx.PageRequest) {
	res, err := testPlanService.GetTestPlanList(c.MantisContext, req, pageReq, c.GetProjectNo())
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *TestPlanController) Insert(plan models.MarsTestPlanDTO) {
	res, err := testPlanService.Insert(c.MantisContext, plan)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *TestPlanController) UpdateStatus(id MarsTestPlanIdInPathReq, status MarsTestPlanStatusReq) {
	err := testPlanService.UpdateStatus(c.MantisContext, id.Id, status.Status)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *TestPlanController) Update(id MarsTestPlanIdInPathReq, plan models.MarsTestPlanDTO) {
	plan.Id = id.Id
	err := testPlanService.Update(c.MantisContext, plan)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *TestPlanController) Copy(req MarsTestPlanIdInPathReq) {
	id, err := testPlanService.Copy(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(id)
	}
}

// GetPlanDemand 获取计划下的需求
func (c *TestPlanController) GetPlanDemand(path MarsTestPlanIdInPathReq, req dto.MarsDemandReq) {
	if req.PageSize < 1 {
		req.PageSize = 10
	}
	if req.Page < 1 {
		req.Page = 1
	}
	data, err := testPlanService.GetPlanDemand(c.MantisContext, path.Id, req)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(data)
	}
}

func (c *TestPlanController) SyncDemand(req MarsTestPlanIdInPathReq, reqBody MarsTestPlanRemoveReq) {
	err := testPlanService.SyncDemand(c.MantisContext, req.Id, reqBody.DemandIds)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *TestPlanController) Info(req MarsTestPlanIdInPathReq) {
	res, err := testPlanService.Info(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *TestPlanController) Delete(req MarsTestPlanIdInPathReq) {
	err := testPlanService.Delete(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *TestPlanController) RelateCase(planId MarsTestPlanIdInPathReq, req dto.MarsTestPlanRelateCaseReq) {
	req.TestPlanId = planId.Id
	err := testPlanService.RelateCase(c.MantisContext, req)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *TestPlanController) GetModel(req MarsTestPlanIdInPathReq) {
	res, err := testPlanService.GetModel(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *TestPlanController) UpdateModel(path MarsTestPlanIdInPathReq, req MarsTestPlanUpdateModelsReq) {
	err := testPlanService.UpdateModel(c.MantisContext, path.Id, req.Models)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *TestPlanController) CaseList(path MarsTestPlanIdInPathReq, req dto.MarsTestPlanCaseListReq, pageReq gormx.PageRequest) {
	req.TestPlanId = path.Id
	res, err := testPlanService.CaseList(c.MantisContext, req, pageReq)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

// AssignExecutors 给用例分配执行人
func (c *TestPlanController) AssignExecutors(reqId MarsTestPlanIdInPathReq, req dto.MarsTestPlanCaseBatchUpdateReq) {
	req.TestPlanId = reqId.Id
	err := testPlanService.AssignExecutors(c.MantisContext, req)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

// UpdateResult 给用例设置执行结果
func (c *TestPlanController) UpdateResult(reqId MarsTestPlanIdInPathReq, req dto.MarsTestPlanCaseBatchUpdateReq) {
	req.TestPlanId = reqId.Id
	err := testPlanService.UpdateResult(c.MantisContext, req)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

// DeleteCasesBatch 批量删除用例
func (c *TestPlanController) DeleteCasesBatch(planId MarsTestPlanIdInPathReq, req MarsTestPlanRemoveReq) {
	err := testPlanService.DeleteCasesBatch(c.MantisContext, planId.Id, req.RelationIds)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *TestPlanController) DeleteDemandBatch(planId MarsTestPlanIdInPathReq, req MarsTestPlanRemoveReq) {
	err := testPlanService.DeleteDemandBatch(c.MantisContext, planId.Id, req.DemandIds, req.Type)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *TestPlanController) MoveCase(path MarsTestPlanIdInPathReq, req dto.MarsTestPlanCaseMoveReq) {
	err := testPlanService.MoveCase(c.MantisContext, path.Id, req)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *TestPlanController) RelateDemand(planId MarsTestPlanIdInPathReq, req MarsTestPlanRelateDemandReq) {
	req.TestPlanId = planId.Id
	err := testPlanService.RelateDemand(c.MantisContext, req.Demands, req.TestPlanId, req.Type)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *TestPlanController) FastCreate(planId MarsTestPlanIdInPathReq, caseDTO dto.TestPlanCaseDto) {
	caseDTO.TestPlanId = planId.Id
	err := caseService.FastCreate(c.MantisContext, caseDTO)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *TestPlanController) GetFunctionCaseInfo(req MarsTestPlanIdInPathReq) {
	data, err := testPlanService.GetFunctionCaseInfo(c.MantisContext, req.Id, req.RelationId)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(data)
	}
}

func (c *TestPlanController) UpdateFunctionCaseInfo(req MarsTestPlanIdInPathReq, planCase *dto.TestPlanCase) {
	err := testPlanService.UpdateFunctionCaseInfo(c.MantisContext, req.Id, req.RelationId, planCase)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

// ExecuteCase 用例执行
func (c *TestPlanController) ExecuteCase(path MarsTestPlanIdInPathReq, req dto.MarsCaseExecReq) {
	err := testPlanService.ExecuteCase(c.MantisContext, path.Id, req)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

type HistoryReportReq struct {
	Type       string `schema:"type"`       // 类型
	RelationId string `schema:"relationId"` // 关联用例的 id
}

// HistoryReport 查看历史报告
func (c *TestPlanController) HistoryReport(path MarsTestPlanIdInPathReq, req HistoryReportReq, pageReq gormx.PageRequest) {
	res, err := testPlanService.HistoryReport(c.MantisContext, path.Id, req.Type, req.RelationId, pageReq)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *TestPlanController) ReportView(path MarsTestPlanIdInPathReq) {
	res, err := testPlanService.ReportView(c.MantisContext, path.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *TestPlanController) SaveReportView(path MarsTestPlanIdInPathReq, report models.MarsTestPlanReport) {
	report.TestPlanId = path.Id
	res, err := testPlanService.SaveReportView(c.MantisContext, report)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}
