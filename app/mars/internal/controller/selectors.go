package controller

import (
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/remote"
)

type SelectorsController struct {
	*controller.BaseController
}

type SearchDemandsReq struct {
	Search      string `schema:"search"`      // 需求名称
	Code        string `schema:"code"`        // 需求编号
	IterationId string `schema:"iterationId"` // 迭代id
	Page        int64  `schema:"page"`
	PageSize    int64  `schema:"pageSize"`
}

type SearchTagInLibReq struct {
	LibId  int64  `schema:"libraryId"`
	Search string `schema:"search"`
}

var (
	DefaultSelectorsController SelectorsController
	projectServer              service.ProjectService
)

func (c *SelectorsController) SearchDemands(req SearchDemandsReq) {
	tmReq := dto.ProjectCommonSelectReq{
		Code:        req.Code,
		Search:      req.Search,
		IterationId: req.IterationId,
		ProjectId:   c.ProjectNo,
	}
	list, total, err := projectServer.GetDemandsByPage(c.MantisContext, tmReq, gormx.PageRequest{Page: req.Page, PageSize: req.PageSize})
	if err != nil {
		c.ResFail(err)
		return
	}
	page := gormx.PageResult{
		CurrentPage: req.Page,
		PageSize:    req.PageSize,
		Total:       total,
		List:        list,
	}
	c.ResSuccessResult(page)
}

func (c *SelectorsController) SearchIteration(req SearchDemandsReq) {
	tmReq := dto.ProjectCommonSelectReq{
		Search:    req.Search,
		ProjectId: c.ProjectNo,
	}
	list, err := projectServer.GetIterationList(c.MantisContext, tmReq, gormx.PageRequest{Page: req.Page, PageSize: req.PageSize})
	if err != nil {
		c.ResFail(err)
		return
	}
	c.ResSuccessResult(list)
}

func (c *SelectorsController) SearchTagInLib(req SearchTagInLibReq) {
	marsCases := make([]models.MarsCase, 0)
	err := gormx.SelectByParamBuilder(c.MantisContext, gormx.NewParamBuilder().Model(&models.MarsCase{}).Eq("library_id", req.LibId).Eq("is_deleted", constants.DeleteNo), &marsCases)
	if err != nil {
		c.ResFail(err)
		return
	}
	tagIds := make([]string, 0)
	for _, c := range marsCases {
		tagIds = append(tagIds, c.TagIds...)
	}
	list, err := remote.CubeBaseRemoteApi{}.GetTagsByIds(c.MantisContext, strings.Join(tagIds, ","))
	if err != nil {
		c.ResFail(err)
		return
	}
	res := make([]commondto.CubeTagDTO, 0)
	for _, tag := range list {
		if strings.Contains(tag.Label, req.Search) {
			res = append(res, tag)
		}
	}
	c.ResSuccessResult(res)
}
