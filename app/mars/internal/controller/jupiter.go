package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/remote"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

type JupiterController struct {
	*controller.BaseController
}

type ReqSearch struct {
	Name      string `schema:"name"`
	BaseId    int64  `schema:"baseId"`
	VersionId int64  `schema:"versionId"`
	NodeId    int64  `schema:"nodeId"` // 目录 id
}
type IdPath struct {
	Id int64 `path:"id"`
}

var (
	DefaultJupiterController JupiterController
	aptRemoteApi             remote.AptRemoteApi
)

// GetLibrary 查询用例库
func (c *JupiterController) GetLibrary(req ReqSearch) {
	res, err := aptRemoteApi.GetLibrary(c.MantisContext, req.Name)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

// GetLibraryVersion 查询用例库版本
func (c *JupiterController) GetLibraryVersion(req ReqSearch) {
	res, err := aptRemoteApi.GetLibraryVersion(c.MantisContext, req.BaseId)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

// GetFolderTree 查询用例库下的目录树
func (c *JupiterController) GetFolderTree(req ReqSearch) {
	res, err := aptRemoteApi.GetTreeWithSet(c.MantisContext, req.BaseId, req.VersionId)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

// GetCaseList 获取用例列表
func (c *JupiterController) GetCaseList(req ReqSearch) {
	res, err := aptRemoteApi.GetCaseListByPidAndName(c.MantisContext, req.NodeId, req.Name)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}
