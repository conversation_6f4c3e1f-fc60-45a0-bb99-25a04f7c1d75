package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type CaseLibraryIdInSchemaReq struct {
	LibraryId int64 `schema:"libraryId"`
}

type CaseLibraryIdInPathReq struct {
	Id int64 `path:"id"`
}

type CaseLibraryCopyReq struct {
	LibraryId int64 `json:"libraryId"`
}

type CaseLibraryController struct {
	*controller.BaseController
}

var (
	DefaultCaseLibraryController CaseLibraryController
	caseLibraryService           service.CaseLibraryService
)

func (c *CaseLibraryController) InsertCaseLibrary(library *models.MarsCaseLibrary) {
	res, err := caseLibraryService.Insert(c.<PERSON>, library)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *CaseLibraryController) UpdateCaseLibrary(library *models.MarsCaseLibrary) {
	res, err := caseLibraryService.Update(c.MantisContext, library)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *CaseLibraryController) CopyCaseLibrary(req *CaseLibraryCopyReq) {
	res, err := caseLibraryService.Copy(c.MantisContext, req.LibraryId)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *CaseLibraryController) DeleteCaseLibrary(req CaseLibraryIdInPathReq) {
	err := caseLibraryService.DeleteCaseLibrary(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *CaseLibraryController) SearchCaseLibraryList(req dto.CaseLibrarySearchReqDto, pageReq gormx.PageRequest) {
	res, err := caseLibraryService.SearchCaseLibraryList(c.MantisContext, c.Request.Header.Get("spaceid"), req, pageReq)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}
