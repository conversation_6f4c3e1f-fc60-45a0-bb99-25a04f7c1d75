package controller

import (
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/enums"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

type EnumsController struct {
	*controller.BaseController
}

var DefaultEnumsController EnumsController

type EnumsRes struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

func (c *EnumsController) Priority() {
	c.ResSuccessResult([]EnumsRes{
		{Label: "P0", Value: "P0"},
		{Label: "P1", Value: "P1"},
		{Label: "P2", Value: "P2"},
		{Label: "P3", Value: "P3"},
	})
}

func (c *EnumsController) CaseReviewStatus() {
	c.ResSuccessResult([]enums.MarsReviewCaseStatusEnum{
		enums.NotReview, enums.PassReview, enums.FailReview,
	})
}

func (c *EnumsController) ReviewStatus() {
	c.ResSuccessResult([]enums.MarsReviewStatusEnum{
		enums.ReviewNotStart, enums.ReviewRunning, enums.ReviewFinished,
	})
}

func (c *EnumsController) PlanType() {
	c.ResSuccessResult([]EnumsRes{
		{Label: "普通测试计划", Value: constants.TestPLanTypeCommon},
		{Label: "需求测试计划", Value: constants.TestPLanTypeDemand},
	})
}

func (c *EnumsController) PlanStatus() {
	c.ResSuccessResult([]EnumsRes{
		{Label: "未开始", Value: constants.TestPLanStatusNotStart},
		{Label: "进行中", Value: constants.TestPLanStatusRunning},
		{Label: "结束", Value: constants.TestPLanStatusFinished},
	})
}

func (c *EnumsController) PlanRelationType() {
	c.ResSuccessResult([]EnumsRes{
		{Label: "UI 计划", Value: constants.TestPlanRelationTypeUI},
		{Label: "API 用例", Value: constants.TestPlanRelationTypeApi},
		{Label: "功能用例", Value: constants.TestPlanRelationTypeCase},
	})
}

func (c *EnumsController) PlanStage() {
	c.ResSuccessResult([]EnumsRes{
		{Label: "功能测试", Value: constants.TestPlanStageFunctionTesting},
		{Label: "集成测试", Value: constants.TestPlanStageIntegrationTesting},
		{Label: "单元测试", Value: constants.TestPlanStageUnitTesting},
		{Label: "冒烟测试", Value: constants.TestPlanStageSmokeTesting},
		{Label: "验收测试", Value: constants.TestPlanStageAcceptanceTesting},
		{Label: "回归测试", Value: constants.TestPlanStageRegressionTesting},
	})
}

func (c *EnumsController) PlanModels() {
	planModels := []EnumsRes{
		{Label: "功能用例", Value: constants.TestPlanRelationTypeCase},
	}
	if configs.Config.Modules.Jupiter.Enable {
		planModels = append(planModels, EnumsRes{Label: "接口用例", Value: constants.TestPlanRelationTypeApi})
	}
	if configs.Config.Modules.Venus.Enable {
		planModels = append(planModels, EnumsRes{Label: "UI用例", Value: constants.TestPlanRelationTypeUI})
	}
	c.ResSuccessResult(planModels)
}

func (c *EnumsController) CaseResult() {
	c.ResSuccessResult([]EnumsRes{
		{Label: "通过", Value: constants.CaseResultTypePass},
		{Label: "失败", Value: constants.CaseResultTypeFail},
		{Label: "阻塞", Value: constants.CaseResultTypeBlock},
		{Label: "重测", Value: constants.CaseResultTypeAgain},
		{Label: "未测", Value: constants.CaseResultTypeUntest},
	})
}

func (c *EnumsController) TmPriority() {
	levels, err := projectService.GetPriorityLevel(c.MantisContext)
	if err != nil {
		c.ResFail(err)
		return
	}
	res := make([]EnumsRes, len(levels))
	for i, level := range levels {
		res[i] = EnumsRes{
			Label: level.Name,
			Value: strconv.Itoa(level.Id),
		}
	}
	c.ResSuccessResult(res)
}

func (c *EnumsController) TmEnable() {
	c.ResSuccessResult(configs.Config.Modules.Mars.TmEnable)
}
