package openapi

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto/ui"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type UiOpenController struct {
	*controller.BaseController
}

type HisIdPath struct {
	HisId int64 `path:"hisId"`
}

var (
	DefaultUiOpenController UiOpenController
	uiCallbackService       service.UICallbackService
)

func (c *UiOpenController) PlanCallBack(path HisIdPath, req ui.MarsUiPlanExecResult) {
	logger.Logger.Infof("UI执行机回调信息,hidId=%d,req=%+v", path.HisId, req)
	err := uiCallbackService.DealPlanCallback(c.<PERSON>xt, path.HisId, req)
	if err != nil {
		c.<PERSON>s<PERSON>ail(err)
	}
	c.Res<PERSON>uc<PERSON>R<PERSON>ult(nil)
}
