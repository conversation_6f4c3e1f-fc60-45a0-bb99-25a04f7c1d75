package openapi

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/remote"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type JupiterOpenController struct {
	*controller.BaseController
}

var (
	DefaultJupiterOpenController JupiterOpenController
	openapiService               service.OpenapiService
	caseService                  service.CaseService
	aptCallbackService           service.AptCallbackService
)

type IdPath struct {
	Id int64 `path:"id"`
}

func (c *JupiterOpenController) ModifyCaseResult(req remote.AptCaseExecCallback) {
	err := aptCallbackService.ModifyCaseResult(c.<PERSON>, req)
	if err != nil {
		c.<PERSON>s<PERSON>(err)
	}
	c.Res<PERSON>()
}

func (c *JupiterOpenController) GetPlan(path IdPath) {
	p := models.MarsTestPlan{}
	p.Id = path.Id
	err := gormx.SelectOneByCondition(c.MantisContext, &p)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(map[string]any{
			"id":   p.Id,
			"name": p.Name,
		})
	}
}

type DemandIdInSchemaReq struct {
	DemandId int64 `schema:"demandId"`
}

type RelateCaseInSchemaReq struct {
	DemandId string  `schema:"demandId"`
	Ids      []int64 `schema:"ids"`
}

type CancelRelateDemandReq struct {
	DemandId string `path:"demandId"`
	CaseId   int64  `path:"caseId"`
}

// todo 待删除
func (c *JupiterOpenController) GetCaseListPage(req DemandIdInSchemaReq, pageReq gormx.PageRequest) {
	res, err := openapiService.GetCaseListByDemand(c.MantisContext, req.DemandId, pageReq)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

// todo 待删除
func (c *JupiterOpenController) RelateCase(req RelateCaseInSchemaReq) {
	err := caseService.RelateCase(c.MantisContext, req.DemandId, req.Ids)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

// todo 待删除
func (c *JupiterOpenController) CancelRelateDemand(req CancelRelateDemandReq) {
	err := caseService.CancelRelatedDemand(c.MantisContext, []int64{req.CaseId}, req.DemandId)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}
