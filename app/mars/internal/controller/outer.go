package controller

import (
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto/outer"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type OuterController struct {
	*controller.BaseController
}

var DefaultOuterController OuterController

type CaseListRequest struct {
	DemandId int64 `schema:"demandId"`
	Page     int64 `schema:"page"`
	PageSize int64 `schema:"pageSize"`
}

type RelateCaseRequest struct {
	DemandId int64   `json:"demandId"` // 需求ID
	CaseIds  []int64 `json:"ids"`      // 用例ID列表
}

type SearchRequest struct {
	Name    string `schema:"name"`
	SpaceId string `schema:"spaceId"`
}

type SearchInLibReq struct {
	Name      string `schema:"name"`
	LibraryId int64  `schema:"libraryId"`
	Page      int64  `schema:"page"`
	PageSize  int64  `schema:"pageSize"`
}

func (c *OuterController) CaseList(req CaseListRequest) {
	pageResult, err := caseService.CaseListByDemand(c.MantisContext, req.DemandId, gormx.PageRequest{
		Page:     req.Page,
		PageSize: req.PageSize,
	})
	if err != nil {
		c.ResFail(err)
		return
	}
	c.ResSuccessResult(pageResult)
}

// RelateCase 需求关联用例
func (c *OuterController) RelateCase(req RelateCaseRequest) {
	err := caseService.RelateCase(c.MantisContext, strconv.FormatInt(req.DemandId, 10), req.CaseIds)
	if err != nil {
		c.ResFail(err)
		return
	}
	c.ResSuccess()
}

// CancelCaseList 需求解除用例关联
func (c *OuterController) CancelCaseList(req RelateCaseRequest) {
	err := caseService.CancelRelatedDemand(c.MantisContext, req.CaseIds, strconv.FormatInt(req.DemandId, 10))
	if err != nil {
		c.ResFail(err)
		return
	}
	c.ResSuccess()
}

func (c *OuterController) GetLibrary(req SearchRequest) {
	res, err := caseLibraryService.SearchLibrary(c.MantisContext, req.SpaceId, req.Name)
	dataList := make([]map[string]any, 0)
	for _, v := range res {
		dataList = append(dataList, map[string]any{
			"value": v.Id,
			"label": v.Name,
		})
	}
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(dataList)
	}
}

func (c *OuterController) SearchInLib(req SearchInLibReq) {
	res, err := caseService.SearchInLib(c.MantisContext, req.Name, req.LibraryId, gormx.PageRequest{
		Page:     req.Page,
		PageSize: req.PageSize,
	})
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *OuterController) GetFolderTree(req SearchInLibReq) {
	res, err := nodeService.GetTree(c.MantisContext, req.LibraryId)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *OuterController) AddBatchAndRelateDemand(req outer.AddBatchAndRelateDemandReq) {
	err := caseService.AddBatchAndRelateDemand(c.MantisContext, req)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}
