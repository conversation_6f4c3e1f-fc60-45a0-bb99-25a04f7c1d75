package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto/project"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type BugController struct {
	*controller.BaseController
}

type BugSearch struct {
	Search        string  `schema:"search"`
	Code          string  `schema:"code"`
	PriorityLevel []int64 `schema:"priorityLevel"`
	Page          int64   `schema:"page"`
	PageSize      int64   `schema:"pageSize"`
}

var (
	DefaultBugController BugController
	bugService           service.BugService
	projectService       service.ProjectService
)

func (c *BugController) Add(req project.MarsBugReq) {
	err := bugService.AddBug(c.Mantis<PERSON>ontext, req)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *BugController) BugList(req project.MarsBugListReq) {
	res, err := bugService.BugList(c.MantisContext, req)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *BugController) GetProjectBugs(pageReq gormx.PageRequest, tmReq BugSearch) {
	commonSearch := dto.ProjectCommonSelectReqInSchema{}
	if tmReq.Search != "" {
		commonSearch.Search = tmReq.Search
	}
	if tmReq.Code != "" {
		commonSearch.Code = tmReq.Code
	}
	if len(tmReq.PriorityLevel) > 0 {
		commonSearch.PriorityLevel = tmReq.PriorityLevel
	}
	if tmReq.Page < 1 {
		tmReq.Page = 1
	}
	if tmReq.PageSize < 10 {
		tmReq.PageSize = 10
	}
	res, err := projectService.GetBugList(c.MantisContext, pageReq, commonSearch)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *BugController) Remove(req project.MarsBugRemoveReq) {
	err := bugService.Remove(c.MantisContext, req)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}
