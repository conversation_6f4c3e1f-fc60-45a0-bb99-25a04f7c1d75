package controller

import (
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type NodeHierarchyIdInPathReq struct {
	Id int64 `path:"id"`
}

type NodeHierarchyLibIdInPathReq struct {
	LibId int64 `path:"libId"`
}

type NodeCopyReq struct {
	Id int64 `json:"id"`
}

type NodeHierarchyController struct {
	*controller.BaseController
}

var (
	DefaultNodeHierarchyController NodeHierarchyController
	nodeService                    service.NodeService
)

func (c *NodeHierarchyController) AddNode(node dto.NodeDto) {
	res, err := nodeService.AddNode(c.<PERSON>t, node)
	if err != nil {
		c.<PERSON>s<PERSON>(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *NodeHierarchyController) Copy(req NodeCopyReq) {
	err := nodeService.CopyNode(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *NodeHierarchyController) CheckDeleteNode(req NodeHierarchyIdInPathReq) {
	res, err := nodeService.CheckDelete(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *NodeHierarchyController) DeleteNode(req NodeHierarchyIdInPathReq) {
	err := nodeService.DeleteNode(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *NodeHierarchyController) Update(node dto.NodeDto) {
	err := nodeService.Update(c.MantisContext, node)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *NodeHierarchyController) Move(moveDTO dto.MarsNodeMoveDTO) {
	err := nodeService.Move(c.MantisContext, moveDTO)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *NodeHierarchyController) GetTree(req NodeHierarchyLibIdInPathReq) {
	res, err := nodeService.GetTree(c.MantisContext, req.LibId)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *NodeHierarchyController) GetCaseList(req dto.MarsCaseListReqDTO, pageRequest gormx.PageRequest) {
	if req.PriorityStr != "" {
		req.Priority = strings.Split(req.PriorityStr, ",")
	}
	if req.TagIdsStr != "" {
		req.TagIds = strings.Split(req.TagIdsStr, ",")
	}
	resp, err := nodeService.GetCaseList(c.MantisContext, req, pageRequest)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(resp)
	}
}
