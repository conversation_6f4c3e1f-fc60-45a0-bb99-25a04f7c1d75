package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

type MarsCaseIdInPathReq struct {
	Id int64 `path:"id"`
}

type MarsCaseIdInSchemaReq struct {
	CaseId   int64 `schema:"caseId"`
	ReviewId int64 `schema:"reviewId"`
}

type MarsRelateTestPlanReq struct {
	Ids  []int64 `schema:"ids"`
	Code string  `schema:"code"`
}

type MarsCaseCheckDeleteReq struct {
	Ids []int64 `json:"ids"`
}

type MarsSearchInLibReq struct {
	Name      string `schema:"name"`
	LibraryId int64  `schema:"libraryId"`
}

type MarsBatchInsertReq struct {
	Cases []*models.MarsCase `json:"cases"`
}

type CaseController struct {
	*controller.BaseController
}

var (
	DefaultCaseController CaseController
	caseService           service.CaseService
)

func (c *CaseController) Insert(marsCase *models.MarsCase) {
	caseModel, err := caseService.Insert(c.MantisContext, marsCase)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(caseModel)
	}
}

func (c *CaseController) Copy(req MarsCaseIdInPathReq) {
	err := caseService.Copy(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *CaseController) Move(req dto.MarsCaseMoveCopyReqDTO) {
	err := caseService.MoveCase(c.MantisContext, req)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *CaseController) BatchCopy(req dto.MarsCaseMoveCopyReqDTO) {
	err := caseService.BatchCopy(c.MantisContext, req)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *CaseController) Info(req MarsCaseIdInPathReq) {
	res, err := caseService.Info(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *CaseController) Update(marsCase *models.MarsCase) {
	res, err := caseService.Update(c.MantisContext, marsCase)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *CaseController) HistoryList(req MarsCaseIdInPathReq) {
	res, err := caseService.HistoryList(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *CaseController) HistoryDetail(req MarsCaseIdInPathReq) {
	res, err := caseService.HistoryDetail(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *CaseController) CheckDelete(req MarsCaseCheckDeleteReq) {
	res, err := caseService.CheckDelete(c.MantisContext, req.Ids)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(res)
	}
}

func (c *CaseController) Delete(req MarsCaseIdInPathReq) {
	err := caseService.Delete(c.MantisContext, req.Id)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}

func (c *CaseController) BatchDelete(req MarsCaseCheckDeleteReq) {
	err := caseService.BatchDelete(c.MantisContext, req.Ids)
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccess()
	}
}
