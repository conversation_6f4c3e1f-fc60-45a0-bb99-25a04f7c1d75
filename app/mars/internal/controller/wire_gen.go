package controller

import (
	"net/http"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/controller/openapi"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

func InitializeCaseLibraryController(rw http.ResponseWriter, req *http.Request) (*CaseLibraryController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &CaseLibraryController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeCaseController(rw http.ResponseWriter, req *http.Request) (*CaseController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &CaseController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeNodeHierarchyController(rw http.ResponseWriter, req *http.Request) (*NodeHierarchyController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &NodeHierarchyController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeBugController(rw http.ResponseWriter, req *http.Request) (*BugController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &BugController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeReviewController(rw http.ResponseWriter, req *http.Request) (*ReviewController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &ReviewController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeTestPlanController(rw http.ResponseWriter, req *http.Request) (*TestPlanController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &TestPlanController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeTestPlanStatisticsController(rw http.ResponseWriter, req *http.Request) (*TestPlanStatisticsController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &TestPlanStatisticsController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeSelectorsController(rw http.ResponseWriter, req *http.Request) (*SelectorsController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &SelectorsController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeEnumsController(rw http.ResponseWriter, req *http.Request) (*EnumsController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &EnumsController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeXmindController(rw http.ResponseWriter, req *http.Request) (*XmindController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &XmindController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeExcelController(rw http.ResponseWriter, req *http.Request) (*ExcelController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &ExcelController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeJupiterController(rw http.ResponseWriter, req *http.Request) (*JupiterController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &JupiterController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeOpenJupiterController(rw http.ResponseWriter, req *http.Request) (*openapi.JupiterOpenController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &openapi.JupiterOpenController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeOpenUiController(rw http.ResponseWriter, req *http.Request) (*openapi.UiOpenController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &openapi.UiOpenController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeOuterController(rw http.ResponseWriter, req *http.Request) (*OuterController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &OuterController{
		BaseController: baseController,
	}
	return c, nil
}
