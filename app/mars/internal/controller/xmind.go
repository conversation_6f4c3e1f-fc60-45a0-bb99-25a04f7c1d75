package controller

import (
	"fmt"
	"net/url"
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/xmind"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

type XmindController struct {
	*controller.BaseController
}

var (
	DefaultXmindController XmindController
	xmindService           service.XmindService
)

type MarsCaseExportReq struct {
	ParentId int64 `json:"parentId"`
}

func (c *XmindController) ImportXmind() {
	req := c.Request
	libIdStr := req.PostFormValue("libraryId")
	libId, err := strconv.ParseInt(libIdStr, 10, 64)
	if err != nil {
		c.ResFail(err)
		return
	}
	_, file, err := req.FormFile("file")
	if err != nil {
		c.ResFail(err)
		return
	}
	total, err := xmindService.Import(c.MantisContext, file, libId)
	if err != nil {
		c.ResFail(err)
		return
	}
	c.ResSuccessResult(total)
}

func (c *XmindController) ExportXmind(req MarsCaseExportReq) {
	res, err := xmindService.Export(c.MantisContext, req.ParentId)
	if err != nil {
		c.ResFail(err)
		return
	}
	c.ResponseWriter.Header().Set("content-type", "application/octet-stream")
	c.ResponseWriter.Header().Set("content-disposition", fmt.Sprintf("attachment; filename=%s.xmind", url.QueryEscape("用例导出")))
	c.ResponseWriter.Header().Set("content-transfer-encoding", "binary")
	c.ResponseWriter.Write(res.Bytes())
}

func (c *XmindController) Template() {
	file, err := xmind.TemplateFS.ReadFile("template.xmind")
	if err != nil {
		c.ResFail(err)
		return
	}
	c.ResponseWriter.Header().Set("content-type", "application/octet-stream")
	c.ResponseWriter.Header().Set("content-disposition", fmt.Sprintf("attachment; filename=%s.xmind", url.QueryEscape("xmind用例模版")))
	c.ResponseWriter.Header().Set("content-transfer-encoding", "binary")
	c.ResponseWriter.Write(file)
}
