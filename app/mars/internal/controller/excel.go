package controller

import (
	"fmt"
	"net/url"
	"strconv"

	service "git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/service/excel"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

type ExcelController struct {
	*controller.BaseController
}

var (
	DefaultExcelController ExcelController
	excelService           service.ExcelService
)

func (c *ExcelController) ImportExcel() {
	req := c.Request
	libIdStr := req.PostFormValue("libraryId")
	libId, err := strconv.ParseInt(libIdStr, 10, 64)
	if err != nil {
		c.ResFail(err)
		return
	}
	_, file, err := req.FormFile("file")
	if err != nil {
		c.ResFail(err)
		return
	}
	total, err := excelService.Import(c.MantisContext, file, libId)
	if err != nil {
		c.ResFail(err)
		return
	}
	c.ResSuccessResult(total)
}

func (c *ExcelController) ExportExcel(req MarsCaseExportReq) {
	res, err := excelService.Export(c.MantisContext, req.ParentId)
	if err != nil {
		c.ResFail(err)
		return
	}
	c.ResponseWriter.Header().Set("content-type", "application/octet-stream")
	c.ResponseWriter.Header().Set("content-disposition", fmt.Sprintf("attachment; filename=%s.xlsx", url.QueryEscape("用例导出")))
	c.ResponseWriter.Header().Set("content-transfer-encoding", "binary")
	c.ResponseWriter.Write(res)
}

func (c *ExcelController) Template() {
	file, err := service.ExcelTemplateFS.ReadFile("template.xlsx")
	if err != nil {
		c.ResFail(err)
		return
	}
	c.ResponseWriter.Header().Set("content-type", "application/octet-stream")
	c.ResponseWriter.Header().Set("content-disposition", fmt.Sprintf("attachment; filename=%s.xlsx", url.QueryEscape("excel用例模版")))
	c.ResponseWriter.Header().Set("content-transfer-encoding", "binary")
	c.ResponseWriter.Write(file)
}
