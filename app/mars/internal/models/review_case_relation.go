package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

// MarsReviewCaseRelation review 和 case的关联关系以及评论
type MarsReviewCaseRelation struct {
	models.Addons
	ReviewId     int64                      `json:"reviewId" gorm:"column:review_id;type:int8"`
	ReviewStatus string                     `json:"reviewStatus" gorm:"column:review_status;type:varchar(100)"`
	CaseId       int64                      `json:"caseId" gorm:"column:case_id;type:int8"`
	Comments     MarsReviewCaseCommentSlice `json:"comments" gorm:"column:comments;type:jsonb;not null;default:'[]'"`
}

func (MarsReviewCaseRelation) TableName() string {
	return "mars_review_case_relation"
}

type MarsReviewCaseComment struct {
	Commentor    string      `json:"commentor"`
	CommentTime  *times.Time `json:"commentTime"`
	Comment      string      `json:"comment"`
	ReviewStatus string      `json:"reviewStatus"`
}

type MarsReviewCaseCommentSlice []MarsReviewCaseComment

func (m MarsReviewCaseCommentSlice) Value() (driver.Value, error) {
	return json.Marshal(m)
}

func (m *MarsReviewCaseCommentSlice) Scan(value any) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalud JSON data")
	}
	return json.Unmarshal(bytes, m)
}
