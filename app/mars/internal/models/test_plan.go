package models

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type MarsTestPlan struct {
	models.Addons
	Name             string             `json:"name" gorm:"column:name;type:varchar(200)"`
	Stage            string             `json:"stage" gorm:"column:stage;type:varchar(100)"`
	Env              string             `json:"env" gorm:"column:env;type:varchar(100)"`
	BeginTime        *times.Time        `json:"beginTime" gorm:"column:begin_time;type:timestamp"`
	EndTime          *times.Time        `json:"endTime" gorm:"column:end_time;type:timestamp"`
	Owner            models.StringSlice `json:"owner" gorm:"column:owner;type:jsonb"`
	Model            models.StringSlice `json:"model" gorm:"column:model;type:jsonb"` // 模块:功能用例，api 用例
	Remark           string             `json:"remark" gorm:"column:remark;type:text"`
	Status           string             `json:"status" gorm:"column:status;type:varchar(100)"`
	RelatedIteration string             `json:"relatedIteration" gorm:"column:related_iteration;type:text"`
	IsAllDemand      bool               `json:"isAllDemand" gorm:"column:is_all_demand;type:bool"`
	PlanType         string             `json:"planType" gorm:"column:plan_type;type:varchar(50)"` // 测试计划类型: 普通测试计划/需求测试计划
	SpaceId          string             `json:"spaceId" gorm:"column:space_id;type:varchar(100)"`
}

func (MarsTestPlan) TableName() string {
	return "mars_test_plan"
}
