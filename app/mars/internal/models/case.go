package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"

	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type MarsCase struct {
	models.Addons
	NodeType         string                  `json:"nodeType" gorm:"column:node_type;type:varchar(50);comment:用例类型，root:用例库 folder:目录，case:用例"`
	Name             string                  `json:"name" gorm:"column:name;type:text"`
	ParentId         int64                   `json:"parentId" gorm:"column:parent_id;type:int8"`
	LibraryId        int64                   `json:"libraryId" gorm:"column:library_id;type:int8"`
	Priority         string                  `json:"priority" gorm:"column:priority;type:varchar(50);comment:P0, P1, P2,P3"`
	Remark           string                  `json:"remark" gorm:"column:remark;type:text"`
	PreCondition     string                  `json:"preCondition" gorm:"column:precondition;type:text"`
	Steps            *MarsCaseSteps          `json:"steps" gorm:"column:steps;type:jsonb;comment:该用例的所有步骤的json"`           // 该用例的所有步骤的json
	Attachments      MarsCaseAttachmentSlice `json:"attachments" gorm:"column:attachments;type:jsonb;comment:该用例的附件的json"` // 该用例的附件的json
	TagIds           models.StringSlice      `json:"-" gorm:"column:tags;type:jsonb"`                                      //  记录 tag 的 ids
	Tags             []commondto.CubeTagDTO  `json:"tags" gorm:"-"`
	DescendantsTotal int64                   `json:"descendantsTotal" gorm:"column:descendants_total;type:int8"` // 子孙节点总数
	SiblingOrder     int64                   `json:"siblingOrder" gorm:"column:sibling_order;type:int8"`         // 兄弟节点的顺序
	RelateDemands    []ProjectDemandDTO      `json:"relateDemands" gorm:"-"`
	ReviewHistory    []ReviewHistory         `json:"reviewHistory" gorm:"-"`
	Children         []*MarsCase             `json:"-" gorm:"-"` // 仅用于导出
	DemandCodes      []string                `json:"-" gorm:"-"` // 仅用于导出
}

func (*MarsCase) TableName() string {
	return "mars_case"
}

type MarsCaseAttachmentSlice []MarsCaseAttachment

func (m MarsCaseAttachmentSlice) Value() (driver.Value, error) {
	return json.Marshal(m)
}

func (m *MarsCaseAttachmentSlice) Scan(value any) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalid JSON data")
	}
	return json.Unmarshal(bytes, m)
}

type MarsCaseAttachment struct {
	Name       string      `json:"name"`
	FilePath   string      `json:"filePath"` // oss路径
	Size       int64       `json:"size"`
	GmtCreated *times.Time `json:"gmtCreated"`
}

type MarsCaseSteps struct {
	Steps []MarsCaseStep    `json:"steps"`
	Text  *MarsCaseStepText `json:"text"`
	Type  string            `json:"type"` // step or text
}

func (m *MarsCaseSteps) Value() (driver.Value, error) {
	if m != nil {
		if m.Type == "text" {
			if m.Text != nil && m.Text.Id == 0 {
				m.Text.Id = snowflake.GenSnowFlakeId()
			}
		} else if m.Type == "step" {
			for i := range m.Steps {
				if m.Steps[i].Id == 0 {
					m.Steps[i].Id = snowflake.GenSnowFlakeId()
				}
			}
		}
	}
	return json.Marshal(m)
}

func (m *MarsCaseSteps) Scan(value any) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalid JSON data")
	}
	return json.Unmarshal(bytes, m)
}

type MarsCaseStep struct { // 分步步骤
	Id              int64  `json:"id"`              // 步骤id
	Description     string `json:"description"`     // 描述
	ExpectedResults string `json:"expectedResults"` // 期望
	ActualResults   string `json:"actualResults"`   // 实际值
	Status          string `json:"status"`          // 步骤状态
}

type MarsCaseStepText struct {
	Id              int64  `json:"id"` // 步骤id
	Description     string `json:"description"`
	ExpectedResults string `json:"expectedResults"`
	ActualResults   string `json:"actualResults"`
}

type ReviewHistory struct {
	Id           int64       `json:"id"`
	Name         string      `json:"name"`
	ReviewStatus string      `json:"reviewStatus"`
	Reviewer     string      `json:"reviewer"`
	ReviewTime   *times.Time `json:"reviewTime"`
}
