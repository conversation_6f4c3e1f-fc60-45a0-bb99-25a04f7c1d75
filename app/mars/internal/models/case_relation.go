package models

import "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

// MarsCaseRelation 用例和需求的关联
type MarsCaseRelation struct {
	models.Addons
	CaseId       int64  `json:"caseId" gorm:"column:case_id;type:int8"`                                            // 用例ID
	RelationType string `json:"relationType" gorm:"column:relation_type;type:varchar(50);not null;default:demand"` // demand 目前只支持需求关联
	RelationId   string `json:"relationId" gorm:"column:relation_id;type:varchar(50)"`                             // 需求ID 	// 分配的执行人
}

func (MarsCaseRelation) TableName() string {
	return "mars_case_relation"
}

type MarsCaseRelationExpand struct {
	MarsCaseRelation
	Name     string `json:"name" gorm:"column:name;type:text"`
	Priority string `json:"priority" gorm:"column:priority;type:varchar(50)"`
}
