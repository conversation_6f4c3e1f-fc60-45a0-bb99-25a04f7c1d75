package models

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
)

// MarsTestPlanCaseRelation 测试计划和用例的关联表
type MarsTestPlanCaseRelation struct {
	models.Addons
	TestPlanId int64              `json:"testPlanId" gorm:"column:test_plan_id;type:int8"`
	DemandId   string             `json:"demandId" gorm:"column:demand_id;type:varchar(50)"`     // 需求 id（只有需求测试计划才有）,默认-1
	RelationId string             `json:"relationId" gorm:"column:relation_id;type:varchar(50)"` // 功能用例，api 用例，压测计划，ui 执行计划
	Sort       int64              `json:"sort" gorm:"column:sort;type:int8"`
	Type       string             `json:"type" gorm:"column:type;type:varchar(50)"`
	LastExecId int64              `json:"lastExecId" gorm:"column:last_exec_id;type:int8"`
	Executors  models.StringSlice `json:"executors" gorm:"column:executors;type:jsonb"` // 分配的执行人
}

func (MarsTestPlanCaseRelation) TableName() string {
	return "mars_test_plan_case_relation"
}

// MarsTestPlanDemandRelation 测试计划和需求关联表
type MarsTestPlanDemandRelation struct {
	models.Addons
	TestPlanId int64  `json:"testPlanId" gorm:"column:test_plan_id;type:int8"`
	DemandId   string `json:"demandId" gorm:"column:demand_id;type:varchar(50)"` // 需求 id
	Type       string `json:"type" gorm:"column:type;type:varchar(50)"`          // 功能用例，api 用例，压测计划，ui 执行计划
}

func (MarsTestPlanDemandRelation) TableName() string {
	return "mars_test_plan_demand_relation"
}
