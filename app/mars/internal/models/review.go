package models

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type MarsReview struct {
	models.Addons
	Name         string             `json:"name" gorm:"column:name;type:varchar(100)"`
	Description  string             `json:"description" gorm:"column:description;type:text"`
	Reviewer     models.StringSlice `json:"reviewer" gorm:"column:reviewer;type:jsonb"`
	ReviewStatus string             `json:"reviewStatus" gorm:"column:review_status;type:varchar(20)"`
	EndTime      *times.Time        `json:"endTime" gorm:"column:end_time;type:timestamp"`
	Comment      string             `json:"comment" gorm:"column:comment;type:text"`               // 总评论内容
	Commentor    string             `json:"commentor" gorm:"column:commentor;type:varchar(200)"`   // 总评论人
	CommentTime  *times.Time        `json:"commentTime" gorm:"column:comment_time;type:timestamp"` // 总评论时间
	SpaceId      string             `json:"spaceId" gorm:"column:space_id;type:varchar(100)"`
}

func (MarsReview) TableName() string {
	return "mars_review"
}
