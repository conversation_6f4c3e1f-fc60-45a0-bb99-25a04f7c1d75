package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto/report"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	pkgmodels "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

// MarsTestPlanReport 测试计划报告表
type MarsTestPlanReport struct {
	models.Addons
	Name             string                         `json:"name" gorm:"column:name;type:varchar(100)"`         // 测试计划名称
	Stage            string                         `json:"stage" gorm:"column:stage;type:varchar(20)"`        // 测试计划阶段
	TestPlanId       int64                          `json:"testPlanId" gorm:"column:test_plan_id;type:int8"`   // 测试计划id
	Env              string                         `json:"env" gorm:"column:env;type:varchar(20)"`            // 测试计划环境
	BeginTime        *times.Time                    `json:"beginTime" gorm:"column:begin_time;type:timestamp"` // 测试计划开始时间
	EndTime          *times.Time                    `json:"endTime" gorm:"column:end_time;type:timestamp"`     // 测试计划结束时间
	Owner            models.StringSlice             `json:"owner" gorm:"column:owner;type:jsonb"`              // 负责人
	Summary          string                         `json:"summary" gorm:"column:summary;type:text"`
	TestPlanPassRate report.MaraTestPlanPassRateDTO `json:"TestPlanPassRate" gorm:"test_plan_pass_rate:type:jsonb"`       // 测试计划总结
	ViewChoiceConfig ViewChoiceConfigSlice          `json:"viewChoiceConfig" gorm:"column:view_choice_config;type:jsonb"` // 视图选择配置
	TestPlanCaseMap  TestPlanCaseMapMapDTO          `json:"testPlanCaseMap" gorm:"column:case_map;type:jsonb"`            // 用例快照
	DemandList       ProjectDemandDTOSlice          `json:"demandList" gorm:"column:demand_list;type:jsonb"`
	BugSnapshot      ProjectBugDTOSlice             `json:"bugSnapshot" gorm:"column:bug_snapshot;type:jsonb"`
	SpaceId          string                         `json:"spaceId" gorm:"column:space_id;type:varchar(20)"`
	EnvName          string                         `json:"envName" gorm:"-"`   // 环境名称
	Iteration        *ProjectItemDTO                `json:"iteration" gorm:"-"` // 迭代对象
}

func (MarsTestPlanReport) TableName() string {
	return "mars_test_plan_report"
}

type ViewChoiceConfigSlice []ViewChoiceConfig

func (m ViewChoiceConfigSlice) Value() (driver.Value, error) {
	return json.Marshal(m)
}

func (m *ViewChoiceConfigSlice) Scan(value any) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalid JSON data")
	}
	return json.Unmarshal(bytes, m)
}

type TestPlanCaseMapMapDTO map[string][]MarsTestPlanCaseDTO

func (m TestPlanCaseMapMapDTO) Value() (driver.Value, error) {
	return json.Marshal(m)
}

func (m *TestPlanCaseMapMapDTO) Scan(value any) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalud JSON data")
	}
	return json.Unmarshal(bytes, m)
}

type MarsTestPlanDTO struct {
	MarsTestPlan
	Iteration   *ProjectItemDTO                 `json:"iteration" gorm:"-"`
	PassRate    string                          `json:"passRate" gorm:"-"`    // 测试计划通过率
	CaseRateDto *report.MaraTestPlanPassRateDTO `json:"caseRateDto" gorm:"-"` // 用例通过率
}

type ProjectItemDTO struct {
	Value     int64  `json:"value"`
	Label     string `json:"label"`
	Extend    string `json:"extend"`
	Sort      int64  `json:"sort"`
	IsDefault string `json:"isDefault"`
	Code      string `json:"code"`
	Id        int64  `json:"id"`
	IsDisable int64  `json:"isDisable"`
	Name      string `json:"name"`
}

type ProjectDTO struct {
	Id          int64  `json:"id"`
	Code        string `json:"code"`
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
}

type ProjectDemandDTO struct {
	Id        int64  `json:"id"`
	Code      string `json:"code"`
	Name      string `json:"name"`
	OverState string `json:"overState"`
	Type      string `json:"type"`
	Priority  string `json:"priority"`
	Project   string `json:"project"`
	Assignee  string `json:"assignee"`
	Iteration string `json:"iteration"`
}

type PriorityLevelDTO struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

type ProjectDemandDTOSlice []ProjectDemandDTO

func (m ProjectDemandDTOSlice) Value() (driver.Value, error) {
	return json.Marshal(m)
}

func (m *ProjectDemandDTOSlice) Scan(value any) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalud JSON data")
	}
	return json.Unmarshal(bytes, m)
}

type ProjectBugDTO struct {
	MarsRecordId int64       `json:"marsRecordId"` // 数据库中 mars 关联的 bug 记录 的 id
	Id           int64       `json:"id"`
	Name         string      `json:"name"`
	CaseName     string      `json:"caseName"`
	Project      string      `json:"project"`
	ProjectId    string      `json:"projectId"`
	Iteration    string      `json:"iteration"`
	State        string      `json:"state"`
	OverState    string      `json:"overState"`
	Assignee     string      `json:"assignee"`
	Creator      string      `json:"creator"`
	TypeName     string      `json:"typeName"`
	Type         string      `json:"type"`
	PriorityId   int64       `json:"priorityId"`
	PriorityName string      `json:"priorityName"`
	FixTime      int64       `json:"fixTime"`
	GmtFixDate   *times.Time `json:"gmtFixDate"`
	ReopenNum    int64       `json:"reopenNum"`
	Link         string      `json:"link"`
	GmtCreated   *times.Time `json:"gmtCreated"`
	Code         string      `json:"code"` // 缺陷编号
}

type ProjectBugDTOSlice []ProjectBugDTO

func (m ProjectBugDTOSlice) Value() (driver.Value, error) {
	return json.Marshal(m)
}

func (m *ProjectBugDTOSlice) Scan(value any) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalid JSON data")
	}
	return json.Unmarshal(bytes, m)
}

type MarsTestPlanCaseDTO struct {
	Id            int64                 `json:"id"`         // 关联记录的 id
	RelationId    int64                 `json:"relationId"` // 关联用例或需求的 id
	Name          string                `json:"name"`
	LibraryId     int64                 `json:"libraryId"`
	Library       string                `json:"library"`
	VersionName   string                `json:"versionName"`
	NodeName      string                `json:"nodeName"`
	ParentNodeId  int64                 `json:"parentNodeId"`
	CaseType      string                `json:"caseType"`
	Priority      string                `json:"priority"`
	Executors     pkgmodels.StringSlice `json:"executors"`
	ExecuteResult string                `json:"executeResult"`
	Sort          int64                 `json:"sort"`
	BugSize       int64                 `json:"bugSize"`
	CaseCount     int64                 `json:"caseCount"` // 用例统计,只有 UI 有
	EnvName       string                `json:"envName"`   // 执行环境,只有 UI 有
	ReportUrl     string                `json:"reportUrl"` // 报告地址,只有 API UI 有
}

type ViewChoiceConfig struct {
	ClassType string `json:"classType"`
	FieldKey  string `json:"fieldKey"`
	Label     string `json:"label"`
	Value     bool   `json:"value"`
	Width     string `json:"width"`
	ChartType string `json:"chartType"`
	Group     string `json:"group"`
	TableType string `json:"tableType"`
	Type      string `json:"type"`
	Sort      int    `json:"sort"`
}
