package models

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

// 测试计划中用例的执行历史
type MarsTestPlanCaseExecHistory struct {
	models.Addons
	CaseId        string      `json:"caseId" gorm:"column:case_id;type:varchar(50)"`
	TestPlanId    int64       `json:"testPlanId" gorm:"column:test_plan_id;type:int8"`
	Type          string      `json:"type" gorm:"column:type;type:varchar;size:50"`
	ExecuteResult string      `json:"executeResult" gorm:"column:execute_result;type:varchar(20)"` // 执行结果状态
	ExecuteTime   *times.Time `json:"executeTime" gorm:"column:execute_time;type:timestamp"`
	Executor      string      `json:"executor" gorm:"column:executor;type:varchar(100)"`
	Result        string      `json:"result" gorm:"column:result;type:text"`
	ReportUrl     string      `json:"reportUrl" gorm:"column:report_url;type:text"`
	ResultInfo    any         `json:"resultInfo" gorm:"-"`
}

func (MarsTestPlanCaseExecHistory) TableName() string {
	return "mars_test_plan_case_exec_history"
}

// MarsCaseResultInfo 功能用例的 result 对象
type MarsCaseResultInfo struct {
	Status           string                   `json:"status"`            // 执行结果状态
	TestData         string                   `json:"testData"`          // 测试数据
	ListActualResult []MarsCaseStepExecResult `json:"stepActualResults"` // 列表的实际结果,key 是步骤 id, value 是实际结果
	TextActualResult *MarsCaseStepExecResult  `json:"textActualResults"` // 文本的实际结果,key 是步骤 id, value 是实际结果
}

type MarsCaseStepExecResult struct {
	StepId  int64  `json:"stepId"`  // 步骤 id
	Status  string `json:"status"`  // 执行结果状态
	Content string `json:"content"` // 执行结果内容
}
