package models

import "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

type MarsBug struct {
	models.Addons
	BugId      string `json:"bugId" gorm:"column:bug_id;type:text"`
	DemandId   string `json:"demandId" gorm:"column:demand_id;type:text"`
	TestPlanId int64  `json:"testPlanId" gorm:"column:test_plan_id;type:int8"`
	CaseId     int64  `json:"caseId" gorm:"column:case_id;type:int8"`
	Type       string `json:"type" gorm:"column:type;type:varchar(100)"`
}

func (MarsBug) TableName() string {
	return "mars_bug"
}
