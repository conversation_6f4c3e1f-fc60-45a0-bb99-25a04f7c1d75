package models

import "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

type MarsCaseLibrary struct {
	models.Addons
	Name        string `json:"name" gorm:"column:name;type:varchar(100)"`
	Description string `json:"description" gorm:"column:description;type:text"`
	SpaceId     string `json:"spaceId" gorm:"column:space_id;type:varchar(50)"`
	Share       *bool  `json:"share" gorm:"column:share;type:bool"`
	Count       int64  `json:"caseCount" gorm:"-"`   // 用例库下用例数统计
	ProjectName string `json:"projectName" gorm:"-"` // 项目名称
}

func (*MarsCaseLibrary) TableName() string {
	return "mars_case_library"
}
