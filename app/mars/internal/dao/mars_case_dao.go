package dao

import (
	"sort"
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/remote/project"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type MarsCaseDao struct{}

type MarsCaseCountDTO struct {
	LibraryId int64 `json:"libraryId" gorm:"column:library_id"`
	Count     int64 `json:"count" gorm:"column:case_count"`
}

// CountMarsCasesByLibId 统计一个lib下的用例数量
func (MarsCaseDao) CountMarsCasesByLibId(ctx *commoncontext.MantisContext, libIds []int64) ([]MarsCaseCountDTO, error) {
	sql := `select library_id, count(1) as case_count from mars_case where library_id in ? and node_type = 'case' and is_deleted = 'N' group by library_id`
	res := make([]MarsCaseCountDTO, 0)
	err := gormx.Raw(ctx, sql, &res, libIds)
	return res, err
}

// SelectIdChildByParentId 根据parentId查询所有的子节点id
func (MarsCaseDao) SelectIdChildByParentId(ctx *commoncontext.MantisContext, parentId int64, nodeTypes []string,
	nesting bool,
) ([]int64, error) {
	var ids []int64
	if nesting {
		sql := `WITH RECURSIVE subordinates AS (
				SELECT id,node_type,parent_id FROM mars_case WHERE id = ? and is_deleted = 'N'
				UNION
				SELECT e.id,e.node_type,e.parent_id FROM (select * from mars_case where is_deleted = 'N') e
      			INNER JOIN subordinates s ON s.id = e.parent_id 
			) SELECT id FROM subordinates where node_type in ?`
		err := gormx.Raw(ctx, sql, &ids, parentId, nodeTypes)
		if err != nil {
			return nil, err
		}
	} else {
		sql := `select id from mars_case where parent_id = ? and node_type in ? and is_deleted = 'N'`
		err := gormx.Raw(ctx, sql, &ids, parentId, nodeTypes)
		if err != nil {
			return nil, err
		}
	}
	return ids, nil
}

// SelectMaxSiblingOrder 获取 parentId 下的最大的 siblingOrder
func (MarsCaseDao) SelectMaxSiblingOrder(ctx *commoncontext.MantisContext, parentId int64) (int64, error) {
	sql := `select COALESCE(max(sibling_order), 0) from mars_case where parent_id = ? and is_deleted = 'N'`
	var maxOrder int64
	err := gormx.Raw(ctx, sql, &maxOrder, parentId)
	return maxOrder, err
}

// SelectSiblingOrder 获取 节点的sibling order
func (MarsCaseDao) SelectSiblingOrder(ctx *commoncontext.MantisContext, id int64) (int64, error) {
	sql := `select sibling_order from mars_case where id = ? and is_deleted = 'N'`
	var order int64
	err := gormx.Raw(ctx, sql, &order, id)
	return order, err
}

// UpdateBrotherSiblingOrder 更新兄弟节点的顺序，将 siblingOrder 大于 siblingOrder 的节点的 siblingOrder + 1
func (MarsCaseDao) UpdateBrotherSiblingOrder(ctx *commoncontext.MantisContext, parentId int64, siblingOrder int64) error {
	sql := `update mars_case set sibling_order = sibling_order + 1 where parent_id = ? and sibling_order > ? and is_deleted = 'N'`
	_, err := gormx.Exec(ctx, sql, parentId, siblingOrder)
	return err
}

func (MarsCaseDao) CountCaseByParentIdInLib(ctx *commoncontext.MantisContext, libId int64) (map[int64]int64, error) {
	sql := `select parent_id, count(1) as count from mars_case where library_id = ? and node_type = 'case' and is_deleted = 'N' group by parent_id`
	type parentCount struct {
		ParentId int64 `gorm:"column:parent_id"`
		Count    int64 `gorm:"column:count"`
	}
	res := make([]parentCount, 0)
	err := gormx.Raw(ctx, sql, &res, libId)
	if err != nil {
		return nil, err
	}
	m := make(map[int64]int64)
	for _, c := range res {
		m[c.ParentId] = c.Count
	}
	return m, nil
}

func (MarsCaseDao) BuildCaseTreeByParentId(ctx *commoncontext.MantisContext, parentId int64) (*models.MarsCase, error) {
	// 查询所有节点
	nodes := make([]models.MarsCase, 0)
	sql := `with recursive sub as (
			select * from mars_case where id = ? and is_deleted = 'N'
			union
			select e.* from (select * from mars_case where is_deleted = 'N') e
			inner join sub s on s.id = e.parent_id
		) select * from sub`
	err := gormx.Raw(ctx, sql, &nodes, parentId)
	if err != nil {
		return nil, err
	}
	// 查询需求
	nodeIds := make([]int64, 0)
	nodeMap := make(map[int64]*models.MarsCase)
	for _, node := range nodes {
		nodeIds = append(nodeIds, node.Id)
		nodeMap[node.Id] = &node
	}
	relations := make([]models.MarsCaseRelation, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCaseRelation{}).In("case_id", nodeIds).Eq("is_deleted", commonconstants.DeleteNo),
		&relations,
	)
	if err != nil {
		return nil, err
	}
	demandIds := make([]string, 0)
	for _, relation := range relations {
		demandIds = append(demandIds, relation.RelationId)
	}
	if len(demandIds) != 0 {
		demands, _, err := project.ProjectRemoteApi.GetDemands(ctx, dto.ProjectCommonSelectReq{Ids: demandIds})
		if err != nil {
			return nil, err
		}
		demandCodeMap := make(map[string]string)
		for _, demand := range demands {
			demandCodeMap[strconv.FormatInt(demand.Id, 10)] = demand.Code
		}
		for _, relation := range relations {
			node := nodeMap[relation.CaseId]
			if node != nil {
				if node.DemandCodes != nil {
					node.DemandCodes = make([]string, 0)
				}
				node.DemandCodes = append(node.DemandCodes, demandCodeMap[relation.RelationId])
			}
		}
	}
	root := nodeMap[parentId]
	for _, node := range nodeMap {
		parent := nodeMap[node.ParentId]
		if parent != nil {
			if parent.Children == nil {
				parent.Children = make([]*models.MarsCase, 0)
			}
			parent.Children = append(parent.Children, node)
		}
	}

	// 递归排序所有节点的子节点
	var sortChildren func(node *models.MarsCase)
	sortChildren = func(node *models.MarsCase) {
		if len(node.Children) > 0 {
			// 根据 SiblingOrder 排序
			sort.Slice(node.Children, func(i, j int) bool {
				return node.Children[i].SiblingOrder < node.Children[j].SiblingOrder
			})

			// 递归排序子节点
			for i := range node.Children {
				childPtr := node.Children[i]
				sortChildren(childPtr)
			}
		}
	}

	// 排序根节点的子节点
	sortChildren(root)
	return root, nil
}
