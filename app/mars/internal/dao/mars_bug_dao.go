package dao

import (
	"fmt"

	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type MarsBugDao struct{}

func (MarsBugDao) CountBugsByTestPlanAndModels(ctx *commoncontext.MantisContext, testPlanId int64, models []string) (int64, error) {
	sql := `select count(*) from mars_bug where test_plan_id=? and type in ? and is_deleted='N'`
	var res int64
	err := gormx.Raw(ctx, sql, &res, testPlanId, models)
	return res, err
}

func (MarsBugDao) CountBugs(ctx *commoncontext.MantisContext, testPlanId int64, demand int64, caseId int64, caseType string) (int64, error) {
	sql := `select count(distinct(bug_id)) from mars_bug`
	params := make(map[string]any)
	if testPlanId != 0 {
		params["test_plan_id"] = testPlanId
	}
	if demand != 0 {
		params["relate_demand"] = demand
	}
	if caseId != 0 {
		params["case_id"] = caseId
	}
	if caseType != "" {
		params["type"] = caseType
	}
	sql += " where "
	if len(params) != 0 {
		for k, v := range params {
			sql += fmt.Sprintf("%s = %v and ", k, v)
		}
	}
	sql += "is_deleted = 'N'"
	var res int64
	err := gormx.Raw(ctx, sql, &res)
	return res, err
}

func (MarsBugDao) CountBugsByCaseIds(ctx *commoncontext.MantisContext, testPlanId int64, demand int64, caseIds []int64, caseType string) (map[int64]int64, error) {
	sql := `select case_id, count(distinct(bug_id)) as count from mars_bug where `
	params := make([]any, 0)
	if testPlanId != 0 {
		sql += "test_plan_id = ? and "
		params = append(params, testPlanId)
	}
	if demand != 0 {
		sql += "relate_demand = ? and "
		params = append(params, demand)
	}
	if len(caseIds) != 0 {
		sql += "case_id in ? and "
		params = append(params, caseIds)
	}
	if caseType != "" {
		sql += "type = ? and "
		params = append(params, caseType)
	}
	sql += "is_deleted = 'N' group by case_id"
	sqlRes := make([]struct {
		CaseId int64 `gorm:"column:case_id"`
		Count  int64 `gorm:"column:count"`
	}, 0)
	err := gormx.Raw(ctx, sql, &sqlRes, params...)
	res := make(map[int64]int64)
	for _, s := range sqlRes {
		res[s.CaseId] = s.Count
	}
	return res, err
}
