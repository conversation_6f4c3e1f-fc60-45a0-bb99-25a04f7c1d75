package dao

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type MarsNodeHierarchyDao struct{}

// SelectMaxSortInParent 查询用例库下parent下的最大sort
func (MarsNodeHierarchyDao) SelectMaxSortInParent(ctx *commoncontext.MantisContext, libId int64, parentId int64) (int64, error) {
	sql := `select COALESCE(max(sort), -1) from mars_case where library_id = ? and parent_id = ? and is_deleted = 'N' `
	var res int64
	err := gormx.Raw(ctx, sql, &res, libId, parentId)
	return res, err
}

// SelectChildrenUnderParent 查出parent下的所有子节点
func (MarsNodeHierarchyDao) SelectChildrenUnderParent(ctx *commoncontext.MantisContext, libId int64, parentId int64) ([]models.MarsCase, error) {
	if parentId != 0 {
		sql := `WITH RECURSIVE subordinates AS (
				SELECT * FROM mars_case WHERE parent_id = ? and is_deleted = 'N'
				UNION
				SELECT e.* FROM mars_case e
      			INNER JOIN subordinates s ON s.id = e.parent_id 
			) SELECT * FROM subordinates where is_deleted = 'N';`
		res := make([]models.MarsCase, 0)
		err := gormx.Raw(ctx, sql, &res, parentId)
		return res, err
	} else {
		sql := `select * from mars_case where library_id = ? and is_deleted = 'N'`
		res := make([]models.MarsCase, 0)
		err := gormx.Raw(ctx, sql, &res, libId)
		return res, err
	}
}

// RecursionRemoveById 按递归删除此节点及所有的子节点
func (MarsNodeHierarchyDao) RecursionRemoveById(ctx *commoncontext.MantisContext, id int64) error {
	sql := `WITH RECURSIVE subordinates AS (
                SELECT * FROM mars_case WHERE id = ? and is_deleted = 'N'
                UNION
                SELECT e.* FROM mars_case e
                INNER JOIN subordinates s ON s.id = e.parent_id 
            ) UPDATE mars_case SET is_deleted = 'Y' 
            WHERE id IN (SELECT id FROM subordinates);`
	_, err := gormx.Exec(ctx, sql, id)
	return err
}

// RecursionRemoveByParentId 按递归删除此节点的所有的子节点
func (MarsNodeHierarchyDao) RecursionRemoveByParentId(ctx *commoncontext.MantisContext, parentId int64) error {
	sql := `WITH RECURSIVE subordinates AS (
                SELECT * FROM mars_case WHERE parent_id = ? and is_deleted = 'N'
                UNION
                SELECT e.* FROM mars_case e
                INNER JOIN subordinates s ON s.id = e.parent_id 
            ) UPDATE mars_case SET is_deleted = 'Y' 
            WHERE id IN (SELECT id FROM subordinates);`
	_, err := gormx.Exec(ctx, sql, parentId)
	return err
}

// RecursionSelectIds 按条件递归获取所有的 ids
func (MarsNodeHierarchyDao) RecursionSelectIds(ctx *commoncontext.MantisContext, id int64, nodeType string) ([]int64, error) {
	sql := `WITH RECURSIVE subordinates AS (
                SELECT * FROM mars_case WHERE id = ? and is_deleted = 'N'
                UNION
                SELECT e.* FROM mars_case e
                  INNER JOIN subordinates s ON s.id = e.parent_id 
            ) SELECT id FROM subordinates WHERE node_type = ? and is_deleted = 'N';`
	res := make([]int64, 0)
	err := gormx.Raw(ctx, sql, &res, id, nodeType)
	return res, err
}

// RecursionSelectChildrenIds 按条件递归获取parentId下所的子节点id
func (MarsNodeHierarchyDao) RecursionSelectChildrenIds(ctx *commoncontext.MantisContext, parentId int64, nodeType string) ([]int64, error) {
	sql := `WITH RECURSIVE subordinates AS (
                SELECT * FROM mars_case WHERE parent_id = ? and is_deleted = 'N'
                UNION
                SELECT e.* FROM mars_case e
                  INNER JOIN subordinates s ON s.id = e.parent_id 
            ) SELECT id FROM subordinates WHERE node_type = ? and is_deleted = 'N';`
	res := make([]int64, 0)
	err := gormx.Raw(ctx, sql, &res, parentId, nodeType)
	return res, err
}

// UpdateSortAfterPlus 将parent下的sort>{sort}的node的sort更新为sort+{plus}
func (MarsNodeHierarchyDao) UpdateSortAfterPlus(ctx *commoncontext.MantisContext, libId int64, parentId int64, sort int64, plus int) error {
	sql := `update mars_case set sibling_order = sibling_order + ? where library_id = ? and parent_id = ? and sibling_order > ?`
	_, err := gormx.Exec(ctx, sql, plus, libId, parentId, sort)
	return err
}

// SelectCaseCountGroupByFolder 查询一个用例库中目录和用例的count的map
func (m MarsNodeHierarchyDao) SelectCaseCountGroupByFolderByLib(ctx *commoncontext.MantisContext, libId int64) map[int64]int64 {
	folderIds := make([]int64, 0)
	gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.MarsCase{}).Select("id").
			Eq("library_id", libId).Eq("is_deleted", commonconstants.DeleteNo),
		&folderIds)
	return m.SelectCaseCountGroupByFolder(ctx, folderIds)
}

func (MarsNodeHierarchyDao) SelectCaseCountGroupByFolder(ctx *commoncontext.MantisContext, folderIds []int64) map[int64]int64 {
	sql := `select COALESCE(count(1)) as count, parent_id from mars_case
                                   where parent_id in ? and is_deleted = 'N' group by parent_id`
	type TmpResForSelectCaseCountGroupByFolder struct {
		Count    int64 `gorm:"column:count"`
		ParentId int64 `gorm:"column:parent_id"`
	}
	sqlRes := make([]TmpResForSelectCaseCountGroupByFolder, 0)
	gormx.Raw(ctx, sql, &sqlRes, folderIds)
	res := make(map[int64]int64)
	for _, re := range sqlRes {
		res[re.ParentId] = re.Count
	}
	return res
}
