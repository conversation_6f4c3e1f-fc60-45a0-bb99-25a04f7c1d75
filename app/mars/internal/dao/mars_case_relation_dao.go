package dao

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type MarsCaseRelationDao struct{}

// GetCaseRelationExpand 获取需求关联的用例
func (MarsCaseRelationDao) GetCaseRelationExpand(ctx *commoncontext.MantisContext, relationId []string, rType string) ([]models.MarsCaseRelationExpand, error) {
	var relations []models.MarsCaseRelationExpand
	sql := `select t1.*,t2."name",t2.priority from mars_case_relation t1 
            left join mars_case t2 on t1.case_id = t2.id 
            where t1.is_deleted ='N' and t1.relation_type=? and t1.relation_id in (?)`
	err := gormx.Raw(ctx, sql, &relations, rType, relationId)
	if err != nil {
		return nil, err
	}
	return relations, nil
}
