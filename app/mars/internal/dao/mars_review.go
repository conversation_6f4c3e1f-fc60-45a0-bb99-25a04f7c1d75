package dao

import (
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type MarsReviewDao struct{}

type CaseCountGroupByReviewRes struct {
	Count    int64 `gorm:"column:count"`
	ReviewId int64 `gorm:"column:review_id"`
}

func (MarsReviewDao) SelectCaseCountGroupByReview(ctx *commoncontext.MantisContext, reviewIds []int64) (map[int64]int64, error) {
	sql := `select count(case_id) as count, review_id from mars_review_case_relation where review_id in ? and is_deleted = 'N' group by review_id`
	list := make([]CaseCountGroupByReviewRes, 0)
	err := gormx.Raw(ctx, sql, &list, reviewIds)
	if err != nil {
		return nil, err
	}
	res := make(map[int64]int64)
	for _, count := range list {
		res[count.ReviewId] = count.Count
	}
	return res, nil
}
