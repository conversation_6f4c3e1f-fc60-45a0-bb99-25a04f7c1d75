package dao

import (
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type MarsTestPlanDao struct{}

func (MarsTestPlanDao) CountDemands(ctx *commoncontext.MantisContext, planId int64) (int64, error) {
	sql := `select count(distinct(relate_demand)) from mars_test_plan_demand where test_plan_id = ? and is_deleted = 'N'`
	var res int64
	err := gormx.Raw(ctx, sql, &res, planId)
	return res, err
}
