package dao

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type MarsCaseTagsDao struct{}

func (MarsCaseTagsDao) GetTagIdsGroupByCaseIds(ctx *commoncontext.MantisContext, caseIds []int64, projectNo string) (map[int64][]string, error) {
	sql := `select * from mars_case where id in ? and is_deleted = 'N'`
	list := make([]models.MarsCase, 0)
	err := gormx.Raw(ctx, sql, &list, caseIds)
	if err != nil {
		return nil, err
	}
	res := make(map[int64][]string)
	for _, c := range list {
		if _, ok := res[c.Id]; !ok {
			res[c.Id] = make([]string, 0)
		}
		res[c.Id] = append(res[c.Id], c.TagIds...)
	}
	return res, nil
}
