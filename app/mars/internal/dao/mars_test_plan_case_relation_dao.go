package dao

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type MarsTestPlanCaseRelationDao struct{}

func (MarsTestPlanCaseRelationDao) SelectMaxSortByTestPlanId(ctx *commoncontext.MantisContext, testPlanId int64, caseType string, demandId string) (int64, error) {
	sql := `select COALESCE(max(sort), 0) from mars_test_plan_case_relation where test_plan_id = ? and type = ? 
	and demand_id = ? and is_deleted = 'N'`
	var res int64
	err := gormx.Raw(ctx, sql, &res, testPlanId, caseType, demandId)
	return res, err
}

func (MarsTestPlanCaseRelationDao) UpdateSortUpByTestPlanId(ctx *commoncontext.MantisContext, testPlanId int64, caseType, demandId string, sort int64) error {
	sql := `update mars_test_plan_case_relation set sort = sort + 1 where test_plan_id = ? and type = ? and demand_id = ? and is_deleted = 'N' and sort >= ?`
	_, err := gormx.Exec(ctx, sql, testPlanId, caseType, demandId, sort)
	return err
}

func (MarsTestPlanCaseRelationDao) SelectRowsAndExecHis(ctx *commoncontext.MantisContext, testPlanId int64, models []string) ([]dto.MarsTestPlanCaseRelationAndHis, error) {
	sql := `select t1.*,t2.execute_result,t2.result,t2.report_url from mars_test_plan_case_relation t1
			left join mars_test_plan_case_exec_history t2 on t1.last_exec_id =t2.id
			where t1.test_plan_id=? and t1.type in ? and t1.is_deleted = 'N' `
	relationAndHis := make([]dto.MarsTestPlanCaseRelationAndHis, 0)
	err := gormx.Raw(ctx, sql, &relationAndHis, testPlanId, models)
	if err != nil {
		return nil, err
	}
	return relationAndHis, nil
}

func (MarsTestPlanCaseRelationDao) UpdateBatchLastExecId(ctx *commoncontext.MantisContext, idMap map[int64]int64) error {
	for k, v := range idMap {
		sql := `update mars_test_plan_case_relation set last_exec_id =? where id =?`
		_, err := gormx.Exec(ctx, sql, v, k)
		if err != nil {
			return err
		}
	}
	return nil
}
