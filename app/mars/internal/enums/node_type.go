package enums

type NodeTypeEnum struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

var (
	RootNode           = NodeTypeEnum{"根节点", "root"}
	FolderNode         = NodeTypeEnum{"目录", "folder"}
	CaseNode           = NodeTypeEnum{"用例", "case"}
	PreConditionNode   = NodeTypeEnum{"前置条件", "precondition"}
	RemarkNode         = NodeTypeEnum{"备注", "remark"}
	StepNode           = NodeTypeEnum{"步骤", "step"}
	ExpectedResultNode = NodeTypeEnum{"预期", "expectedResult"}
)
