package enums

type CaseExecuteResultEnum struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

var (
	UnTest                 = CaseExecuteResultEnum{"未测", "untest"}
	Pass                   = CaseExecuteResultEnum{"通过", "pass"}
	Fail                   = CaseExecuteResultEnum{"失败", "fail"}
	Block                  = CaseExecuteResultEnum{"受阻", "block"}
	Again                  = CaseExecuteResultEnum{"重测", "again"}
	caseExecuteResultEnums = []CaseExecuteResultEnum{
		UnTest, Pass, Fail, Block, Again,
	}
)

func (CaseExecuteResultEnum) GetDefault() CaseExecuteResultEnum {
	return UnTest
}

func (CaseExecuteResultEnum) GetLableByValue(value string) string {
	for _, enum := range caseExecuteResultEnums {
		if enum.Value == value {
			return enum.Label
		}
	}
	return ""
}

func (CaseExecuteResultEnum) GetEnumByValue(value string) CaseExecuteResultEnum {
	for _, enum := range caseExecuteResultEnums {
		if enum.Value == value {
			return enum
		}
	}
	return UnTest
}

func (CaseExecuteResultEnum) GetValueByLable(label string) string {
	for _, enum := range caseExecuteResultEnums {
		if enum.Label == label {
			return enum.Value
		}
	}
	return ""
}
