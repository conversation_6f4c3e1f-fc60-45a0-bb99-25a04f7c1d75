package enums

type MarsReviewStatusEnum struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

var (
	ReviewNotStart = MarsReviewStatusEnum{"未开始", "NotStart"}
	ReviewRunning  = MarsReviewStatusEnum{"进行中", "Running"}
	ReviewFinished = MarsReviewStatusEnum{"已结束", "Finished"}
)

func (MarsReviewStatusEnum) GetDefault() MarsReviewStatusEnum {
	return ReviewNotStart
}

type MarsReviewCaseStatusEnum struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

var (
	NotReview  = MarsReviewCaseStatusEnum{"未评审", "NotReview"}
	PassReview = MarsReviewCaseStatusEnum{"通过", "PassReview"}
	FailReview = MarsReviewCaseStatusEnum{"不通过", "FailReview"}
)

func (MarsReviewCaseStatusEnum) GetDefault() MarsReviewCaseStatusEnum {
	return NotReview
}
