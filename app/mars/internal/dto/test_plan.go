package dto

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto/ui"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	pkg_models "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type MarsTestPlanSimpleDTO struct {
	PlanId   int64  `json:"planId"`
	PlanName string `json:"planName"`
}

type MarsTestPlanListReq struct {
	Name      string `schema:"name"`
	Status    string `schema:"status"`
	Stage     string `schema:"stage"`
	Owner     string `schema:"owner"`
	Creator   string `schema:"creator"`
	BeginTime string `schema:"beginTime"`
	EndTime   string `schema:"endTime"`
	Iteration string `schema:"iteration"`
	PlanType  string `schema:"planType"`
}

type MarsTestPlanCaseTreeReq struct {
	Model        string `schema:"model"`
	Name         string `schema:"name"`
	LibraryId    int64  `schema:"libraryId"`
	BaseId       int64  `schema:"baseId"`    // jupiter 主表id
	VersionId    int64  `schema:"versionId"` // jupiter 版本id
	Nesting      bool   `schema:"nesting"`   // 是否查询出子目录下的case
	ParentNodeId int64  `schema:"parentNodeId"`
	Review       string `schema:"review"` // 评审状态
}

type MarsTestPlanRelateCaseReq struct {
	Type       string   `json:"type"`
	TestPlanId int64    `json:"testPlanId"`
	Ids        []string `json:"ids"`
	DemandId   string   `json:"demandId"` // 需求 id
}

type MarsTestPlanCaseResDTO struct {
	CaseId    int64    `json:"caseId"`
	TagIds    []string `json:"tagIds"`
	LibraryId int64    `json:"sId"`      // 用例集；用例库id
	CaseName  string   `json:"caseName"` // 用例名称
	Priority  string   `json:"priority"`
}

type MarsTestPlanResultStatisticsDTO struct {
	Id               int64   `json:"id"`
	PassRate         float32 `json:"passRate"`
	TestedRate       float32 `json:"testedRate"`
	TestedCase       int64   `json:"testedCase"`
	PassedCase       int64   `json:"passedCase"`
	TotalCase        int64   `json:"totalCase"`
	TotalExecuteCase int64   `json:"totalExecuteCase"`
	TotalDemands     int64   `json:"totalDemands"`
	AutoCoverage     float32 `json:"autoCoverage"`
	UnTested         int64   `json:"unTested"`
	AgainTest        int64   `json:"againTest"`
	FailTest         int64   `json:"failTest"`
	BlockTest        int64   `json:"blockTest"`
	AbandonTest      int64   `json:"abandonTest"`
}

type MarsTestPlanCaseListReq struct {
	TestPlanId    int64  `schema:"testPlanId"`
	Type          string `schema:"type"`
	Executor      string `schema:"executor"`
	Priority      string `schema:"priority"`
	CaseName      string `schema:"caseName"`
	TagIds        string `schema:"tagIds"`
	ExecuteResult string `schema:"executeResult"` // 测试结果
}

type MarsTestPlanCaseBatchUpdateReq struct {
	Type            string                 `json:"type"`
	TestPlanId      int64                  `json:"testPlanId"`
	Ids             []int64                `json:"ids"`
	Executor        string                 `json:"executor"` // 用于查询
	ExecuteResult   string                 `json:"executeResult"`
	AssignExecutors pkg_models.StringSlice `json:"assignExecutors"` // 分配的执行人
}

type MarsExecuteRecord struct {
	CaseId      int64       `json:"caseId"`
	Result      string      `json:"result"`
	Executor    string      `json:"executor"`
	TestPlanId  int64       `json:"testPlanId"`
	ExecuteTime *times.Time `json:"executeTime"`
	Type        string      `json:"type"`
}

type MarsTestPlanDemandReqDTO struct {
	Id           int64           `json:"id"`
	SelectedCase []ui.SelectCase `json:"selectedCase"`
}

type MarsTestPlanDemandDTO struct {
	Id                string                       `json:"id"`
	Code              string                       `json:"code"`
	Name              string                       `json:"name"`
	PriorityLevelName string                       `json:"priorityLevelName"`
	State             string                       `json:"state"`
	Assignee          string                       `json:"assignee"`
	SelectedCases     []models.MarsTestPlanCaseDTO `json:"selectedCases"`
	TotalCases        int64                        `json:"totalCases"`
}

type MarsTestPlanDemandCaseDTO struct {
	RelateDemand     models.ProjectDemandDTO      `json:"relateDemand"`
	CaseCount        int64                        `json:"caseCount"`
	MarsPlanCaseList []models.MarsTestPlanCaseDTO `json:"marsPlanCaseList"`
}

type MarsTestPlanCaseMoveReq struct {
	DemandId  string `json:"demandId"`  // 需求 id，普通测试计划则没有
	Id        int64  `json:"id"`        // 要移动的节点 id
	SiblingId int64  `json:"siblingId"` // 后一个兄弟节点的 id,如果移动到最后,则 siblingId 为 0
	Type      string `json:"type"`      // 模块
}

type MarsCaseExecReq struct {
	Ids  []int64 `json:"ids"`  // 用例记录 id
	Type string  `json:"type"` // 类型 case/ui/api
	Env  string  `json:"env"`  // 执行环境
}
