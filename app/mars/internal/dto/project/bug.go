package project

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
)

type MarsBugReq struct {
	CaseId       int64   `json:"caseId"`
	Type         string  `json:"type"`
	BugIds       []int64 `json:"bugIds"`
	RelateDemand string  `json:"relateDemand"`
	TestPlanId   int64   `json:"testPlanId"`
}

type MarsBugListReq struct {
	CaseId       int64  `schema:"caseId"`
	Type         string `schema:"type"`
	RelateDemand string `schema:"relateDemand"`
	TestPlanId   int64  `schema:"testPlanId"`
}

type MarsBugRemoveReq struct {
	RecordIds  []int64 `json:"recordIds"`
	TestPlanId int64   `json:"testPlanId"`
}

type ProjectBugDTO struct {
	ProjectBugStatisticsDTO dto.ProjectBugStatisticsDTO `json:"projectBugStatisticsDTO"`
	BugDTOS                 []models.ProjectBugDTO      `json:"bugDTOS"`
}
