package report

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
)

type MaraTestPlanPassRateDTO struct {
	TotalCase        int64                               `json:"totalCase"`        // 用例总数
	TotalExecuteCase int64                               `json:"totalExecuteCase"` // 执行总数
	RemainderTime    string                              `json:"remainderTime"`    // 剩余时间
	TotalPass        int64                               `json:"totalPass"`        // 通过数
	TotalFail        int64                               `json:"totalFail"`        // 失败数
	TotalAgain       int64                               `json:"totalReTest"`      // 重测数
	TotalBlock       int64                               `json:"totalBlock"`       // 阻塞数
	PassRate         string                              `json:"passRate"`         // 通过率 = 通过数/执行总数
	ExecRate         string                              `json:"testedCaseRate"`   // 执行率 = 执行总数/用例总数
	TotalUnTest      int64                               `json:"totalUnTest"`      // 未执行数 = 用例总数-执行总数
	TotalBug         int64                               `json:"totalBug"`         // bug数
	ModelRateMap     map[string]*MaraTestPlanPassRateDTO `json:"modelRateMap"`     // 模块的通过率
}

// Cal 计算 xxx 率
func (dto *MaraTestPlanPassRateDTO) Cal() {
	if dto.TotalCase == 0 {
		dto.PassRate = "0"
		dto.ExecRate = "0"
	} else {
		pr := float32(dto.TotalPass) / float32(dto.TotalCase)
		dto.PassRate = fmt.Sprintf("%.2f", pr*100)
		er := float32(dto.TotalExecuteCase) / float32(dto.TotalCase)
		dto.ExecRate = fmt.Sprintf("%.2f", er*100)
	}
	dto.TotalUnTest = dto.TotalCase - dto.TotalExecuteCase
	// 计算各个模块的统计
	for _, v := range dto.ModelRateMap {
		v.Cal()
	}
}

func (dto *MaraTestPlanPassRateDTO) GetModelRate(modelType string) *MaraTestPlanPassRateDTO {
	if dto.ModelRateMap == nil {
		dto.ModelRateMap = make(map[string]*MaraTestPlanPassRateDTO)
	}
	_, ok := dto.ModelRateMap[modelType]
	if !ok {
		dto.ModelRateMap[modelType] = &MaraTestPlanPassRateDTO{}
	}
	return dto.ModelRateMap[modelType]
}

func (m MaraTestPlanPassRateDTO) Value() (driver.Value, error) {
	return json.Marshal(m)
}

func (m *MaraTestPlanPassRateDTO) Scan(value any) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalid JSON data")
	}
	return json.Unmarshal(bytes, m)
}
