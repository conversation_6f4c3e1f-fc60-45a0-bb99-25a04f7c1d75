package outer

import "git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"

type AddBatchAndRelateDemandReq struct {
	LibraryID       int64            `json:"libraryId"`
	ParentNodeID    int64            `json:"parentNodeId"`
	MarsCaseGptDTOS []MarsCaseGptDTO `json:"marsCaseGptDTOS"`
	RelateDemandIDs []int64          `json:"relateDemandIds"`
}

type MarsCaseGptDTO struct {
	Name            string                `json:"name"`
	Priority        string                `json:"priority"`
	Precondition    string                `json:"precondition"`
	CaseStepReqDTOs *models.MarsCaseSteps `json:"steps"`
}
