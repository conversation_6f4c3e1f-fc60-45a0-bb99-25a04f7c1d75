package dto

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type MarsReviewSearchReqDTO struct {
	SpaceId      string   `schema:"spaceid"`
	Name         string   `schema:"name"`
	ReviewStatus []string `schema:"reviewStatus"`
	Reviewer     string   `schema:"reviewer"`
	Creator      []string `schema:"creator"`
	EndTime      string   `schema:"endTime"`
}

type MarsReviewCaseReq struct {
	CaseIds  []int64 `json:"caseIds"`
	ReviewId int64   `json:"reviewId"`
}

type MarsReviewDTO struct {
	models.MarsReview
	CaseSize int64 `json:"caseSize"`
}

type MarsReviewStatusUpdateReq struct {
	Id int64 `json:"id"`
}

type MarsReviewCommentDTO struct {
	ReviewId    int64       `json:"reviewId"`
	Comment     string      `json:"comment"`
	Commentor   string      `json:"commentor"`
	CommentTime *times.Time `json:"commentTime"`
}

type MarsReviewDemandCaseList struct {
	Demand struct {
		Name string `json:"name"`
		Id   string `json:"id"`
		Code string `json:"code"`
	} `json:"demand"`
	CaseList []struct {
		Id           int64  `json:"id"`
		Code         string `json:"code"`
		Name         string `json:"name"`
		Library      string `json:"library"`
		ReviewStatus string `json:"reviewStatus"`
	} `json:"caseList"`
}

type MarsReviewCaseDTO struct {
	models.MarsCase
	ReviewStatus string                            `json:"reviewStatus"`
	ParentName   string                            `json:"ParentName"`
	LibraryName  string                            `json:"libraryName"`
	Comments     models.MarsReviewCaseCommentSlice `json:"comments"`
}

type MarsReviewCaseComment struct {
	ReviewId int64                        `json:"reviewId"`
	CaseId   int64                        `json:"caseId"`
	Comment  models.MarsReviewCaseComment `json:"comment"`
}
