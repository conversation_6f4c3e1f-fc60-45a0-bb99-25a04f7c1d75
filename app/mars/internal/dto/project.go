package dto

type ProjectBugStatisticsDTO struct {
	Total     int64   `json:"total"`
	Fixed     int64   `json:"fixed"`
	FixedRate float32 `json:"fixedRate"`
}

type ProjectCommonSelectReq struct {
	Search           string   `json:"search"`
	SearchFieldKey   string   `json:"searchFieldKey"`
	SearchFieldValue string   `json:"searchFieldValue"`
	Ids              []string `json:"ids"`
	Value            any      `json:"value"`
	ProjectId        string   `json:"projectId"`
	IterationId      string   `json:"iterationId"`
	PriorityLevel    []int64  `json:"priorityLevel"`
	Code             string   `json:"code"`
	Codes            []string `json:"codes"`
	Model            int64    `json:"model"`
	Page             int64    `json:"page"`
	PageSize         int64    `json:"pageSize"`
}

type ProjectCommonSelectReqInSchema struct {
	TestPlanId       int64    `schema:"testPlanId"`
	Search           string   `schema:"search"`
	SearchFieldKey   string   `schema:"searchField<PERSON>ey"`
	SearchFieldValue string   `schema:"searchFieldValue"`
	Ids              []string `schema:"ids"`
	Value            any      `schema:"value"`
	ProjectId        string   `schema:"projectId"`
	IterationId      string   `schema:"iterationId"`
	PriorityLevel    []int64  `schema:"priorityLevel"`
	Code             string   `schema:"code"`
	Model            int64    `schema:"model"`
}

type MarsDemandReq struct {
	Page          int64  `schema:"page"`
	PageSize      int64  `schema:"pageSize"`
	Model         string `schema:"model"`
	Code          string `schema:"code"`          // 需求编码
	PriorityLevel int64  `schema:"priorityLevel"` // 需求优先级
	Search        string `schema:"search"`        // 需求名称
}
