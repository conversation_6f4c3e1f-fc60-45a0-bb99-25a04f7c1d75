package dto

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
)

type MarsFolderTree struct {
	Id               int64             `json:"id"`
	LibraryId        int64             `json:"libraryId"`
	Name             string            `json:"name"`
	NodeType         string            `json:"nodeType"`
	ParentId         int64             `json:"parentId"`
	DescendantsTotal int64             `json:"descendantsTotal" ` // 子孙节点总数
	SiblingOrder     int64             `json:"siblingOrder" `     // 兄弟节点的顺序
	Children         []*MarsFolderTree `json:"children" gorm:"-"`
}

type TestPlanCaseDto struct {
	models.MarsCase
	TestPlanId int64  `json:"testPlanId"`
	DemandId   string `json:"demandId"`
}

type AddMarsCaseRelationReq struct {
	CaseId       int64    `json:"caseId"`          // 用例 ids
	RelatedIds   []string `json:"relateDemandIds"` // 关联的需求/用例 id
	RelationType string   `json:"relationType"`    // 关联的类型 demand
}

// MarsCaseTMReqDTO TM保存测试用例dto
type MarsCaseTMReqDTO struct {
	SpaceId         string          `json:"spaceId"`
	LibraryId       int64           `json:"libraryId"`
	ParentNodeId    int64           `json:"parentNodeId"`
	RelateDemandIds []int64         `json:"relateDemandIds"`
	MarsCaseGptDTOS []MarsCaseTMDTO `json:"marsCaseGptDTOS"`
}

type MarsCaseTMDTO struct {
	Name               string                `json:"name"`
	Precondition       string                `json:"precondition"`
	Priority           string                `json:"priority"`
	CaseStepReqDTOList []models.MarsCaseStep `json:"caseStepReqDTOList"`
}

// MarsCaseMoveCopyReqDTO 批量移动以及复制用例的请求dto
type MarsCaseMoveCopyReqDTO struct {
	CaseIds         []int64 `json:"caseIds" validate:"required=true"`
	SourceLibraryId int64   `json:"sourceLibraryId" validate:"required=true"`
	TargetLibraryId int64   `json:"targetLibraryId" validate:"required=true"`
	NodeId          int64   `json:"nodeId" validate:"required=true"` // 目标文件夹id
}

type MarsCaseCommentDTO struct {
	models.MarsReviewCaseRelation
	Commenter string `json:"commenter" gorm:"-"`
}

// TM查询mars用例的dto
type TMMarsCaseDTO struct {
	Id       int64  `json:"id"`
	Name     string `json:"json"`
	Library  string `json:"library"`
	NodePath string `json:"nodePath"`
}

type TestPlanCase struct {
	models.MarsCase
	Status   string `json:"status"`
	TestData string `json:"testData"`
}

// CaseListDTO 用例列表返回DTO
type CaseListDTO struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	LibraryName string `json:"library"`  // 用例库名称
	NodePath    string `json:"nodePath"` // 用例目录名称
}
