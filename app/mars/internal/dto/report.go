package dto

type MarsReportSearchReq struct {
	Name      string `schema:"name"`
	Stage     string `schema:"stage"`
	BeginTime string `schema:"beginTime"`
	EndTime   string `schema:"endTime"`
	Owner     string `schema:"owner"`
}

type MarsHistoryGramItemVo struct {
	Key   string `json:"key"`
	Count int64  `json:"count"`
}

type MarsEmailReportReq struct {
	EmailUserIds []string `json:"emailUserIds"`
	TestPlanId   int64    `json:"testPlanId"`
	File         string   `json:"file"`
	Url          string   `json:"url"`
	NoticeType   []string `json:"noticeType"`
}

type DownloadReportReq struct {
	Id     int64  `json:"id"`
	PlanId int64  `json:"planId"`
	Image  string `json:"image"`
}
