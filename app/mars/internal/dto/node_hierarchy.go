package dto

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
)

type NodeDto struct {
	Id        int64  `json:"id"`
	ParentId  int64  `json:"parentId"`
	NodeType  string `json:"nodeType"` // 节点类型
	LibraryId int64  `json:"libraryId"`
	Name      string `json:"name"`
}

type MarsNodeHierarchyDTO struct {
	Id        int64                   `json:"id"`
	ParentId  int64                   `json:"parentId"`
	NodeType  int                     `json:"nodeType"` // 节点类型
	LibraryId int64                   `json:"libraryId"`
	NodeName  string                  `json:"nodeName"`
	NodeCount int64                   `json:"nodeCount" gorm:"-"`
	Children  []*MarsNodeHierarchyDTO `json:"children" gorm:"-"`
	Sort      int64                   `json:"sort"`
}

type MarsNodeMoveDTO struct {
	LibraryId    int64 `json:"libraryId"`
	Id           int64 `json:"id"`           // 移动节点的 id
	NewParentId  int64 `json:"newParentId"`  // 新的父节点 id
	NewSiblingId int64 `json:"newSiblingId"` // 新的后置兄弟节点 ID。"-1" 表示无，即当前目录最后一个节点
}

type MarsCaseListReqDTO struct {
	DemandId    int64    `schema:"demandId"`
	Name        string   `schema:"name"`
	PriorityStr string   `schema:"priority"`
	Priority    []string `schema:"-"`
	Creator     string   `schema:"creator"`
	LibraryId   int64    `schema:"libraryId"`
	ParentId    int64    `schema:"parentId"`
	Nesting     bool     `schema:"nesting"` // true 包含子目录的case，false 不包含
	ReviewId    int64    `schema:"reviewId"`
	TagIdsStr   string   `schema:"tagIds"`
	TagIds      []string `schema:"-"` // 标签 id 列表
	dto.AddonTimeDTO
}
