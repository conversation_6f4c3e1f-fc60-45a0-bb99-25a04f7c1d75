package ui

import "git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"

// MarsUiPlanExecResult 计划执行结果
type MarsUiPlanExecResult struct {
	AppId          string `json:"app_id"`
	PlanId         string `json:"plan_id"`
	PlanName       string `json:"plan_name"`
	CaseFailCount  int64  `json:"case_fail_count"`
	CasePassCount  int64  `json:"case_pass_count"`
	CaseTotalCount int64  `json:"case_total_count"`
	Duration       int64  `json:"duration"` // 执行时长
	Env            string `json:"env"`
	Msg            string `json:"msg"`
	PassRate       string `json:"pass_rate"`
	ReportUrl      string `json:"report_url"`
	Success        bool   `json:"success"`
	ReportName     string `json:"report_name"`
}

type MarsUiDemandExpand struct {
	models.ProjectDemandDTO
	Selected     bool         `json:"selected"`     // 需求是否已被选择
	SelectedCase []SelectCase `json:"selectedCase"` // 选中的用例
	TotalCase    []SelectCase `json:"totalCase"`    // 可选的用例
}

type SelectCase struct {
	CaseId   string `json:"caseId"`   // 选中的用例 id
	CaseName string `json:"caseName"` // 选中的用例名称
	Priority string `json:"priority"` // 优先级
}

type VenusPlanDto struct {
	Id        string `json:"id"`
	Name      string `json:"name"`
	Env       string `json:"env"`
	CaseCount int64  `json:"caseCount"`
}
