package remote

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	pkdto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/netutil"
	"github.com/duke-git/lancet/v2/xerror"
)

type AptRemoteApi struct{}

// MagicResponse 定义通用API响应结构，包含业务数据
type MagicResponse[T any] struct {
	commondto.MagicCommonResp
	Data T `json:"data"`
}

// MagicResponsePage 定义分页API响应结构
type MagicResponsePage[T any] struct {
	commondto.MagicCommonResp
	Data struct {
		Total int64 `json:"total"`
		List  []T   `json:"list"`
	} `json:"data"`
}

// magicRequestDeal 处理HTTP请求并解码响应
func magicRequestDeal[T any](request *netutil.HttpRequest, bizPageResp *MagicResponsePage[T], bizData *MagicResponse[T]) error {
	httpClient := netutil.NewHttpClient()
	startTime := time.Now()

	// 发送请求
	resp, err := httpClient.SendRequest(request)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("请求返回非200状态码: %d", resp.StatusCode)
	}

	// 解码响应
	var decodeErr error
	var code, message string

	if bizData != nil {
		decodeErr = httpClient.DecodeResponse(resp, bizData)
		code, message = bizData.Code, bizData.Message
	} else {
		decodeErr = httpClient.DecodeResponse(resp, bizPageResp)
		code, message = bizPageResp.Code, bizPageResp.Message
	}

	if decodeErr != nil {
		return fmt.Errorf("解码响应失败: %w", decodeErr)
	}

	// 检查业务码
	if code != "1000" {
		return fmt.Errorf("业务错误: %s", message)
	}

	// 记录请求耗时
	logger.Logger.Infof("请求 %s 执行耗时: %v", request.RawURL, time.Since(startTime))
	return nil
}

type MagicCommonResp struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

type AptLibrary struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	ExtendId    int64  `json:"extendId"`
	VersionId   int64  `json:"versionId"`
	VersionName string `json:"versionName"`
}

type AptVersionInfo struct {
	Version   string `json:"version"`
	VersionId int64  `json:"versionId"`
}

type AptFolderInfo struct {
	Id        int64           `json:"id"`
	ParentId  int64           `json:"parentId"`
	NodeName  string          `json:"nodeName"`
	NodeType  int64           `json:"nodeType"`
	LibraryId int64           `json:"libraryId"`
	Children  []AptFolderInfo `json:"children"`
}

type AptCaseInfo struct {
	CaseId      int64  `json:"caseId"`
	CaseName    string `json:"caseName"`
	SuiteId     int64  `json:"suiteId"`
	LibraryName string `json:"libraryName"`
	SuiteName   string `json:"suiteName"`
	VersionName string `json:"versionName"`
}

type AptCaseExecResult struct {
	ExecuteTag    string  `json:"executeTag"`
	DeleteCaseIds []int64 `json:"deleteCaseIds"`
	ExistCase     bool    `json:"existCase"` // 是否存在可执行的用例
}

// AptCaseExecCallback 执行callback
type AptCaseExecCallback struct {
	ReportUrl       string `json:"reportUrl"`
	ExecuteTag      string `json:"executeTag"`
	CaseExecResults []struct {
		ExecuteResult int64  `json:"executeResult"`
		RelatedCaseId int64  `json:"relatedCaseId"`
		Result        string `json:"result"`
	} `json:"caseExecResults"`
}

func (AptRemoteApi) GetTreeWithSet(ctx *commoncontext.MantisContext, baseId int64, versionId int64) (any, error) {
	if baseId == 0 || versionId == 0 {
		logger.Logger.Panicf("%+v", xerror.New("baseId 或 versionId为空"))
	}
	uri := "/api/nodes/hierarchy/getTreeWithSet?"
	uri += fmt.Sprintf("baseId=%d&versionId=%d", baseId, versionId)
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Modules.Jupiter.Endpoint + uri,
		Method:  "GET",
		Headers: ctx.Header,
	}
	var rs MagicResponse[[]AptFolderInfo]
	err := magicRequestDeal(request, nil, &rs)
	if err != nil {
		return nil, err
	}
	return rs.Data, err
}

// GetLibrary 查询用例库
func (AptRemoteApi) GetLibrary(ctx *commoncontext.MantisContext, name string) ([]AptLibrary, error) {
	uri := "/v2/api/case/library/query?"
	uri += fmt.Sprintf("page=1&pageSize=999&name=%s", name)
	logger.Logger.Infof("请求 %s", configs.Config.Modules.Jupiter.Endpoint+uri)
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Modules.Jupiter.Endpoint + uri,
		Method:  "GET",
		Headers: ctx.Header,
	}
	var rs MagicResponsePage[AptLibrary]
	err := magicRequestDeal(request, &rs, nil)
	if err != nil {
		return nil, err
	}
	return rs.Data.List, err
}

func (AptRemoteApi) GetCaseListByIds(ctx *commoncontext.MantisContext, ids []int64) ([]AptCaseInfo, error) {
	uri := "/api/case/queryCases"
	return getCaseList(ctx, uri, map[string]any{
		"idList": ids,
	})
}

func (AptRemoteApi) GetCaseListByPidAndName(ctx *commoncontext.MantisContext, nodeId int64, name string) ([]AptCaseInfo, error) {
	uri := "/api/case/queryTestPlanCases"
	return getCaseList(ctx, uri, map[string]any{
		"nodeId": nodeId,
		"name":   name,
	})
}

func getCaseList(ctx *commoncontext.MantisContext, uri string, params map[string]any) ([]AptCaseInfo, error) {
	bodyData, _ := json.Marshal(params)
	headers := ctx.Header
	headers["Content-Type"] = []string{"application/json"}
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Modules.Jupiter.Endpoint + uri,
		Method:  http.MethodPost,
		Headers: ctx.Header,
		Body:    bodyData,
	}
	var rs MagicResponse[[]AptCaseInfo]
	err := magicRequestDeal(request, nil, &rs)
	if err != nil {
		return nil, err
	}

	return rs.Data, err
}

// GetLibraryVersion 查询用例库版本
func (a AptRemoteApi) GetLibraryVersion(ctx *commoncontext.MantisContext, id int64) ([]AptVersionInfo, error) {
	uriPath := "%s/api/case/library/selectCaseUseVersionList/%d"
	uri := fmt.Sprintf(uriPath, configs.Config.Modules.Jupiter.Endpoint, id)
	request := &netutil.HttpRequest{
		RawURL:  uri,
		Method:  "GET",
		Headers: ctx.Header,
	}
	var rs MagicResponse[[]AptVersionInfo]
	err := magicRequestDeal(request, nil, &rs)
	if err != nil {
		return nil, err
	}
	return rs.Data, err
}

// GetDefaultMachine 获取默认的执行机
func (a AptRemoteApi) GetDefaultMachine(ctx *commoncontext.MantisContext) (int64, error) {
	uriPath := "%s/api/config/machine/list?isSubject=0"
	uri := fmt.Sprintf(uriPath, configs.Config.Modules.Jupiter.Endpoint)
	request := &netutil.HttpRequest{
		RawURL:  uri,
		Method:  "GET",
		Headers: ctx.Header,
	}
	type Machine struct {
		Value int64  `json:"value"`
		Label string `json:"label"`
	}
	var rs MagicResponse[[]Machine]
	err := magicRequestDeal(request, nil, &rs)
	if err != nil {
		return 0, err
	}
	if len(rs.Data) == 0 {
		return 0, fmt.Errorf("没有默认的执行机")
	}
	return rs.Data[0].Value, err
}

type AptCaseExecParam struct {
	SpaceId string         `json:"spaceId"`
	User    pkdto.UserInfo `json:"baseUserDTO"`
	CaseIds []int64        `json:"ids"`
	SiteId  int64          `json:"siteId"`
	EnvId   string         `json:"envId"`
	PlanId  int64          `json:"planId"`
}

// CaseExec 用例执行
func (a AptRemoteApi) CaseExec(ctx *commoncontext.MantisContext, params AptCaseExecParam) (*AptCaseExecResult, error) {
	uri := "/api/core/executeMarsCases"
	logger.Logger.Infof("执行接口用例：请求 %s，body=%+v", configs.Config.Modules.Jupiter.Endpoint+uri, params)
	bodyData, _ := json.Marshal(params)
	headers := ctx.Header
	headers["Content-Type"] = []string{"application/json"}
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Modules.Jupiter.Endpoint + uri,
		Method:  http.MethodPost,
		Headers: ctx.Header,
		Body:    bodyData,
	}
	var rs MagicResponse[AptCaseExecResult]
	err := magicRequestDeal(request, nil, &rs)
	if err != nil {
		return nil, err
	}
	logger.Logger.Infof("执行接口用例响应 %+v", rs)
	return &rs.Data, err
}
