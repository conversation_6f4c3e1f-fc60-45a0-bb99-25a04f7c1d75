package project

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type projectRemoteApi interface {
	GetBugs(ctx *commoncontext.MantisContext, req dto.ProjectCommonSelectReq) ([]models.ProjectBugDTO, int64, error)
	GetIterations(ctx *commoncontext.MantisContext, req dto.ProjectCommonSelectReq) ([]models.ProjectItemDTO, int64, error)
	GetDemands(ctx *commoncontext.MantisContext, req dto.ProjectCommonSelectReq) ([]models.ProjectDemandDTO, int64, error)
	GetPriorityLevel(ctx *commoncontext.MantisContext) ([]models.PriorityLevelDTO, error)
	GetProjectInfo(ctx *commoncontext.MantisContext, ids []string, search string) ([]models.ProjectDTO, error)
}

var ProjectRemoteApi projectRemoteApi

func Init() {
	switch configs.Config.Modules.Mars.ProjectRemoteApi.Type {
	case "tm":
		ProjectRemoteApi = tmRemoteApi{}
	default:
		logger.Logger.Panicf("unkown type: %s", configs.Config.Modules.Mars.ProjectRemoteApi.Type)
	}
}

const OverStateEnd = "结束"
