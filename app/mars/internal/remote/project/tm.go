package project

import (
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	"github.com/duke-git/lancet/v2/netutil"
	"github.com/duke-git/lancet/v2/xerror"
)

type tmRemoteApi struct{}

type TMItemPageDTO struct {
	commondto.CubeBaseCommonResp
	Data struct {
		CurrentPage int64 `json:"currentPage"`
		PageSize    int64 `json:"pageSize"`
		Pages       int64 `json:"pages"`
		Total       int64 `json:"total"`
		List        []struct {
			Id            int64  `json:"id"`
			Name          string `json:"name"`
			PriorityLevel struct {
				IsDisable int    `json:"isDisable"`
				Id        int64  `json:"id"`
				Name      string `json:"name"`
				Type      string `json:"type"`
			} `json:"priorityLevel"`
			TaskTypeId struct {
				Name string `json:"name"`
			} `json:"taskTypeId"`
			Code      string `json:"code"`
			OverState int64  `json:"overState"`
			ProjectVo struct {
				Name string `json:"name"`
				Id   int64  `json:"id"`
			} `json:"projectVo"`
			GmtCreated  int64 `json:"gmtCreated"`
			FixTime     int64 `json:"fixTime"`
			GmtFixDate  int64 `json:"gmtFixDate"`
			ReopenNum   int64 `json:"reopenNum"`
			IterationId struct {
				Name string `json:"name"`
			} `json:"iterationId"`
			Assignee struct {
				Name string `json:"name"`
			} `json:"assignee"`
			StateVo struct {
				Name string `json:"name"`
			} `json:"stateVo"`
			Creator struct {
				Name string `json:"name"`
			} `json:"creator"`
		} `json:"list"`
	} `json:"data"`
}

type TMIterationPageDTO struct {
	commondto.CubeBaseCommonResp
	Data struct {
		CurrentPage int64                   `json:"currentPage"`
		PageSize    int64                   `json:"pageSize"`
		Pages       int64                   `json:"pages"`
		Total       int64                   `json:"total"`
		List        []models.ProjectItemDTO `json:"list"`
	} `json:"data"`
}

type TMPriorityLevelPageDTO struct {
	commondto.CubeBaseCommonResp
	Data struct {
		CurrentPage int64                     `json:"currentPage"`
		PageSize    int64                     `json:"pageSize"`
		Pages       int64                     `json:"pages"`
		Total       int64                     `json:"total"`
		List        []models.PriorityLevelDTO `json:"list"`
	} `json:"data"`
}

type TMProjectPageDTO struct {
	commondto.CubeBaseCommonResp
	Data struct {
		CurrentPage int64               `json:"currentPage"`
		PageSize    int64               `json:"pageSize"`
		Pages       int64               `json:"pages"`
		Total       int64               `json:"total"`
		List        []models.ProjectDTO `json:"list"`
	} `json:"data"`
}

func (tmRemoteApi) initQuery(req dto.ProjectCommonSelectReq) map[string]any {
	searchSlice := []map[string]any{{
		"value":    req.Value,
		"judge":    3,
		"fieldKey": req.SearchFieldKey,
		"type":     "Array",
	}}
	if req.ProjectId != "" {
		searchSlice = append(searchSlice, map[string]any{
			"value":    []string{req.ProjectId},
			"judge":    1,
			"fieldKey": "projectVo.id",
			"type":     "Array",
		})
	}
	if req.IterationId != "" {
		searchSlice = append(searchSlice, map[string]any{
			"value":    []string{req.IterationId},
			"judge":    1,
			"fieldKey": "iterationId.id",
			"type":     "Array",
		})
	}
	if len(req.Ids) != 0 {
		ids := make([]int64, 0)
		for _, id := range req.Ids {
			id, _ := strconv.ParseInt(id, 10, 64)
			ids = append(ids, id)
		}
		searchSlice = append(searchSlice, map[string]any{
			"value":    ids,
			"judge":    1,
			"fieldKey": "id",
			"type":     "Array",
		})
	}
	if req.Search != "" {
		searchSlice = append(searchSlice, map[string]any{
			"value":    req.Search,
			"judge":    1,
			"fieldKey": "name",
			"type":     "String",
		})
	}
	if len(req.PriorityLevel) != 0 {
		searchSlice = append(searchSlice, map[string]any{
			"value":    req.PriorityLevel,
			"judge":    1,
			"fieldKey": "priorityLevel.id",
			"type":     "Array",
		})
	}
	if req.Code != "" {
		searchSlice = append(searchSlice, map[string]any{
			"value":    req.Code,
			"judge":    3,
			"fieldKey": "code",
			"type":     "String",
		})
	}
	if len(req.Codes) != 0 {
		searchSlice = append(searchSlice, map[string]any{
			"value":    req.Codes,
			"judge":    1,
			"fieldKey": "code",
			"type":     "Array",
		})
	}
	res := map[string]any{
		"sort": map[string]any{
			"gmtCreated": "desc",
		},
		"query": []any{searchSlice},
	}
	if req.Page != 0 && req.PageSize != 0 {
		res["pageVo"] = map[string]any{
			"currentPage": req.Page,
			"pageSize":    req.PageSize,
		}
	} else {
		res["pageVo"] = map[string]any{
			"currentPage": 1,
			"pageSize":    math.MaxInt32,
		}
	}
	return res
}

func (tmRemoteApi) transOverState2Name(overState int64) string {
	switch overState {
	case 0:
		return "创建"
	case 1:
		return "结束"
	case 2:
		return "进行中"
	case 3:
		return "挂起"
	case 4:
		return "会签"
	default:
		return "其他"
	}
}

func (t tmRemoteApi) GetBugs(ctx *commoncontext.MantisContext, req dto.ProjectCommonSelectReq) ([]models.ProjectBugDTO, int64, error) {
	request := t.initQuery(req)
	queryRes, err := t.getItems(ctx, request)
	if err != nil {
		return nil, 0, err
	}
	total := queryRes.Data.Total
	projectDemandDTOs := make([]models.ProjectBugDTO, 0)
	for _, item := range queryRes.Data.List {
		projectDemand := models.ProjectBugDTO{
			Name:         item.Name,
			Id:           item.Id,
			Project:      item.ProjectVo.Name,
			ProjectId:    strconv.FormatInt(item.ProjectVo.Id, 10),
			Iteration:    item.IterationId.Name,
			Assignee:     item.Assignee.Name,
			State:        t.transOverState2Name(item.OverState),
			Creator:      item.Creator.Name,
			GmtCreated:   times.UnixToTimes(item.GmtCreated),
			PriorityId:   item.PriorityLevel.Id,
			PriorityName: item.PriorityLevel.Name,
			FixTime:      item.FixTime,
			GmtFixDate:   times.UnixToTimes(item.GmtFixDate),
			ReopenNum:    item.ReopenNum,
			Link:         fmt.Sprintf("/team/space/1/project/%d/bug/%d", item.ProjectVo.Id, item.Id),
			OverState:    t.transOverState2Name(item.OverState),
			Code:         item.Code,
		}
		projectDemandDTOs = append(projectDemandDTOs, projectDemand)
	}
	return projectDemandDTOs, total, nil
}

func (t tmRemoteApi) GetIterations(ctx *commoncontext.MantisContext, req dto.ProjectCommonSelectReq) ([]models.ProjectItemDTO, int64, error) {
	m := make(map[string]string)
	if req.Search != "" {
		m["search"] = req.Search
	}
	if len(req.Ids) != 0 {
		ids, err := json.Marshal(&(req.Ids))
		if err != nil {
			return nil, 0, err
		}
		m["ids"] = string(ids)
	}
	if req.ProjectId != "" {
		env, err := json.Marshal(&map[string]any{
			"projectIds": []string{req.ProjectId},
		})
		if err != nil {
			return nil, 0, err
		}
		m["env"] = string(env)
	}
	m["pageSize"] = fmt.Sprintf("%d", req.PageSize)
	m["currentPage"] = fmt.Sprintf("%d", req.Page)
	queryRes, err := t.getIterationList(ctx, m)
	if err != nil {
		return nil, 0, err
	}
	total := queryRes.Data.Total
	res := queryRes.Data.List
	for i := range res {
		res[i].Value = res[i].Id
		res[i].Label = res[i].Name
	}
	return res, total, nil
}

func (t tmRemoteApi) GetDemands(ctx *commoncontext.MantisContext, req dto.ProjectCommonSelectReq) ([]models.ProjectDemandDTO, int64, error) {
	req.Value = []int64{11}
	req.SearchFieldKey = "projectType.id"
	request := t.initQuery(req)
	queryRes, err := t.getItems(ctx, request)
	if err != nil {
		return nil, 0, err
	}
	projectDemandDTOS := make([]models.ProjectDemandDTO, 0, len(queryRes.Data.List))
	for _, item := range queryRes.Data.List {
		projectDemandDTOS = append(projectDemandDTOS, models.ProjectDemandDTO{
			Id:        item.Id,
			Code:      item.Code,
			Name:      item.Name,
			Type:      item.TaskTypeId.Name,
			OverState: item.StateVo.Name,
			Assignee:  item.Assignee.Name,
			Priority:  item.PriorityLevel.Name,
			Project:   item.ProjectVo.Name,
			Iteration: item.IterationId.Name,
		})
	}
	return projectDemandDTOS, queryRes.Data.Total, nil
}

func (tmRemoteApi) getItems(ctx *commoncontext.MantisContext, req map[string]any) (*TMItemPageDTO, error) {
	marshal, _ := json.Marshal(req)
	logger.Logger.Infof("请求tm筛选事项，参数:%s", string(marshal))
	start := time.Now()
	httpClient := netutil.NewHttpClient()
	body, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	uri := "/base/Filter/query"
	headers := ctx.Header
	headers["Content-Type"] = []string{"application/json"}
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Modules.Mars.ProjectRemoteApi.Endpoint + uri,
		Method:  http.MethodPost,
		Body:    body,
		Headers: headers,
	}
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("error in send request to tm getItems, err=%+v, resp=%v", err, resp)
	}
	var rs TMItemPageDTO
	err = httpClient.DecodeResponse(resp, &rs)
	if err != nil {
		return nil, fmt.Errorf("error in decode tm items, err=%+v", err)
	}
	if rs.ReturnCode != commondto.CubeBaseRespOkCode {
		return nil, fmt.Errorf("%v", xerror.New("error calling tm getItems, msg = %s", rs.ReturnMsg))
	}
	logger.Logger.Info("Func -> GetItemOpen执行耗时：", time.Since(start))
	return &rs, nil
}

func (tmRemoteApi) getIterationList(ctx *commoncontext.MantisContext, req map[string]string) (*TMIterationPageDTO, error) {
	logger.Logger.Infof("请求迭代，参数:%v", req)
	start := time.Now()
	httpClient := netutil.NewHttpClient()
	uri := "/base/search/select/iteration"
	if len(req) != 0 {
		uri += "?"
		for k, v := range req {
			uri += fmt.Sprintf("%s=%s&", url.QueryEscape(k), url.QueryEscape(v))
		}
	}
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Modules.Mars.ProjectRemoteApi.Endpoint + uri,
		Method:  http.MethodGet,
		Headers: ctx.Header,
	}
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("error in send request to tm getIterations, err=%+v, resp=%v", err, resp)
	}
	var rs TMIterationPageDTO
	err = httpClient.DecodeResponse(resp, &rs)
	if err != nil {
		return nil, fmt.Errorf("error in decode tm iterations, err=%+v", err)
	}
	if rs.ReturnCode != commondto.CubeBaseRespOkCode {
		return nil, fmt.Errorf("%v", xerror.New("error calling tm getIterations, msg = %s", rs.ReturnMsg))
	}
	logger.Logger.Info("Func -> GetItemOpen执行耗时：", time.Since(start))
	return &rs, nil
}

func (t tmRemoteApi) GetPriorityLevel(ctx *commoncontext.MantisContext) ([]models.PriorityLevelDTO, error) {
	page, err := t.getPriorityLevel(ctx)
	if err != nil {
		return nil, err
	}
	return page.Data.List, nil
}

func (tmRemoteApi) getPriorityLevel(ctx *commoncontext.MantisContext) (*TMPriorityLevelPageDTO, error) {
	logger.Logger.Infof("请求tm优先级...")
	start := time.Now()
	httpClient := netutil.NewHttpClient()
	uri := "/base/search/select/priorityLevel"
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Modules.Mars.ProjectRemoteApi.Endpoint + uri,
		Method:  http.MethodGet,
		Headers: ctx.Header,
	}
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("error in send request to tm getPriorityLevel, err=%+v, resp=%v", err, resp)
	}
	var rs TMPriorityLevelPageDTO
	err = httpClient.DecodeResponse(resp, &rs)
	if err != nil {
		return nil, fmt.Errorf("error in decode tm getPriorityLevel, err=%+v", err)
	}
	if rs.ReturnCode != commondto.CubeBaseRespOkCode {
		return nil, fmt.Errorf("%v", xerror.New("error calling tm getPriorityLevel, msg = %s", rs.ReturnMsg))
	}
	logger.Logger.Info("Func -> getPriorityLevel执行耗时：", time.Since(start))
	return &rs, nil
}

func (tmRemoteApi) GetProjectInfo(ctx *commoncontext.MantisContext, ids []string, search string) ([]models.ProjectDTO, error) {
	logger.Logger.Infof("请求 tm 查询项目信息...")
	start := time.Now()
	httpClient := netutil.NewHttpClient()
	uri := "/base/open/v1/getProjectList?"
	if len(ids) > 0 {
		for _, id := range ids {
			uri += fmt.Sprintf("ids=%s&", id)
		}
	}
	if search != "" {
		uri += fmt.Sprintf("search=%s", search)
	}
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Modules.Mars.ProjectRemoteApi.Endpoint + uri,
		Method:  http.MethodGet,
		Headers: ctx.Header,
	}
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("error in send request to tm GetProjectInfo, err=%+v, resp=%v", err, resp)
	}
	var rs TMProjectPageDTO
	err = httpClient.DecodeResponse(resp, &rs)
	if err != nil {
		return nil, fmt.Errorf("error in decode tm GetProjectInfo, err=%+v", err)
	}
	if rs.ReturnCode != commondto.CubeBaseRespOkCode {
		return nil, fmt.Errorf("%v", xerror.New("error calling tm GetProjectInfo, msg = %s", rs.ReturnMsg))
	}
	logger.Logger.Info("Func -> GetProjectInfo 耗时：", time.Since(start))
	return rs.Data.List, nil
}
