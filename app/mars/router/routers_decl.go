package router

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/controller/openapi"
	. "git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codegen/router/decl"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/auditlog"
)

const (
	accessLog       = "accessLog"
	recovery        = "recovery"
	trace           = "trace"
	authentication  = "authentication"
	auditLog        = "auditLog"
	authorization   = "authorization"
	authenticationx = "authentication-"
	authorizationx  = "authorization-"
)

const (
	MarsAuditResourceLabelKey = auditlog.AuditResourceLabelKey + "=" + "测试管理"
)

var _ = Path("/mars", Middlewares([]string{recovery, trace, accessLog, authentication, auditLog},
	Labels([]string{MarsAuditResourceLabelKey},
		Constructor(controller.InitializeEnumsController,
			Path("/enums",
				Path("/priority", GET(controller.DefaultEnumsController.Priority)),
				Path("/caseReviewStatus", GET(controller.DefaultEnumsController.CaseReviewStatus)),
				Path("/reviewStatus", GET(controller.DefaultEnumsController.ReviewStatus)),
				Path("/planType", GET(controller.DefaultEnumsController.PlanType)),
				Path("/planStatus", GET(controller.DefaultEnumsController.PlanStatus)),
				Path("/planRelationType", GET(controller.DefaultEnumsController.PlanRelationType)),
				Path("/planStage", GET(controller.DefaultEnumsController.PlanStage)),
				Path("/planModels", GET(controller.DefaultEnumsController.PlanModels)),
				Path("/caseResult", GET(controller.DefaultEnumsController.CaseResult)),
				Path("/tmPriority", GET(controller.DefaultEnumsController.TmPriority)),
				Path("/tmEnable", GET(controller.DefaultEnumsController.TmEnable)),
			),
		),
		Constructor(controller.InitializeCaseLibraryController,
			Path("/caseLibrary",
				Middleware(authorization,
					// 用例库新增
					Path("/add", POST(controller.DefaultCaseLibraryController.InsertCaseLibrary, "auditlog.id=$resp.data.id", "auth.code=MANTIS-MARS-CASELIBRARY-CREATE")),
					// 用例库修改
					Path("/update", PUT(controller.DefaultCaseLibraryController.UpdateCaseLibrary, "auditlog.id=$resp.data.id", "auth.code=MANTIS-MARS-CASELIBRARY-EDIT")),
					// 用例库复制
					Path("/copy", POST(controller.DefaultCaseLibraryController.CopyCaseLibrary, "auditlog.id=$json.libraryId", "auth.code=MANTIS-MARS-CASELIBRARY-COPY")),
					// 用例库删除
					Path("/delete/{id}", DELETE(controller.DefaultCaseLibraryController.DeleteCaseLibrary, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-CASELIBRARY-DELETE")),
				),
				// 用例库列表查询
				Path("/list", GET(controller.DefaultCaseLibraryController.SearchCaseLibraryList)),
			),
		),
		Constructor(controller.InitializeSelectorsController,
			Path("/selectors",
				// 查询需求
				Path("/demands", GET(controller.DefaultSelectorsController.SearchDemands)),
				// 查询迭代
				Path("/iterations", GET(controller.DefaultSelectorsController.SearchIteration)),
				// 查询用例库下标签
				Path("/tagsInLib", GET(controller.DefaultSelectorsController.SearchTagInLib)),
			),
		),
		Constructor(controller.InitializeXmindController,
			Path("/xmind",
				Middleware(authorization,
					Path("/import", POST(controller.DefaultXmindController.ImportXmind, "auth.code=MANTIS-MARS-CASELIBRARY-IMPORT")),
					Path("/export", POST(controller.DefaultXmindController.ExportXmind, "auditlog.id=$json.parentId", "auth.code=MANTIS-MARS-CASELIBRARY-EXPORT")),
				),
				Path("/template", GET(controller.DefaultXmindController.Template)),
			),
		),
		Constructor(controller.InitializeExcelController,
			Path("/excel",
				Middleware(authorization,
					Path("/import", POST(controller.DefaultExcelController.ImportExcel, "auth.code=MANTIS-MARS-CASELIBRARY-IMPORT")),
					Path("/export", POST(controller.DefaultExcelController.ExportExcel, "auditlog.id=$json.parentId", "auth.code=MANTIS-MARS-CASELIBRARY-EXPORT")),
				),
				Path("/template", GET(controller.DefaultExcelController.Template)),
			),
		),
		Constructor(controller.InitializeCaseController,
			Path("/case",
				Middleware(authorization,
					// 用例保存
					POST(controller.DefaultCaseController.Insert, "auditlog.id=$resp.data.id", "auth.code=MANTIS-MARS-CASELIBRARY-CASE-CREATE"),
					// 移动
					Path("/moveCase", POST(controller.DefaultCaseController.Move, "auditlog.id=$json.caseIds,.sourceLibraryId,.targetLibraryId,.nodeId", "auth.code=MANTIS-MARS-CASELIBRARY-BATCH-MOVE")),
					// 快速复制
					Path("/fastCopy/{id}", POST(controller.DefaultCaseController.Copy, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-CASELIBRARY-CASE-COPY")),
					// 批量复制
					Path("/batchCopy", POST(controller.DefaultCaseController.BatchCopy, "auditlog.id=$json.caseIds,.sourceLibraryId,.targetLibraryId,.nodeId", "auth.code=MANTIS-MARS-CASELIBRARY-BATCH-SHARE")),
					// 删除
					Path("/delete/{id}", DELETE(controller.DefaultCaseController.Delete, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-CASELIBRARY-CASE-DELETE")),
					// 批量删除
					Path("/batchDelete", DELETE(controller.DefaultCaseController.BatchDelete, "auditlog.id=$json.ids", "auth.code=MANTIS-MARS-CASELIBRARY-BATCH-DELETE")),
					// 用例更新(更新并将上次记录写入历史)
					Path("/{id}", PUT(controller.DefaultCaseController.Update, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-CASELIBRARY-CASE-EDIT")),
				),
				// 删除检查
				Path("/checkDelete", POST(controller.DefaultCaseController.CheckDelete)),
				// 用例历史记录（id 是 case 的 id）  -- 前端需要配合修改
				Path("/history/list/{id}", GET(controller.DefaultCaseController.HistoryList)),
				// 用例具体信息   -- 前端需要配合修改
				Path("/history/{id}", GET(controller.DefaultCaseController.HistoryDetail)),
				// 查询用例
				Path("/{id}", GET(controller.DefaultCaseController.Info)),
			),
		),
		Constructor(controller.InitializeNodeHierarchyController,
			Path("/nodes",
				// 添加节点
				Middleware(authorization,
					Path("/add", POST(controller.DefaultNodeHierarchyController.AddNode, "auditlog.id=$resp.data.id", "auth.code=MANTIS-MARS-CASELIBRARY-GROUP-CREATE")),
					// 树节点复制
					Path("/copy", POST(controller.DefaultNodeHierarchyController.Copy, "auditlog.id=$json.id", "auth.code=MANTIS-MARS-CASELIBRARY-CASE-COPY")),
					// 树节点删除
					Path("/delete/{id}", DELETE(controller.DefaultNodeHierarchyController.DeleteNode, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-CASELIBRARY-CASE-DELETE")),
					// 树节点移动 -- 参数有变化，前端配合修改
					Path("/moveNode", POST(controller.DefaultNodeHierarchyController.Move, "auditlog.id=$json.id", "auth.code=MANTIS-MARS-CASELIBRARY-BATCH-MOVE")),
					Path("/update", PUT(controller.DefaultNodeHierarchyController.Update, "auditlog.id=$json.id", "auth.code=MANTIS-MARS-CASELIBRARY-CASE-EDIT")),
				),
				Path("/checkDelete/{id}", POST(controller.DefaultNodeHierarchyController.CheckDeleteNode)),
				// 目录树（非叶子节点）
				Path("/tree/{libId}", GET(controller.DefaultNodeHierarchyController.GetTree)),
				// 用例列表查询
				Path("/caseList", GET(controller.DefaultNodeHierarchyController.GetCaseList)),
			),
		),
		Constructor(controller.InitializeReviewController,
			Path("/review",
				Middleware(authorization,
					// 创建评审，状态需要修改  -- 前端需要配合修改
					POST(controller.DefaultReviewController.Insert, "auditlog.id=$resp.data.id", "auth.code=MANTIS-MARS-REVIEW-CREATE"),
					// 修改评审，状态需要修改  -- 前端需要配合修改
					PUT(controller.DefaultReviewController.Update, "auditlog.id=$json.id", "auth.code=MANTIS-MARS-REVIEW-EDIT"),
					// 删除评审
					Path("/{id}", DELETE(controller.DefaultReviewController.Delete, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-REVIEW-DELETE")),
					// 添加（修改）总评
					Path("/reviewComment", POST(controller.DefaultReviewController.SaveReviewComment, "auditlog.id=$json.reviewId", "auth.code=MANTIS-MARS-REVIEW-SUMMARY")),
					// 评审关联用例
					Path("/relateCases", POST(controller.DefaultReviewController.RelateCase, "auditlog.id=$json.reviewId,.caseIds", "auth.code=MANTIS-MARS-REVIEW-CASE-RELATE")),
					// 移除关联的用例
					Path("/relateCases/remove", DELETE(controller.DefaultReviewController.RemoveCase, "auditlog.id=$json.reviewId,.caseIds", "auth.code=MANTIS-MARS-REVIEW-CASE-RELATE-CANCEL")),
					// 开始评审
					Path("/start", POST(controller.DefaultReviewController.Start, "auditlog.id=$json.id", "auth.code=MANTIS-MARS-REVIEW-EXECUTE")),
					// 结束评审
					Path("/finish", POST(controller.DefaultReviewController.Finish, "auditlog.id=$json.id", "auth.code=MANTIS-MARS-REVIEW-EXECUTE")),
					// 用例评审
					Path("/relateCases/comment", POST(controller.DefaultReviewController.SaveReviewCaseComment, "auditlog.id=$json.reviewId,.caseId", "auth.code=MANTIS-MARS-REVIEW-RESULT-MARK")),
				),

				// 查看评审列表
				Path("/list", GET(controller.DefaultReviewController.List)),
				// 查看总评
				Path("/reviewComment/{id}", GET(controller.DefaultReviewController.GetReviewComment)),
				// 查询单个关联用例
				Path("/relateCaseComments", GET(controller.DefaultReviewController.GetCaseComments)),
				// 查看评审绑定的用例
				Path("/relateCases/listByDemand", GET(controller.DefaultReviewController.CaseListByDemand)),
				// 查看评审详情
				Path("/{id}", GET(controller.DefaultReviewController.Info)),
			),
		),
		Constructor(controller.InitializeTestPlanController,
			Path("/testPlan",
				Middleware(authorization,
					// 新增测试计划
					POST(controller.DefaultTestPlanController.Insert, "auditlog.id=$resp.data.id", "auth.code=MANTIS-MARS-PLAN-CREATE"),
					// 编辑测试计划
					Path("/{id}", PUT(controller.DefaultTestPlanController.Update, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-PLAN-EDIT")),
					// 复制计划
					Path("/{id}/copy", POST(controller.DefaultTestPlanController.Copy, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-PLAN-COPY")),
					// 删除计划
					Path("/{id}", DELETE(controller.DefaultTestPlanController.Delete, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-PLAN-DELETE")),
					// 修改测试计划状态
					Path("/{id}/status", PUT(controller.DefaultTestPlanController.UpdateStatus, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-PLAN-EXECUTE")),
					// 计划修改模块
					Path("/{id}/model", PUT(controller.DefaultTestPlanController.UpdateModel, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-PLAN-MODULE-EDIT")),
					// 计划关联需求及需求下的用例
					Path("/{id}/relateDemand", POST(controller.DefaultTestPlanController.RelateDemand, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-PLAN-DEMAND-RELATE")),
					// 计划中批量删除需求
					Path("/{id}/demand/deleteBatch", DELETE(controller.DefaultTestPlanController.DeleteDemandBatch, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-PLAN-DEMAND-RELATE-CANCEL")),
					// 计划关联用例
					Path("/{id}/relateCase", POST(controller.DefaultTestPlanController.RelateCase, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-PLAN-CASE-RELATE")),
					// 计划中用例批量删除
					Path("/{id}/cases/deleteBatch", DELETE(controller.DefaultTestPlanController.DeleteCasesBatch, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-PLAN-CASE-RELATE-CANCEL")),
					// 计划中测试用例分配执行结果
					Path("/{id}/cases/updateResult", PUT(controller.DefaultTestPlanController.UpdateResult, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-PLAN-RESULT-BATCH-MARK")),
					// 计划中测试用例分配执行人
					Path("/{id}/cases/assignExecutors", PUT(controller.DefaultTestPlanController.AssignExecutors, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-PLAN-EXECUTOR-BATCH-ASSIGN")),
					// 同步需求下新的用例
					Path("/{id}/demand/sync", POST(controller.DefaultTestPlanController.SyncDemand, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-PLAN-DATA-BATCH-SYNC")),
					// 修改执行计划下单个功能用例结果
					Path("/{id}/functionCase/{relationId}", PUT(controller.DefaultTestPlanController.UpdateFunctionCaseInfo, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-PLAN-RESULT-MARK")),
					// 测试计划中创建用例并绑定测试计划 -- 前端修改路由 fastCreate
					Path("/{id}/caseFastCreate", POST(controller.DefaultTestPlanController.FastCreate, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-CASELIBRARY-CASE-CREATE")),
					// 保存报告
					Path("/{id}/saveReportView", POST(controller.DefaultTestPlanController.SaveReportView, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-PLAN-REPORT-CREATE")),
					// 用例移动(不能跨需求移动)
					Path("/{id}/moveCase", POST(controller.DefaultTestPlanController.MoveCase, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-PLAN-CASE-RELATE-SORT")),
				),
				// 查看测试计划列表
				Path("/list", GET(controller.DefaultTestPlanController.GetTestPlanList)),
				// 查看测试计划详情
				Path("/{id}/info", GET(controller.DefaultTestPlanController.Info)),
				// 计划查询模块
				Path("/{id}/model", GET(controller.DefaultTestPlanController.GetModel)),
				// 查询计划下的用例 case/ui/api
				Path("/{id}/cases", GET(controller.DefaultTestPlanController.CaseList)),
				// 获取计划下的需求信息及对应的用例信息
				Path("/{id}/demand", GET(controller.DefaultTestPlanController.GetPlanDemand)),
				// 获取执行计划下单个功能用例详情信息（包含结果）
				Path("/{id}/functionCase/{relationId}", GET(controller.DefaultTestPlanController.GetFunctionCaseInfo)),
				// 用例执行
				Path("/{id}/case/execute", POST(controller.DefaultTestPlanController.ExecuteCase)),
				// 查看历史报告
				Path("/{id}/historyReport", GET(controller.DefaultTestPlanController.HistoryReport)),
				// 生成报告
				Path("/{id}/reportView", GET(controller.DefaultTestPlanController.ReportView)),
			),
		),
		Constructor(controller.InitializeBugController,
			Path("/bug",
				// 获取项目下所有缺陷
				Path("/getProjectBugs", GET(controller.DefaultBugController.GetProjectBugs)),
				// 查询计划下的缺陷
				Path("/bugList", GET(controller.DefaultBugController.BugList)),
				Middleware(authorization,
					// 添加缺陷
					Path("/add", POST(controller.DefaultBugController.Add, "auditlog.id=$json.testPlanId,.bugIds", "auth.code=MANTIS-MARS-PLAN-BUG-RELATE")),
					// 解除关联的缺陷
					Path("/remove", DELETE(controller.DefaultBugController.Remove, "auditlog.id=$json.testPlanId,.recordIds", "auth.code=MANTIS-MARS-PLAN-REPORT-CREATE")),
				),
			),
		),
		Constructor(controller.InitializeJupiterController,
			Path("/apt",
				// 查询用例库
				Path("/library", GET(controller.DefaultJupiterController.GetLibrary)),
				// 查询用例库版本
				Path("/library/version", GET(controller.DefaultJupiterController.GetLibraryVersion)),
				// 查询用例库下的目录树
				Path("/library/folder", GET(controller.DefaultJupiterController.GetFolderTree)),
				// 获取用例列表
				Path("/library/caseList", GET(controller.DefaultJupiterController.GetCaseList)),
			),
		),
		Constructor(controller.InitializeTestPlanStatisticsController,
			Path("/testPlanStatistics",
				// 查询报告列表
				Path("/list", GET(controller.DefaultTestPlanStatisticsController.GetTestPlanList)),
				Path("/sendMail", POST(controller.DefaultTestPlanStatisticsController.SendReportEmail)),
				// 查看报告详情
				Path("/{id}", GET(controller.DefaultTestPlanStatisticsController.Info)),
				Middleware(authorization,
					// 修改报告
					Path("/{id}", PUT(controller.DefaultTestPlanStatisticsController.Update, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-REPORT-EDIT")),
					// 删除报告
					Path("/{id}", DELETE(controller.DefaultTestPlanStatisticsController.Delete, "auditlog.id=$path.id", "auth.code=MANTIS-MARS-REPORT-DELETE")),
					// 下载报告
					Path("/download/{type}", POST(controller.DefaultTestPlanStatisticsController.DownloadReport, "auditlog.id=$json.id", "auth.code=MANTIS-MARS-REPORT-DOWNLOAD")),
				),
			),
		),
		Path("/openapi/v1",
			Middlewares([]string{recovery, trace, accessLog, authenticationx},
				Constructor(controller.InitializeOpenJupiterController,
					Path("/apt",
						Path("/executeCase/modifyCaseResult", POST(openapi.DefaultJupiterOpenController.ModifyCaseResult)),
						Path("/testPlan/{id}", GET(openapi.DefaultJupiterOpenController.GetPlan)),
					),
				),
				Constructor(controller.InitializeOpenUiController,
					Path("/ui",
						Path("/planCallback/{hisId}", POST(openapi.DefaultUiOpenController.PlanCallBack)),
					),
				),
			),
		),
		// 外部调用
		Constructor(controller.InitializeOuterController,
			Path("/outer",
				Middlewares([]string{recovery, trace, accessLog, authentication},
					// 查询需求下的用例列表
					Path("/demand/caseList", GET(controller.DefaultOuterController.CaseList)),
					// 需求关联用例
					Path("/demand/relate/case", PUT(controller.DefaultOuterController.RelateCase)),
					// 需求解除用例关联
					Path("/demand/cancel/case", PUT(controller.DefaultOuterController.CancelCaseList)),
					// 获取用例库
					Path("/library", GET(controller.DefaultOuterController.GetLibrary)),
					// 查询用例库下的用例
					Path("/searchInLib", GET(controller.DefaultOuterController.SearchInLib)),
					// 查询用例库下的目录树
					Path("/library/folderTree", GET(controller.DefaultOuterController.GetFolderTree)),
					// 批量新增需求及用例
					Path("/library/addBatchAndRelateDemand", POST(controller.DefaultOuterController.AddBatchAndRelateDemand)),
				),
			),
		),
	),
))
