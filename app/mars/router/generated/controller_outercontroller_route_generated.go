// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto/outer"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// GET Path("/mars/outer/demand/caseList") -> controller.OuterController.CaseList
func controllerOuterControllerCaseListHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "CaseList",
		Patten:            "/mars/outer/demand/caseList",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "OuterController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeOuterController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.CaseListRequest{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.CaseList(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// RelateCase 需求关联用例
// PUT Path("/mars/outer/demand/relate/case") -> controller.OuterController.RelateCase
func controllerOuterControllerRelateCaseHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RelateCase",
		Patten:            "/mars/outer/demand/relate/case",
		Desc:              "RelateCase 需求关联用例",
		ControllerPkgName: "controller",
		ControllerName:    "OuterController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeOuterController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.RelateCaseRequest{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.RelateCase(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// CancelCaseList 需求解除用例关联
// PUT Path("/mars/outer/demand/cancel/case") -> controller.OuterController.CancelCaseList
func controllerOuterControllerCancelCaseListHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "CancelCaseList",
		Patten:            "/mars/outer/demand/cancel/case",
		Desc:              "CancelCaseList 需求解除用例关联",
		ControllerPkgName: "controller",
		ControllerName:    "OuterController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeOuterController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.RelateCaseRequest{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.CancelCaseList(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GetLibrary 查询用例库
// GET Path("/mars/outer/library") -> controller.OuterController.GetLibrary
func controllerOuterControllerGetLibraryHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetLibrary",
		Patten:            "/mars/outer/library",
		Desc:              "GetLibrary 查询用例库",
		ControllerPkgName: "controller",
		ControllerName:    "OuterController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeOuterController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.SearchRequest{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetLibrary(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/outer/searchInLib") -> controller.OuterController.SearchInLib
func controllerOuterControllerSearchInLibHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "SearchInLib",
		Patten:            "/mars/outer/searchInLib",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "OuterController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeOuterController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.SearchInLibReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.SearchInLib(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GetFolderTree 查询用例库下的目录树
// GET Path("/mars/outer/library/folderTree") -> controller.OuterController.GetFolderTree
func controllerOuterControllerGetFolderTreeHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetFolderTree",
		Patten:            "/mars/outer/library/folderTree",
		Desc:              "GetFolderTree 查询用例库下的目录树",
		ControllerPkgName: "controller",
		ControllerName:    "OuterController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeOuterController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.SearchInLibReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetFolderTree(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mars/outer/library/addBatchAndRelateDemand") -> controller.OuterController.AddBatchAndRelateDemand
func controllerOuterControllerAddBatchAndRelateDemandHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "AddBatchAndRelateDemand",
		Patten:            "/mars/outer/library/addBatchAndRelateDemand",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "OuterController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeOuterController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := outer.AddBatchAndRelateDemandReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.AddBatchAndRelateDemand(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}
