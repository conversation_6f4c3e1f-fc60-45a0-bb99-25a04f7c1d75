// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// GET Path("/mars/testPlanStatistics/list") -> controller.TestPlanStatisticsController.GetTestPlanList
func controllerTestPlanStatisticsControllerGetTestPlanListHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetTestPlanList",
		Patten:            "/mars/testPlanStatistics/list",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanStatisticsController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanStatisticsController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.MarsReportSearchReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		pageReq := gormx.PageRequest{}
		if i, ok := decode.Implements(&pageReq); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &pageReq); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(pageReq); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetTestPlanList(req1, pageReq)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mars/testPlanStatistics/sendMail") -> controller.TestPlanStatisticsController.SendReportEmail
func controllerTestPlanStatisticsControllerSendReportEmailHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "SendReportEmail",
		Patten:            "/mars/testPlanStatistics/sendMail",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanStatisticsController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanStatisticsController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.MarsEmailReportReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.SendReportEmail(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/testPlanStatistics/{id}") -> controller.TestPlanStatisticsController.Info
func controllerTestPlanStatisticsControllerInfoHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Info",
		Patten:            "/mars/testPlanStatistics/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanStatisticsController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanStatisticsController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.MarsTestPlanStatisticsIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Info(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/mars/testPlanStatistics/{id}") -> controller.TestPlanStatisticsController.Update
func controllerTestPlanStatisticsControllerUpdateHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Update",
		Patten:            "/mars/testPlanStatistics/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanStatisticsController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-REPORT-EDIT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanStatisticsController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		path1 := controller.MarsTestPlanStatisticsIdInPathReq{}
		if i, ok := decode.Implements(&path1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &path1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(path1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.UpdateReportReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Update(path1, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/mars/testPlanStatistics/{id}") -> controller.TestPlanStatisticsController.Delete
func controllerTestPlanStatisticsControllerDeleteHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Delete",
		Patten:            "/mars/testPlanStatistics/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanStatisticsController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-REPORT-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanStatisticsController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.MarsTestPlanStatisticsIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Delete(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mars/testPlanStatistics/download/{type}") -> controller.TestPlanStatisticsController.DownloadReport
func controllerTestPlanStatisticsControllerDownloadReportHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "DownloadReport",
		Patten:            "/mars/testPlanStatistics/download/{type}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanStatisticsController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$json.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-REPORT-DOWNLOAD",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanStatisticsController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		path1 := controller.MarsTestPlanStatisticsIdInPathReq{}
		if i, ok := decode.Implements(&path1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &path1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(path1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.DownloadReportReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.DownloadReport(path1, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}
