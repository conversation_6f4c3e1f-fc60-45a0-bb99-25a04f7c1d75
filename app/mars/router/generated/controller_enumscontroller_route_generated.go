// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/controller"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// GET Path("/mars/enums/priority") -> controller.EnumsController.Priority
func controllerEnumsControllerPriorityHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Priority",
		Patten:            "/mars/enums/priority",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "EnumsController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeEnumsController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Priority()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/enums/caseReviewStatus") -> controller.EnumsController.CaseReviewStatus
func controllerEnumsControllerCaseReviewStatusHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "CaseReviewStatus",
		Patten:            "/mars/enums/caseReviewStatus",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "EnumsController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeEnumsController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.CaseReviewStatus()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/enums/reviewStatus") -> controller.EnumsController.ReviewStatus
func controllerEnumsControllerReviewStatusHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ReviewStatus",
		Patten:            "/mars/enums/reviewStatus",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "EnumsController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeEnumsController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ReviewStatus()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/enums/planType") -> controller.EnumsController.PlanType
func controllerEnumsControllerPlanTypeHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "PlanType",
		Patten:            "/mars/enums/planType",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "EnumsController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeEnumsController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.PlanType()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/enums/planStatus") -> controller.EnumsController.PlanStatus
func controllerEnumsControllerPlanStatusHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "PlanStatus",
		Patten:            "/mars/enums/planStatus",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "EnumsController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeEnumsController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.PlanStatus()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/enums/planRelationType") -> controller.EnumsController.PlanRelationType
func controllerEnumsControllerPlanRelationTypeHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "PlanRelationType",
		Patten:            "/mars/enums/planRelationType",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "EnumsController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeEnumsController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.PlanRelationType()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/enums/planStage") -> controller.EnumsController.PlanStage
func controllerEnumsControllerPlanStageHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "PlanStage",
		Patten:            "/mars/enums/planStage",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "EnumsController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeEnumsController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.PlanStage()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/enums/planModels") -> controller.EnumsController.PlanModels
func controllerEnumsControllerPlanModelsHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "PlanModels",
		Patten:            "/mars/enums/planModels",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "EnumsController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeEnumsController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.PlanModels()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/enums/caseResult") -> controller.EnumsController.CaseResult
func controllerEnumsControllerCaseResultHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "CaseResult",
		Patten:            "/mars/enums/caseResult",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "EnumsController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeEnumsController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.CaseResult()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/enums/tmPriority") -> controller.EnumsController.TmPriority
func controllerEnumsControllerTmPriorityHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "TmPriority",
		Patten:            "/mars/enums/tmPriority",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "EnumsController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeEnumsController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.TmPriority()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/enums/tmEnable") -> controller.EnumsController.TmEnable
func controllerEnumsControllerTmEnableHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "TmEnable",
		Patten:            "/mars/enums/tmEnable",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "EnumsController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeEnumsController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.TmEnable()

		}
	}
	return chain.ThenFunc(HandleFunc)
}
