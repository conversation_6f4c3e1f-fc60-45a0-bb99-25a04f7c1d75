// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// POST Path("/mars/caseLibrary/add") -> controller.CaseLibraryController.InsertCaseLibrary
func controllerCaseLibraryControllerInsertCaseLibraryHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "InsertCaseLibrary",
		Patten:            "/mars/caseLibrary/add",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseLibraryController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.data.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-CASELIBRARY-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseLibraryController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		library := models.MarsCaseLibrary{}
		if i, ok := decode.Implements(&library); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &library); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(library); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.InsertCaseLibrary(&library)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/mars/caseLibrary/update") -> controller.CaseLibraryController.UpdateCaseLibrary
func controllerCaseLibraryControllerUpdateCaseLibraryHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateCaseLibrary",
		Patten:            "/mars/caseLibrary/update",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseLibraryController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.data.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-CASELIBRARY-EDIT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseLibraryController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		library := models.MarsCaseLibrary{}
		if i, ok := decode.Implements(&library); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &library); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(library); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateCaseLibrary(&library)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mars/caseLibrary/copy") -> controller.CaseLibraryController.CopyCaseLibrary
func controllerCaseLibraryControllerCopyCaseLibraryHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "CopyCaseLibrary",
		Patten:            "/mars/caseLibrary/copy",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseLibraryController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$json.libraryId",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-CASELIBRARY-COPY",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseLibraryController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.CaseLibraryCopyReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.CopyCaseLibrary(&req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/mars/caseLibrary/delete/{id}") -> controller.CaseLibraryController.DeleteCaseLibrary
func controllerCaseLibraryControllerDeleteCaseLibraryHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "DeleteCaseLibrary",
		Patten:            "/mars/caseLibrary/delete/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseLibraryController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-CASELIBRARY-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseLibraryController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.CaseLibraryIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.DeleteCaseLibrary(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/caseLibrary/list") -> controller.CaseLibraryController.SearchCaseLibraryList
func controllerCaseLibraryControllerSearchCaseLibraryListHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "SearchCaseLibraryList",
		Patten:            "/mars/caseLibrary/list",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseLibraryController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseLibraryController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.CaseLibrarySearchReqDto{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		pageReq := gormx.PageRequest{}
		if i, ok := decode.Implements(&pageReq); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &pageReq); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(pageReq); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.SearchCaseLibraryList(req1, pageReq)

		}
	}
	return chain.ThenFunc(HandleFunc)
}
