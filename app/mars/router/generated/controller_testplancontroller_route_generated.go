// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// POST Path("/mars/testPlan") -> controller.TestPlanController.Insert
func controllerTestPlanControllerInsertHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Insert",
		Patten:            "/mars/testPlan",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.data.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-PLAN-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		plan := models.MarsTestPlanDTO{}
		if i, ok := decode.Implements(&plan); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &plan); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(plan); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Insert(plan)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/mars/testPlan/{id}") -> controller.TestPlanController.Update
func controllerTestPlanControllerUpdateHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Update",
		Patten:            "/mars/testPlan/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-PLAN-EDIT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		id := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&id); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &id); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(id); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		plan := models.MarsTestPlanDTO{}
		if i, ok := decode.Implements(&plan); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &plan); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(plan); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Update(id, plan)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mars/testPlan/{id}/copy") -> controller.TestPlanController.Copy
func controllerTestPlanControllerCopyHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Copy",
		Patten:            "/mars/testPlan/{id}/copy",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-PLAN-COPY",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Copy(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/mars/testPlan/{id}") -> controller.TestPlanController.Delete
func controllerTestPlanControllerDeleteHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Delete",
		Patten:            "/mars/testPlan/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-PLAN-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Delete(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/mars/testPlan/{id}/status") -> controller.TestPlanController.UpdateStatus
func controllerTestPlanControllerUpdateStatusHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateStatus",
		Patten:            "/mars/testPlan/{id}/status",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-PLAN-EXECUTE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		id := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&id); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &id); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(id); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		status := controller.MarsTestPlanStatusReq{}
		if i, ok := decode.Implements(&status); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &status); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(status); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateStatus(id, status)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/mars/testPlan/{id}/model") -> controller.TestPlanController.UpdateModel
func controllerTestPlanControllerUpdateModelHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateModel",
		Patten:            "/mars/testPlan/{id}/model",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-PLAN-MODULE-EDIT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		path1 := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&path1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &path1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(path1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.MarsTestPlanUpdateModelsReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateModel(path1, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mars/testPlan/{id}/relateDemand") -> controller.TestPlanController.RelateDemand
func controllerTestPlanControllerRelateDemandHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RelateDemand",
		Patten:            "/mars/testPlan/{id}/relateDemand",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-PLAN-DEMAND-RELATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		planId := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&planId); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &planId); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(planId); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.MarsTestPlanRelateDemandReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.RelateDemand(planId, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/mars/testPlan/{id}/demand/deleteBatch") -> controller.TestPlanController.DeleteDemandBatch
func controllerTestPlanControllerDeleteDemandBatchHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "DeleteDemandBatch",
		Patten:            "/mars/testPlan/{id}/demand/deleteBatch",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-PLAN-DEMAND-RELATE-CANCEL",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		planId := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&planId); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &planId); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(planId); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.MarsTestPlanRemoveReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.DeleteDemandBatch(planId, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// RelateCase 需求关联用例
// POST Path("/mars/testPlan/{id}/relateCase") -> controller.TestPlanController.RelateCase
func controllerTestPlanControllerRelateCaseHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RelateCase",
		Patten:            "/mars/testPlan/{id}/relateCase",
		Desc:              "RelateCase 需求关联用例",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-PLAN-CASE-RELATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		planId := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&planId); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &planId); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(planId); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.MarsTestPlanRelateCaseReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.RelateCase(planId, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DeleteCasesBatch 批量删除用例
// DELETE Path("/mars/testPlan/{id}/cases/deleteBatch") -> controller.TestPlanController.DeleteCasesBatch
func controllerTestPlanControllerDeleteCasesBatchHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "DeleteCasesBatch",
		Patten:            "/mars/testPlan/{id}/cases/deleteBatch",
		Desc:              "DeleteCasesBatch 批量删除用例",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-PLAN-CASE-RELATE-CANCEL",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		planId := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&planId); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &planId); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(planId); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.MarsTestPlanRemoveReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.DeleteCasesBatch(planId, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// UpdateResult 给用例设置执行结果
// PUT Path("/mars/testPlan/{id}/cases/updateResult") -> controller.TestPlanController.UpdateResult
func controllerTestPlanControllerUpdateResultHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateResult",
		Patten:            "/mars/testPlan/{id}/cases/updateResult",
		Desc:              "UpdateResult 给用例设置执行结果",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-PLAN-RESULT-BATCH-MARK",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		reqId := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&reqId); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &reqId); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(reqId); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.MarsTestPlanCaseBatchUpdateReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateResult(reqId, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// AssignExecutors 给用例分配执行人
// PUT Path("/mars/testPlan/{id}/cases/assignExecutors") -> controller.TestPlanController.AssignExecutors
func controllerTestPlanControllerAssignExecutorsHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "AssignExecutors",
		Patten:            "/mars/testPlan/{id}/cases/assignExecutors",
		Desc:              "AssignExecutors 给用例分配执行人",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-PLAN-EXECUTOR-BATCH-ASSIGN",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		reqId := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&reqId); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &reqId); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(reqId); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.MarsTestPlanCaseBatchUpdateReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.AssignExecutors(reqId, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mars/testPlan/{id}/demand/sync") -> controller.TestPlanController.SyncDemand
func controllerTestPlanControllerSyncDemandHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "SyncDemand",
		Patten:            "/mars/testPlan/{id}/demand/sync",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-PLAN-DATA-BATCH-SYNC",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		reqBody := controller.MarsTestPlanRemoveReq{}
		if i, ok := decode.Implements(&reqBody); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &reqBody); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(reqBody); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.SyncDemand(req1, reqBody)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/mars/testPlan/{id}/functionCase/{relationId}") -> controller.TestPlanController.UpdateFunctionCaseInfo
func controllerTestPlanControllerUpdateFunctionCaseInfoHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateFunctionCaseInfo",
		Patten:            "/mars/testPlan/{id}/functionCase/{relationId}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-PLAN-RESULT-MARK",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		planCase := dto.TestPlanCase{}
		if i, ok := decode.Implements(&planCase); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &planCase); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(planCase); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateFunctionCaseInfo(req1, &planCase)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mars/testPlan/{id}/caseFastCreate") -> controller.TestPlanController.FastCreate
func controllerTestPlanControllerFastCreateHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "FastCreate",
		Patten:            "/mars/testPlan/{id}/caseFastCreate",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-CASELIBRARY-CASE-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		planId := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&planId); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &planId); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(planId); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		caseDTO := dto.TestPlanCaseDto{}
		if i, ok := decode.Implements(&caseDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &caseDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(caseDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.FastCreate(planId, caseDTO)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mars/testPlan/{id}/saveReportView") -> controller.TestPlanController.SaveReportView
func controllerTestPlanControllerSaveReportViewHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "SaveReportView",
		Patten:            "/mars/testPlan/{id}/saveReportView",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-PLAN-REPORT-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		path1 := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&path1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &path1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(path1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		report := models.MarsTestPlanReport{}
		if i, ok := decode.Implements(&report); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &report); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(report); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.SaveReportView(path1, report)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mars/testPlan/{id}/moveCase") -> controller.TestPlanController.MoveCase
func controllerTestPlanControllerMoveCaseHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "MoveCase",
		Patten:            "/mars/testPlan/{id}/moveCase",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-PLAN-CASE-RELATE-SORT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		path1 := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&path1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &path1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(path1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.MarsTestPlanCaseMoveReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.MoveCase(path1, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/testPlan/list") -> controller.TestPlanController.GetTestPlanList
func controllerTestPlanControllerGetTestPlanListHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetTestPlanList",
		Patten:            "/mars/testPlan/list",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.MarsTestPlanListReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		pageReq := gormx.PageRequest{}
		if i, ok := decode.Implements(&pageReq); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &pageReq); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(pageReq); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetTestPlanList(req1, pageReq)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/testPlan/{id}/info") -> controller.TestPlanController.Info
func controllerTestPlanControllerInfoHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Info",
		Patten:            "/mars/testPlan/{id}/info",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Info(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/testPlan/{id}/model") -> controller.TestPlanController.GetModel
func controllerTestPlanControllerGetModelHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetModel",
		Patten:            "/mars/testPlan/{id}/model",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetModel(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/testPlan/{id}/cases") -> controller.TestPlanController.CaseList
func controllerTestPlanControllerCaseListHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "CaseList",
		Patten:            "/mars/testPlan/{id}/cases",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		path1 := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&path1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &path1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(path1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.MarsTestPlanCaseListReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		pageReq := gormx.PageRequest{}
		if i, ok := decode.Implements(&pageReq); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &pageReq); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(pageReq); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.CaseList(path1, req1, pageReq)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GetPlanDemand 获取计划下的需求
// GET Path("/mars/testPlan/{id}/demand") -> controller.TestPlanController.GetPlanDemand
func controllerTestPlanControllerGetPlanDemandHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetPlanDemand",
		Patten:            "/mars/testPlan/{id}/demand",
		Desc:              "GetPlanDemand 获取计划下的需求",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		path1 := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&path1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &path1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(path1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.MarsDemandReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetPlanDemand(path1, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/testPlan/{id}/functionCase/{relationId}") -> controller.TestPlanController.GetFunctionCaseInfo
func controllerTestPlanControllerGetFunctionCaseInfoHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetFunctionCaseInfo",
		Patten:            "/mars/testPlan/{id}/functionCase/{relationId}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetFunctionCaseInfo(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// ExecuteCase 用例执行
// POST Path("/mars/testPlan/{id}/case/execute") -> controller.TestPlanController.ExecuteCase
func controllerTestPlanControllerExecuteCaseHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ExecuteCase",
		Patten:            "/mars/testPlan/{id}/case/execute",
		Desc:              "ExecuteCase 用例执行",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		path1 := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&path1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &path1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(path1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.MarsCaseExecReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ExecuteCase(path1, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// HistoryReport 查看历史报告
// GET Path("/mars/testPlan/{id}/historyReport") -> controller.TestPlanController.HistoryReport
func controllerTestPlanControllerHistoryReportHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "HistoryReport",
		Patten:            "/mars/testPlan/{id}/historyReport",
		Desc:              "HistoryReport 查看历史报告",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		path1 := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&path1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &path1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(path1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.HistoryReportReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		pageReq := gormx.PageRequest{}
		if i, ok := decode.Implements(&pageReq); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &pageReq); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(pageReq); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.HistoryReport(path1, req1, pageReq)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/testPlan/{id}/reportView") -> controller.TestPlanController.ReportView
func controllerTestPlanControllerReportViewHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ReportView",
		Patten:            "/mars/testPlan/{id}/reportView",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "TestPlanController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeTestPlanController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		path1 := controller.MarsTestPlanIdInPathReq{}
		if i, ok := decode.Implements(&path1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &path1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(path1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ReportView(path1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}
