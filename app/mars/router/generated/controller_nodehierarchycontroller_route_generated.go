// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// POST Path("/mars/nodes/add") -> controller.NodeHierarchyController.AddNode
func controllerNodeHierarchyControllerAddNodeHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "AddNode",
		Patten:            "/mars/nodes/add",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "NodeHierarchyController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.data.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-CASELIBRARY-GROUP-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeNodeHierarchyController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		node := dto.NodeDto{}
		if i, ok := decode.Implements(&node); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &node); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(node); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.AddNode(node)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mars/nodes/copy") -> controller.NodeHierarchyController.Copy
func controllerNodeHierarchyControllerCopyHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Copy",
		Patten:            "/mars/nodes/copy",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "NodeHierarchyController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$json.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-CASELIBRARY-CASE-COPY",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeNodeHierarchyController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.NodeCopyReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Copy(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/mars/nodes/delete/{id}") -> controller.NodeHierarchyController.DeleteNode
func controllerNodeHierarchyControllerDeleteNodeHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "DeleteNode",
		Patten:            "/mars/nodes/delete/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "NodeHierarchyController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-CASELIBRARY-CASE-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeNodeHierarchyController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.NodeHierarchyIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.DeleteNode(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mars/nodes/moveNode") -> controller.NodeHierarchyController.Move
func controllerNodeHierarchyControllerMoveHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Move",
		Patten:            "/mars/nodes/moveNode",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "NodeHierarchyController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$json.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-CASELIBRARY-BATCH-MOVE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeNodeHierarchyController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		moveDTO := dto.MarsNodeMoveDTO{}
		if i, ok := decode.Implements(&moveDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &moveDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(moveDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Move(moveDTO)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/mars/nodes/update") -> controller.NodeHierarchyController.Update
func controllerNodeHierarchyControllerUpdateHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Update",
		Patten:            "/mars/nodes/update",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "NodeHierarchyController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$json.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-CASELIBRARY-CASE-EDIT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeNodeHierarchyController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		node := dto.NodeDto{}
		if i, ok := decode.Implements(&node); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &node); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(node); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Update(node)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mars/nodes/checkDelete/{id}") -> controller.NodeHierarchyController.CheckDeleteNode
func controllerNodeHierarchyControllerCheckDeleteNodeHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "CheckDeleteNode",
		Patten:            "/mars/nodes/checkDelete/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "NodeHierarchyController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeNodeHierarchyController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.NodeHierarchyIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.CheckDeleteNode(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/nodes/tree/{libId}") -> controller.NodeHierarchyController.GetTree
func controllerNodeHierarchyControllerGetTreeHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetTree",
		Patten:            "/mars/nodes/tree/{libId}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "NodeHierarchyController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeNodeHierarchyController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.NodeHierarchyLibIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetTree(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GetCaseList 获取用例列表
// GET Path("/mars/nodes/caseList") -> controller.NodeHierarchyController.GetCaseList
func controllerNodeHierarchyControllerGetCaseListHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetCaseList",
		Patten:            "/mars/nodes/caseList",
		Desc:              "GetCaseList 获取用例列表",
		ControllerPkgName: "controller",
		ControllerName:    "NodeHierarchyController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeNodeHierarchyController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.MarsCaseListReqDTO{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		pageRequest := gormx.PageRequest{}
		if i, ok := decode.Implements(&pageRequest); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &pageRequest); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(pageRequest); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetCaseList(req1, pageRequest)

		}
	}
	return chain.ThenFunc(HandleFunc)
}
