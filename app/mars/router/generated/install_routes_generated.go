// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
	"github.com/gorilla/mux"
	"github.com/justinas/alice"
)

func InstallRoutes(mux *mux.Router, opt types.Option) {
	//generate from Pkg: controller

	//
	mux.Methods("GET").Path("/mars/bug/getProjectBugs").Handler(controllerBugControllerGetProjectBugsHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/bug/bugList").Handler(controllerBugControllerBugListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("POST").Path("/mars/bug/add").Handler(controllerBugControllerAddHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mars/bug/remove").Handler(controllerBugControllerRemoveHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))

	//
	mux.Methods("POST").Path("/mars/case").Handler(controllerCaseControllerInsertHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/case/moveCase").Handler(controllerCaseControllerMoveHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/case/fastCopy/{id}").Handler(controllerCaseControllerCopyHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/case/batchCopy").Handler(controllerCaseControllerBatchCopyHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mars/case/delete/{id}").Handler(controllerCaseControllerDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mars/case/batchDelete").Handler(controllerCaseControllerBatchDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mars/case/{id}").Handler(controllerCaseControllerUpdateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/case/checkDelete").Handler(controllerCaseControllerCheckDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/case/history/list/{id}").Handler(controllerCaseControllerHistoryListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/case/history/{id}").Handler(controllerCaseControllerHistoryDetailHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/case/{id}").Handler(controllerCaseControllerInfoHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/mars/caseLibrary/add").Handler(controllerCaseLibraryControllerInsertCaseLibraryHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mars/caseLibrary/update").Handler(controllerCaseLibraryControllerUpdateCaseLibraryHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/caseLibrary/copy").Handler(controllerCaseLibraryControllerCopyCaseLibraryHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mars/caseLibrary/delete/{id}").Handler(controllerCaseLibraryControllerDeleteCaseLibraryHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/mars/caseLibrary/list").Handler(controllerCaseLibraryControllerSearchCaseLibraryListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/mars/enums/priority").Handler(controllerEnumsControllerPriorityHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/enums/caseReviewStatus").Handler(controllerEnumsControllerCaseReviewStatusHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/enums/reviewStatus").Handler(controllerEnumsControllerReviewStatusHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/enums/planType").Handler(controllerEnumsControllerPlanTypeHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/enums/planStatus").Handler(controllerEnumsControllerPlanStatusHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/enums/planRelationType").Handler(controllerEnumsControllerPlanRelationTypeHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/enums/planStage").Handler(controllerEnumsControllerPlanStageHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/enums/planModels").Handler(controllerEnumsControllerPlanModelsHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/enums/caseResult").Handler(controllerEnumsControllerCaseResultHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/enums/tmPriority").Handler(controllerEnumsControllerTmPriorityHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/enums/tmEnable").Handler(controllerEnumsControllerTmEnableHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/mars/excel/import").Handler(controllerExcelControllerImportExcelHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/excel/export").Handler(controllerExcelControllerExportExcelHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/mars/excel/template").Handler(controllerExcelControllerTemplateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	// GetLibrary 查询用例库
	mux.Methods("GET").Path("/mars/apt/library").Handler(controllerJupiterControllerGetLibraryHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	// GetLibraryVersion 查询用例库版本
	mux.Methods("GET").Path("/mars/apt/library/version").Handler(controllerJupiterControllerGetLibraryVersionHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	// GetFolderTree 查询用例库下的目录树
	mux.Methods("GET").Path("/mars/apt/library/folder").Handler(controllerJupiterControllerGetFolderTreeHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	// GetCaseList 获取用例列表
	mux.Methods("GET").Path("/mars/apt/library/caseList").Handler(controllerJupiterControllerGetCaseListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/mars/openapi/v1/apt/executeCase/modifyCaseResult").Handler(controllerJupiterOpenControllerModifyCaseResultHandleFunc(alice.New(_recovery, _trace, _accessLog, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/openapi/v1/apt/testPlan/{id}").Handler(controllerJupiterOpenControllerGetPlanHandleFunc(alice.New(_recovery, _trace, _accessLog, _auditLog), opt))

	//
	mux.Methods("POST").Path("/mars/nodes/add").Handler(controllerNodeHierarchyControllerAddNodeHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/nodes/copy").Handler(controllerNodeHierarchyControllerCopyHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mars/nodes/delete/{id}").Handler(controllerNodeHierarchyControllerDeleteNodeHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/nodes/moveNode").Handler(controllerNodeHierarchyControllerMoveHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mars/nodes/update").Handler(controllerNodeHierarchyControllerUpdateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/nodes/checkDelete/{id}").Handler(controllerNodeHierarchyControllerCheckDeleteNodeHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/nodes/tree/{libId}").Handler(controllerNodeHierarchyControllerGetTreeHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	// GetCaseList 获取用例列表
	mux.Methods("GET").Path("/mars/nodes/caseList").Handler(controllerNodeHierarchyControllerGetCaseListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/mars/outer/demand/caseList").Handler(controllerOuterControllerCaseListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	// RelateCase 需求关联用例
	mux.Methods("PUT").Path("/mars/outer/demand/relate/case").Handler(controllerOuterControllerRelateCaseHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	// CancelCaseList 需求解除用例关联
	mux.Methods("PUT").Path("/mars/outer/demand/cancel/case").Handler(controllerOuterControllerCancelCaseListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	// GetLibrary 查询用例库
	mux.Methods("GET").Path("/mars/outer/library").Handler(controllerOuterControllerGetLibraryHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/outer/searchInLib").Handler(controllerOuterControllerSearchInLibHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	// GetFolderTree 查询用例库下的目录树
	mux.Methods("GET").Path("/mars/outer/library/folderTree").Handler(controllerOuterControllerGetFolderTreeHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("POST").Path("/mars/outer/library/addBatchAndRelateDemand").Handler(controllerOuterControllerAddBatchAndRelateDemandHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/mars/review").Handler(controllerReviewControllerInsertHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mars/review").Handler(controllerReviewControllerUpdateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mars/review/{id}").Handler(controllerReviewControllerDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/review/reviewComment").Handler(controllerReviewControllerSaveReviewCommentHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	// RelateCase 需求关联用例
	mux.Methods("POST").Path("/mars/review/relateCases").Handler(controllerReviewControllerRelateCaseHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mars/review/relateCases/remove").Handler(controllerReviewControllerRemoveCaseHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/review/start").Handler(controllerReviewControllerStartHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/review/finish").Handler(controllerReviewControllerFinishHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/review/relateCases/comment").Handler(controllerReviewControllerSaveReviewCaseCommentHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/mars/review/list").Handler(controllerReviewControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/review/reviewComment/{id}").Handler(controllerReviewControllerGetReviewCommentHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/review/relateCaseComments").Handler(controllerReviewControllerGetCaseCommentsHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/review/relateCases/listByDemand").Handler(controllerReviewControllerCaseListByDemandHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/review/{id}").Handler(controllerReviewControllerInfoHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/mars/selectors/demands").Handler(controllerSelectorsControllerSearchDemandsHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/selectors/iterations").Handler(controllerSelectorsControllerSearchIterationHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/selectors/tagsInLib").Handler(controllerSelectorsControllerSearchTagInLibHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/mars/testPlan").Handler(controllerTestPlanControllerInsertHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mars/testPlan/{id}").Handler(controllerTestPlanControllerUpdateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/testPlan/{id}/copy").Handler(controllerTestPlanControllerCopyHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mars/testPlan/{id}").Handler(controllerTestPlanControllerDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mars/testPlan/{id}/status").Handler(controllerTestPlanControllerUpdateStatusHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mars/testPlan/{id}/model").Handler(controllerTestPlanControllerUpdateModelHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/testPlan/{id}/relateDemand").Handler(controllerTestPlanControllerRelateDemandHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mars/testPlan/{id}/demand/deleteBatch").Handler(controllerTestPlanControllerDeleteDemandBatchHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	// RelateCase 需求关联用例
	mux.Methods("POST").Path("/mars/testPlan/{id}/relateCase").Handler(controllerTestPlanControllerRelateCaseHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	// DeleteCasesBatch 批量删除用例
	mux.Methods("DELETE").Path("/mars/testPlan/{id}/cases/deleteBatch").Handler(controllerTestPlanControllerDeleteCasesBatchHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	// UpdateResult 给用例设置执行结果
	mux.Methods("PUT").Path("/mars/testPlan/{id}/cases/updateResult").Handler(controllerTestPlanControllerUpdateResultHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	// AssignExecutors 给用例分配执行人
	mux.Methods("PUT").Path("/mars/testPlan/{id}/cases/assignExecutors").Handler(controllerTestPlanControllerAssignExecutorsHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/testPlan/{id}/demand/sync").Handler(controllerTestPlanControllerSyncDemandHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mars/testPlan/{id}/functionCase/{relationId}").Handler(controllerTestPlanControllerUpdateFunctionCaseInfoHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/testPlan/{id}/caseFastCreate").Handler(controllerTestPlanControllerFastCreateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/testPlan/{id}/saveReportView").Handler(controllerTestPlanControllerSaveReportViewHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/testPlan/{id}/moveCase").Handler(controllerTestPlanControllerMoveCaseHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/mars/testPlan/list").Handler(controllerTestPlanControllerGetTestPlanListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/testPlan/{id}/info").Handler(controllerTestPlanControllerInfoHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/testPlan/{id}/model").Handler(controllerTestPlanControllerGetModelHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/testPlan/{id}/cases").Handler(controllerTestPlanControllerCaseListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	// GetPlanDemand 获取计划下的需求
	mux.Methods("GET").Path("/mars/testPlan/{id}/demand").Handler(controllerTestPlanControllerGetPlanDemandHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/testPlan/{id}/functionCase/{relationId}").Handler(controllerTestPlanControllerGetFunctionCaseInfoHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	// ExecuteCase 用例执行
	mux.Methods("POST").Path("/mars/testPlan/{id}/case/execute").Handler(controllerTestPlanControllerExecuteCaseHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	// HistoryReport 查看历史报告
	mux.Methods("GET").Path("/mars/testPlan/{id}/historyReport").Handler(controllerTestPlanControllerHistoryReportHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/testPlan/{id}/reportView").Handler(controllerTestPlanControllerReportViewHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/mars/testPlanStatistics/list").Handler(controllerTestPlanStatisticsControllerGetTestPlanListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("POST").Path("/mars/testPlanStatistics/sendMail").Handler(controllerTestPlanStatisticsControllerSendReportEmailHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mars/testPlanStatistics/{id}").Handler(controllerTestPlanStatisticsControllerInfoHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("PUT").Path("/mars/testPlanStatistics/{id}").Handler(controllerTestPlanStatisticsControllerUpdateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mars/testPlanStatistics/{id}").Handler(controllerTestPlanStatisticsControllerDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/testPlanStatistics/download/{type}").Handler(controllerTestPlanStatisticsControllerDownloadReportHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))

	//
	mux.Methods("POST").Path("/mars/openapi/v1/ui/planCallback/{hisId}").Handler(controllerUiOpenControllerPlanCallBackHandleFunc(alice.New(_recovery, _trace, _accessLog, _auditLog), opt))

	//
	mux.Methods("POST").Path("/mars/xmind/import").Handler(controllerXmindControllerImportXmindHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mars/xmind/export").Handler(controllerXmindControllerExportXmindHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/mars/xmind/template").Handler(controllerXmindControllerTemplateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

}
