// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/models"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// POST Path("/mars/review") -> controller.ReviewController.Insert
func controllerReviewControllerInsertHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Insert",
		Patten:            "/mars/review",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ReviewController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.data.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-REVIEW-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeReviewController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		review := models.MarsReview{}
		if i, ok := decode.Implements(&review); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &review); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(review); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Insert(&review)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/mars/review") -> controller.ReviewController.Update
func controllerReviewControllerUpdateHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Update",
		Patten:            "/mars/review",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ReviewController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$json.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-REVIEW-EDIT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeReviewController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		review := models.MarsReview{}
		if i, ok := decode.Implements(&review); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &review); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(review); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Update(&review)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/mars/review/{id}") -> controller.ReviewController.Delete
func controllerReviewControllerDeleteHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Delete",
		Patten:            "/mars/review/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ReviewController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$path.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-REVIEW-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeReviewController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ReviewIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Delete(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mars/review/reviewComment") -> controller.ReviewController.SaveReviewComment
func controllerReviewControllerSaveReviewCommentHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "SaveReviewComment",
		Patten:            "/mars/review/reviewComment",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ReviewController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$json.reviewId",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-REVIEW-SUMMARY",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeReviewController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.MarsReviewCommentDTO{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.SaveReviewComment(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// RelateCase 需求关联用例
// POST Path("/mars/review/relateCases") -> controller.ReviewController.RelateCase
func controllerReviewControllerRelateCaseHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RelateCase",
		Patten:            "/mars/review/relateCases",
		Desc:              "RelateCase 需求关联用例",
		ControllerPkgName: "controller",
		ControllerName:    "ReviewController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$json.reviewId,.caseIds",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-REVIEW-CASE-RELATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeReviewController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.MarsReviewCaseReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.RelateCase(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/mars/review/relateCases/remove") -> controller.ReviewController.RemoveCase
func controllerReviewControllerRemoveCaseHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RemoveCase",
		Patten:            "/mars/review/relateCases/remove",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ReviewController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$json.reviewId,.caseIds",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-REVIEW-CASE-RELATE-CANCEL",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeReviewController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.MarsReviewCaseReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.RemoveCase(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mars/review/start") -> controller.ReviewController.Start
func controllerReviewControllerStartHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Start",
		Patten:            "/mars/review/start",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ReviewController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$json.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-REVIEW-EXECUTE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeReviewController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.MarsReviewStatusUpdateReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Start(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mars/review/finish") -> controller.ReviewController.Finish
func controllerReviewControllerFinishHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Finish",
		Patten:            "/mars/review/finish",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ReviewController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$json.id",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-REVIEW-EXECUTE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeReviewController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.MarsReviewStatusUpdateReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Finish(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mars/review/relateCases/comment") -> controller.ReviewController.SaveReviewCaseComment
func controllerReviewControllerSaveReviewCaseCommentHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "SaveReviewCaseComment",
		Patten:            "/mars/review/relateCases/comment",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ReviewController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$json.reviewId,.caseId",
			"auditlog.resource": "测试管理",
			"auth.code":         "MANTIS-MARS-REVIEW-RESULT-MARK",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeReviewController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		comment := dto.MarsReviewCaseComment{}
		if i, ok := decode.Implements(&comment); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &comment); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(comment); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.SaveReviewCaseComment(comment)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/review/list") -> controller.ReviewController.List
func controllerReviewControllerListHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "List",
		Patten:            "/mars/review/list",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ReviewController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeReviewController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.MarsReviewSearchReqDTO{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		pageReq := gormx.PageRequest{}
		if i, ok := decode.Implements(&pageReq); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &pageReq); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(pageReq); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.List(req1, pageReq)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/review/reviewComment/{id}") -> controller.ReviewController.GetReviewComment
func controllerReviewControllerGetReviewCommentHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetReviewComment",
		Patten:            "/mars/review/reviewComment/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ReviewController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeReviewController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ReviewIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetReviewComment(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/review/relateCaseComments") -> controller.ReviewController.GetCaseComments
func controllerReviewControllerGetCaseCommentsHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetCaseComments",
		Patten:            "/mars/review/relateCaseComments",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ReviewController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeReviewController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ReviewAndCaseIdInSchemaReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetCaseComments(&req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/review/relateCases/listByDemand") -> controller.ReviewController.CaseListByDemand
func controllerReviewControllerCaseListByDemandHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "CaseListByDemand",
		Patten:            "/mars/review/relateCases/listByDemand",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ReviewController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeReviewController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ReviewAndCaseIdInSchemaReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.CaseListByDemand(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mars/review/{id}") -> controller.ReviewController.Info
func controllerReviewControllerInfoHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Info",
		Patten:            "/mars/review/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ReviewController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeReviewController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ReviewIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Info(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}
