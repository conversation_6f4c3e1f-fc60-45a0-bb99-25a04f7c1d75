// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/controller/openapi"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mars/internal/dto/ui"
)

// POST Path("/mars/openapi/v1/ui/planCallback/{hisId}") -> controller.UiOpenController.PlanCallBack
func controllerUiOpenControllerPlanCallBackHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "PlanCallBack",
		Patten:            "/mars/openapi/v1/ui/planCallback/{hisId}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "UiOpenController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "测试管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeOpenUiController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		path1 := openapi.HisIdPath{}
		if i, ok := decode.Implements(&path1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &path1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(path1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := ui.MarsUiPlanExecResult{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.PlanCallBack(path1, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}
