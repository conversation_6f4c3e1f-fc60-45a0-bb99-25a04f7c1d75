package dao

import (
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type UiElementDao struct{}

func (UiElementDao) FindElementById(ctx *commoncontext.MantisContext, id string) (*models.UiElement, error) {
	record := &models.UiElement{}
	record.Id = id
	record.IsDeleted = false
	err := gormx.SelectOneByCondition(ctx, record)
	return record, err
}

func (UiElementDao) BatchDeleteCheck(ctx *commoncontext.MantisContext, elementIds []string) (int, error) {
	// 构建IN查询的占位符
	placeholders := make([]string, len(elementIds))
	for i := range elementIds {
		placeholders[i] = "?"
	}
	query := `SELECT count(1) as count FROM ui_case_step
			WHERE exists (
				select 1 from jsonb_array_elements_text(element_ids) as element_id
				where element_id in (` + strings.Join(placeholders, ",") + `)
			) AND is_deleted = 'false'`

	// 构建参数数组
	args := make([]any, len(elementIds))
	for i, elementId := range elementIds {
		args[i] = elementId
	}

	var res dto.CountDto
	err := gormx.Raw(ctx, query, &res, args...)
	if err != nil {
		return 0, err
	}
	return res.Count, nil
}
