package dao

import (
	"errors"
	"fmt"
	"sort"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"gorm.io/gorm"
)

type UiCaseStepDao struct{}

func (UiCaseStepDao) FindStepById(ctx *commoncontext.MantisContext, id string) (*models.UiCaseStep, error) {
	record := models.UiCaseStep{}
	record.Id = id
	err := gormx.SelectOneByCondition(ctx, &record)
	if err != nil {
		return nil, err
	}
	return &record, nil
}

// FindStepsByCaseId 根据用例id查询步骤
// @param withRoot 是否带着根节点
func (UiCaseStepDao) FindStepsByCaseId(ctx *commoncontext.MantisContext, caseId, disable string, withRoot bool) ([]models.UiCaseStep, error) {
	steps := make([]models.UiCaseStep, 0)
	err := gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.UiCaseStep{}).Eq("case_id", caseId).Eq("disable", disable).Eq("is_deleted", false),
		&steps,
	)
	if err != nil {
		return nil, err
	}
	if withRoot {
		return steps, nil
	} else {
		res := make([]models.UiCaseStep, 0)
		for _, step := range steps {
			if step.IsRoot {
				continue
			}
			res = append(res, step)
		}
		return res, nil
	}
}

// FindStepsByCondition 根据用例id，是否可用查询步骤,order 升序
// @param parentId 父节点id,如果传的是"",则不加该条件,全部查出来
func (UiCaseStepDao) FindStepsByCondition(ctx *commoncontext.MantisContext, cid, parentId string, disable string) ([]models.UiCaseStep, error) {
	builder := gormx.NewParamBuilder().Model(&models.UiCaseStep{}).Eq("is_deleted", false)
	if parentId != "" {
		builder.Eq("parent_id", parentId)
	}
	if disable != "" {
		builder.Eq("disable", disable)
	}
	if cid != "" {
		builder.Eq("case_id", cid)
	}
	builder.OrderByAsc("sibling_order")
	records := make([]models.UiCaseStep, 0)
	err := gormx.SelectByParamBuilder(ctx, builder, &records)
	if err != nil {
		return nil, err
	}
	return records, nil
}

// FindStepsByParentId 根据父节点id，是否可用查询步骤,order 升序 (查一层)
func (UiCaseStepDao) FindStepsByParentId(ctx *commoncontext.MantisContext, parentId string, disable string) ([]models.UiCaseStep, error) {
	builder := gormx.NewParamBuilder().Model(&models.UiCaseStep{}).Eq("parent_id", parentId).Eq("is_deleted", false).OrderByAsc("sibling_order")
	if disable != "" {
		builder.Eq("disable", disable)
	}
	records := make([]models.UiCaseStep, 0)
	err := gormx.SelectByParamBuilder(ctx,
		builder,
		&records,
	)
	if err != nil {
		return nil, err
	}
	return records, nil
}

func (UiCaseStepDao) BatchInsert(ctx *commoncontext.MantisContext, steps []*models.UiCaseStep) error {
	_, err := gormx.InsertBatch(ctx, steps)
	return err
}

// CountByCaseId 统计用例下有多少步骤
func (UiCaseStepDao) CountByCaseId(ctx *commoncontext.MantisContext, eId string, disable string) (int, error) {
	var res dto.CountDto
	sql := `select count(1) as count from ui_case_step where case_id=? and is_deleted='false'  and disable=? and is_dir='false'`
	err := gormx.Raw(ctx, sql, &res, eId, disable)
	return res.Count, err
}

func (UiCaseStepDao) RecursiveUpdateDisable(ctx *commoncontext.MantisContext, stepIds []string, disable string) error {
	if len(stepIds) == 0 {
		return nil
	}
	stepIdStr := ""
	for _, id := range stepIds {
		stepIdStr += "'" + id + "',"
	}
	stepIdStr = stepIdStr[:len(stepIdStr)-1]
	sql := fmt.Sprintf(
		`WITH RECURSIVE step_hierarchy AS (
			-- 从拼接的ID列表中选择初始记录
			SELECT id
			FROM ui_case_step
			WHERE id IN (%s)

			UNION ALL

			-- 递归获取所有子节点
			SELECT ucs.id
			FROM ui_case_step ucs
			INNER JOIN step_hierarchy sh ON ucs.parent_id = sh.id
			WHERE ucs.is_deleted = false
		)
		UPDATE ui_case_step ucs
		SET disable = ?
		FROM step_hierarchy sh
		WHERE ucs.id = sh.id;`, stepIdStr)
	_, err := gormx.Exec(ctx, sql, disable)
	return err
}

func (UiCaseStepDao) RecursiveGetStepsByParentId(ctx *commoncontext.MantisContext, parentId string) ([]models.UiCaseStep, error) {
	res := make([]models.UiCaseStep, 0)
	sql := `WITH RECURSIVE step_hierarchy AS (
			SELECT *, 0 as level
			FROM ui_case_step
			WHERE id = ?

			UNION ALL

			SELECT ucs.*, sh.level + 1
			FROM ui_case_step ucs
			INNER JOIN step_hierarchy sh ON ucs.parent_id = sh.id
			WHERE ucs.is_deleted = false
		)
		SELECT * FROM step_hierarchy;`
	err := gormx.Raw(ctx, sql, &res, parentId)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// UpdateSiblingOrderAfter 更新同级后续步骤的 SiblingOrder
func (UiCaseStepDao) UpdateSiblingOrderAfter(ctx *commoncontext.MantisContext, parentID, caseID string, siblingOrder, offset int64) error {
	sql := `update ui_case_step set sibling_order=sibling_order+? where case_id = ? and parent_id = ? and sibling_order > ?`
	_, err := gormx.Exec(ctx, sql, offset, caseID, parentID, siblingOrder)
	return err
}

// Delete 删除步骤
func (UiCaseStepDao) Delete(ctx *commoncontext.MantisContext, stepID string) error {
	step := models.UiCaseStep{}
	step.Id = stepID
	step.IsDeleted = true
	_, err := gormx.UpdateOneByCondition(ctx, &step)
	return err
}

// queryRootStepsByCaseID 查询用例的第一层步骤
func (UiCaseStepDao) QueryRootStepsByCaseID(ctx *commoncontext.MantisContext, caseID string, steps *[]models.UiCaseStep) error {
	rootSteps := make([]models.UiCaseStep, 0)
	err := gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.UiCaseStep{}).Eq("case_id", caseID).Eq("parent_id", "").Eq("name", "根节点").Eq("is_deleted", false),
		&rootSteps,
	)
	if err != nil {
		return err
	}
	if len(rootSteps) == 0 {
		return nil
	}
	return gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.UiCaseStep{}).Eq("case_id", caseID).Eq("parent_id", rootSteps[0].Id).Eq("is_deleted", false).OrderByAsc("sibling_order"),
		&steps,
	)
}

// BuildStepTreeByCaseID 组装指定用例的解引用步骤树
// 接收 caseId，返回递归解引用后的步骤树
// 返回步骤树（[]UiCaseStep）和错误
// stepId会进行组合以保证其在case内唯一
func (s UiCaseStepDao) BuildStepTreeByCaseID(ctx *commoncontext.MantisContext, caseID string) ([]models.UiCaseStep, error) {
	// 获取用例的根步骤
	var rootSteps []models.UiCaseStep
	err := s.QueryRootStepsByCaseID(ctx, caseID, &rootSteps)
	if err != nil {
		return nil, fmt.Errorf("failed to query root steps for case %s: %w", caseID, err)
	}

	// 递归构建步骤树
	result, _, err := s.BuildStepTree(ctx, rootSteps, "", caseID, 0, false, false, "")
	if err != nil {
		return nil, fmt.Errorf("failed to build step tree for case %s: %w", caseID, err)
	}

	return result, nil
}

// BuildStepTree 递归构建解引用后的步骤树
// 返回组装好的步骤列表、插入的步骤总数和错误
// step里的distinctId会进行组合以保证其在case内唯一
func (s UiCaseStepDao) BuildStepTree(ctx *commoncontext.MantisContext, steps []models.UiCaseStep, parentID, caseID string, baseSiblingOrder int64, disable bool, isReference bool, referenceStepId string) ([]models.UiCaseStep, int64, error) {
	var result []models.UiCaseStep
	siblingOffset := int64(0)

	for _, step := range steps {
		if isReference {
			step.DistinctId = referenceStepId + "-" + step.Id
		} else {
			step.DistinctId = step.Id
		}
		if step.IsReference != nil && *step.IsReference && step.ReferenceCaseId != "" {
			// 查询被引用的根步骤
			var childSteps []models.UiCaseStep
			err := s.QueryRootStepsByCaseID(ctx, step.ReferenceCaseId, &childSteps)
			if err != nil {
				return nil, 0, fmt.Errorf("failed to query root steps for case %s: %w", step.ReferenceCaseId, err)
			}

			// 递归构建子树
			childResult, childStepCount, err := s.BuildStepTree(ctx, childSteps, parentID, caseID, baseSiblingOrder+siblingOffset, step.Disable == constants.StepDisableTrue, true, step.DistinctId)
			if err != nil {
				return nil, 0, err
			}

			result = append(result, childResult...)
			siblingOffset += childStepCount
		} else {
			// 准备非引用步骤
			newStep := step
			newStep.CaseId = caseID
			newStep.ParentId = parentID
			newStep.SiblingOrder = baseSiblingOrder + siblingOffset
			if disable {
				newStep.Disable = constants.StepDisableTrue
			}

			// 如果是文件夹，构建子树
			if newStep.IsDir {
				childSteps, err := s.FindStepsByParentId(ctx, step.Id, "")
				if err != nil {
					return nil, 0, fmt.Errorf("failed to query child steps for step %s: %w", step.Id, err)
				}

				// 递归构建子步骤树
				childResult, childStepCount, err := s.BuildStepTree(ctx, childSteps, newStep.Id, caseID, 0, newStep.Disable == constants.StepDisableTrue, isReference || false, referenceStepId)
				if err != nil {
					return nil, 0, err
				}
				newStep.Children = childResult
				siblingOffset += childStepCount
			}

			result = append(result, newStep)
			siblingOffset++
		}
	}
	return result, siblingOffset, nil
}

// FlattenStepArrayByCaseID 生成指定用例的解引用平铺步骤数组
// 接受 caseId，返回递归解引用后的平铺步骤数组，保留引用步骤
// 被引用用例的步骤的 ParentId 设置为引用步骤的 ID，仅修改结构体属性
// 保留原始 SiblingOrder，不进行修改
// 返回平铺步骤数组（[]UiCaseStep）和错误
func (s UiCaseStepDao) FlattenStepArrayByCaseID(ctx *commoncontext.MantisContext, caseID string) ([]models.UiCaseStep, error) {
	// 获取用例的根步骤
	rootSteps := make([]models.UiCaseStep, 0)
	err := s.QueryRootStepsByCaseID(ctx, caseID, &rootSteps)
	if err != nil {
		return nil, fmt.Errorf("failed to query root steps for case %s: %w", caseID, err)
	}
	virtualRootStep := models.UiCaseStep{}
	virtualRootStep.IsRoot = true
	virtualRootStep.CaseId = caseID
	virtualRootStep.IsDeleted = false
	err = gormx.SelectOneByCondition(ctx, &virtualRootStep)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return []models.UiCaseStep{}, nil
		} else {
			return nil, err
		}
	}
	virtualRootStep.DistinctId = virtualRootStep.Id

	// 递归构建平铺步骤数组
	result, err := s.flattenStepArray(ctx, rootSteps, virtualRootStep.Id, false, false, "")
	if err != nil {
		return nil, fmt.Errorf("failed to flatten step array for case %s: %w", caseID, err)
	}

	// 将根节点加入数组
	result = append(result, virtualRootStep)

	return result, nil
}

// flattenStepArray 递归构建解引用后的平铺步骤数组
// 参数：steps（当前层步骤），parentID（父步骤ID），caseID（当前用例ID）
// 返回：平铺步骤数组和错误
func (s UiCaseStepDao) flattenStepArray(ctx *commoncontext.MantisContext, steps []models.UiCaseStep, parentID string, disable, isReference bool, referenceStepId string) ([]models.UiCaseStep, error) {
	var result []models.UiCaseStep

	for _, step := range steps {
		// 复制步骤并更新 ParentId，保留原始 SiblingOrder
		newStep := step
		newStep.ParentId = parentID

		// 如果外层父步骤禁用，则将当前步骤标记为禁用
		if disable {
			newStep.Disable = constants.StepDisableTrue
		}

		if newStep.IsReference != nil && *newStep.IsReference && newStep.ReferenceCaseId != "" {
			// 修改名字为被引用例的名字
			component := models.UiCase{}
			component.Id = newStep.ReferenceCaseId
			err := gormx.SelectOneByCondition(ctx, &component)
			if err != nil {
				return nil, err
			}
			newStep.Name = component.Name
		}

		// 如果是被引用例的步骤，则重新设置id
		if isReference {
			newStep.DistinctId = referenceStepId + "-" + newStep.Id
		} else {
			newStep.DistinctId = newStep.Id
		}

		// 添加当前步骤到结果
		result = append(result, newStep)

		// 处理引用步骤
		if newStep.IsReference != nil && *newStep.IsReference && newStep.ReferenceCaseId != "" {
			// 查询被引用的根步骤
			var childSteps []models.UiCaseStep
			err := s.QueryRootStepsByCaseID(ctx, newStep.ReferenceCaseId, &childSteps)
			if err != nil {
				return nil, fmt.Errorf("failed to query root steps for case %s: %w", newStep.ReferenceCaseId, err)
			}

			// 递归处理被引用步骤，设置 ParentId 为当前引用步骤的 ID
			childResult, err := s.flattenStepArray(ctx, childSteps, newStep.DistinctId, disable || newStep.Disable == constants.StepDisableTrue, isReference || true, newStep.DistinctId)
			if err != nil {
				return nil, err
			}
			result = append(result, childResult...)
		}

		// 处理文件夹
		if newStep.IsDir {
			// 查询子步骤
			childSteps, err := s.FindStepsByCondition(ctx, "", newStep.Id, "")
			if err != nil {
				return nil, fmt.Errorf("failed to query child steps for step %s: %w", newStep.Id, err)
			}

			// 递归处理子步骤，设置 ParentId 为当前文件夹的 ID
			childResult, err := s.flattenStepArray(ctx, childSteps, newStep.DistinctId, disable || newStep.Disable == constants.StepDisableTrue, isReference || false, referenceStepId)
			if err != nil {
				return nil, err
			}
			result = append(result, childResult...)
		}
	}
	return result, nil
}

func (s UiCaseStepDao) SortNodeIds(ctx *commoncontext.MantisContext, caseId string, nodeIds []string) ([]string, error) {
	if len(nodeIds) == 0 {
		return nil, errors.New("节点 ID 数组为空")
	}
	steps := make([]models.UiCaseStep, 0)
	err := gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.UiCaseStep{}).Eq("case_id", caseId).Eq("is_deleted", false), &steps)
	if err != nil {
		return nil, err
	}
	if len(steps) == 0 {
		return nil, errors.New("步骤数组为空")
	}

	// 构建 ID 到节点的映射，便于快速查找
	// 构建 ID 到节点的映射
	stepMap := make(map[string]*models.UiCaseStep)
	for i := range steps {
		stepMap[steps[i].Id] = &steps[i]
	}

	// 验证所有 nodeIds 存在
	for _, id := range nodeIds {
		if _, exists := stepMap[id]; !exists {
			return nil, errors.New("节点 ID 不存在: " + id)
		}
	}

	// 定义节点信息结构体，用于排序
	type nodeInfo struct {
		id           string
		level        int   // 层级
		siblingOrder int64 // 当前节点的 SiblingOrder
	}

	// 计算每个节点的层级和路径
	nodes := make([]nodeInfo, 0, len(nodeIds))
	for _, id := range nodeIds {
		node := stepMap[id]
		level := 0

		// 追溯到根节点，构建路径
		current := node
		for current != nil && !current.IsRoot && current.ParentId != "" {
			level++
			current = stepMap[current.ParentId]
			if current == nil {
				return nil, fmt.Errorf("父节点不存在")
			}
		}
		if current != nil {
			level++
		}

		nodes = append(nodes, nodeInfo{
			id:           id,
			level:        level,
			siblingOrder: node.SiblingOrder,
		})
	}

	// 按层级和路径排序
	sort.Slice(nodes, func(i, j int) bool {
		// 按层级排序（浅层优先）
		if nodes[i].level != nodes[j].level {
			return nodes[i].level < nodes[j].level
		}

		// 如果同层级，按 SiblingOrder 排序
		return nodes[i].siblingOrder < nodes[j].siblingOrder
	})

	// 提取排序后的 nodeIds
	result := make([]string, len(nodes))
	for i, node := range nodes {
		result[i] = node.id
	}

	return result, nil
}
