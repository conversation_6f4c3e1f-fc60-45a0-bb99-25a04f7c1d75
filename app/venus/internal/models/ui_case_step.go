package models

import (
	"encoding/json"
	"errors"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/openapi"
	scene "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"gorm.io/gorm"
)

type UiCaseStep struct {
	models.Addons2
	CaseId             string             `json:"case_id" gorm:"column:case_id;type:text;index:idx_case_id;comment:用例id" expand:"ui_case"`
	Name               string             `json:"name" gorm:"column:name;type:text;comment:步骤名称" `
	ParentId           string             `json:"parent_id" gorm:"column:parent_id;type:text;comment:父节点id" expand:"ui_case_step"`
	ActionType         string             `json:"action_type" gorm:"column:action_type;type:text;comment:操作类型" `
	Action             models.JSONAny     `json:"action" gorm:"column:action;type:jsonb;comment:操作内容" `
	SettingsContent    *scene.StepConfig  `json:"settings_content" gorm:"column:settings_content;type:jsonb;comment:设置信息"`
	SettingEnable      *bool              `json:"setting_enable" gorm:"column:setting_enable;type:bool;comment:设置开关;not null;default:false"`
	GenSource          string             `json:"gen_source" gorm:"column:gen_source;type:text;comment:生成来源"`
	ElementIds         models.StringSlice `json:"element_ids" gorm:"column:element_ids;type:jsonb;comment:绑定的元素id" expand:"ui_element"`
	Disable            string             `json:"disable" gorm:"column:disable;type:text;comment:禁用状态:1是0否" `
	SiblingOrder       int64              `json:"sibling_order" gorm:"column:sibling_order;type:numeric;comment:元素顺序"`
	IsDir              bool               `json:"is_dir" gorm:"column:is_dir;type:bool;comment:是否是文件夹"` // 组其实就是文件夹
	IsRoot             bool               `json:"is_root" gorm:"column:is_root;type:bool;not null;default:false"`
	LatestReportStepId string             `json:"latest_report_step_id" gorm:"column:latest_report_step_id;type:text;comment:最近一次执行的报告id" expand:"ui_report_step_detail"`
	IsReference        *bool              `json:"is_reference" gorm:"column:is_reference;type:bool;not null;default:false"`
	ReferenceCaseId    string             `json:"reference_case_id" gorm:"column:reference_case_id;type:text;not null;default:''"`
	Expand             map[string]any     `json:"expand" gorm:"-"`
	Children           []UiCaseStep       `json:"-" gorm:"-"` // 仅用于内存组装树的字段
	ReportStepId       string             `json:"-" gorm:"-"` // 仅用于组件执行对象时暂存ui_report_step_detail表的id
	DistinctId         string             `json:"distinct_id" gorm:"-"`
}

func (UiCaseStep) TableName() string {
	return "ui_case_step"
}

func (UiCaseStep) Tree() {}

func (m *UiCaseStep) BeforeSave(tx *gorm.DB) (err error) {
	if m.IsDeleted {
		return nil
	}
	m.SetNewId()
	m.Updated = time.Now().UnixMilli()
	if m.Disable == "" {
		m.Disable = "0"
	}

	if m.SettingsContent == nil {
		m.SettingsContent = &scene.StepConfig{
			EndsWhenFail:    constants.DealErrorStop,
			RetryTimes:      0,
			ScreenShotTime:  constants.ScreenshotAlways,
			TimeoutMilli:    15000,
			WaitBeforeMilli: 0,
		}
	}
	err = dealRefElementIds(m)
	if err != nil {
		return err
	}
	// 检查for绑定的数据源id是否失效
	if m.ActionType == constants.ActionAdvancedLoopOperate {
		var action LoopOperateDTO
		marshal, err := json.Marshal(m.Action)
		if err != nil {
			return err
		}
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return err
		}
		if action.LoopCondition == constants.LoopConditionData {
			_, err = openapi.DatasourceOpenapi{}.GetData(action.DatasourceId)
			if err != nil {
				return errors.New("无法查询到for循环的数据源")
			}
		}
	}
	if m.ParentId == "" && m.Name != "根节点" && !m.IsRoot {
		vm := UiCaseStep{
			IsRoot:     true,
			IsDir:      true,
			Name:       "根节点",
			ActionType: "dir",
			CaseId:     m.CaseId,
			Disable:    "0",
		}
		res, err := addVirtualNode(vm, gormx.NewParamBuilder().Model(&UiCaseStep{}).Eq("case_id", m.CaseId).Eq("is_deleted", false).Eq("is_root", true))
		if err != nil {
			return err
		}
		m.ParentId = res.Id
	}
	return err
}

// dealRefElementIds 将step的action中的element的refElementId添加到step的elementIds中
func dealRefElementIds(step *UiCaseStep) error {
	if step.Action.Data == nil {
		return nil
	}
	marshal, err := json.Marshal(step.Action)
	if err != nil {
		return err
	}
	switch step.ActionType {
	case constants.ActionMouseClick:
		var action MouseClickDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return err
		}
		doElementRef(action.Element, step, false)
		step.Action.Data = action
	case constants.ActionMouseMove:
		var action MouseMoveDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return err
		}
		doElementRef(action.Element, step, false)
		step.Action.Data = action
	case constants.ActionMouseScroll:
		var action MouseScrollDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return err
		}
		doElementRef(action.ScrollElement, step, false)
		if action.Type == constants.MouseScrollTypeElement {
			doElementRef(action.Element, step, true)
		}
		step.Action.Data = action
	case constants.ActionMouseDrag:
		var action MouseDragDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return err
		}
		doElementRef(action.Element, step, false)
		if action.Target == constants.MouseDragTypeElement {
			doElementRef(action.TargetElement, step, true)
		}
		step.Action.Data = action
	case constants.ActionMouseHold:
		var action MouseHoldDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return err
		}
		doElementRef(action.Element, step, false)
		step.Action.Data = action
	case constants.ActionInput:
		var action InputOperationsDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return err
		}
		if action.Type == constants.InputTypeElement {
			doElementRef(action.Element, step, false)
		} else {
			step.ElementIds = []string{}
		}
		step.Action.Data = action
	case constants.ActionInputUploadFiles:
		var action UploadFilesDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return err
		}
		doElementRef(action.Element, step, false)
		step.Action.Data = action
	case constants.ActionInputSelect:
		var action SelectDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return err
		}
		doElementRef(action.Element, step, false)
		step.Action.Data = action
	case constants.ActionAdvancedWait:
		var action WaitEventsDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return err
		}
		if action.Type != constants.WaitEventTime {
			doElementRef(action.Element, step, false)
		} else {
			step.ElementIds = []string{}
		}
		step.Action.Data = action
	case constants.ActionAdvancedAssert:
		var action AssertDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return err
		}
		if action.Type == constants.WaitEventElementExist || action.Type == constants.WaitEventElementNotExist {
			doElementRef(action.Element, step, false)
		} else {
			step.ElementIds = []string{}
		}
	case constants.ActionAdvancedDataWithdraw:
		var action DataWithdrawDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return err
		}
		if action.Type == "element" && action.Element != nil {
			doElementRef(action.Element.Element, step, false)
		} else {
			step.ElementIds = []string{}
		}
	case constants.ActionAdvancedCodeOperation:
		var action CodeOperateDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return err
		}
		if action.OperateType == "element" {
			doElementRef(action.Element, step, false)
		} else {
			step.ElementIds = []string{}
		}
	case constants.ActionAdvancedIfOperate:
		var action IfOperateDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return err
		}
		if len(action.Condition) < 1 {
			return err
		}
		for i, con := range action.Condition {
			if i == 0 {
				doElementRef(con.Element, step, false)
				continue
			}
			doElementRef(con.Element, step, true)
		}
	default:
		step.ElementIds = []string{}
	}
	return nil
}

func doElementRef(ele *StepElementDTO, step *UiCaseStep, isAppend bool) {
	if ele == nil {
		return
	}
	if ele.TargetType == constants.ElementTypeCustom && !isAppend {
		step.ElementIds = []string{}
		return
	}
	eIds := make([]string, 0)
	if step.ElementIds == nil || !isAppend {
		eIds = append(eIds, ele.RefElementId)
	} else {
		eIds = append(step.ElementIds, ele.RefElementId)
	}
	step.ElementIds = eIds
}

var NeedDealActionType = []string{
	constants.ActionAdvancedAssert,
	constants.ActionMouseClick,
	constants.ActionMouseMove,
	constants.ActionMouseScroll,
	constants.ActionMouseDrag,
	constants.ActionMouseHold,
	constants.ActionInput,
	constants.ActionInputUploadFiles,
	constants.ActionInputSelect,
	constants.ActionAdvancedWait,
	constants.ActionAdvancedAssert,
	constants.ActionAdvancedDataWithdraw,
	constants.ActionAdvancedCodeOperation,
	constants.ActionAdvancedIfOperate,
}
