package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
)

type CaseController struct {
	*controller.BaseController
}

var (
	DefaultCaseController CaseController
	caseService           service.CaseService
)

type TransParam struct {
	Id  string `json:"case_id"`
	Env string `json:"env"`
}

type CaseCopyReq struct {
	Id string `json:"case_id"`
}

type GetLastRunResultReq struct {
	CaseId   string `schema:"case_id"`
	ReportId string `schema:"report_id"`
}

type StopDebugReq struct {
	CaseId   string `json:"case_id"`
	ReportId string `json:"report_id"`
}

type FindReferenceCaseReq struct {
	Page    int64 `schema:"page"`    // 当前页
	PerPage int64 `schema:"perPage"` // 页大小
}

type RunRes struct {
	ReportId string `json:"report_id"`
}

type RespId struct {
	Id string `json:"id"`
}

func (c *CaseController) Run(req *TransParam) (RunRes, error) {
	reportId, err := caseService.Run(c.MantisContext, req.Id, req.Env)
	return RunRes{ReportId: reportId}, err
}

func (c *CaseController) DebugPrepare(req *TransParam) (*dto.DebugCaseDto, error) {
	return caseService.DebugPrepare(c.MantisContext, req.Id, req.Env)
}

func (c *CaseController) StopExecute(req *StopDebugReq) error {
	return caseService.StopExecute(c.MantisContext, req.CaseId, req.ReportId)
}

func (c *CaseController) StartRecord(req *CaseCopyReq) error {
	return caseService.StartRecord(c.MantisContext, req.Id)
}

func (c *CaseController) StopRecord(req *CaseCopyReq) error {
	return caseService.StopRecord(c.MantisContext, req.Id)
}

func (c *CaseController) SaveRecord(agentCase *dto.AgentCaseDto) error {
	return caseService.SaveRecord(c.MantisContext, agentCase.Id, constants.GenByRecord, agentCase.Steps)
}

func (c *CaseController) SaveAiGenerate(agentSteps *dto.AgentCaseDto) (RespId, error) {
	id, err := caseService.SaveAIGenCase(c.MantisContext, constants.GenByAI, agentSteps)
	return RespId{Id: id}, err
}

func (c *CaseController) Copy(req *CaseCopyReq) error {
	return caseService.CopyCase(c.MantisContext, req.Id)
}

func (c *CaseController) GetLastRunResult(req *GetLastRunResultReq) (*dto.CaseRunResDTO, error) {
	return caseService.GetLastRunResult(c.MantisContext, req.CaseId, req.ReportId)
}

func (c *CaseController) Extract(req *CaseCopyReq) error {
	return caseService.Extract(c.MantisContext, req.Id)
}

func (c *CaseController) FindReferenceCases(req *FindReferenceCaseReq, idReq *dto.PathIdDto) (*commondto.PaginationRecord[models.UiCase], error) {
	return caseService.FindReferenceCaseByCaseId(c.MantisContext, idReq.Id, req.Page, req.PerPage)
}

func (c *CaseController) FindReferencePlan(req *FindReferenceCaseReq, idReq *dto.PathIdDto) (*commondto.PaginationRecord[models.UiPlan], error) {
	return caseService.FindReferencePlanByCaseId(c.MantisContext, idReq.Id, req.Page, req.PerPage)
}
