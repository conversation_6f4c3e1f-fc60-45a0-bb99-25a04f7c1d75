package service

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/openapi"
	cases "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/action"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/element"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/iancoleman/strcase"
)

// toAgentStep ui步骤转agent步骤
// notice: 此时step数组中的stepReportId字段为ui_report_step_detail表的id, id并不是ui_case_step表的id，而是经过组合的case内唯一的step id。（可能会组合reference_step_id）
func toAgentStep(ctx *commoncontext.MantisContext, caseRun models.UiCase, steps []models.UiCaseStep) (*cases.Case, error) {
	// 组装成agent的case
	var caseAgent cases.Case
	caseAgent.Id = caseRun.Id
	caseAgent.Config = cases.Config{
		BrowserType:       strings.ToLower(caseRun.BrowserConfig.Browser),
		IgnoreHttpsErrors: *caseRun.BrowserConfig.IgnoreHttpsCertificateError,
	}
	if caseRun.BrowserConfig.Window == constants.WindowMobile {
		caseAgent.Config.IsMoblie = true
		caseAgent.Config.Viewport = &struct {
			Width  int "json:\"width\""
			Height int "json:\"height\""
		}{
			Width:  720,
			Height: 1080,
		}
	}
	caseAgent.DefaultStepConfig = caseRun.StepsConfig
	if len(steps) == 0 {
		logger.Logger.Errorf("用例%s没有步骤，请先添加步骤", caseRun.Name)
		return nil, nil
	}
	caseAgentSteps := make([]*cases.Step, 0)
	err := recursiveDeal(ctx, steps, caseRun.StepsConfig, &caseAgentSteps)
	if err != nil {
		return nil, err
	}
	caseAgent.Steps = caseAgentSteps
	return &caseAgent, nil
}

func recursiveDeal(ctx *commoncontext.MantisContext, steps []models.UiCaseStep, caseStepConfig *cases.StepConfig,
	caseAgentSteps *[]*cases.Step,
) error {
	for _, step := range steps {
		if step.Disable == constants.StepDisableTrue {
			continue
		}
		if step.SiblingOrder < 1 && step.ParentId == "" && step.IsRoot {
			// 虚拟根节点,跳过
			continue
		}
		cs, err := uiStepToAgentStep(ctx, step)
		if err != nil {
			logger.Logger.Errorf("ui步骤转换agent步骤异常!!! stepId: %s, error: %+v", step.Id, err)
			return err
		}
		*caseAgentSteps = append(*caseAgentSteps, cs)
	}
	return nil
}

// uiStepToAgentStep 转换步骤
func uiStepToAgentStep(ctx *commoncontext.MantisContext, step models.UiCaseStep) (*cases.Step, error) {
	// 浏览器操作
	cs, typeExit, err := ofActionBrowser(ctx, step)
	if typeExit {
		cs.StepReportId = step.ReportStepId
		return cs, err
	}
	// 鼠标操作
	cs, typeExit, err = ofActionMouse(ctx, step)
	if typeExit {
		cs.StepReportId = step.ReportStepId
		return cs, err
	}
	// 输入操作
	cs, typeExit, err = ofActionInput(ctx, step)
	if typeExit {
		cs.StepReportId = step.ReportStepId
		return cs, err
	}
	// 高级操作
	cs, typeExit, err = ofActionAdvanced(ctx, step)
	if typeExit {
		cs.StepReportId = step.ReportStepId
		return cs, err
	}
	logger.Logger.Warnf("未识别的操作类型%s", step.ActionType)
	return nil, errors.New("未识别的操作类型:" + step.ActionType)
}

// 浏览器操作
func ofActionBrowser(ctx *commoncontext.MantisContext, step models.UiCaseStep) (*cases.Step, bool, error) {
	arr := []string{
		constants.ActionBrowserOpen, constants.ActionBrowserClose, constants.ActionBrowserToggleWindow,
		constants.ActionBrowserForward, constants.ActionBrowserBack, constants.ActionBrowserRefresh,
	}
	boolX := slices.Contains(arr, step.ActionType)
	if !boolX {
		return nil, false, nil
	}
	var cs *cases.Step
	at := strcase.ToLowerCamel(step.ActionType)
	switch step.ActionType {
	// 1 浏览器打开
	case constants.ActionBrowserOpen:
		var tt models.OpenPageDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		cs = &cases.Step{
			Id: step.Id,
			Action: &action.OpenPage{
				IsNewPage: true,
				Url:       tt.Url,
			},
			ActionType: at,
		}
	// 2 浏览器关闭
	case constants.ActionBrowserClose:
		var tt models.OperatePageDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		cs = &cases.Step{
			Id: step.Id,
			Action: &action.ClosePage{
				CustomIndex: tt.CustomIndex,
				Type:        tt.WindowAction,
			},
			ActionType: at,
		}
	// 3 切换窗口
	case constants.ActionBrowserToggleWindow:
		var tt models.OperatePageDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		cs = &cases.Step{
			Id: step.Id,
			Action: &action.ToggleWindow{
				CustomIndex: tt.CustomIndex,
				Type:        tt.WindowAction,
			},
			ActionType: at,
		}
	// 4 前进
	case constants.ActionBrowserForward:
		cs = &cases.Step{
			Id:         step.Id,
			ActionType: at,
			Action:     &action.Forward{},
		}
	// 5 后退
	case constants.ActionBrowserBack:
		cs = &cases.Step{
			Id:         step.Id,
			ActionType: at,
			Action:     &action.Back{},
		}
	// 6 刷新
	case constants.ActionBrowserRefresh:
		cs = &cases.Step{
			Id:         step.Id,
			ActionType: at,
			Action:     &action.Refresh{},
		}

	}
	if step.SettingEnable != nil && *step.SettingEnable {
		cs.Config = step.SettingsContent
	}
	return cs, true, nil
}

// 鼠标操作
func ofActionMouse(ctx *commoncontext.MantisContext, step models.UiCaseStep) (*cases.Step, bool, error) {
	arr := []string{constants.ActionMouseMove, constants.ActionMouseClick, constants.ActionMouseScroll, constants.ActionMouseDrag, constants.ActionMouseHold}
	bool := slices.Contains(arr, step.ActionType)
	if !bool {
		return nil, false, nil
	}
	var cs *cases.Step
	at := strcase.ToLowerCamel(step.ActionType)
	switch step.ActionType {
	// 1 点击
	case constants.ActionMouseClick:
		var tt models.MouseClickDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		elementInfo, err := getElementAndToAgent(ctx, tt.Element)
		if err != nil {
			return nil, true, err
		}
		agentButton := ""
		agentType := ""
		if tt.Type == constants.MouseDoubleClick {
			agentType = "double"
			agentButton = "left"
		} else if tt.Type == constants.MouseRightClick {
			agentButton = "right"
			agentType = "single"
		} else if tt.Type == constants.MouseLeftClick {
			agentButton = "left"
			agentType = "single"
		}
		cs = &cases.Step{
			Id:         step.Id,
			ActionType: at,
			Action: &action.MouseClick{
				Type:    agentType,
				Button:  agentButton,
				Element: elementInfo,
			},
		}
	// 2 鼠标移动/悬浮
	case constants.ActionMouseMove:
		var tt models.MouseMoveDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		elementInfo, err := getElementAndToAgent(ctx, tt.Element)
		if err != nil {
			return nil, true, err
		}
		cs = &cases.Step{
			Id:         step.Id,
			ActionType: at,
			Action: &action.MouseMove{
				Type:    tt.Type,
				Element: elementInfo,
			},
		}
	// 3 鼠标滚动
	case constants.ActionMouseScroll:
		var tt models.MouseScrollDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		actionx := &action.MouseScroll{
			Type: tt.Type,
		}
		cs = &cases.Step{
			Id:         step.Id,
			ActionType: at,
			Action:     actionx,
		}
		if tt.Type == "element" {
			scallElement, err := getElementAndToAgent(ctx, tt.Element)
			if err != nil {
				return nil, true, err
			}
			actionx.Element = scallElement
		}
		if tt.Target == "pos" {
			actionx.ScrollType = "position"
			actionx.Position = &action.ScrollPosition{
				X: tt.Distance.X,
				Y: tt.Distance.Y,
			}
		} else {
			actionx.ScrollType = "element"
			scallTargetElement, err1 := getElementAndToAgent(ctx, tt.ScrollElement)
			if err1 != nil {
				return nil, true, err1
			}
			actionx.ScrollElement = scallTargetElement
		}
	// 4 鼠标拖拽
	case constants.ActionMouseDrag:
		var tt models.MouseDragDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		actionx := &action.MouseDrag{}
		cs = &cases.Step{
			Id:         step.Id,
			ActionType: at,
			Action:     actionx,
		}
		dragElement, err := getElementAndToAgent(ctx, tt.Element)
		if err != nil {
			return nil, true, err
		}
		actionx.Element = dragElement
		if tt.Target == constants.MouseDragTypePos {
			actionx.Type = constants.MouseDragTypePos
			actionx.EndPointCoordinates = &action.DragPointCoordinates{
				X: tt.Distance.X,
				Y: tt.Distance.Y,
			}
		} else {
			actionx.Type = constants.MouseDragTypeElement
			dragTargetElement, err := getElementAndToAgent(ctx, tt.TargetElement)
			if err != nil {
				return nil, true, err
			}
			actionx.TargetElement = dragTargetElement
		}
	// 5 鼠标按住
	case constants.ActionMouseHold:
		var tt models.MouseHoldDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		holdElement, err := getElementAndToAgent(ctx, tt.Element)
		if err != nil {
			return nil, true, err
		}
		cs = &cases.Step{
			Id:         step.Id,
			ActionType: at,
			Action: &action.MouseHold{
				Element:   holdElement,
				HoldMilli: tt.Hold,
			},
		}
	}
	if step.SettingEnable != nil && *step.SettingEnable {
		cs.Config = step.SettingsContent
	}
	return cs, true, nil
}

// 输入操作
func ofActionInput(ctx *commoncontext.MantisContext, step models.UiCaseStep) (*cases.Step, bool, error) {
	arr := []string{constants.ActionInput, constants.ActionInputSelect, constants.ActionInputUploadFiles, constants.ActionInputPress}
	boolX := slices.Contains(arr, step.ActionType)
	if !boolX {
		return nil, false, nil
	}
	var cs *cases.Step
	var err error
	at := strcase.ToLowerCamel(step.ActionType)
	switch step.ActionType {
	case constants.ActionInputPress:
		var tt models.PressDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		cs = &cases.Step{
			Id:         step.Id,
			ActionType: at,
			Action: &action.Press{
				Key:   tt.PressKey,
				Delay: tt.PressMilli,
			},
		}
	// 输入操作
	case constants.ActionInput:
		var tt models.InputOperationsDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		elementInfo, err := getElementAndToAgent(ctx, tt.Element)
		if err != nil {
			return nil, true, err
		}
		parseBool, err := strconv.ParseBool(tt.IsAppendContent)
		if err != nil {
			logger.Logger.Warnf("parse %s to bool error:%s", tt.IsAppendContent, err.Error())
			parseBool = false
		}
		cs = &cases.Step{
			Id:         step.Id,
			ActionType: at,
			Action: &action.Input{
				Element:         elementInfo,
				InputContent:    tt.InputContent,
				IsAppendContent: parseBool,
			},
		}
	// 选择操作
	case constants.ActionInputSelect:
		var tt models.SelectDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		elementInfo, err := getElementAndToAgent(ctx, tt.Element)
		if err != nil {
			return nil, true, err
		}
		cs = &cases.Step{
			Id:         step.Id,
			ActionType: at,
			Action: &action.Select{
				Type:    tt.Type,
				Element: elementInfo,
				Text:    tt.Text,
				Value:   tt.Value,
				Index:   tt.Index,
			},
		}
	// 文件上传
	case constants.ActionInputUploadFiles:
		var tt models.UploadFilesDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		elementInfo, err := getElementAndToAgent(ctx, tt.Element)
		if err != nil {
			return nil, true, err
		}
		for _, file := range tt.Files {
			decode, err := base64.StdEncoding.DecodeString(file.OssKey)
			if err != nil {
				return nil, false, err
			}
			file.OssKey = string(decode)
		}
		files := make([]struct {
			FileName string `json:"fileName"`
			MimeType string `json:"mimeType"`
			OssKey   string `json:"ossKey"`
		}, 0)
		for _, f := range tt.Files {
			files = append(files, struct {
				FileName string "json:\"fileName\""
				MimeType string "json:\"mimeType\""
				OssKey   string "json:\"ossKey\""
			}{
				FileName: f.FileName,
				MimeType: f.MimeType,
				OssKey:   f.OssKey,
			})
		}
		cs = &cases.Step{
			Id:         step.Id,
			ActionType: at,
			Action: &action.UploadFiles{
				Element: elementInfo,
				Files:   files,
			},
		}
	}
	if step.SettingEnable != nil && *step.SettingEnable {
		cs.Config = step.SettingsContent
	}
	return cs, true, err
}

func convertAssertAction(ctx *commoncontext.MantisContext, tt models.AssertDTO) (*action.Assert, error) {
	assertType := tt.Type
	if assertType == "" {
		return nil, errors.New("未选择断言类型")
	}
	if strings.HasPrefix(assertType, "element") {
		elementInfo, err := getElementAndToAgent(ctx, tt.Element)
		if err != nil {
			return nil, err
		}
		if assertType == "element_exist" {
			return &action.Assert{
				Type: "element",
				Element: &action.ElementAssert{
					Type:    "exist",
					Element: elementInfo,
				},
			}, nil
		}
		if assertType == "element_not_exist" {
			return &action.Assert{
				Type: "element",
				Element: &action.ElementAssert{
					Type:    "notexist",
					Element: elementInfo,
				},
			}, nil
		}
		if assertType == "element" {
			return &action.Assert{
				Type: "element",
				Element: &action.ElementAssert{
					Type:     tt.AssertElement.Type,
					Element:  elementInfo,
					AttrKey:  tt.AssertElement.AttrKey,
					Expected: tt.AssertElement.Expected,
					Operator: tt.AssertElement.Operator,
				},
			}, nil
		}
	}
	if assertType == "page" {
		return &action.Assert{
			Type: assertType,
			Page: tt.Page,
		}, nil
	} else if assertType == "variable" {
		return &action.Assert{
			Type:     assertType,
			Variable: tt.Variable,
		}, nil
	} else if assertType == "text_exist" {
		return &action.Assert{
			Type: "text",
			Text: &action.TextAssert{
				Texts: tt.Text,
				Type:  "exist",
			},
		}, nil
	} else if assertType == "text_not_exist" {
		return &action.Assert{
			Type: "text",
			Text: &action.TextAssert{
				Texts: tt.Text,
				Type:  "notexist",
			},
		}, nil
	} else {
		return nil, errors.New("不支持的断言类型")
	}
}

// 高级操作
func ofActionAdvanced(ctx *commoncontext.MantisContext, step models.UiCaseStep) (*cases.Step, bool, error) {
	arr := []string{
		constants.ActionAdvancedAssert, constants.ActionAdvancedCodeOperation,
		constants.ActionAdvancedCallDatabase, constants.ActionAdvancedIfOperate,
		constants.ActionAdvancedLoopOperate,
		constants.ActionAdvancedWait, constants.ActionAdvancedDataWithdraw,
	}
	boolX := slices.Contains(arr, step.ActionType)
	if !boolX {
		return nil, false, nil
	}
	var cs *cases.Step
	var err error
	at := strcase.ToLowerCamel(step.ActionType)
	switch step.ActionType {
	// 断言
	case constants.ActionAdvancedAssert:
		var tt models.AssertDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		assert, err := convertAssertAction(ctx, tt)
		if err != nil {
			return nil, true, err
		}
		cs = &cases.Step{
			Id:         step.Id,
			ActionType: at,
			Action:     assert,
		}
	// 代码操作
	case constants.ActionAdvancedCodeOperation:
		var tt models.CodeOperateDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		elementInfo, err := getElementAndToAgent(ctx, tt.Element)
		if err != nil {
			return nil, true, err
		}
		cs = &cases.Step{
			Id:         step.Id,
			ActionType: at,
			Action: &action.CodeOperation{
				Type:          tt.CodeType,
				Element:       elementInfo,
				OperationType: tt.OperateType,
				CodeText:      tt.CodeContent,
			},
		}
	// 数据库调用
	case constants.ActionAdvancedCallDatabase:
		cs = &cases.Step{
			Id:         step.Id,
			ActionType: at,
			Action:     &action.CallDatabase{
				// todo
			},
		}
	// 条件操作
	case constants.ActionAdvancedIfOperate:
		return ofActionIfOrFor(ctx, step)
	// 循环操作
	case constants.ActionAdvancedLoopOperate:
		return ofActionIfOrFor(ctx, step)
	// 等待事件
	case constants.ActionAdvancedWait:
		var tt models.WaitEventsDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		var elementInfo *element.Element
		if tt.Element != nil {
			elementInfo, err = getElementAndToAgent(ctx, tt.Element)
			if err != nil {
				return nil, true, err
			}
		}
		cs = &cases.Step{
			Id:         step.Id,
			ActionType: at,
			Action: &action.Wait{
				Type:      tt.Type,
				WaitMilli: tt.WaitTime,
				Element:   elementInfo,
			},
		}
	// 数据提取
	case constants.ActionAdvancedDataWithdraw:
		var tt models.DataWithdrawDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		var eleDraw *action.WithdrawElement
		if tt.Type == "element" {
			elementInfo, err := getElementAndToAgent(ctx, tt.Element.Element)
			if err != nil {
				return nil, true, err
			}
			eleDraw = &action.WithdrawElement{
				Type:          tt.Element.Type,
				Element:       elementInfo,
				AttributeName: tt.Element.AttributeName,
			}
		}
		dw := &action.DataWithdraw{
			VariableName: tt.VariableName,
			Element:      eleDraw,
			Webpage:      tt.Webpage,
		}
		if tt.RegexEnable {
			dw.Regex = tt.Regex
		}
		cs = &cases.Step{
			Id:         step.Id,
			ActionType: at,
			Action:     dw,
		}
	}
	if step.SettingEnable != nil && *step.SettingEnable {
		cs.Config = step.SettingsContent
	}
	return cs, true, err
}

// 转换if或者for
func ofActionIfOrFor(ctx *commoncontext.MantisContext, step models.UiCaseStep) (*cases.Step, bool, error) {
	switch step.ActionType {
	case constants.ActionAdvancedIfOperate:
		var tt models.IfOperateDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		asserts := make([]*action.Assert, 0)
		for _, ass := range tt.Condition {
			assert, err := convertAssertAction(ctx, ass)
			if err != nil {
				return nil, true, err
			}
			asserts = append(asserts, assert)
		}
		steps := step.Children
		childSteps := make([]*cases.Step, 0)
		// 递归处理子步骤
		err = recursiveDeal(ctx, steps, nil, &childSteps)
		if err != nil {
			return nil, true, err
		}
		act := &cases.If{
			Condition: cases.Condition{
				Relation: tt.Relationship,
				Asserts:  asserts,
			},
			Child: &cases.Case{
				Steps: childSteps,
			},
		}
		return &cases.Step{
			Id:         step.Id,
			ActionType: "if",
			Action:     act,
		}, true, nil
	case constants.ActionAdvancedLoopOperate:
		var tt models.LoopOperateDTO
		err := typeConversion(step.Action, &tt)
		if err != nil {
			return nil, true, err
		}
		steps := step.Children
		childSteps := make([]*cases.Step, 0)
		err = recursiveDeal(ctx, steps, nil, &childSteps)
		if err != nil {
			return nil, true, err
		}
		data := make([]map[string]string, 0)
		if tt.DatasourceId != "" && tt.LoopCondition == constants.LoopConditionData {
			// 数据源
			dataSource, err := openapi.DatasourceOpenapi{}.GetData(tt.DatasourceId)
			if err != nil {
				return nil, true, fmt.Errorf("无法查询到for循环的数据源, step:%s", step.Name)
			}
			for _, d := range dataSource {
				current := make(map[string]string)
				for k, v := range d {
					if k == "id" || k == "isNew" {
						continue
					}
					current[k] = v.(string)
				}
				data = append(data, current)
			}
		}
		act := &cases.For{
			Type:  tt.LoopCondition,
			Times: int(tt.LoopTimes),
			Data:  data,
			Child: &cases.Case{
				Steps: childSteps,
			},
		}
		return &cases.Step{
			Id:         step.Id,
			ActionType: "for",
			Action:     act,
		}, true, nil
	default:
		return nil, false, nil
	}
}

// 获取元素
func getElementAndToAgent(ctx *commoncontext.MantisContext, eleDTO *models.StepElementDTO) (*element.Element, error) {
	if eleDTO == nil {
		logger.Logger.Warn("element is nil")
		return nil, nil
	}
	var uiElement *element.ElementLocator
	// 自定义元素
	if eleDTO.TargetType == constants.ElementTypeCustom {
		uiElement = eleDTO.CustomElement
	} else if eleDTO.TargetType == constants.ElementTypeRef {
		// 引用元素
		uiRefElement, err := elementDao.FindElementById(ctx, eleDTO.RefElementId)
		if err != nil {
			return nil, err
		}
		if uiRefElement.Id == "" {
			return nil, errors.New("element is nil")
		}
		uiElement = &uiRefElement.Locator.ElementLocator
	} else {
		return nil, errors.New("element type is not support")
	}
	res := element.Element{}
	if uiElement.Type == constants.ElementTypeLocator {
		res.Locator = uiElement.Locator
		res.Index = uiElement.Locator.Index
	} else if uiElement.Type == constants.ElementTypeSelector {
		res.Selector = &uiElement.Selector.Value
		res.Index = uiElement.Selector.Index
	}
	return &res, nil
}

// typeConversion 类型转换
// @param src 源数据
// @param dst 目标数据,必须是指针
func typeConversion(src any, dst any) error {
	marshal, err := json.Marshal(src)
	if err != nil {
		return err
	}
	err = json.Unmarshal(marshal, dst)
	if err != nil {
		return err
	}
	return nil
}
