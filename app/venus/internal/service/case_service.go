package service

import (
	"encoding/json"
	"errors"
	"time"

	cases "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/message/payload"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/pubsub"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	commonmodels "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/types"
)

var reportService ReportService

type CaseService struct{}

// Run 执行用例
func (s CaseService) Run(ctx *commoncontext.MantisContext, caseId, env string) (string, error) {
	uiCase := models.UiCase{}
	uiCase.Id = caseId
	err := gormx.SelectOneByCondition(ctx, &uiCase)
	if err != nil {
		return "", err
	}
	reportId, caseStepMap, err := reportService.InitReport(ctx, []string{caseId}, uiCase.Name, caseId, uiCase.SpaceId, env, types.Cloud, nil, false, nil, nil)
	if err != nil {
		return "", err
	}
	agentCase, err := toAgentStep(ctx, uiCase, caseStepMap[caseId])
	if err != nil {
		return "", err
	}
	if _, err := gormx.Exec(ctx, "update ui_case set last_report_id = ? where id = ?", reportId, caseId); err != nil {
		return "", err
	}
	msgByte, err := json.Marshal([]cases.Case{*agentCase})
	if err != nil {
		return "", err
	}
	msg := payload.UiExecuteTaskPayload{
		Callback:        configs.Config.Modules.Venus.StepReportCallback,
		ReportId:        reportId,
		Input:           string(msgByte),
		FileDownloadUrl: configs.Config.Modules.Venus.FileDownloadUrl,
		VariableUrl:     configs.Config.Modules.Venus.VariableUrl,
		Env:             env,
	}
	pubsub.SendToRedisQueue(msg, pubsub.WebQueue)
	logger.Logger.Infof("发送消息称中->执行用例: %s,报告id: %s", string(msgByte), reportId)
	return reportId, nil
}

func (s CaseService) DebugPrepare(ctx *commoncontext.MantisContext, caseId, env string) (*dto.DebugCaseDto, error) {
	uiCase := models.UiCase{}
	uiCase.Id = caseId
	err := gormx.SelectOneByCondition(ctx, &uiCase)
	if err != nil {
		return nil, err
	}
	// 初始化report
	reportId, caseStepsMap, err := reportService.InitReport(ctx, []string{caseId}, uiCase.Name, caseId, uiCase.SpaceId, env, types.Debug, nil, false, nil, nil)
	if err != nil {
		return nil, err
	}
	agentCase, err := toAgentStep(ctx, uiCase, caseStepsMap[caseId])
	if err != nil {
		return nil, err
	}
	if _, err := gormx.Exec(ctx, "update ui_case set last_report_id = ? where id = ?", reportId, caseId); err != nil {
		return nil, err
	}
	uiSteps, err := caseStepDao.FlattenStepArrayByCaseID(ctx, caseId)
	if err != nil {
		return nil, err
	}
	res := &dto.DebugCaseDto{
		Steps:     uiSteps,
		AgentCase: agentCase,
		ReportId:  reportId,
	}
	return res, nil
}

func (s CaseService) StopExecute(ctx *commoncontext.MantisContext, caseId string, reportId string) error {
	// 判断是否是云端执行，如果是，则停止pod
	report := models.UiReport{}
	report.Id = reportId
	report.IsDeleted = false
	err := gormx.SelectOneByCondition(ctx, &report)
	if err != nil {
		return err
	}
	reportService.DealCaseFinish(ctx, reportId, caseId, false)
	if report.ExecType == string(types.Cloud) {
		// shut down k8s
		if len(report.K8sTaskIds) != 0 {
			provider, _ := driver.NewDriverProvider()
			for _, k8sTaskId := range report.K8sTaskIds {
				err := provider.Cancel(ctx, k8sTaskId)
				if err != nil {
					return err
				}
			}
		}
	}
	return nil
}

func (s CaseService) StartRecord(ctx *commoncontext.MantisContext, caseId string) error {
	// 检查是否有人正在录制
	uiCase := models.UiCase{}
	uiCase.Id = caseId
	uiCase.IsDeleted = false
	err := gormx.SelectOneByCondition(ctx, &uiCase)
	if err != nil {
		return err
	}
	if *uiCase.Recording {
		return errors.New("其他用户正在录制中, 请等待")
	}
	// 设置状态
	recording := true
	uiCase.Recording = &recording
	_, err = gormx.UpdateOneByCondition(ctx, &uiCase)
	if err != nil {
		return err
	}
	return nil
}

func (s CaseService) StopRecord(ctx *commoncontext.MantisContext, caseId string) error {
	// 设置状态
	uiCase := models.UiCase{}
	uiCase.Id = caseId
	recording := false
	uiCase.Recording = &recording
	_, err := gormx.UpdateOneByCondition(ctx, &uiCase)
	if err != nil {
		return err
	}
	return nil
}

// SaveRecord 保存录制的步骤并停止录制
func (s CaseService) SaveRecord(ctx *commoncontext.MantisContext, caseId, genSource string, steps []map[string]any) error {
	// 保存录制结果
	uiSteps := make([]*models.UiCaseStep, 0)
	for _, step := range steps {
		uiStep, err := agentStepToUIStep(ctx, step, caseId, genSource)
		if err != nil {
			return err
		}
		if uiStep == nil {
			continue
		}
		uiSteps = append(uiSteps, uiStep)
	}
	// 保存前清空原有的步骤
	if _, err := gormx.Exec(ctx, "delete from ui_case_step where case_id = ?", caseId); err != nil {
		return err
	}
	return caseStepDao.BatchInsert(ctx, uiSteps)
}

// SaveAIGenCase 保存AI生成的用例
func (s CaseService) SaveAIGenCase(ctx *commoncontext.MantisContext, genSource string, aiGen *dto.AgentCaseDto) (string, error) {
	caseId := aiGen.Id
	if aiGen.Id == "" {
		record := models.UiCase{
			Name:        aiGen.Name,
			SpaceId:     aiGen.SpaceId,
			Description: aiGen.Name,
			Addons2: commonmodels.Addons2{
				Creator:  ctx.User.Name,
				Modifier: ctx.User.Name,
			},
		}
		record.IsDir = false
		record.ParentId = aiGen.ParentId
		_, err := gormx.InsertOne(ctx, &record)
		if err != nil {
			return "", err
		}
		caseId = record.Id
	}
	err := s.SaveRecord(ctx, caseId, genSource, aiGen.Steps)
	if err != nil {
		// 删除用例下的步骤
		_, errDel := gormx.Exec(ctx, "delete from ui_case_step where case_id = ?", caseId)
		if errDel != nil {
			logger.Logger.Warnf("删除用例下的步骤失败,err=%s", errDel)
		}
		return "", err
	}
	return caseId, err
}

func (s CaseService) CopyCase(ctx *commoncontext.MantisContext, Id string) error {
	// 先复制用例
	oldCase := models.UiCase{}
	oldCase.Id = Id
	oldCase.IsDeleted = false
	err := gormx.SelectOneByCondition(ctx, &oldCase)
	if err != nil {
		return err
	}
	parentId := oldCase.ParentId
	siblingOrder := oldCase.SiblingOrder
	newCase := oldCase
	newCase.Id = ""
	newCase.Name += "(复制)"
	newCase.SiblingOrder = 0
	newCase.LastReportId = ""
	_, err = gormx.InsertOne(ctx, &newCase)
	if err != nil {
		return err
	}
	newSiblingId := ""
	silbingCases := make([]models.UiCase, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.UiCase{}).OrderByAsc("sibling_order").Eq("parent_id", parentId).Gt("sibling_order", siblingOrder).Eq("is_deleted", false),
		&silbingCases,
	)
	if err != nil {
		return err
	}
	if len(silbingCases) == 0 {
		newSiblingId = "-1"
	} else {
		newSiblingId = silbingCases[0].Id
	}
	tableName := "ui_case"
	if err := gormx.Move(ctx, tableName, newCase.Id, newCase.ParentId, newSiblingId); err != nil {
		return err
	}
	// copy steps
	return CaseStepService{}.recursiveCopyByCase(ctx, Id, newCase.Id)
}

func (s CaseService) GetLastRunResult(ctx *commoncontext.MantisContext, caseId, reportId string) (*dto.CaseRunResDTO, error) {
	if reportId == "" {
		case1 := models.UiCase{}
		case1.Id = caseId
		case1.IsDeleted = false
		err := gormx.SelectOneByCondition(ctx, &case1)
		if err != nil {
			return nil, err
		}
		if case1.LastReportId == "" {
			return nil, nil
		}
		reportId = case1.LastReportId
	}
	// 查询report
	report := models.UiReport{}
	report.Id = reportId
	report.IsDeleted = false
	err := gormx.SelectOneByCondition(ctx, &report)
	if err != nil {
		return nil, err
	}
	res := &dto.CaseRunResDTO{
		Finished:  report.Status == constants.ReportStatusSuccess || report.Status == constants.ReportStatusFail,
		StartTime: report.ExecStartTime,
		Duration:  time.Now().UnixMilli() - report.ExecStartTime,
		Res:       make(map[string][]models.ReportRes),
	}
	// 查询 step report
	reportSteps := make([]models.UiReportStepDetail, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.UiReportStepDetail{}).Eq("case_id", caseId).Eq("report_id", report.Id).Eq("is_deleted", false),
		&reportSteps,
	)
	if err != nil {
		return nil, err
	}
	if len(reportSteps) == 0 {
		return res, nil
	}
	for _, detail := range reportSteps {
		res.Res[detail.StepId] = detail.ReportMessage
	}
	return res, nil
}

func (s CaseService) Extract(ctx *commoncontext.MantisContext, caseId string) error {
	component := models.UiCase{}
	component.Id = caseId
	isComponent := true
	component.IsComponent = &isComponent
	_, err := gormx.UpdateOneByCondition(ctx, &component)
	return err
}

func (s CaseService) FindReferenceCaseByCaseId(ctx *commoncontext.MantisContext, id string, page, perPage int64) (*commondto.PaginationRecord[models.UiCase], error) {
	total, err := caseDao.CountCaseByComponentIds(ctx, []string{id})
	if err != nil {
		logger.Logger.Panicf("%+v", err)
	}
	if total == 0 {
		return &commondto.PaginationRecord[models.UiCase]{
			Page:    int(page),
			PerPage: int(perPage),
			Items:   []models.UiCase{},
		}, nil
	}
	items, err := caseDao.GetReferenceCaseByComponentIdWithPage(ctx, id, page, perPage)
	if err != nil {
		return nil, err
	}
	return &commondto.PaginationRecord[models.UiCase]{
		Page:       int(page),
		PerPage:    int(perPage),
		TotalItems: int64(total),
		Items:      items,
	}, nil
}

func (s CaseService) FindReferencePlanByCaseId(ctx *commoncontext.MantisContext, caseId string, page, perPage int64) (*commondto.PaginationRecord[models.UiPlan], error) {
	total, err := caseDao.CountPlanByCaseIds(ctx, []string{caseId})
	if err != nil {
		logger.Logger.Panicf("%+v", err)
	}
	if total == 0 {
		return &commondto.PaginationRecord[models.UiPlan]{
			Page:    int(page),
			PerPage: int(perPage),
			Items:   []models.UiPlan{},
		}, nil
	}
	items, err := caseDao.GetReferencePlanByCaseIdWithPage(ctx, caseId, page, perPage)
	if err != nil {
		return nil, err
	}
	return &commondto.PaginationRecord[models.UiPlan]{
		Page:       int(page),
		PerPage:    int(perPage),
		TotalItems: int64(total),
		Items:      items,
	}, nil
}

func (s CaseService) Delete(ctx *commoncontext.MantisContext, id string) error {
	current := models.UiCase{}
	current.Id = id
	err := gormx.SelectOneByCondition(ctx, &current)
	if err != nil {
		return err
	}
	childrenIds := make([]string, 0)
	childrenIds = append(childrenIds, current.Id)
	// 如果是目录，则要向下递归查询子节点
	if current.IsDir {
		children, err := caseDao.RecursiveGetCasesByParentId(ctx, current.Id)
		if err != nil {
			return err
		}
		for _, child := range children {
			childrenIds = append(childrenIds, child.Id)
		}
	}
	allowed, err := s.batchDeleteCheckReferenceCase(ctx, childrenIds)
	if err != nil {
		return err
	}
	if !allowed {
		return errors.New("当前组件或子组件被其他用例关联，无法删除")
	}
	allowed, err = s.batchDeleteCheckReferencePlan(ctx, childrenIds)
	if err != nil {
		return err
	}
	if !allowed {
		return errors.New("当前组件或子组件被测试计划关联，无法删除")
	}
	current.IsDeleted = true
	_, err = gormx.UpdateOneByCondition(ctx, &current)
	if err != nil {
		return err
	}
	return nil
}

func (s CaseService) batchDeleteCheckReferenceCase(ctx *commoncontext.MantisContext, caseIds []string) (bool, error) {
	count, err := caseDao.CountCaseByComponentIds(ctx, caseIds)
	return count == 0, err
}

func (s CaseService) batchDeleteCheckReferencePlan(ctx *commoncontext.MantisContext, planIds []string) (bool, error) {
	count, err := caseDao.CountPlanByCaseIds(ctx, planIds)
	return count == 0, err
}
