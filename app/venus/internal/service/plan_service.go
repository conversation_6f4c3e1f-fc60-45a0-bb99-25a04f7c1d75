package service

import (
	"encoding/json"
	"errors"
	"fmt"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"gorm.io/gorm"

	cases "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/message/payload"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/pubsub"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/types"
)

type PlanService struct{}

func (PlanService) ManualExecute(ctx *commoncontext.MantisContext, planId string) (string, error) {
	// 查询plan
	plan := models.UiPlan{}
	plan.Id = planId
	plan.IsDeleted = false
	err := gormx.SelectOneByCondition(ctx, &plan)
	if err != nil {
		return "", err
	}
	if len(plan.CaseIds) == 0 {
		return "", errors.New("执行前请先选择用例")
	}
	cids, err := filterCase(ctx, plan.CaseIds)
	if err != nil {
		return "", err
	}
	if len(cids) == 0 {
		return "", errors.New("计划下无可执行的用例")
	}
	reportId, caseStepsMap, err := reportService.InitReport(ctx, cids, plan.Name, plan.Id, plan.SpaceId, plan.Env, types.Manual, nil, *plan.EnableConfig, plan.BrowserConfig, plan.StepConfig)
	if err != nil {
		return "", err
	}
	err = updateLastReportInfo(ctx, planId, reportId)
	if err != nil {
		logger.Logger.Errorf("测试计划中更新报告id失败! planId=%s,reportId=%s", plan.Id, reportId)
		return "", err
	}
	caseList := make([]models.UiCase, 0)
	err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.UiCase{}).In("id", cids), &caseList)
	if err != nil {
		return "", err
	}
	caseMap := make(map[string]models.UiCase)
	for _, c := range caseList {
		caseMap[c.Id] = c
	}
	for _, caseId := range cids {
		if steps, ok := caseStepsMap[caseId]; !ok || len(steps) == 0 {
			continue
		}
		agentCase, err := toAgentStep(ctx, caseMap[caseId], caseStepsMap[caseId])
		if err != nil {
			return "", err
		}
		if agentCase == nil {
			continue
		}
		if *plan.EnableConfig {
			agentCase.Config = cases.Config{
				BrowserType:       plan.BrowserConfig.Browser,
				IgnoreHttpsErrors: *plan.BrowserConfig.IgnoreHttpsCertificateError,
			}
			if plan.BrowserConfig.Window == constants.WindowMobile {
				agentCase.Config.IsMoblie = true
				agentCase.Config.Viewport = &struct {
					Width  int "json:\"width\""
					Height int "json:\"height\""
				}{
					Width:  720,
					Height: 1080,
				}
			}
			agentCase.DefaultStepConfig = plan.StepConfig
		}
		msgByte, err := json.Marshal([]*cases.Case{agentCase})
		if err != nil {
			return "", err
		}
		msg := payload.UiExecuteTaskPayload{
			Callback:        configs.Config.Modules.Venus.StepReportCallback,
			ReportId:        reportId,
			Input:           string(msgByte),
			FileDownloadUrl: configs.Config.Modules.Venus.FileDownloadUrl,
			VariableUrl:     configs.Config.Modules.Venus.VariableUrl,
			Env:             plan.Env,
		}
		pubsub.SendToRedisQueue(msg, pubsub.WebQueue)
	}
	return reportId, nil
}

func (PlanService) OpenExecute(ctx *commoncontext.MantisContext, appId, planId, env string, webhooks []string) (string, error) {
	// 查询plan
	var plan models.UiPlan
	if planId == "" {
		plans := make([]models.UiPlan, 0)
		err := gormx.SelectByParamBuilder(ctx,
			gormx.NewParamBuilder().Model(&models.UiPlan{}).Eq("app_id", appId).Eq("env", env).Eq("is_deleted", false),
			&plans,
		)
		if err != nil {
			return "", err
		}
		if len(plans) == 0 {
			return "", errors.New("当前应用和环境下不存在测试计划")
		}
		plan = plans[0]
	} else {
		plan.Id = planId
		plan.IsDeleted = false
		err := gormx.SelectOneByCondition(ctx, &plan)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return "", fmt.Errorf("plan not found")
			}
			return "", err
		}
	}
	return Execute(ctx, plan, types.ThirdParty, webhooks)
}

func (PlanService) Search(ctx *commoncontext.MantisContext, ids []string) ([]models.UiPlan, error) {
	res := make([]models.UiPlan, 0)
	if len(ids) == 0 {
		return res, nil
	}
	selectSql := "select * from ui_plan where id in ? and is_deleted='false'"
	err := gormx.Raw(ctx, selectSql, &res, ids)
	return res, err
}

func Execute(ctx *commoncontext.MantisContext, plan models.UiPlan, execType types.ExecuteType, webhooks []string) (string, error) {
	if len(plan.CaseIds) == 0 {
		return "", errors.New("执行前请先选择用例")
	}
	cids, err := filterCase(ctx, plan.CaseIds)
	if err != nil {
		return "", err
	}
	if len(cids) == 0 {
		return "", errors.New("计划下无可执行的用例")
	}
	reportId, caseStepsMap, err := reportService.InitReport(ctx, cids, plan.Name, plan.Id, plan.SpaceId, plan.Env, execType, webhooks, *plan.EnableConfig, plan.BrowserConfig, plan.StepConfig)
	if err != nil {
		logger.Logger.Errorf("测试计划中调用报告初始化失败! planId=%s", plan.Id)
		return "", err
	}
	err = updateLastReportInfo(ctx, plan.Id, reportId)
	if err != nil {
		logger.Logger.Errorf("测试计划中更新报告id失败! planId=%s,reportId=%s", plan.Id, reportId)
		return "", err
	}
	caseList := make([]models.UiCase, 0)
	err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.UiCase{}).In("id", cids), &caseList)
	if err != nil {
		return "", err
	}
	caseMap := make(map[string]models.UiCase)
	for _, c := range caseList {
		caseMap[c.Id] = c
	}
	for _, caseId := range cids {
		if steps, ok := caseStepsMap[caseId]; !ok || len(steps) == 0 {
			continue
		}
		agentCase, err := toAgentStep(ctx, caseMap[caseId], caseStepsMap[caseId])
		if err != nil {
			return "", err
		}
		if agentCase == nil {
			continue
		}
		if *plan.EnableConfig {
			agentCase.Config = cases.Config{
				BrowserType:       plan.BrowserConfig.Browser,
				IgnoreHttpsErrors: *plan.BrowserConfig.IgnoreHttpsCertificateError,
			}
			if plan.BrowserConfig.Window == constants.WindowMobile {
				agentCase.Config.IsMoblie = true
				agentCase.Config.Viewport = &struct {
					Width  int "json:\"width\""
					Height int "json:\"height\""
				}{
					Width:  720,
					Height: 1080,
				}
			}
			agentCase.DefaultStepConfig = plan.StepConfig
		}
		msgByte, err := json.Marshal([]*cases.Case{agentCase})
		if err != nil {
			return "", err
		}
		msg := payload.UiExecuteTaskPayload{
			Callback:        configs.Config.Modules.Venus.StepReportCallback,
			ReportId:        reportId,
			Input:           string(msgByte),
			FileDownloadUrl: configs.Config.Modules.Venus.FileDownloadUrl,
			VariableUrl:     configs.Config.Modules.Venus.VariableUrl,
			Env:             plan.Env,
		}
		pubsub.SendToRedisQueue(msg, pubsub.PipelineQueue)
	}
	return fmt.Sprintf("%s/project/%s/ui-test/history-report/detail/%s", configs.Config.Domain.Cube, plan.SpaceId, reportId), nil
}

// 过滤用例,获取可执行的用例
func filterCase(ctx *commoncontext.MantisContext, caseIds []string) ([]string, error) {
	selectSql := "select id from ui_case where id in ? and is_deleted='false' and is_dir='false'"
	res := make([]models.UiCase, 0)
	err := gormx.Raw(ctx, selectSql, &res, caseIds)
	if err != nil {
		return nil, err
	}
	cids := make([]string, 0)
	for _, c := range res {
		cids = append(cids, c.Id)
	}
	return cids, nil
}

// 更新测试计划中报告id
func updateLastReportInfo(ctx *commoncontext.MantisContext, planId, reportId string) error {
	_, err := gormx.Exec(ctx, "update ui_plan set last_exec_info=? where id=?", reportId, planId)
	return err
}
