package service

import (
	"bytes"
	"encoding/base64"
	"errors"
	"fmt"
	"math"
	"mime/multipart"
	"net/http"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/openapi"
	scene "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/result"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	commonmodels "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/types"
)

type ReportService struct{}

var noticeService NoticeService

// InitReport 初始化报告
// 返回 reportId, caseId和step树的map, 错误
func (s ReportService) InitReport(ctx *commoncontext.MantisContext, caseIdList []string, reportName, relationId,
	spaceId, env string, execType types.ExecuteType, webHooks []string, enableConfig bool, browserConfig *models.BrowserConfigInfo, stepConfig *scene.StepConfig,
) (string, map[string][]models.UiCaseStep, error) {
	if len(caseIdList) == 0 {
		return "", nil, errors.New("caseIdList is empty")
	}
	start := time.Now().UnixMilli()
	report := models.UiReport{
		Name:          reportName,
		RelationId:    relationId,
		SpaceId:       spaceId,
		ExecType:      string(execType),
		Executor:      ctx.User.AdAccount,
		ExecStartTime: start,
		ExecEndTime:   start,
		Status:        constants.ReportStatusRunning,
		Env:           env,
		Webhooks:      webHooks,
		Addons2: commonmodels.Addons2{
			Creator:  ctx.User.AdAccount,
			Modifier: ctx.User.AdAccount,
		},
		CaseTotalCount: len(caseIdList),
		EnableConfig:   &enableConfig,
		BrowserConfig:  browserConfig,
		StepConfig:     stepConfig,
	}
	_, err := gormx.InsertOne(ctx, &report)
	if err != nil {
		logger.Logger.Errorf("报告用例初始化失败,%v \n", err)
		return "", nil, err
	}
	logger.Logger.Infof("start 初始化报告,report id =%s", report.Id)
	// 用例下所有的步骤
	caseStepsMap := make(map[string][]models.UiCaseStep)
	type caseSteps struct {
		CaseId string
		Steps  []models.UiCaseStep
	}
	stepsChan := make(chan *caseSteps, len(caseIdList)+1)
	// 步骤id和步骤报告id的映射
	tasks := make([]func() error, len(caseIdList))
	for i, cid := range caseIdList {
		tasks[i] = func() error {
			steps, err := s.initReportOfOneCase(ctx, cid, report)
			if err != nil {
				return err
			}
			if len(steps) != 0 {
				stepsChan <- &caseSteps{
					CaseId: cid,
					Steps:  steps,
				}
			}
			return nil
		}
	}
	err = goroutine.RunTasksX(tasks)
	if err != nil {
		return "", nil, err
	}
	stepsChan <- nil
	for {
		cs := <-stepsChan
		if cs == nil {
			break
		}
		caseStepsMap[cs.CaseId] = cs.Steps
	}
	allCaseNoSteps := true
	for _, cid := range caseIdList {
		if steps, ok := caseStepsMap[cid]; !ok || len(steps) == 0 {
			// 用例不存在或者用例没有步骤
			gormx.UpdateBatchByParamBuilderAndMap(ctx, gormx.NewParamBuilder().Model(&models.UiReportCase{}).Eq("report_id", report.Id).Eq("case_id", cid), map[string]any{
				"status": constants.ReportStatusSuccess,
			})
		} else {
			allCaseNoSteps = false
		}
	}
	if allCaseNoSteps {
		gormx.UpdateBatchByParamBuilderAndMap(ctx, gormx.NewParamBuilder().Model(&models.UiReport{}).Eq("id", report.Id), map[string]any{
			"status":          constants.ReportStatusSuccess,
			"case_pass_count": len(caseIdList),
			"pass_rate":       "1.0000",
		})
		noticeService.PlanNotice(report.Id)
	}
	logger.Logger.Info("end 初始化报告,report id =", report.Id)
	return report.Id, caseStepsMap, nil
}

func (s ReportService) initReportOfOneCase(ctx *commoncontext.MantisContext, cid string, report models.UiReport) ([]models.UiCaseStep, error) {
	caseRun, err := caseDao.FindCaseById(ctx, cid)
	if err != nil {
		return nil, err
	}
	steps, err := caseStepDao.BuildStepTreeByCaseID(ctx, cid)
	if err != nil {
		return nil, err
	}

	// 先插入一个虚拟根节点
	rootStepDetail := &models.UiReportStepDetail{
		ReportId:     report.Id,
		ReportCaseId: cid,
		Status:       constants.ReportStatusRunning, // 全部置为运行中
		CaseId:       cid,
		StepId:       "",
		Name:         "根节点",
		ParentId:     "",
		IsDir:        true,
	}
	_, err = gormx.InsertOne(ctx, &rootStepDetail)
	if err != nil {
		return nil, err
	}
	stepReportIdMap := map[string]string{"": rootStepDetail.Id}
	if len(steps) != 0 {
		err = s.insertReportStepTree(ctx, report.Id, cid, steps, stepReportIdMap)
		if err != nil {
			return nil, err
		}
	}
	// 初始化caseReport
	count, err := reportStepDetailDao.CountByReportIdAndCaseId(ctx, report.Id, cid)
	if err != nil {
		return nil, err
	}
	caseReport := models.UiReportCase{
		ReportId:        report.Id,
		CaseId:          cid,
		Name:            caseRun.Name,
		Executor:        ctx.User.AdAccount,
		StepTotalCount:  count,
		StepUnexecCount: count,
		ExecStartTime:   time.Now().UnixMilli(),
		Status:          constants.ReportStatusRunning,
		IsComponent:     *caseRun.IsComponent,
	}
	_, err = gormx.InsertOne(ctx, &caseReport)
	if err != nil {
		logger.Logger.Errorf("报告用例初始化失败,report id =%s,%v \n", report.Id, err)
		return nil, err
	}
	logger.Logger.Infof("报告用例初始化完成,report id =%s,case id=%s", report.Id, cid)
	return steps, nil
}

// insertStepTree 分层插入步骤树
func (s ReportService) insertReportStepTree(ctx *commoncontext.MantisContext, reportId, caseId string, steps []models.UiCaseStep, stepReportIdMap map[string]string) error {
	// 插入当前层步骤
	stepReportList := make([]*models.UiReportStepDetail, 0)
	for i, step := range steps {
		if step.Disable == constants.StepDisableTrue {
			continue
		}
		reportStepDetail := &models.UiReportStepDetail{
			ReportId:        reportId,
			ReportCaseId:    caseId,
			Status:          constants.ReportStatusRunning, // 全部置为运行中
			CaseId:          step.CaseId,
			StepId:          step.DistinctId,
			Name:            step.Name,
			ParentId:        stepReportIdMap[step.ParentId],
			SettingEnable:   step.SettingEnable,
			SiblingOrder:    step.SiblingOrder,
			IsDir:           step.IsDir,
			SettingsContent: step.SettingsContent,
			Action:          step.Action,
			ActionType:      step.ActionType,
			IsCount:         !step.IsDir, // 默认节点都是统计的,文件类型不统计
			ElementIds:      step.ElementIds,
		}
		if reportStepDetail.ParentId == "" {
			reportStepDetail.ParentId = stepReportIdMap[""]
		}
		reportStepDetail.SetNewId()
		stepReportList = append(stepReportList, reportStepDetail)
		stepReportIdMap[step.Id] = reportStepDetail.Id
		// 置换step树的id以便传给执行机和回调
		steps[i].ReportStepId = reportStepDetail.Id
	}
	if len(stepReportList) != 0 {
		_, err := gormx.InsertBatch(ctx, &stepReportList)
		if err != nil {
			return fmt.Errorf("failed to batch insert report steps: %w", err)
		}
		// 递归插入子步骤
		for _, step := range steps {
			if step.Disable == constants.StepDisableTrue {
				continue
			}
			if len(step.Children) > 0 {
				err = s.insertReportStepTree(ctx, reportId, caseId, step.Children, stepReportIdMap)
				if err != nil {
					return err
				}
			}
		}
	}
	return nil
}

// DealReportRes 处理执行机回调的步骤结果
// 报告处理逻辑
// 1. actionType 是 if 或者 for ,不计入步骤总数 count_node=false
// 2. if 或者 for 状态为 false , 认定为异常或错误, 所有子节点都修改为 count_node=false (不统计)
// 3. 每一步都要刷新case表中的统计数据
// notice: 此处返回的id是经过组合的step id，不可以当作ui_case_step表的id进行处理
func (s ReportService) DealReportRes(ctx *commoncontext.MantisContext, req *result.StepResult) {
	logger.Logger.Infof("ui执行机回调信息,ReportId=%s,Res=%v,id=%s,report_step_id=%scaseId=%s,success=%v,error=%s,duration=%d,type=%s",
		req.ReportId, req.Res, req.Id, req.StepReportId, req.CaseId, req.Success, req.Error, req.ConsumeMilli, req.Type)
	stepReport := models.UiReportStepDetail{}
	stepReport.Id = req.StepReportId
	stepReport.CaseId = req.CaseId
	stepReport.ReportId = req.ReportId
	err := gormx.SelectOneByCondition(ctx, &stepReport)
	if err != nil {
		logger.Logger.Errorf("查询report_step_detail失败")
	}
	stepReport.Status = constants.ReportStatusFail
	if req.Success {
		stepReport.Status = constants.ReportStatusSuccess
	}
	if stepReport.ReportMessage == nil {
		stepReport.ReportMessage = make([]models.ReportRes, 0)
	}
	url, err := ImageUpload(req.Screenshot)
	if err != nil {
		logger.Logger.Errorf("处理截图失败,err=%s", err.Error())
	}
	stepReport.ReportMessage = append(stepReport.ReportMessage, models.ReportRes{
		Id:         req.Id,
		Success:    req.Success,
		Res:        req.Res,
		Error:      req.Error,
		Screenshot: url,
		Duration:   req.ConsumeMilli,
	})
	_, err = gormx.UpdateOneByCondition(ctx, &stepReport)
	if err != nil {
		logger.Logger.Errorf("更新步骤报告失败,%v \n", err)
	}
	// 如果步骤类型是if或者for,并且是失败，则子节点全部不统计
	if (stepReport.ActionType == constants.ActionAdvancedIfOperate || stepReport.ActionType == constants.ActionAdvancedLoopOperate) && !req.Success {
		// 递归更新所有的子节点
		updateSql := `
			WITH RECURSIVE child_nodes AS (
				SELECT id
				FROM ui_report_step_detail
				WHERE parent_id = ?   -- 获取直接子节点
				UNION ALL
				SELECT c.id
				FROM ui_report_step_detail c
				INNER JOIN child_nodes cn ON c.parent_id = cn.id  -- 递归获取所有后代
			)
			UPDATE ui_report_step_detail
			SET is_count = false
			WHERE id IN (SELECT id FROM child_nodes);
			`
		_, err := gormx.Exec(ctx, updateSql, stepReport.Id)
		if err != nil {
			logger.Logger.Errorf("递归更新子节点失败!,%v \n", err)
			return
		}
	}
	// 更新case信息
	err = updateReportCase(ctx, req.ReportId, stepReport.CaseId, false, false)
	if err != nil {
		return
	}
}

// DealCaseFinish 处理用例执行完成
// 1 统计步骤信息
// 2 更新报告信息
func (s ReportService) DealCaseFinish(ctx *commoncontext.MantisContext, reportId, caseId string, caseSuccess bool) {
	logger.Logger.Infof("ui执行机回调信息,case run finished,reportId=%+v", reportId)
	err := updateReportCase(ctx, reportId, caseId, true, caseSuccess)
	if err != nil {
		return
	}
	var caseCount dto.ResCountDto
	countCaseSql := `select count(*) as "all", 
       count(case when status='pass' then 'pass' end ) as "pass", 
       count(case when status='fail' then 'fail' end ) as "fail", 
       count(case when status='running' then 'running' end ) as "running"
       from ui_report_case_detail where report_id=?`
	err = gormx.Raw(ctx, countCaseSql, &caseCount, reportId)
	if err != nil {
		return
	}
	if caseCount.Running < 1 {
		logger.Logger.Infof("报告下所有的用例已经执行完毕!更新报告状态")
		// 更新报告状态
		reportStatus := constants.ReportStatusSuccess
		if caseCount.Fail > 0 {
			reportStatus = constants.ReportStatusFail
		}
		// 计算通过率
		passRate := "0"
		if caseCount.All != 0 {
			res := float64(caseCount.Pass) / float64(caseCount.All)
			rounded := math.Round(res*10000) / 10000
			passRate = fmt.Sprintf("%.4f", rounded)
		}
		updateReportSql := `update ui_report set status=?, case_fail_count=?, case_pass_count=?, pass_rate=?
                 ,exec_end_time=?,duration=((EXTRACT(EPOCH FROM clock_timestamp()) * 1000)::BIGINT-exec_start_time) 
                 where id=?`
		_, err = gormx.Exec(ctx, updateReportSql, reportStatus,
			caseCount.Fail, caseCount.Pass, passRate,
			time.Now().UnixMilli(), reportId)
		if err != nil {
			logger.Logger.Errorf("更新报告状态失败,%v \n", err)
			return
		}
		// 结果通知
		noticeService.PlanNotice(reportId)
	} else {
		logger.Logger.Infof("报告下的用例还存在运行中的状态!更新报告统计信息...")
		updateReportSql := `update ui_report set case_fail_count=?,case_pass_count=?
                 ,exec_end_time=?
                 where id=?`
		_, err = gormx.Exec(ctx, updateReportSql, caseCount.Fail, caseCount.Pass,
			time.Now().UnixMilli(), reportId)
		if err != nil {
			logger.Logger.Errorf("更新报告状态失败,%v \n", err)
			return
		}
	}
}

func (s ReportService) StopExecute(ctx *commoncontext.MantisContext, reportId string) error {
	// 判断是否是云端执行，如果是，则停止pod
	report := models.UiReport{}
	report.Id = reportId
	report.IsDeleted = false
	err := gormx.SelectOneByCondition(ctx, &report)
	if err != nil {
		return err
	}
	shutDownK8s := func(report models.UiReport) error {
		// shut down k8s
		if len(report.K8sTaskIds) != 0 {
			provider, _ := driver.NewDriverProvider()
			for _, k8sTaskId := range report.K8sTaskIds {
				err := provider.Cancel(ctx, k8sTaskId)
				if err != nil {
					return err
				}
			}
		}
		return nil
	}
	switch report.ExecType {
	case string(types.Debug):
		s.DealCaseFinish(ctx, reportId, report.RelationId, false)
	case string(types.Cloud):
		s.DealCaseFinish(ctx, reportId, report.RelationId, false)
		goroutine.RunWithoutLimit(func() {
			if err := shutDownK8s(report); err != nil {
				logger.Logger.Errorf("error end k8s task, taskId: %v, err: %s", report.K8sTaskIds, err.Error())
			}
		})
	case string(types.Manual), string(types.Cron), string(types.ThirdParty):
		// 更新ui_report_case_detail表
		reportCaseSql := `UPDATE ui_report_case_detail
			SET
			status = 'fail',
			exec_end_time = CASE
				WHEN exec_start_time != 0 THEN EXTRACT(EPOCH FROM now())
				ELSE exec_end_time
			END,
			duration = CASE
				WHEN exec_start_time != 0 THEN EXTRACT(EPOCH FROM now()) - exec_start_time
				ELSE duration
			END,
			updated = EXTRACT(EPOCH FROM now())
			WHERE report_id = ? and status = 'running';
		`
		if _, err := gormx.Exec(ctx, reportCaseSql, reportId); err != nil {
			return err
		}
		// 更新 ui_report_step_detail表
		reportStepSql := `update ui_report_step_detail set status = ? where report_id = ? and status = 'running'`
		if _, err := gormx.Exec(ctx, reportStepSql, constants.ReportStatusUnExec, reportId); err != nil {
			return err
		}
		// 更新report
		var caseCount dto.ResCountDto
		countCaseSql := `select count(*) as "all", 
			count(case when status='pass' then 'pass' end ) as "pass", 
			count(case when status='fail' then 'fail' end ) as "fail", 
			count(case when status='running' then 'running' end ) as "running"
			from ui_report_case_detail where report_id=?`
		err = gormx.Raw(ctx, countCaseSql, &caseCount, reportId)
		if err != nil {
			return err
		}
		reportStatus := constants.ReportStatusSuccess
		if caseCount.Fail > 0 {
			reportStatus = constants.ReportStatusFail
		}
		// 计算通过率
		passRate := "0"
		if caseCount.All != 0 {
			res := float64(caseCount.Pass) / float64(caseCount.All)
			rounded := math.Round(res*10000) / 10000
			passRate = fmt.Sprintf("%.4f", rounded)
		}
		updateReportSql := `update ui_report set status=? ,case_fail_count=?,case_pass_count=?,pass_rate=?
                 ,exec_end_time=?,duration=((EXTRACT(EPOCH FROM clock_timestamp()) * 1000)::BIGINT-exec_start_time) 
                 where id=?`
		_, err = gormx.Exec(ctx, updateReportSql, reportStatus, caseCount.Fail, caseCount.Pass,
			passRate, time.Now().UnixMilli(), reportId)
		if err != nil {
			return err
		}
		// 关闭pod
		goroutine.RunWithoutLimit(func() {
			if err := shutDownK8s(report); err != nil {
				logger.Logger.Errorf("error end k8s task, taskId: %v, err: %s", report.K8sTaskIds, err.Error())
			}
		})
		noticeService.PlanNotice(reportId)
	}
	return nil
}

func updateReportCase(ctx *commoncontext.MantisContext, reportId, caseId string, finish, caseSuccess bool) error {
	var stepCount dto.ResCountDto
	countSql := `select 
    	count(*) as "all",
    	count(case when status='pass' then 'pass' end ) as "pass", 
    	count(case when status='fail' then 'fail' end ) as "fail",
    	count(case when status='running' then 'running' end ) as "running"
    	from ui_report_step_detail where is_count='true' and report_id=? and case_id=?
	`
	err := gormx.Raw(ctx, countSql, &stepCount, reportId, caseId)
	if err != nil {
		logger.Logger.Errorf("统计步骤信息失败,%v \n", err)
		return err
	}
	params := []any{
		stepCount.All,
		stepCount.Fail,
		stepCount.All - stepCount.Fail - stepCount.Pass,
		stepCount.Pass,
		reportId,
		caseId,
	}
	// 修改用例报告信息
	updateSql := `update ui_report_case_detail 
		set  step_total_count=?,step_fail_count=?,step_unexec_count=?,step_pass_count=?
		where  report_id=? and case_id=?`
	if finish {
		caseStatus := constants.ReportStatusFail
		if caseSuccess {
			caseStatus = constants.ReportStatusSuccess
		}
		updateSql = `update ui_report_case_detail 
		set status=?, step_total_count=?,step_fail_count=?,step_unexec_count=?,step_pass_count=?,
		exec_end_time=?,duration=((EXTRACT(EPOCH FROM clock_timestamp()) * 1000)::BIGINT-exec_start_time) 
		where  report_id=? and case_id=?`
		params = []any{
			caseStatus,
			stepCount.All,
			stepCount.Fail,
			stepCount.All - stepCount.Fail - stepCount.Pass,
			stepCount.Pass,
			time.Now().UnixMilli(),
			reportId,
			caseId,
		}
		// 更新 ui_report_step_detail的状态
		stepSql := `update ui_report_step_detail set status=? where case_id=? and report_id=? and status='running'`
		_, err := gormx.Exec(ctx, stepSql, constants.ReportStatusUnExec, caseId, reportId)
		if err != nil {
			return err
		}
	}
	_, err = gormx.Exec(ctx, updateSql, params...)
	if err != nil {
		logger.Logger.Errorf("更新用例报告信息失败,%v \n", err)
		return err
	}
	return nil
}

// ImageUpload 将图片上穿到 oss
func ImageUpload(baseImage string) (string, error) {
	if baseImage == "" {
		logger.Logger.Infof("baseImage is empty")
		return "", nil
	}
	fileHeader, err := Base64ToFileHeader(baseImage, "image.png", "file")
	if err != nil {
		return "", err
	}
	// 调用 openapi.UploadFile 上传文件
	url, err := openapi.UploadFile(fileHeader, "ui_screenshot")
	if err != nil {
		return "", fmt.Errorf("failed to upload file: %v", err)
	}
	url, _ = strings.CutPrefix(url, configs.Config.Domain.Cube)
	return url, nil
}

func Base64ToFileHeader(base64Data, filename, fieldName string) (*multipart.FileHeader, error) {
	// 分离数据部分
	var dataPart string
	if idx := strings.Index(base64Data, ","); idx != -1 {
		dataPart = base64Data[idx+1:]
	} else {
		dataPart = base64Data
	}
	// Base64解码
	decoded, err := base64.StdEncoding.DecodeString(dataPart)
	if err != nil {
		return nil, fmt.Errorf("base64解码失败: %w", err)
	}
	// 创建内存中的multipart请求体
	body := new(bytes.Buffer)
	writer := multipart.NewWriter(body)
	// 创建文件表单字段
	partWriter, err := writer.CreateFormFile(fieldName, filename)
	if err != nil {
		return nil, fmt.Errorf("创建表单字段失败: %w", err)
	}
	// 写入文件内容
	if _, err = partWriter.Write(decoded); err != nil {
		return nil, fmt.Errorf("写入文件内容失败: %w", err)
	}
	writer.Close() // 必须关闭以结束multipart结构
	// 构造虚拟HTTP请求
	req, _ := http.NewRequest("POST", "", body)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 解析multipart表单
	if err := req.ParseMultipartForm(32 << 20); err != nil { // 32MB内存缓冲
		return nil, fmt.Errorf("解析multipart失败: %w", err)
	}
	// 获取文件头信息
	files := req.MultipartForm.File[fieldName]
	if len(files) == 0 {
		return nil, fmt.Errorf("未找到对应文件字段")
	}
	return files[0], nil
}
