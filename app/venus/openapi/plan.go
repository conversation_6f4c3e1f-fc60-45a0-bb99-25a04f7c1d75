package openapi

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/service"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
)

var planService service.PlanService

type UiPLanOpenapi struct{}

// ExecUiPlan 执行UI测试计划
// 如果 planId 为 ""，则 appId 和 env 不能为空
func (s UiPLanOpenapi) ExecUiPlan(ctx *commoncontext.MantisContext, appId, planId, env string, webhooks []string) (string, error) {
	reportUrl, err := planService.OpenExecute(ctx, appId, planId, env, webhooks)
	return reportUrl, err
}

// PlanList 根据 ids 查询计划列表
func (s UiPLanOpenapi) PlanList(ctx *commoncontext.MantisContext, ids []string) ([]models.UiPlan, error) {
	plans, err := planService.Search(ctx, ids)
	if err != nil {
		return nil, err
	}
	return plans, nil
}
