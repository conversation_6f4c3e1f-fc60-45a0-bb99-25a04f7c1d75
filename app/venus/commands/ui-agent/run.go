package uiagent

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"

	cases "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/action"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/result"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/variable"
)

// 全局默认步骤配置
var defaultStepConfig *cases.StepConfig

// Run 执行UI测试用例
func Run(input string, callBack string, reportId string, fileDownloadUrl string, variableurl string, env string) error {
	// 验证参数并初始化
	if callBack == "" || fileDownloadUrl == "" {
		return errors.New("callback URL and file download URL are required")
	}

	configs.CallBackUrl = callBack
	configs.ReportId = reportId
	configs.FileDownloadUrl = fileDownloadUrl

	if err := variable.Init(variableurl, env); err != nil {
		return err
	}

	// 解析用例并执行
	var datas []map[string]any
	if err := json.Unmarshal([]byte(input), &datas); err != nil {
		return err
	}

	var lastErr error
	for _, data := range datas {
		c, err := convertJSONToCase(data)
		if err != nil {
			return err
		}

		cases.PanicFlag = false
		func() {
			defer func() {
				if err := recover(); err != nil {
					lastErr = fmt.Errorf("%v", err)
				}
				// 回调
				stepRes := result.StepResult{
					Type:     "case",
					CaseId:   c.Id,
					ReportId: reportId,
					Success:  (lastErr == nil && !cases.PanicFlag),
				}

				if jsonData, err := json.Marshal(&stepRes); err == nil {
					log.Printf("回调case, case id: %s", c.Id)
					if req, err := http.NewRequest("POST", configs.CallBackUrl, bytes.NewBuffer(jsonData)); err == nil {
						_, _ = (&http.Client{}).Do(req)
					}
				}
			}()
			lastErr = c.ExecuteWithPW(nil, nil, nil)
		}()
	}

	return nil
}

// convertJSONToCase 将JSON数据转换为Case对象
func convertJSONToCase(data map[string]any) (cases.Case, error) {
	var caseData cases.Case

	// 解析ID和配置
	id, ok := data["id"].(string)
	if !ok {
		return caseData, errors.New("case id is not a string")
	}
	caseData.Id = id

	// 解析配置和默认步骤配置
	if err := unmarshalMapField(data, "config", &caseData.Config); err != nil {
		return caseData, err
	}

	// 处理默认步骤配置
	if configData, ok := data["defaultStepConfig"].(map[string]any); ok && configData != nil {
		var stepConfig cases.StepConfig
		if err := unmarshalJSON(configData, &stepConfig); err != nil {
			return caseData, err
		}
		caseData.DefaultStepConfig = &stepConfig
		defaultStepConfig = &stepConfig
	} else {
		caseData.DefaultStepConfig = defaultStepConfig
	}

	// 解析步骤
	stepsData, ok := data["steps"].([]any)
	if !ok {
		return caseData, errors.New("steps is not a slice")
	}

	steps := make([]*cases.Step, len(stepsData))
	for i, stepData := range stepsData {
		stepMap, ok := stepData.(map[string]any)
		if !ok {
			return caseData, errors.New("step is not a map")
		}

		// 获取基本属性
		step := &cases.Step{}
		if step.Id, ok = stepMap["id"].(string); !ok {
			return caseData, errors.New("step id is not a string")
		}
		if step.StepReportId, ok = stepMap["stepReportId"].(string); !ok {
			return caseData, errors.New("stepReportId is not a string")
		}
		if step.ActionType, ok = stepMap["actionType"].(string); !ok {
			return caseData, errors.New("actionType is not a string")
		}

		// 解析动作
		actionData, ok := stepMap["action"].(map[string]interface{})
		if !ok {
			return caseData, errors.New("action is not a map")
		}

		// 创建动作对象
		action, err := convertJSONToAction(step.ActionType, actionData)
		if err != nil {
			return caseData, err
		}
		step.Action = action

		// 解析步骤配置
		if configData, ok := stepMap["config"]; ok && configData != nil {
			var config cases.StepConfig
			if err := unmarshalJSON(configData, &config); err != nil {
				return caseData, err
			}
			step.Config = &config
		}

		steps[i] = step
	}

	caseData.Steps = steps
	return caseData, nil
}

// convertJSONToAction 将JSON数据转换为Action对象
func convertJSONToAction(actionType string, actionData map[string]interface{}) (action.Action, error) {
	// 处理简单动作
	simpleActions := map[string]func() action.Action{
		"assert":        func() action.Action { return &action.Assert{} },
		"input":         func() action.Action { return &action.Input{} },
		"press":         func() action.Action { return &action.Press{} },
		"select":        func() action.Action { return &action.Select{} },
		"uploadFiles":   func() action.Action { return &action.UploadFiles{} },
		"mouseClick":    func() action.Action { return &action.MouseClick{} },
		"mouseScroll":   func() action.Action { return &action.MouseScroll{} },
		"mouseMove":     func() action.Action { return &action.MouseMove{} },
		"mouseDrag":     func() action.Action { return &action.MouseDrag{} },
		"mouseHold":     func() action.Action { return &action.MouseHold{} },
		"wait":          func() action.Action { return &action.Wait{} },
		"codeOperation": func() action.Action { return &action.CodeOperation{} },
		"dataWithdraw":  func() action.Action { return &action.DataWithdraw{} },
		"callApi":       func() action.Action { return &action.CallApi{} },
		"callDatabase":  func() action.Action { return &action.CallDatabase{} },
		"openPage":      func() action.Action { return &action.OpenPage{} },
		"closePage":     func() action.Action { return &action.ClosePage{} },
		"toggleWindow":  func() action.Action { return &action.ToggleWindow{} },
		"forward":       func() action.Action { return &action.Forward{} },
		"back":          func() action.Action { return &action.Back{} },
		"refresh":       func() action.Action { return &action.Refresh{} },
		"setWindowSize": func() action.Action { return &action.SetWindowSize{} },
	}

	if simpleAction, ok := simpleActions[actionType]; ok {
		act := simpleAction()
		if err := unmarshalJSON(actionData, act); err != nil {
			return nil, err
		}
		return act, nil
	}

	// 处理复合动作
	switch actionType {
	case "if", "while":
		// 解析条件和子用例
		var condition cases.Condition
		if err := unmarshalJSON(actionData["condition"], &condition); err != nil {
			return nil, err
		}

		childData, ok := actionData["child"].(map[string]any)
		if !ok {
			return nil, errors.New("child is not a map")
		}
		childCase, err := convertJSONToCase(childData)
		if err != nil {
			return nil, err
		}

		if actionType == "if" {
			return &cases.If{Condition: condition, Child: &childCase}, nil
		}
		return &cases.While{Condition: condition, Child: &childCase}, nil

	case "for":
		// 解析子用例
		childData, ok := actionData["child"].(map[string]any)
		if !ok {
			return nil, errors.New("child is not a map")
		}
		childCase, err := convertJSONToCase(childData)
		if err != nil {
			return nil, err
		}

		// 解析循环参数
		forAction := &cases.For{Child: &childCase}
		if tp, ok := actionData["type"].(string); ok {
			forAction.Type = tp
		}
		if times, ok := actionData["times"].(float64); ok {
			forAction.Times = int(times)
		}

		// 解析循环数据
		if dataList, ok := actionData["data"]; ok {
			if err := unmarshalJSON(dataList, &forAction.Data); err != nil {
				return nil, err
			}
		}

		return forAction, nil

	default:
		return nil, fmt.Errorf("unknown action type: %s", actionType)
	}
}

// 工具函数：将map字段解析为指定类型
func unmarshalMapField(data map[string]any, fieldName string, target interface{}) error {
	if fieldData, ok := data[fieldName].(map[string]any); ok {
		return unmarshalJSON(fieldData, target)
	}
	return fmt.Errorf("%s is not a map", fieldName)
}

// 工具函数：将任意数据结构转换为JSON然后解析为目标类型
func unmarshalJSON(data interface{}, target interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	return json.Unmarshal(jsonData, target)
}
