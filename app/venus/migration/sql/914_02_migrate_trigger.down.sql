-- Drop triggers
DROP TRIGGER IF EXISTS ui_element_set_sibling_order ON ui_element;

DROP TRIGGER IF EXISTS ui_element_after_delete_trigger ON ui_element;

DROP TRIGGER IF EXISTS ui_case_set_sibling_order ON ui_case;

DROP TRIGGER IF EXISTS ui_case_after_delete_trigger ON ui_case;

DROP TRIGGER IF EXISTS ui_case_step_set_sibling_order ON ui_case_step;

DROP TRIGGER IF EXISTS ui_case_step_after_delete_trigger ON ui_case_step;

-- Drop functions
DROP FUNCTION IF EXISTS recursion_delete_children ();

DROP FUNCTION IF EXISTS set_sibling_order ();

DROP FUNCTION IF EXISTS increment_descendants ();

DROP FUNCTION IF EXISTS decrement_descendants ();

DROP FUNCTION IF EXISTS recursion_delete_children_x ();