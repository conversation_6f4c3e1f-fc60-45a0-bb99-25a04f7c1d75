-- Create table ui_case
CREATE TABLE ui_case (
    id varchar(32) NOT NULL,
    creator varchar(128),
    modifier varchar(128),
    created bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    updated bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    is_deleted boolean DEFAULT false,
    name text,
    parent_id text,
    description text,
    browser_config jsonb,
    step_config jsonb,
    sibling_order bigint,
    is_dir boolean,
    is_root boolean NOT NULL DEFAULT false,
    last_report_id text,
    space_id text,
    recording boolean NOT NULL DEFAULT false,
    is_component boolean NOT NULL DEFAULT false,
    PRIMARY KEY (id)
);

-- Create table ui_case_step
CREATE TABLE ui_case_step (
    id varchar(32) NOT NULL,
    creator varchar(128),
    modifier varchar(128),
    created bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    updated bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    is_deleted boolean DEFAULT false,
    case_id text,
    name text,
    parent_id text,
    action_type text,
    "action" jsonb,
    settings_content jsonb,
    setting_enable boolean NOT NULL DEFAULT false,
    gen_source text,
    element_ids jsonb,
    disable text,
    sibling_order numeric,
    is_dir boolean,
    is_root boolean NOT NULL DEFAULT false,
    latest_report_step_id text,
    is_reference boolean NOT NULL DEFAULT false,
    reference_case_id text NOT NULL DEFAULT ''::text,
    PRIMARY KEY (id)
);

CREATE INDEX idx_case_id ON ui_case_step USING btree (case_id);

COMMENT ON COLUMN ui_case_step.case_id IS '用例id';

COMMENT ON COLUMN ui_case_step.name IS '步骤名称';

COMMENT ON COLUMN ui_case_step.parent_id IS '父节点id';

COMMENT ON COLUMN ui_case_step.action_type IS '操作类型';

COMMENT ON COLUMN ui_case_step."action" IS '操作内容';

COMMENT ON COLUMN ui_case_step.settings_content IS '设置信息';

COMMENT ON COLUMN ui_case_step.setting_enable IS '设置开关';

COMMENT ON COLUMN ui_case_step.gen_source IS '生成来源';

COMMENT ON COLUMN ui_case_step.element_ids IS '绑定的元素id';

COMMENT ON COLUMN ui_case_step.disable IS '禁用状态:1是0否';

COMMENT ON COLUMN ui_case_step.sibling_order IS '元素顺序';

COMMENT ON COLUMN ui_case_step.is_dir IS '是否是文件夹';

COMMENT ON COLUMN ui_case_step.latest_report_step_id IS '最近一次执行的报告id';

-- Create table ui_element
CREATE TABLE ui_element (
    id varchar(32) NOT NULL,
    creator varchar(128),
    modifier varchar(128),
    created bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    updated bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    is_deleted boolean DEFAULT false,
    name text,
    parent_id text,
    locator jsonb,
    sibling_order bigint,
    is_dir boolean,
    is_root boolean NOT NULL DEFAULT false,
    space_id text,
    PRIMARY KEY (id)
);

COMMENT ON COLUMN ui_element.name IS '元素名称';

COMMENT ON COLUMN ui_element.parent_id IS '父节点id';

COMMENT ON COLUMN ui_element.locator IS '定位元素str';

COMMENT ON COLUMN ui_element.sibling_order IS '元素顺序';

COMMENT ON COLUMN ui_element.is_dir IS '是否是文件夹';

COMMENT ON COLUMN ui_element.space_id IS '所属项目id';

-- Create table ui_plan
CREATE TABLE ui_plan (
    id varchar(32) NOT NULL,
    creator varchar(128),
    modifier varchar(128),
    created bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    updated bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    is_deleted boolean DEFAULT false,
    name text,
    description text,
    enable_timer boolean NOT NULL DEFAULT false,
    notice_rule text,
    notice_users jsonb,
    notice_type jsonb,
    env text,
    retry_times integer,
    case_ids jsonb,
    case_count bigint,
    space_id text,
    app_id text,
    timing_info jsonb,
    last_exec_info text,
    webhooks jsonb,
    enable_config boolean NOT NULL DEFAULT false,
    browser_config jsonb,
    step_config jsonb,
    company_id text,
    PRIMARY KEY (id)
);

-- Create table ui_report
CREATE TABLE ui_report (
    id varchar(32) NOT NULL,
    creator varchar(128),
    modifier varchar(128),
    created bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    updated bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    is_deleted boolean DEFAULT false,
    name text,
    relation_id text,
    exec_type text,
    env text,
    status text,
    msg text,
    case_total_count integer,
    case_pass_count integer,
    case_fail_count integer,
    pass_rate text,
    exec_start_time bigint,
    exec_end_time bigint,
    duration bigint,
    executor text,
    space_id text,
    k8s_task_ids jsonb,
    webhooks jsonb,
    webhook_results jsonb,
    config_open boolean,
    browser_config jsonb,
    step_config jsonb,
    PRIMARY KEY (id)
);

-- Create table ui_report_case_detail
CREATE TABLE ui_report_case_detail (
    id varchar(32) NOT NULL,
    creator varchar(128),
    modifier varchar(128),
    created bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    updated bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    is_deleted boolean DEFAULT false,
    status text,
    step_total_count integer,
    step_pass_count integer,
    step_fail_count integer,
    step_unexec_count integer,
    executor text,
    exec_start_time bigint,
    exec_end_time bigint,
    duration bigint,
    name text,
    report_id text,
    case_id text,
    is_component boolean DEFAULT false,
    PRIMARY KEY (id)
);

-- Create table ui_report_step_detail
CREATE TABLE ui_report_step_detail (
    id varchar(32) NOT NULL,
    creator varchar(128),
    modifier varchar(128),
    created bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    updated bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    is_deleted boolean DEFAULT false,
    report_id text,
    report_case_id text,
    status text,
    is_count boolean,
    report_message jsonb,
    case_id text,
    step_id text,
    name text,
    parent_id text,
    action_type text,
    "action" jsonb,
    settings_content jsonb,
    setting_enable boolean,
    gen_source text,
    element_ids jsonb,
    disable text,
    sibling_order bigint,
    is_dir boolean,
    PRIMARY KEY (id)
);

COMMENT ON COLUMN ui_report_step_detail.case_id IS '用例id';

COMMENT ON COLUMN ui_report_step_detail.name IS '步骤名称';

COMMENT ON COLUMN ui_report_step_detail.parent_id IS '父节点id';

COMMENT ON COLUMN ui_report_step_detail.action_type IS '操作类型';

COMMENT ON COLUMN ui_report_step_detail."action" IS '操作内容';

COMMENT ON COLUMN ui_report_step_detail.settings_content IS '设置信息';

COMMENT ON COLUMN ui_report_step_detail.setting_enable IS '设置开关';

COMMENT ON COLUMN ui_report_step_detail.gen_source IS '生成来源';

COMMENT ON COLUMN ui_report_step_detail.element_ids IS '绑定的元素id';

COMMENT ON COLUMN ui_report_step_detail.disable IS '禁用状态:1是0否';

COMMENT ON COLUMN ui_report_step_detail.sibling_order IS '元素顺序';

COMMENT ON COLUMN ui_report_step_detail.is_dir IS '是否是文件夹';