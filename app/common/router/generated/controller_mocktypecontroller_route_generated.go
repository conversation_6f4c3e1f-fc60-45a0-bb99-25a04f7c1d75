// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/log"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/models"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/terrors"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// POST Path("/common/api/v1/collections/mock_type/records") -> controller.MockTypeController.Create
func controllerMockTypeControllerCreateHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Create",
		Patten:            "/common/api/v1/collections/mock_type/records",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "MockTypeController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "COMMON 接口",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeMockTypeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := models.MockType{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_mockType, err1 := ctrl.Create(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call Create failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _mockType)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/common/api/v1/collections/mock_type/records/{id}") -> controller.MockTypeController.Get
func controllerMockTypeControllerGetHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Get",
		Patten:            "/common/api/v1/collections/mock_type/records/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "MockTypeController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "COMMON 接口",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeMockTypeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.MockTypeIdRequest{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_mockType, err1 := ctrl.Get(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call Get failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _mockType)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/common/api/v1/collections/mock_type/records/{id}") -> controller.MockTypeController.Update
func controllerMockTypeControllerUpdateHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Update",
		Patten:            "/common/api/v1/collections/mock_type/records/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "MockTypeController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "COMMON 接口",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeMockTypeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := models.MockType{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		idReq := controller.MockTypeIdRequest{}
		if i, ok := decode.Implements(&idReq); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &idReq); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(idReq); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_mockType, err1 := ctrl.Update(&req1, &idReq)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call Update failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _mockType)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/common/api/v1/collections/mock_type/records/{id}") -> controller.MockTypeController.Delete
func controllerMockTypeControllerDeleteHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Delete",
		Patten:            "/common/api/v1/collections/mock_type/records/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "MockTypeController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "COMMON 接口",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeMockTypeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.MockTypeIdRequest{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.Delete(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call Delete failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/common/api/v1/collections/mock_type/records") -> controller.MockTypeController.List
func controllerMockTypeControllerListHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "List",
		Patten:            "/common/api/v1/collections/mock_type/records",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "MockTypeController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "COMMON 接口",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeMockTypeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_paginationRecord, err1 := ctrl.List()
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call List failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _paginationRecord)
		}
	}
	return chain.ThenFunc(HandleFunc)
}
