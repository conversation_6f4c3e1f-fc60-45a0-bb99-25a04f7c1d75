// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/log"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/terrors"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// GET Path("/common/api/v1/biz/mock/list") -> controller.MockController.GetMockList
func controllerMockControllerGetMockListHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetMockList",
		Patten:            "/common/api/v1/biz/mock/list",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "MockController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "COMMON 接口",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeMockController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.MockListRequest{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_mockType, err1 := ctrl.GetMockList(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call GetMockList failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _mockType)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/common/api/v1/biz/mock/types") -> controller.MockController.GetMockTypes
func controllerMockControllerGetMockTypesHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetMockTypes",
		Patten:            "/common/api/v1/biz/mock/types",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "MockController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "COMMON 接口",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeMockController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_string, err1 := ctrl.GetMockTypes()
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call GetMockTypes failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _string)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/common/api/v1/biz/mock/getTypes") -> controller.MockController.GetMockParamDataTree
func controllerMockControllerGetMockParamDataTreeHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetMockParamDataTree",
		Patten:            "/common/api/v1/biz/mock/getTypes",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "MockController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "COMMON 接口",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeMockController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_mockTree, err1 := ctrl.GetMockParamDataTree()
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call GetMockParamDataTree failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _mockTree)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/common/api/v1/biz/mock/getMockPreview") -> controller.MockController.GetMockPreview
func controllerMockControllerGetMockPreviewHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetMockPreview",
		Patten:            "/common/api/v1/biz/mock/getMockPreview",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "MockController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "accessLog", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "COMMON 接口",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeMockController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.MockPreviewRequest{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_mockResponse := ctrl.GetMockPreview(req1)

			opt.Codec.Encode(rw, _mockResponse)
		}
	}
	return chain.ThenFunc(HandleFunc)
}
