package controller

import (
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/third_party/cmdb"
)

type EnvController struct {
	*controller.BaseController
}

type EnvSearch struct {
	Search string `schema:"search"`
}

var DefaultEnvController = EnvController{}

func (c *EnvController) List(req *EnvSearch) {
	envs, err := cmdb.Client.GetBaseEnvList(c.<PERSON>onte<PERSON>t, c.MantisContext.User.CompanyID)
	if err != nil {
		c.ResFail(err)
		return
	}
	res := make([]commondto.CodeEnumDTO, 0)
	for _, env := range envs {
		if strings.Contains(env.InstanceBizName, req.Search) {
			res = append(res, commondto.CodeEnumDTO{
				Label: env.InstanceBizName,
				Value: env.InstanceBizID,
			})
		}
	}
	c.ResSuccessResult(res)
}
