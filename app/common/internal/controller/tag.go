package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/remote"
)

type TagController struct {
	*controller.BaseController
}

var DefaultTagController TagController

func (c *TagController) TagList() ([]dto.CubeTagDTO, error) {
	return remote.CubeBaseRemoteApi{}.GetTagsByIds(c.<PERSON>ontext, "")
}
