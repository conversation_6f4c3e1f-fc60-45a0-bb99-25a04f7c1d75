package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/remote"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/cmdb"
	"github.com/duke-git/lancet/v2/netutil"
)

type UserSearchReq struct {
	Search string `schema:"search"`
}

type UserController struct {
	*controller.BaseController
}

var (
	DefaultUserController UserController
	userService           service.UserService
	cubeBaseRemoteApi     remote.CubeBaseRemoteApi
)

func (c *UserController) GetUserPower() {
	resource, err := userService.GetChildResource(c.GetUser(), c.Man<PERSON>onte<PERSON>)
	if err != nil {
		c.ResFail(err)
		return
	}
	c.ResSuccessResult(resource)
}

func (c *UserController) GetUserProjectPower() {
	resource, err := cubeBaseRemoteApi.GetProjectChileResource(c.MantisContext, c.MantisContext.Header.Get("spaceid"))
	if err != nil {
		c.ResFail(err)
	} else {
		c.ResSuccessResult(resource)
	}
}

func (c *UserController) GetApps() {
	var apps []cmdb.CmdbApp
	if c.Request.Header.Get(commonconstants.GitRepoUrlKey) != "" {
		apps = shipRemoteApi.GetAppInfosByHubUrl(c.MantisContext, c.Request.Header.Get(commonconstants.GitRepoUrlKey))
	} else {
		cmdbapps, err := cubeBaseRemoteApi.GetAppsByUser(c.MantisContext.User, "")
		if err != nil {
			c.ResFail(err)
			return
		}
		apps = cmdbapps
	}
	res := make([]map[string]any, 0)
	for _, app := range apps {
		res = append(res, map[string]any{
			"id":   app.AppId,
			"name": app.Name,
		})
	}
	c.ResSuccessResult(res)
}

func (c *UserController) Current() {
	c.ResSuccessResult(c.MantisContext.User)
}

func (c *UserController) ProjectList() {
	res, err := cubeBaseRemoteApi.GetProjectListByUser(c.MantisContext)
	if err != nil {
		c.ResFail(err)
		return
	}
	c.ResSuccessResult(res)
}

func (c *UserController) Logout() {
	httpClient := netutil.NewHttpClient()
	uri := "/api/logout"
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Auth.UaUrl + uri,
		Method:  "POST",
		Headers: c.Request.Header,
	}
	resp, err := httpClient.SendRequest(request)
	if err != nil {
		c.ResFail(err)
		return
	}
	var rs map[string]any
	err = httpClient.DecodeResponse(resp, &rs)
	if err != nil {
		c.ResFail(err)
		return
	}
	c.ResSuccessResult(rs)
}
