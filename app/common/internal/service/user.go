package service

import (
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/remote"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
)

type UserService struct{}

var cubeBaseRemoteApi remote.CubeBaseRemoteApi

func (UserService) GetChildResource(userId string, ctx *commoncontext.MantisContext) ([]dto.CubeResourceDTO, error) {
	resource := make([]dto.CubeResourceDTO, 0)
	companyId := utils.IDString(ctx.User.CompanyID)
	resourceMantis, err := cubeBaseRemoteApi.GetChildResource(userId, companyId, "lochness", "MANTIS")
	if err != nil {
		return resource, err
	}
	resource = append(resource, resourceMantis...)
	resourceCodeUp, err := cubeBaseRemoteApi.GetChildResource(userId, companyId, "lochness", "codeup")
	if err != nil {
		return resource, err
	}
	resource = append(resource, resourceCodeUp...)
	return resource, nil
}
