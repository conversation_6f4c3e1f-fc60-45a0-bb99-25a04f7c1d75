-- 清空 mock_type 表
DELETE FROM mock_type;
-- 插入所有函数

INSERT INTO
    mock_type (
        "id",
        "name",
        "type",
        "command",
        "description",
        "examples",
        "creator",
        "modifier",
        "is_deleted"
    )
VALUES (
        '0',
        '文件扩展名',
        '文件',
        '{{_FileExt}}',
        '生成文件扩展名',
        '[{"example": "{{_FileExt}}", "result": ["txt", "pdf", "jpg"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '1',
        'MIME类型',
        '文件',
        '{{_MimeType}}',
        '生成MIME类型',
        '[{"example": "{{_MimeType}}", "result": ["text/plain", "image/jpeg", "application/json"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '2',
        '产品名称',
        '产品',
        '{{_Product}}',
        '生成产品名称',
        '[{"example": "{{_Product}}", "result": ["Laptop", "Smartphone", "Tablet"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '3',
        '产品描述',
        '产品',
        '{{_ProductDescription}}',
        '生成产品描述',
        '[{"example": "{{_ProductDescription}}", "result": ["A high-end laptop", "A powerful smartphone", "A versatile tablet"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '4',
        '产品分类',
        '产品',
        '{{_ProductCategory}}',
        '生成产品分类',
        '[{"example": "{{_ProductCategory}}", "result": ["Electronics", "Clothing", "Home"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '5',
        '产品特征',
        '产品',
        '{{_ProductFeature}}',
        '生成产品特征',
        '[{"example": "{{_ProductFeature}}", "result": ["High performance", "Durable", "Ergonomic"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '6',
        '产品材质',
        '产品',
        '{{_ProductMaterial}}',
        '生成产品材质',
        '[{"example": "{{_ProductMaterial}}", "result": ["Plastic", "Metal", "Wood"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '7',
        '产品UPC',
        '产品',
        '{{_ProductUPC}}',
        '生成产品UPC',
        '[{"example": "{{_ProductUPC}}", "result": ["*********012"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '8',
        '产品尺寸',
        '产品',
        '{{_ProductDimension}}',
        '生成产品尺寸',
        '[{"example": "{{_ProductDimension}}", "result": ["10x10x10"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '9',
        '产品用例',
        '产品',
        '{{_ProductUseCase}}',
        '生成产品用例',
        '[{"example": "{{_ProductUseCase}}", "result": ["Business", "Personal", "Education"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '10',
        '产品收益',
        '产品',
        '{{_ProductBenefit}}',
        '生成产品收益',
        '[{"example": "{{_ProductBenefit}}", "result": ["100% satisfaction", "100% satisfaction", "100% satisfaction"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '11',
        '产品后缀',
        '产品',
        '{{_ProductSuffix}}',
        '生成产品后缀',
        '[{"example": "{{_ProductSuffix}}", "result": ["Pro", "Max", "Plus"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '12',
        '产品ISBN',
        '产品',
        '{{_ProductISBN}}',
        '生成产品ISBN',
        '[{"example": "{{_ProductISBN}}", "result": ["978-3-16-148410-0"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '13',
        '人名',
        '人',
        '{{_PersonName}}',
        '生成人名',
        '[{"example": "{{_PersonName}}", "result": ["John Doe", "Jane Smith", "Mike Johnson"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '14',
        '姓名前缀',
        '人',
        '{{_NamePrefix}}',
        '生成姓名前缀',
        '[{"example": "{{_NamePrefix}}", "result": ["Mr.", "Mrs.", "Dr."]}]',
        'admin',
        'admin',
        false
    ),
    (
        '15',
        '姓名后缀',
        '人',
        '{{_NameSuffix}}',
        '生成姓名后缀',
        '[{"example": "{{_NameSuffix}}", "result": ["Jr.", "Sr.", "III"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '16',
        '名字',
        '人',
        '{{_FirstName}}',
        '生成名字',
        '[{"example": "{{_FirstName}}", "result": ["John", "Jane", "Mike"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '17',
        '中间名',
        '人',
        '{{_MiddleName}}',
        '生成中间名',
        '[{"example": "{{_MiddleName}}", "result": ["Michael", "Elizabeth", "David"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '18',
        '姓氏',
        '人',
        '{{_LastName}}',
        '生成姓氏',
        '[{"example": "{{_LastName}}", "result": ["Smith", "Johnson", "Williams"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '19',
        '社会保障号',
        '人',
        '{{_SSN}}',
        '生成社会保障号',
        '[{"example": "{{_SSN}}", "result": ["***********", "***********", "***********"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '20',
        '性别',
        '人',
        '{{_Gender}}',
        '生成性别',
        '[{"example": "{{_Gender}}", "result": ["male", "female"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '21',
        '爱好',
        '人',
        '{{_Hobby}}',
        '生成爱好',
        '[{"example": "{{_Hobby}}", "result": ["reading", "swimming", "cooking"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '22',
        '邮箱地址',
        '人',
        '{{_Email}}',
        '生成邮箱地址',
        '[{"example": "{{_Email}}", "result": ["<EMAIL>", "<EMAIL>", "<EMAIL>"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '23',
        '电话号码',
        '人',
        '{{_Phone}}',
        '生成电话号码',
        '[{"example": "{{_Phone}}", "result": ["18297299192"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '24',
        '格式化电话号码',
        '人',
        '{{_PhoneFormatted}}',
        '生成格式化电话号码',
        '[{"example": "{{_PhoneFormatted}}", "result": ["+8618297299192"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '25',
        '用户名',
        '登录验证',
        '{{_Username}}',
        '生成用户名',
        '[{"example": "{{_Username}}", "result": ["john_doe", "jane_smith", "mike_j"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '26',
        '密码',
        '登录验证',
        '{{_Password(true,true,true,true,false,8)}}',
        '生成密码',
        '[{"example": "{{_Password(true,true,true,true,false,8)}}", "result": ["Abc123!@", "Xyz789#$", "Def456%^"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '27',
        '地址',
        '地址',
        '{{_Address}}',
        '生成完整地址',
        '[{"example": "{{_Address}}", "result": ["123 Main St, New York, NY 10001", "456 Oak Ave, Los Angeles, CA 90210", "789 Pine Rd, Chicago, IL 60601"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '28',
        '街道',
        '地址',
        '{{_Street}}',
        '生成街道名',
        '[{"example": "{{_Street}}", "result": ["Main Street", "Oak Avenue", "Pine Road"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '29',
        '街道名称',
        '地址',
        '{{_StreetName}}',
        '生成街道名称',
        '[{"example": "{{_StreetName}}", "result": ["Main", "Oak", "Pine"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '30',
        '街道号码',
        '地址',
        '{{_StreetNumber}}',
        '生成街道号码',
        '[{"example": "{{_StreetNumber}}", "result": ["123", "456", "789"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '31',
        '街道前缀',
        '地址',
        '{{_StreetPrefix}}',
        '生成街道前缀',
        '[{"example": "{{_StreetPrefix}}", "result": ["North", "South", "East"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '32',
        '街道后缀',
        '地址',
        '{{_StreetSuffix}}',
        '生成街道后缀',
        '[{"example": "{{_StreetSuffix}}", "result": ["Street", "Avenue", "Road"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '33',
        '城市',
        '地址',
        '{{_City}}',
        '生成城市名',
        '[{"example": "{{_City}}", "result": ["New York", "Los Angeles", "Chicago"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '34',
        '州名',
        '地址',
        '{{_State}}',
        '生成州名',
        '[{"example": "{{_State}}", "result": ["New York", "California", "Illinois"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '35',
        '州缩写',
        '地址',
        '{{_StateAbr}}',
        '生成州缩写',
        '[{"example": "{{_StateAbr}}", "result": ["NY", "CA", "IL"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '36',
        '邮政编码',
        '地址',
        '{{_Zip}}',
        '生成邮政编码',
        '[{"example": "{{_Zip}}", "result": ["10001", "90210", "60601"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '37',
        '国家',
        '地址',
        '{{_Country}}',
        '生成国家名',
        '[{"example": "{{_Country}}", "result": ["United States", "Canada", "Mexico"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '38',
        '国家缩写',
        '地址',
        '{{_CountryAbr}}',
        '生成国家缩写',
        '[{"example": "{{_CountryAbr}}", "result": ["US", "CA", "MX"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '39',
        '纬度',
        '地址',
        '{{_Latitude}}',
        '生成纬度',
        '[{"example": "{{_Latitude}}", "result": ["40.7128", "34.0522", "41.8781"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '40',
        '经度',
        '地址',
        '{{_Longitude}}',
        '生成经度',
        '[{"example": "{{_Longitude}}", "result": ["-74.0060", "-118.2437", "-87.6298"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '41',
        '游戏玩家标签',
        '游戏',
        '{{_Gamertag}}',
        '生成游戏标签',
        '[{"example": "{{_Gamertag}}", "result": ["Player123", "GamerX", "ProGamer99"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '42',
        '骰子点数',
        '游戏',
        '{{_Dice(1,6)}}',
        '生成骰子点数',
        '[{"example": "{{_Dice(1,6)}}", "result": ["4", "1", "6"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '43',
        '啤酒酒精度',
        '啤酒',
        '{{_BeerAlcohol}}',
        '生成啤酒酒精度',
        '[{"example": "{{_BeerAlcohol}}", "result": ["5.2%", "4.8%", "6.1%"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '44',
        '啤酒柏拉图度',
        '啤酒',
        '{{_BeerBlg}}',
        '生成啤酒柏拉图度',
        '[{"example": "{{_BeerBlg}}", "result": ["12.5°P", "11.2°P", "14.8°P"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '45',
        '啤酒花品种',
        '啤酒',
        '{{_BeerHop}}',
        '生成啤酒花品种',
        '[{"example": "{{_BeerHop}}", "result": ["Cascade", "Centennial", "Chinook"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '46',
        '啤酒苦味值',
        '啤酒',
        '{{_BeerIbu}}',
        '生成啤酒苦味值',
        '[{"example": "{{_BeerIbu}}", "result": ["35 IBU", "42 IBU", "28 IBU"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '47',
        '啤酒麦芽',
        '啤酒',
        '{{_BeerMalt}}',
        '生成啤酒麦芽',
        '[{"example": "{{_BeerMalt}}", "result": ["Pilsner", "Munich", "Vienna"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '48',
        '啤酒名称',
        '啤酒',
        '{{_BeerName}}',
        '生成啤酒名称',
        '[{"example": "{{_BeerName}}", "result": ["Golden Ale", "Dark Stout", "Wheat Beer"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '49',
        '啤酒风格',
        '啤酒',
        '{{_BeerStyle}}',
        '生成啤酒风格',
        '[{"example": "{{_BeerStyle}}", "result": ["IPA", "Lager", "Stout"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '50',
        '啤酒酵母',
        '啤酒',
        '{{_BeerYeast}}',
        '生成啤酒酵母',
        '[{"example": "{{_BeerYeast}}", "result": ["Ale Yeast", "Lager Yeast", "Wild Yeast"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '51',
        '汽车信息',
        '汽车',
        '{{_Car}}',
        '生成汽车信息',
        '[{"example": "{{_Car}}", "result": ["Toyota Camry", "Honda Civic", "Ford F-150"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '52',
        '汽车制造商',
        '汽车',
        '{{_CarMaker}}',
        '生成汽车制造商',
        '[{"example": "{{_CarMaker}}", "result": ["Toyota", "Honda", "Ford"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '53',
        '汽车型号',
        '汽车',
        '{{_CarModel}}',
        '生成汽车型号',
        '[{"example": "{{_CarModel}}", "result": ["Camry", "Civic", "F-150"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '54',
        '汽车类型',
        '汽车',
        '{{_CarType}}',
        '生成汽车类型',
        '[{"example": "{{_CarType}}", "result": ["Sedan", "SUV", "Truck"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '55',
        '汽车燃料类型',
        '汽车',
        '{{_CarFuelType}}',
        '生成汽车燃料类型',
        '[{"example": "{{_CarFuelType}}", "result": ["Gasoline", "Diesel", "Electric"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '56',
        '汽车变速器类型',
        '汽车',
        '{{_CarTransmissionType}}',
        '生成汽车变速器类型',
        '[{"example": "{{_CarTransmissionType}}", "result": ["Automatic", "Manual", "CVT"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '57',
        '单词',
        '单词',
        '{{_Word}}',
        '生成单词',
        '[{"example": "{{_Word}}", "result": ["example", "sample", "test"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '58',
        '句子',
        '单词',
        '{{_Sentence(5)}}',
        '生成句子',
        '[{"example": "{{_Sentence(5)}}", "result": ["This is an example sentence.", "Here we have another sample.", "Testing the word generation function."]}]',
        'admin',
        'admin',
        false
    ),
    (
        '59',
        '段落',
        '单词',
        '{{_Paragraph(3,5,10," ")}}',
        '生成段落',
        '[{"example": "{{_Paragraph(3,5,10,\" \")}}", "result": ["This is an example paragraph with multiple sentences.", "Here is another sample paragraph for testing.", "Generated content for demonstration purposes."]}]',
        'admin',
        'admin',
        false
    ),
    (
        '60',
        'Lorem Ipsum单词',
        '单词',
        '{{_LoremIpsumWord}}',
        '生成Lorem Ipsum单词',
        '[{"example": "{{_LoremIpsumWord}}", "result": ["lorem", "ipsum", "dolor"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '61',
        'Lorem Ipsum句子',
        '单词',
        '{{_LoremIpsumSentence(5)}}',
        '生成Lorem Ipsum句子',
        '[{"example": "{{_LoremIpsumSentence(5)}}", "result": ["Lorem ipsum dolor sit amet.", "Consectetur adipiscing elit sed.", "Do eiusmod tempor incididunt ut."]}]',
        'admin',
        'admin',
        false
    ),
    (
        '62',
        'Lorem Ipsum段落',
        '单词',
        '{{_LoremIpsumParagraph(3,5,10," ")}}',
        '生成Lorem Ipsum段落',
        '[{"example": "{{_LoremIpsumParagraph(3,5,10,\" \")}}", "result": ["Lorem ipsum dolor sit amet, consectetur adipiscing elit.", "Sed do eiusmod tempor incididunt ut labore et dolore.", "Ut enim ad minim veniam, quis nostrud exercitation."]}]',
        'admin',
        'admin',
        false
    ),
    (
        '63',
        '问题',
        '单词',
        '{{_Question}}',
        '生成问题',
        '[{"example": "{{_Question}}", "result": ["What is your name?", "How are you today?", "Where do you live?"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '64',
        '引用',
        '单词',
        '{{_Quote}}',
        '生成引用',
        '[{"example": "{{_Quote}}", "result": ["To be or not to be.", "I have a dream.", "The only thing we have to fear is fear itself."]}]',
        'admin',
        'admin',
        false
    ),
    (
        '65',
        '短语',
        '单词',
        '{{_Phrase}}',
        '生成短语',
        '[{"example": "{{_Phrase}}", "result": ["hello world", "good morning", "thank you"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '66',
        '水果',
        '食物',
        '{{_Fruit}}',
        '生成水果名',
        '[{"example": "{{_Fruit}}", "result": ["apple", "banana", "orange"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '67',
        '蔬菜',
        '食物',
        '{{_Vegetable}}',
        '生成蔬菜名',
        '[{"example": "{{_Vegetable}}", "result": ["carrot", "broccoli", "spinach"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '68',
        '早餐',
        '食物',
        '{{_Breakfast}}',
        '生成早餐食物',
        '[{"example": "{{_Breakfast}}", "result": ["pancakes", "cereal", "toast"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '69',
        '午餐',
        '食物',
        '{{_Lunch}}',
        '生成午餐食物',
        '[{"example": "{{_Lunch}}", "result": ["sandwich", "salad", "soup"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '70',
        '晚餐',
        '食物',
        '{{_Dinner}}',
        '生成晚餐食物',
        '[{"example": "{{_Dinner}}", "result": ["steak", "pasta", "chicken"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '71',
        '零食',
        '食物',
        '{{_Snack}}',
        '生成零食',
        '[{"example": "{{_Snack}}", "result": ["chips", "cookies", "nuts"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '72',
        '甜点',
        '食物',
        '{{_Dessert}}',
        '生成甜点',
        '[{"example": "{{_Dessert}}", "result": ["cake", "ice cream", "pie"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '73',
        '布尔值',
        '通用',
        '{{_Bool}}',
        '生成布尔值',
        '[{"example": "{{_Bool}}", "result": [true, false]}]',
        'admin',
        'admin',
        false
    ),
    (
        '74',
        'UUID',
        '通用',
        '{{_UUID}}',
        '生成UUID',
        '[{"example": "{{_UUID}}", "result": ["550e8400-e29b-41d4-a716-************", "6ba7b810-9dad-11d1-80b4-00c04fd430c8"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '75',
        '抛硬币',
        '通用',
        '{{_FlipACoin}}',
        '抛硬币',
        '[{"example": "{{_FlipACoin}}", "result": ["Heads", "Tails"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '76',
        '颜色',
        '颜色',
        '{{_Color}}',
        '生成颜色名',
        '[{"example": "{{_Color}}", "result": ["red", "blue", "green"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '77',
        '十六进制颜色',
        '颜色',
        '{{_HexColor}}',
        '生成十六进制颜色',
        '[{"example": "{{_HexColor}}", "result": ["#FF0000", "#00FF00", "#0000FF"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '78',
        'RGB颜色',
        '颜色',
        '{{_RGBColor}}',
        '生成RGB颜色',
        '[{"example": "{{_RGBColor}}", "result": ["rgb(255,0,0)"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '79',
        '安全颜色',
        '颜色',
        '{{_SafeColor}}',
        '生成安全颜色',
        '[{"example": "{{_SafeColor}}", "result": ["red", "blue", "green"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '80',
        '好看的颜色',
        '颜色',
        '{{_NiceColors}}',
        '生成好看的颜色',
        '[{"example": "{{_NiceColors}}", "result": ["#FF6B6B", "#4ECDC4", "#45B7D1"]}]',
        'admin',
        'admin',
        false
    ),
    -- TODO: 修改图片的实例
    (
        '81',
        '图片',
        '图片',
        '{{_Image(300,200)}}',
        '生成图片URL',
        '[{"example": "{{_Image(300,200)}}", "result": ["https://picsum.photos/300/200", "https://picsum.photos/300/200?random=1", "https://picsum.photos/300/200?random=2"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '82',
        'JPEG图片',
        '图片',
        '{{_ImageJpeg(300,200)}}',
        '生成JPEG图片URL',
        '[{"example": "{{_ImageJpeg(300,200)}}", "result": ["https://picsum.photos/300/200.jpg", "https://picsum.photos/300/200.jpg?random=1", "https://picsum.photos/300/200.jpg?random=2"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '83',
        'PNG图片',
        '图片',
        '{{_ImagePng(300,200)}}',
        '生成PNG图片URL',
        '[{"example": "{{_ImagePng(300,200)}}", "result": ["https://picsum.photos/300/200.png", "https://picsum.photos/300/200.png?random=1", "https://picsum.photos/300/200.png?random=2"]}]',
        'admin',
        'admin',
        false
    ),
    -- Internet
    (
        '84',
        '网址',
        '网络',
        '{{_URL}}',
        '生成URL',
        '[{"example": "{{_URL}}", "result": ["https://example.com", "https://test.org", "https://demo.net"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '85',
        '域名',
        '网络',
        '{{_DomainName}}',
        '生成域名',
        '[{"example": "{{_DomainName}}", "result": ["example.com", "test.org", "demo.net"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '86',
        '域名后缀',
        '网络',
        '{{_DomainSuffix}}',
        '生成域名后缀',
        '[{"example": "{{_DomainSuffix}}", "result": ["com", "org", "net"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '87',
        'IPv4地址',
        '网络',
        '{{_IPv4Address}}',
        '生成IPv4地址',
        '[{"example": "{{_IPv4Address}}", "result": ["***********", "********", "**********"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '88',
        'IPv6地址',
        '网络',
        '{{_IPv6Address}}',
        '生成IPv6地址',
        '[{"example": "{{_IPv6Address}}", "result": ["2001:0db8:85a3:0000:0000:8a2e:0370:7334", "fe80::1", "::1"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '89',
        'MAC地址',
        '网络',
        '{{_MacAddress}}',
        '生成MAC地址',
        '[{"example": "{{_MacAddress}}", "result": ["00:1B:44:11:3A:B7", "AA:BB:CC:DD:EE:FF", "12:34:56:78:9A:BC"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '90',
        'HTTP状态码',
        '网络',
        '{{_HTTPStatusCode}}',
        '生成HTTP状态码',
        '[{"example": "{{_HTTPStatusCode}}", "result": ["200", "404", "500"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '91',
        '简单HTTP状态码',
        '网络',
        '{{_HTTPStatusCodeSimple}}',
        '生成简单HTTP状态码',
        '[{"example": "{{_HTTPStatusCodeSimple}}", "result": ["200", "404", "500"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '92',
        '日志级别',
        '网络',
        '{{_LogLevel}}',
        '生成日志级别',
        '[{"example": "{{_LogLevel}}", "result": ["INFO", "ERROR", "DEBUG"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '93',
        'HTTP方法',
        '网络',
        '{{_HTTPMethod}}',
        '生成HTTP方法',
        '[{"example": "{{_HTTPMethod}}", "result": ["GET", "POST", "PUT"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '94',
        'HTTP版本',
        '网络',
        '{{_HTTPVersion}}',
        '生成HTTP版本',
        '[{"example": "{{_HTTPVersion}}", "result": ["HTTP/1.1", "HTTP/2.0", "HTTP/1.0"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '95',
        '用户代理',
        '网络',
        '{{_UserAgent}}',
        '生成用户代理',
        '[{"example": "{{_UserAgent}}", "result": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64)", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)", "Mozilla/5.0 (X11; Linux x86_64)"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '96',
        'Chrome用户代理',
        '网络',
        '{{_ChromeUserAgent}}',
        '生成Chrome用户代理',
        '[{"example": "{{_ChromeUserAgent}}", "result": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '97',
        'Firefox用户代理',
        '网络',
        '{{_FirefoxUserAgent}}',
        '生成Firefox用户代理',
        '[{"example": "{{_FirefoxUserAgent}}", "result": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:91.0) Gecko/20100101 Firefox/91.0", "Mozilla/5.0 (X11; Linux x86_64; rv:91.0) Gecko/20100101 Firefox/91.0"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '98',
        'Opera用户代理',
        '网络',
        '{{_OperaUserAgent}}',
        '生成Opera用户代理',
        '[{"example": "{{_OperaUserAgent}}", "result": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 OPR/77.0.4054.277", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 OPR/77.0.4054.277", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 OPR/77.0.4054.277"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '99',
        'Safari用户代理',
        '网络',
        '{{_SafariUserAgent}}',
        '生成Safari用户代理',
        '[{"example": "{{_SafariUserAgent}}", "result": ["Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15", "Mozilla/5.0 (iPad; CPU OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15"]}]',
        'admin',
        'admin',
        false
    );

INSERT INTO
    mock_type (
        "id",
        "name",
        "type",
        "command",
        "description",
        "examples",
        "creator",
        "modifier",
        "is_deleted"
    )
VALUES (
        '100',
        '输入字段名',
        '网络',
        '{{_InputName}}',
        '生成输入字段名',
        '[{"example": "{{_InputName}}", "result": ["firstName", "lastName", "email"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '101',
        'SVG图形',
        '网络',
        '{{_Svg}}',
        '生成SVG',
        '[{"example": "{{_Svg}}", "result": ["<svg>...</svg>", "<svg width=\"100\" height=\"100\">...</svg>", "<svg viewBox=\"0 0 100 100\">...</svg>"]}]',
        'admin',
        'admin',
        false
    ),
    -- Date/Time
    (
        '102',
        '日期',
        '时间',
        '{{_Date}}',
        '生成日期(RFC3339格式)',
        '[{"example": "{{_Date}}", "result": ["1946-07-26T03:17:11Z"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '103',
        '过去日期',
        '时间',
        '{{_PastDate}}',
        '生成过去日期(RFC3339格式)',
        '[{"example": "{{_PastDate}}", "result": ["2025-08-04T16:41:16+08:00"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '104',
        '未来日期',
        '时间',
        '{{_FutureDate}}',
        '生成未来日期(RFC3339格式)',
        '[{"example": "{{_FutureDate}}", "result": ["2080-08-05T03:43:10+08:00"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '105',
        '日期范围',
        '时间',
        '{{_DateRange(1946-07-26T03:17:11Z, 2046-07-26T03:17:11Z)}}',
        '生成日期范围内的日期(RFC3339格式)',
        '[{"example": "{{_DateRange(1946-07-26T03:17:11Z, 2046-07-26T03:17:11Z)}}", "result": ["1960-07-26T03:17:11Z"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '106',
        '纳秒',
        '时间',
        '{{_NanoSecond}}',
        '生成纳秒',
        '[{"example": "{{_NanoSecond}}", "result": ["*********", "*********", "*********"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '107',
        '秒',
        '时间',
        '{{_Second}}',
        '生成秒',
        '[{"example": "{{_Second}}", "result": ["30", "45", "15"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '108',
        '分钟',
        '时间',
        '{{_Minute}}',
        '生成分钟',
        '[{"example": "{{_Minute}}", "result": ["45", "20", "58"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '109',
        '小时',
        '时间',
        '{{_Hour}}',
        '生成小时',
        '[{"example": "{{_Hour}}", "result": ["14", "09", "22"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '110',
        '月份',
        '时间',
        '{{_Month}}',
        '生成月份',
        '[{"example": "{{_Month}}", "result": ["7", "12", "3"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '111',
        '月份字符串',
        '时间',
        '{{_MonthString}}',
        '生成月份字符串',
        '[{"example": "{{_MonthString}}", "result": ["July", "December", "March"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '112',
        '日',
        '时间',
        '{{_Day}}',
        '生成日',
        '[{"example": "{{_Day}}", "result": ["15", "28", "03"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '113',
        '星期',
        '时间',
        '{{_WeekDay}}',
        '生成星期',
        '[{"example": "{{_WeekDay}}", "result": ["Monday", "Friday", "Sunday"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '114',
        '年份',
        '时间',
        '{{_Year}}',
        '生成年份',
        '[{"example": "{{_Year}}", "result": ["2023", "2024", "2022"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '115',
        '时区',
        '时间',
        '{{_TimeZone}}',
        '生成时区',
        '[{"example": "{{_TimeZone}}", "result": ["America/New_York", "Europe/London", "Asia/Tokyo"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '116',
        '时区缩写',
        '时间',
        '{{_TimeZoneAbv}}',
        '生成时区缩写',
        '[{"example": "{{_TimeZoneAbv}}", "result": ["EST", "GMT", "JST"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '117',
        '完整时区',
        '时间',
        '{{_TimeZoneFull}}',
        '生成完整时区',
        '[{"example": "{{_TimeZoneFull}}", "result": ["Eastern Standard Time", "Greenwich Mean Time", "Japan Standard Time"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '118',
        '时区偏移',
        '时间',
        '{{_TimeZoneOffset}}',
        '生成时区偏移',
        '[{"example": "{{_TimeZoneOffset}}", "result": ["-5", "0", "+9"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '119',
        '时区区域',
        '时间',
        '{{_TimeZoneRegion}}',
        '生成时区区域',
        '[{"example": "{{_TimeZoneRegion}}", "result": ["America", "Europe", "Asia"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '120',
        '价格',
        '支付',
        '{{_Price(10,100)}}',
        '生成价格',
        '[{"example": "{{_Price(10,100)}}", "result": ["49.99", "75.50", "23.75"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '121',
        '信用卡CVV',
        '支付',
        '{{_CreditCardCvv}}',
        '生成信用卡CVV',
        '[{"example": "{{_CreditCardCvv}}", "result": ["123", "456", "789"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '122',
        '信用卡过期日期',
        '支付',
        '{{_CreditCardExp}}',
        '生成信用卡过期日期',
        '[{"example": "{{_CreditCardExp}}", "result": ["12/25", "06/27", "09/24"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '123',
        '信用卡号',
        '支付',
        '{{_CreditCardNumber}}',
        '生成信用卡号',
        '[{"example": "{{_CreditCardNumber}}", "result": ["****************", "****************", "***************"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '124',
        '信用卡类型',
        '支付',
        '{{_CreditCardType}}',
        '生成信用卡类型',
        '[{"example": "{{_CreditCardType}}", "result": ["Visa", "MasterCard", "American Express"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '126',
        '货币全名',
        '支付',
        '{{_CurrencyLong}}',
        '生成货币全名',
        '[{"example": "{{_CurrencyLong}}", "result": ["United States Dollar", "Euro", "Japanese Yen"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '127',
        '货币缩写',
        '支付',
        '{{_CurrencyShort}}',
        '生成货币缩写',
        '[{"example": "{{_CurrencyShort}}", "result": ["USD", "EUR", "JPY"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '128',
        'ACH路由号',
        '支付',
        '{{_AchRouting}}',
        '生成ACH路由号',
        '[{"example": "{{_AchRouting}}", "result": ["*********", "*********", "*********"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '129',
        'ACH账户号',
        '支付',
        '{{_AchAccount}}',
        '生成ACH账户号',
        '[{"example": "{{_AchAccount}}", "result": ["*********", "*********", "*********"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '130',
        '比特币地址',
        '支付',
        '{{_BitcoinAddress}}',
        '生成比特币地址',
        '[{"example": "{{_BitcoinAddress}}", "result": ["**********************************", "**********************************", "******************************************"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '131',
        '比特币私钥',
        '支付',
        '{{_BitcoinPrivateKey}}',
        '生成比特币私钥',
        '[{"example": "{{_BitcoinPrivateKey}}", "result": ["5HueCGU8rMjxEXxiPuD5BDku4MkFqeZyd4dZ1jvhTVqvbTLvyTJ", "KwdMAjGmerYanjeui5SHS7JkmpZvVipYvB2LJGU1ZxJwYvP98617", "L1aW4aubDFB7yfras2S1mN3bqg9nwySY8nkoLmJebSLD5BWv3ENZ"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '132',
        '银行名称',
        '支付',
        '{{_BankName}}',
        '生成银行名称',
        '[{"example": "{{_BankName}}", "result": ["Chase Bank", "Bank of America", "Wells Fargo"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '133',
        '银行类型',
        '支付',
        '{{_BankType}}',
        '生成银行类型',
        '[{"example": "{{_BankType}}", "result": ["Commercial", "Investment", "Savings"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '134',
        'CUSIP代码',
        '支付',
        '{{_Cusip}}',
        '生成CUSIP代码',
        '[{"example": "{{_Cusip}}", "result": ["*********", "*********", "68389X105"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '135',
        'ISIN代码',
        '支付',
        '{{_Isin}}',
        '生成ISIN代码',
        '[{"example": "{{_Isin}}", "result": ["US*********5", "GB0002634946", "JP3633400001"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '136',
        '商业术语',
        '商业',
        '{{_BS}}',
        '生成商业术语',
        '[{"example": "{{_BS}}", "result": ["synergistic solutions", "innovative technologies", "strategic partnerships"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '137',
        '公司简介',
        '商业',
        '{{_Blurb}}',
        '生成简介',
        '[{"example": "{{_Blurb}}", "result": ["Innovative technology company", "Leading software solutions provider", "Global consulting firm"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '138',
        '商业流行词',
        '商业',
        '{{_BuzzWord}}',
        '生成流行词',
        '[{"example": "{{_BuzzWord}}", "result": ["synergy", "paradigm", "leverage"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '139',
        '公司名称',
        '商业',
        '{{_Company}}',
        '生成公司名',
        '[{"example": "{{_Company}}", "result": ["Tech Corp", "Innovation Inc", "Global Solutions"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '140',
        '公司后缀',
        '商业',
        '{{_CompanySuffix}}',
        '生成公司后缀',
        '[{"example": "{{_CompanySuffix}}", "result": ["Inc", "Corp", "LLC"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '141',
        '职位描述符',
        '商业',
        '{{_JobDescriptor}}',
        '生成工作描述符',
        '[{"example": "{{_JobDescriptor}}", "result": ["Senior", "Lead", "Principal"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '142',
        '职位级别',
        '商业',
        '{{_JobLevel}}',
        '生成工作级别',
        '[{"example": "{{_JobLevel}}", "result": ["Manager", "Director", "VP"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '143',
        '职位标题',
        '商业',
        '{{_JobTitle}}',
        '生成工作标题',
        '[{"example": "{{_JobTitle}}", "result": ["Software Engineer", "Product Manager", "Data Scientist"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '144',
        '公司标语',
        '商业',
        '{{_Slogan}}',
        '生成标语',
        '[{"example": "{{_Slogan}}", "result": ["Just Do It", "Think Different", "I am Loving It"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '145',
        '应用名称',
        'App',
        '{{_AppName}}',
        '生成应用名称',
        '[{"example": "{{_AppName}}", "result": ["MyApp", "SuperTool", "QuickHelper"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '146',
        '应用版本',
        'App',
        '{{_AppVersion}}',
        '生成应用版本',
        '[{"example": "{{_AppVersion}}", "result": ["1.0.0", "2.1.3", "0.9.5"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '147',
        '应用作者',
        'App',
        '{{_AppAuthor}}',
        '生成应用作者',
        '[{"example": "{{_AppAuthor}}", "result": ["John Developer", "Jane Coder", "Mike Programmer"]}]',
        'admin',
        'admin',
        false
    ),
    -- Animal
    (
        '148',
        '宠物名',
        '动物',
        '{{_PetName}}',
        '生成宠物名',
        '[{"example": "{{_PetName}}", "result": ["Buddy", "Max", "Bella"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '149',
        '动物名',
        '动物',
        '{{_Animal}}',
        '生成动物名',
        '[{"example": "{{_Animal}}", "result": ["dog", "cat", "bird"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '150',
        '动物类型',
        '动物',
        '{{_AnimalType}}',
        '生成动物类型',
        '[{"example": "{{_AnimalType}}", "result": ["mammal", "bird", "reptile"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '151',
        '农场动物',
        '动物',
        '{{_FarmAnimal}}',
        '生成农场动物',
        '[{"example": "{{_FarmAnimal}}", "result": ["cow", "pig", "chicken"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '152',
        '猫品种',
        '动物',
        '{{_Cat}}',
        '生成猫品种',
        '[{"example": "{{_Cat}}", "result": ["Persian", "Siamese", "Maine Coon"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '153',
        '狗品种',
        '动物',
        '{{_Dog}}',
        '生成狗品种',
        '[{"example": "{{_Dog}}", "result": ["Golden Retriever", "German Shepherd", "Labrador"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '154',
        '鸟类',
        '动物',
        '{{_Bird}}',
        '生成鸟类',
        '[{"example": "{{_Bird}}", "result": ["Eagle", "Sparrow", "Robin"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '155',
        '表情符号',
        '表情',
        '{{_Emoji}}',
        '生成表情符号',
        '[{"example": "{{_Emoji}}", "result": ["😀", "😊", "🎉"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '156',
        '表情符号描述',
        '表情',
        '{{_EmojiDescription}}',
        '生成表情符号描述',
        '[{"example": "{{_EmojiDescription}}", "result": ["grinning face", "smiling face", "party popper"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '157',
        '表情符号类别',
        '表情',
        '{{_EmojiCategory}}',
        '生成表情符号类别',
        '[{"example": "{{_EmojiCategory}}", "result": ["Smileys & Emotion", "People & Body", "Activities"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '158',
        '表情符号别名',
        '表情',
        '{{_EmojiAlias}}',
        '生成表情符号别名',
        '[{"example": "{{_EmojiAlias}}", "result": ["grinning", "smile", "party"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '159',
        '表情符号标签',
        '表情',
        '{{_EmojiTag}}',
        '生成表情符号标签',
        '[{"example": "{{_EmojiTag}}", "result": ["happy", "joy", "celebration"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '160',
        '语言',
        '语言',
        '{{_Language}}',
        '生成语言',
        '[{"example": "{{_Language}}", "result": ["English", "Spanish", "French"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '161',
        '语言缩写',
        '语言',
        '{{_LanguageAbbreviation}}',
        '生成语言缩写',
        '[{"example": "{{_LanguageAbbreviation}}", "result": ["en", "es", "fr"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '162',
        '编程语言',
        '语言',
        '{{_ProgrammingLanguage}}',
        '生成编程语言',
        '[{"example": "{{_ProgrammingLanguage}}", "result": ["Python", "JavaScript", "Java"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '163',
        '范围内的整数',
        '数字',
        '{{_IntRange(1,100)}}',
        '生成范围内的整数',
        '[{"example": "{{_IntRange(1,100)}}", "result": ["42", "73", "15"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '164',
        '整数',
        '数字',
        '{{_Int}}',
        '生成整数',
        '[{"example": "{{_Int}}", "result": ["42", "123", "789"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '165',
        'N以内整数',
        '数字',
        '{{_IntN(100)}}',
        '生成N以内的整数',
        '[{"example": "{{_IntN(100)}}", "result": ["42", "73", "15"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '166',
        '8位整数',
        '数字',
        '{{_Int8}}',
        '生成8位整数',
        '[{"example": "{{_Int8}}", "result": ["42", "127", "-128"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '167',
        '16位整数',
        '数字',
        '{{_Int16}}',
        '生成16位整数',
        '[{"example": "{{_Int16}}", "result": ["42", "32767", "-32768"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '168',
        '32位整数',
        '数字',
        '{{_Int32}}',
        '生成32位整数',
        '[{"example": "{{_Int32}}", "result": ["42", "2147483647", "-2147483648"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '169',
        '64位整数',
        '数字',
        '{{_Int64}}',
        '生成64位整数',
        '[{"example": "{{_Int64}}", "result": ["42", "9223372036854775807", "-9223372036854775808"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '170',
        '无符号整数',
        '数字',
        '{{_Uint}}',
        '生成无符号整数',
        '[{"example": "{{_Uint}}", "result": ["42", "123", "789"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '171',
        'N以内无符号整数',
        '数字',
        '{{_UintN(100)}}',
        '生成N以内的无符号整数',
        '[{"example": "{{_UintN(100)}}", "result": ["42", "73", "15"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '172',
        '8位无符号整数',
        '数字',
        '{{_Uint8}}',
        '生成8位无符号整数',
        '[{"example": "{{_Uint8}}", "result": ["42", "255", "0"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '173',
        '16位无符号整数',
        '数字',
        '{{_Uint16}}',
        '生成16位无符号整数',
        '[{"example": "{{_Uint16}}", "result": ["42", "65535", "0"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '174',
        '32位无符号整数',
        '数字',
        '{{_Uint32}}',
        '生成32位无符号整数',
        '[{"example": "{{_Uint32}}", "result": ["42", "4294967295", "0"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '175',
        '64位无符号整数',
        '数字',
        '{{_Uint64}}',
        '生成64位无符号整数',
        '[{"example": "{{_Uint64}}", "result": ["42", "18446744073709551615", "0"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '176',
        '32位浮点数',
        '数字',
        '{{_Float32}}',
        '生成32位浮点数',
        '[{"example": "{{_Float32}}", "result": ["3.14", "2.71", "1.41"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '177',
        '范围内的32位浮点数',
        '数字',
        '{{_Float32Range(0,100)}}',
        '生成范围内的32位浮点数',
        '[{"example": "{{_Float32Range(0,100)}}", "result": ["42.5", "73.2", "15.8"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '178',
        '64位浮点数',
        '数字',
        '{{_Float64}}',
        '生成64位浮点数',
        '[{"example": "{{_Float64}}", "result": ["3.14159", "2.71828", "1.41421"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '179',
        '范围内的64位浮点数',
        '数字',
        '{{_Float64Range(0,100)}}',
        '生成范围内的64位浮点数',
        '[{"example": "{{_Float64Range(0,100)}}", "result": ["42.5", "73.2", "15.8"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '180',
        '数字字符',
        '字符串',
        '{{_Digit}}',
        '生成数字字符',
        '[{"example": "{{_Digit}}", "result": ["5", "3", "8"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '181',
        'N位数字字符串',
        '字符串',
        '{{_DigitN(5)}}',
        '生成N位数字字符串',
        '[{"example": "{{_DigitN(5)}}", "result": ["12345", "67890", "54321"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '182',
        '字母',
        '字符串',
        '{{_Letter}}',
        '生成字母',
        '[{"example": "{{_Letter}}", "result": ["a", "Z", "m"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '183',
        'N位字母字符串',
        '字符串',
        '{{_LetterN(5)}}',
        '生成N位字母字符串',
        '[{"example": "{{_LetterN(5)}}", "result": ["abcde", "XYZTW", "mNoPq"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '184',
        '字母模式生成',
        '字符串',
        '{{_Lexify(???-###)}}',
        '根据模式生成字符串',
        '[{"example": "{{_Lexify(???-###)}}", "result": ["abc-123", "xyz-789", "def-456"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '185',
        '数字模式生成',
        '字符串',
        '{{_Numerify(###-###-####)}}',
        '根据数字模式生成字符串',
        '[{"example": "{{_Numerify(###-###-####)}}", "result": ["123-456-7890", "987-654-3210", "555-123-4567"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '186',
        '演员名',
        '名人',
        '{{_CelebrityActor}}',
        '生成演员名',
        '[{"example": "{{_CelebrityActor}}", "result": ["Tom Hanks", "Meryl Streep", "Leonardo DiCaprio"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '187',
        '商业名人',
        '名人',
        '{{_CelebrityBusiness}}',
        '生成商业名人',
        '[{"example": "{{_CelebrityBusiness}}", "result": ["Elon Musk", "Bill Gates", "Warren Buffett"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '188',
        '体育明星',
        '名人',
        '{{_CelebritySport}}',
        '生成体育明星',
        '[{"example": "{{_CelebritySport}}", "result": ["Michael Jordan", "Serena Williams", "Cristiano Ronaldo"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '189',
        '书名',
        '书',
        '{{_BookTitle}}',
        '生成书名',
        '[{"example": "{{_BookTitle}}", "result": ["The Great Gatsby", "To Kill a Mockingbird", "1984"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '190',
        '作者名',
        '书',
        '{{_BookAuthor}}',
        '生成作者名',
        '[{"example": "{{_BookAuthor}}", "result": ["F. Scott Fitzgerald", "Harper Lee", "George Orwell"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '191',
        '书籍类型',
        '书',
        '{{_BookGenre}}',
        '生成书籍类型',
        '[{"example": "{{_BookGenre}}", "result": ["Fiction", "Mystery", "Romance"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '192',
        '电影名',
        '电影',
        '{{_MovieName}}',
        '生成电影名',
        '[{"example": "{{_MovieName}}", "result": ["The Shawshank Redemption", "The Godfather", "Pulp Fiction"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '193',
        '电影类型',
        '电影',
        '{{_MovieGenre}}',
        '生成电影类型',
        '[{"example": "{{_MovieGenre}}", "result": ["Drama", "Action", "Comedy"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '194',
        '错误信息',
        '错误',
        '{{_Error}}',
        '生成错误信息',
        '[{"example": "{{_Error}}", "result": ["File not found", "Access denied", "Connection timeout"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '196',
        '数据库错误',
        '错误',
        '{{_ErrorDatabase}}',
        '生成数据库错误',
        '[{"example": "{{_ErrorDatabase}}", "result": ["Connection failed", "Table not found", "Syntax error"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '197',
        'GRPC错误',
        '错误',
        '{{_ErrorGRPC}}',
        '生成GRPC错误',
        '[{"example": "{{_ErrorGRPC}}", "result": ["UNAVAILABLE", "DEADLINE_EXCEEDED", "PERMISSION_DENIED"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '198',
        'HTTP错误',
        '错误',
        '{{_ErrorHTTP}}',
        '生成HTTP错误',
        '[{"example": "{{_ErrorHTTP}}", "result": ["Bad Request", "Internal Server Error", "Forbidden"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '199',
        'HTTP客户端错误',
        '错误',
        '{{_ErrorHTTPClient}}',
        '生成HTTP客户端错误',
        '[{"example": "{{_ErrorHTTPClient}}", "result": ["Bad Request", "Unauthorized", "Forbidden"]}]',
        'admin',
        'admin',
        false
    );

INSERT INTO
    mock_type (
        "id",
        "name",
        "type",
        "command",
        "description",
        "examples",
        "creator",
        "modifier",
        "is_deleted"
    )
VALUES (
        '200',
        'HTTP服务器错误',
        '错误',
        '{{_ErrorHTTPServer}}',
        '生成HTTP服务器错误',
        '[{"example": "{{_ErrorHTTPServer}}", "result": ["Internal Server Error", "Bad Gateway", "Service Unavailable"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '201',
        '运行时错误',
        '错误',
        '{{_ErrorRuntime}}',
        '生成运行时错误',
        '[{"example": "{{_ErrorRuntime}}", "result": ["Null pointer exception", "Stack overflow", "Out of memory"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '202',
        '验证错误',
        '错误',
        '{{_ErrorValidation}}',
        '生成验证错误',
        '[{"example": "{{_ErrorValidation}}", "result": ["Invalid email format", "Password too short", "Required field missing"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '203',
        '学校名',
        '学校',
        '{{_School}}',
        '生成学校名',
        '[{"example": "{{_School}}", "result": ["Harvard University", "Stanford University", "MIT"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '204',
        '歌曲名',
        '音乐',
        '{{_SongName}}',
        '生成歌曲名',
        '[{"example": "{{_SongName}}", "result": ["Bohemian Rhapsody", "Stairway to Heaven", "Hotel California"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '205',
        '歌手名',
        '音乐',
        '{{_SongArtist}}',
        '生成歌手名',
        '[{"example": "{{_SongArtist}}", "result": ["Queen", "Led Zeppelin", "The Beatles"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '206',
        '音乐类型',
        '音乐',
        '{{_SongGenre}}',
        '生成音乐类型',
        '[{"example": "{{_SongGenre}}", "result": ["Rock", "Pop", "Jazz"]}]',
        'admin',
        'admin',
        false
    ),
    (
        '207',
        '身份证号',
        '人',
        '{{_IDCardNumber}}',
        '生成中国身份证号',
        '[{"example": "{{_IDCardNumber}}", "result": ["370104199504215001"]}]',
        'admin',
        'admin',
        false
    );