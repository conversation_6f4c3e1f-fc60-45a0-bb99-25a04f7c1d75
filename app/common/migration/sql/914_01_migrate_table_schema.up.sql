-- Create Table database_config
CREATE TABLE database_config (
    id varchar(32) NOT NULL,
    creator varchar(128),
    modifier varchar(128),
    created bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    updated bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    is_deleted boolean DEFAULT false,
    name text,
    db_type text,
    db_host text,
    port numeric,
    db_name text,
    db_user text,
    db_pw text,
    env text,
    space_id text,
    PRIMARY KEY (id)
);

-- Create Table datasource
CREATE TABLE datasource (
    id varchar(32) NOT NULL,
    creator varchar(128),
    modifier varchar(128),
    created bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    updated bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    is_deleted boolean DEFAULT false,
    name text,
    "data" jsonb,
    space_id text,
    PRIMARY KEY (id)
);

-- <PERSON><PERSON> Table variable
CREATE TABLE variable (
    id varchar(32) NOT NULL,
    creator varchar(128),
    modifier varchar(128),
    created bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    updated bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    is_deleted boolean DEFAULT false,
    name text,
    "value" text,
    description text,
    env text,
    var_type text,
    space_id text,
    PRIMARY KEY (id)
);

-- Create Table mock_type
CREATE TABLE mock_type (
    id varchar(32) NOT NULL,
    creator varchar(128),
    modifier varchar(128),
    created bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    updated bigint DEFAULT (
        EXTRACT(
            epoch
            FROM CURRENT_TIMESTAMP
        ) * (1000)::numeric
    ),
    is_deleted boolean DEFAULT false,
    "type" text,
    name text,
    description text,
    command text,
    examples jsonb,
    PRIMARY KEY (id)
);