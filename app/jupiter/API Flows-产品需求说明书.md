# API Flows 产品需求说明书

## 1. 引言

### 1.1 目的

本文件旨在定义 API Flows 的产品需求，这是一个基于 Web 的低代码平台，专注于接口测试和性能压测，通过可视化工作流编排支持多种测试对象（API、数据库）、场景管理、性能测试和接口测试，旨在加速产品交付周期、保证交付质量。API Flows 提供直观的拖放界面、强大的 AI 集成和团队协作功能，满足开发者、测试工程师和业务用户的需求。

### 1.2 范围

API Flows 是一个类似 Postman Flows 的平台，支持用户通过节点式编辑器构建工作流，连接 API 请求、数据转换、逻辑处理和 AI 功能。核心功能包括多种块类型、单向数据流连接、高级开发功能（如 JavaScript 和 JsonPath/XPath）、组织与协作工具以及 AI 辅助功能。前端使用 React 和 React Flow，后端使用 Golang 和 Gin 框架，集成 k6 进行接口测试和性能压测。

### 1.3 定义

- **块（Block）**：执行特定任务或存储数据的基本单元，左侧为输入端口，右侧为输出端口。
- **连接（Connection）**：定义块之间的单向数据流和触发机制。
- **流程模块（Flow Module）**：可复用的工作流单元，可在其他工作流中调用。
- **测试对象**：API 请求和数据库查询，支持接口测试和性能压测。
- **场景管理**：通过可视化组件库实现复杂测试场景的编排。
- **k6 执行引擎**：支持 API 调试 Run、定时任务执行、性能压测三种执行模式。

### 1.4 参考资料

- Postman Flows 文档：https://learning.postman.com/docs/postman-flows/overview/
- React Flow：https://reactflow.dev/
- k6 文档：https://k6.io/docs/

## 2. 总体描述

### 2.1 产品视角

API Flows 是一个专注于接口测试和性能压测的低代码平台，旨在简化 API 驱动的工作流创建，增强模块化设计、AI 集成和实时协作功能。它受 Postman Flows 启发，结合 React Flow 的可视化编辑能力，集成 k6 提供强大的接口测试和性能压测能力，提供高效的工作流开发体验。

### 2.2 产品功能

- **可视化工作流编辑器**：拖放式界面，支持固定和自由布局，实时显示数据流。
- **多种块类型**：包括动作、触发器、AI、逻辑、循环、可视化、数据块和测试对象块。
- **测试对象管理**：支持 API 请求和数据库查询，集成 k6 进行接口测试和性能压测。
- **场景管理**：提供可视化场景组件库，支持复杂测试场景的编排。
- **七种压测模式**：支持并发、轮次、阶梯、错误率、响应时间、每秒应答数、自定义模式。
- **工作流执行**：支持 API 调试 Run、定时任务执行、性能压测三种执行模式。
- **AI 辅助**：通过自然语言生成或修改工作流，提供调试建议。
- **高级功能**：支持 JavaScript 编写复杂逻辑，JsonPath/XPath 处理 JSON/XML 数据。
- **组织与协作**：支持颜色高亮、注释、分组、文件夹、模板库和实时协作。

### 2.3 用户类别

- **开发者**：需要快速集成和测试 API。
- **测试工程师**：进行接口测试和性能压测。
- **数据分析师**：处理和可视化复杂数据。
- **业务用户**：通过低代码界面实现自动化工作流。

### 2.4 运行环境

- **前端**：支持主流浏览器（Chrome、Firefox、Safari），使用 React 和 React Flow。
- **后端**：运行在 Golang 服务器上，使用 Gin 框架、PostgreSQL 数据库。
- **测试引擎**：集成 k6 进行接口测试和性能压测。
- **容器化**：支持 Docker 和 Kubernetes 部署。

### 2.5 设计约束

- 前端使用 React 和 React Flow，通过 CDN 加载。
- 后端使用 Golang 和 Gin，遵循 Apache-2.0 许可证。
- 避免使用 `<form>` 元素，使用 `onClick` 处理交互。
- 确保安全性和可扩展性。
- 集成 k6 进行接口测试和性能压测。

### 2.6 假设与依赖

- 用户具备基本的 API 和工作流知识。
- 依赖外部 AI 服务（如 OpenAI 兼容模型）。
- 依赖 k6 进行接口测试和性能压测。
- 依赖 Elasticsearch 进行性能指标存储和查询。

## 3. 具体需求

### 3.1 功能需求

#### 3.1.1 可视化工作流编辑器

- **拖放界面**：用户可通过拖放添加块并创建连接，支持固定布局（节点吸附到网格）和自由布局（节点自由放置）。
- **块配置**：提供动态表单配置块属性（如 API URL、AI 提示词、测试参数）。
- **数据流可视化**：实时显示块之间的数据流动画。
- **工具栏**：支持添加块、保存工作流、执行、撤销/重做操作。
- **结果面板**：展示执行结果，支持文本、表格、图表等格式。
- **错误提示**：显示用户友好的错误信息（如 API 失败、配置错误、测试失败）。

#### 3.1.2 测试对象

- **API 请求块**：
  - **功能**：执行 HTTP 请求（GET、POST、PUT、DELETE 等），支持与任何 REST 或 SOAP API 交互。
  - **参数化**：支持将上游块的数据动态填充到路径变量、查询参数或请求体。
  - **结果处理**：提供成功（2xx 状态码）和失败（非 2xx 或测试失败）输出端口，允许不同的处理路径。
  - **断言功能**：支持状态码、响应内容、响应时间等断言。
  - **用途**：核心功能，用于与外部服务交互，如调用第三方 API。
- **数据库查询块**：
  - **功能**：执行 SQL 查询，验证数据库操作结果。
  - **断言功能**：支持数据库查询结果的断言验证。
  - **用途**：验证数据库操作的正确性。

#### 3.1.3 场景管理

- **可视化场景组件库**：提供 Start、Output、HTTP Request、逻辑块、循环块、数据块等组件。
- **拖放交互**：通过拖放和连接实现复杂测试场景。
- **场景编排**：支持条件、循环、分支等流程控制。

#### 3.1.4 性能测试

- **七种压测模式**：
  - **并发模式**：固定虚拟用户数，持续执行指定时间。
  - **轮次模式**：固定请求次数，每个虚拟用户执行指定轮次。
  - **阶梯模式**：逐步增加/减少虚拟用户数，支持复杂的负载曲线。
  - **错误率模式**：基于错误率动态调整负载，自动控制测试强度。
  - **响应时间模式**：基于响应时间调整负载，确保性能目标。
  - **每秒应答数模式**：基于 RPS（每秒请求数）控制负载。
  - **自定义模式**：支持用户自定义负载模型和阶段配置。
- **负载参数配置**：支持 vus、duration、stages、thresholds、customConfig 等参数。
- **实时性能监控**：通过 Elasticsearch 实时输出性能指标，使用 Chart.js 渲染曲线图。

#### 3.1.5 接口测试

- **接口测试脚本**：基于真实场景创建接口测试脚本。
- **定时执行**：支持定时执行测试，使用 MUI 的时间选择器配置。
- **丰富断言**：支持 JsonPath、XPath、jq 表达式进行断言。
- **数据驱动测试**：支持 CSV/JSON 文件或数据库查询作为测试数据源。

#### 3.1.6 k6 执行引擎

- **API 调试 Run**：服务端直接通过 k6 CLI 执行，适用于快速验证和调试场景。
- **定时任务执行**：通过 Tekton TaskRun 调度包含 k6 的 Pod 执行，使用 asyncq 实现队列机制。
- **性能压测执行**：通过 Tekton TaskRun 调度包含 k6 的 Pod 执行，支持大规模负载测试。
- **统一脚本生成**：根据测试配置动态生成统一的 k6 脚本模板。

#### 3.1.7 块类型

- **动作（Actions）块**：
  - **HTTP 请求块**：
    - **功能**：执行 HTTP 请求（GET、POST、PUT、DELETE 等），支持与任何 REST 或 SOAP API 交互。
    - **参数化**：支持将上游块的数据动态填充到路径变量、查询参数或请求体。
    - **结果处理**：提供成功（2xx 状态码）和失败（非 2xx 或测试失败）输出端口，允许不同的处理路径。
    - **用途**：核心功能，用于与外部服务交互，如调用第三方 API。
  - **流程模块块**：
    - **功能**：引用并运行工作区中其他已保存的流程模块，实现模块化和复用。
    - **函数式编程**：将复杂逻辑封装为可重用的"函数"。
    - **数据传递**：通过输入端口将数据传递给被引用流程的 Start 块，通过输出端口接收 Output 块的结果。
    - **状态通知**：提供"Done"输出端口，指示被引用流程的完成状态。
    - **用途**：支持模块化工作流设计，提高开发效率。
  - **扩展性**：支持添加其他动作块，如发送邮件或集成特定服务（如 Slack、数据库），以满足未来需求。

- **触发器（Trigger）块**：
  - **开始块（Start）**：
    - **功能**：每个流程模块的入口，自动包含，作为工作流运行的起点。
    - **数据接收**：可配置以接收外部触发（如 Webhook）或流程模块块的输入数据。
    - **触发机制**：其输出端口连接的块在流程启动时被触发。
    - **用途**：定义工作流的起点，支持外部触发或模块化调用。
  - **输出块（Output）**：
    - **功能**：定义流程模块的输出数据，传递给引用它的流程模块块。
    - **数据定义**：其输入端口定义流程的输出数据结构。
    - **用途**：实现流程模块化和数据传递。

- **AI 块**：
  - **AI 创建块**：
    - **功能**：根据自然语言提示生成文本、图像、JSON 数据或智能模式（AI 自动选择输出类型）。
    - **动态提示**：支持在提示中引用其他块的变量数据，实现上下文相关的生成。
    - **用途**：快速构建 AI 驱动的工具，如生成报告、格式化数据或创建视觉内容。
  - **AI 请求块**：
    - **功能**：与特定 AI 模型交互，执行复杂 AI 请求（如分类、翻译）。
    - **用途**：支持高级 AI 功能，增强工作流智能性。

- **逻辑（Logic）块**：
  - **评估块（Evaluate）**：
    - **功能**：评估输入数据是否满足条件（如等于、大于、包含），根据结果（True/False）路由数据。支持 JavaScript 编写自定义逻辑和复杂数据操作。
    - **用途**：实现条件逻辑和决策点。
  - **If 块**：
    - **功能**：根据布尔条件执行不同逻辑路径。
    - **用途**：支持经典的"如果...那么...否则..."逻辑。
  - **Switch 块**：
    - **功能**：根据条件执行多分支逻辑路径。
    - **用途**：处理多条件分支场景。
  - **选择块（Select）**：
    - **功能**：从 JSON、XML 或其他结构化数据中提取特定字段，支持 JsonPath/XPath 语法。
    - **用途**：解析 API 响应，获取所需数据。
  - **扩展性**：支持添加其他逻辑块，如 Filter（数据过滤）、Sort（数据排序），以处理复杂逻辑。

- **循环（Looping）块**：
  - **For 块**：
    - **功能**：遍历数组或列表，支持串行或并行执行内部逻辑。
    - **用途**：处理批量数据，如为每个元素调用 API。
  - **重复块（Repeat）**：
    - **功能**：按指定次数重复执行一组块。
    - **用途**：支持重试、批量生成等场景。
  - **收集块（Collection）**：
    - **功能**：收集循环块的输出数据。
    - **用途**：整合循环处理结果。
  - **扩展性**：支持添加其他循环块，如 While 或 Do While，以满足动态循环需求。

- **可视化（Visualize）块**：
  - **显示块（Display）**：
    - **功能**：接收数据并以文本、数字、表格或图表（如折线图、柱状图）格式展示。
    - **自动检测**：自动检测数据格式或允许手动选择。
    - **调试工具**：用于查看中间数据和结果，辅助流程开发和调试。
    - **用途**：构建简单仪表板，监控流程输出。

- **数据（Data）块**：
  - **基本数据类型**：
    - **字符串块（String）**：创建和输出文本字符串。
    - **数字块（Number）**：创建和输出整数或小数。
    - **布尔块（Bool）**：创建和输出 True/False 值。
    - **空值块（Null）**：输出 null 值。
    - **日期/时间块（Date, Date & Time, Now）**：创建日期、时间或当前时间戳。
  - **复合数据类型**：
    - **列表块（List）**：创建和管理数组，支持嵌套不同类型的数据。
    - **对象块（Object）**：创建和管理键值对对象，支持嵌套。
    - **模板块（Template）**：
      - **功能**：输入大段格式化文本或 JSON，支持 Mustache 语法引用其他块的变量。
      - **用途**：动态生成请求体、邮件内容、报告等。
  - **变量管理**：
    - **创建变量块（Create Variable）**：定义全局变量并赋值，可在流程中访问。
    - **获取变量块（Get Variable）**：获取已定义变量的值。
  - **数据转换**：
    - **解析 JSON 块（Parse JSON）**：将字符串解析为 JSON 对象。
    - **连接块（Concatenate）**：连接字符串或列表。
    - **合并块（Merge）**：合并两个对象。
    - **扩展性**：支持添加其他数据转换块，如 Split（拆分）、Join（连接）、Map（映射），以处理复杂数据操作。

#### 3.1.8 连接（Connections）

- **功能**：定义块之间的单向数据流和触发机制。
- **特性**：
  - 数据和信号从输出端口流向输入端口。
  - 支持多对一（多个输出连接一个输入）和一对多（一个输出连接多个输入），具体取决于块类型。
- **用途**：确保工作流中的数据和控制流按预期流动。

#### 3.1.9 工作流执行

- **功能**：
  - 将工作流定义（JSON 格式）发送到后端执行。
  - 在前端结果面板显示执行结果，支持多种格式（文本、表格、图表）。
  - 提供错误反馈，如 API 请求失败、配置错误、测试失败。
- **实时性**：支持通过 WebSocket 或长轮询实时更新执行状态。
- **执行模式**：
  - **API 调试 Run**：快速验证 API 功能，适用于调试场景。
  - **定时任务执行**：可重复执行的测试配置，支持参数化测试。
  - **性能压测**：包含复杂负载模型的测试配置，支持多种压测模式。

#### 3.1.10 AI 辅助

- **AI 助手**：
  - **功能**：通过自然语言界面辅助用户构建工作流，理解用户意图并建议块和连接。
  - **用途**：降低学习曲线，适合非技术用户。
- **流程生成**：
  - **功能**：根据文本描述生成或修改工作流块，如"创建一个获取天气数据的流程"。
  - **用途**：加速工作流开发。
- **AI 请求**：
  - **功能**：调用特定 AI 请求，执行复杂 AI 任务（如分类、翻译）。
  - **用途**：增强工作流智能性。
- **调试辅助**：
  - **功能**：检测工作流中的错误（如断开连接、无效配置），提供修复建议。
  - **用途**：提高工作流可靠性。

#### 3.1.11 高级功能与开发

- **JavaScript 支持**：
  - **功能**：在评估块和模板块中支持 JavaScript 代码编写，用于复杂逻辑和数据操作。
  - **用途**：满足开发者对高级逻辑的需求。
- **JsonPath/XPath**：
  - **功能**：高效提取、过滤和重塑复杂 JSON/XML 数据。
  - **用途**：简化数据处理，提高效率。

#### 3.1.12 组织与协作

- **颜色高亮**：
  - **功能**：为块或组设置颜色，增强视觉区分度。
  - **用途**：提高复杂工作流的可读性。
- **注释（Annotations）**：
  - **功能**：在画布上添加文本说明，用于文档和协作。
  - **用途**：记录流程逻辑，方便团队理解。
- **组（Groups）**：
  - **功能**：将相关块和注释分组，支持命名、着色和调整大小。
  - **用途**：组织复杂工作流，简化视图管理。
- **文件夹**：
  - **功能**：在侧边栏组织流程模块，支持层级结构。
  - **用途**：便于工作区导航和管理。
- **模板库**：
  - **功能**：提供预构建的工作流模板，如 API 测试、数据处理模板。
  - **用途**：加速开发，降低学习成本。
- **分享与协作**：
  - **功能**：支持团队共享和实时共同编辑工作流。
  - **用途**：支持多人协作，提高团队效率。

### 3.2 非功能需求

- **性能**：支持包含最多 100 个块的工作流，执行延迟低于 1 秒。
- **可扩展性**：支持多用户并发操作，处理至少 100 个并发工作流。
- **可用性**：提供直观界面、工具提示和在线文档。
- **安全性**：
  - 使用 HTTPS 保护通信。
  - 安全存储 API 密钥（加密存储）。
  - 定期更新依赖以修复安全漏洞。
  - 限制 k6 脚本的执行环境，使用 Docker 沙盒运行 k6 脚本，防止恶意脚本。
- **兼容性**：支持主流浏览器，适配桌面和移动设备。

### 3.3 接口需求

- **用户界面**：响应式 Web 界面，支持拖放、配置和结果展示。
- **API**：
  - `POST /api/workflows`：保存工作流。
  - `GET /api/workflows`：列出所有工作流。
  - `GET /api/workflows/:id`：获取特定工作流。
  - `POST /api/execute`：执行工作流并返回结果。
  - `POST /api/execute-k6-debug`：API 调试 Run，直接执行 k6 CLI。
  - `POST /api/execute-k6-scheduled`：定时任务执行，通过 Tekton TaskRun 调度。
  - `POST /api/execute-k6-performance`：性能压测执行，通过 Tekton TaskRun 调度。
  - `GET /api/k6-results/:id`：获取 k6 执行结果。
  - `GET /api/scheduled-tasks`：获取定时任务列表。
  - `GET /api/performance-tests`：获取性能测试列表。

## 4. 示例工作流

### 4.1 API 调试 Run 示例

1. 用户创建 HTTP 请求块，配置 `POST https://api.example.com/data`，设置断言（状态码 200、响应时间 < 500ms）。
2. 连接到 Select 块，提取 JSON 字段 `data.items` 使用 JsonPath。
3. 前端提交测试配置（JSON 格式），调用 `/api/execute-k6-debug` 端点。
4. 后端根据配置生成统一的 k6 脚本模板，直接执行 k6 CLI，返回实时结果。

### 4.2 定时任务执行示例

1. 用户创建 HTTP 请求块，配置 `POST https://api.example.com/data`，设置参数化测试数据。
2. 配置定时执行（如每天 00:00），前端提交测试配置（JSON 格式），调用 `/api/execute-k6-scheduled` 端点。
3. 后端根据配置生成统一的 k6 脚本模板，创建定时任务记录，添加到 asyncq 队列。
4. 队列处理器通过 Tekton TaskRun 调度包含 k6 的 Pod 执行。

### 4.3 性能压测示例

1. 用户创建 HTTP 请求块，配置 `POST https://api.example.com/data`，设置复杂负载模型（自定义模式：支持用户自定义阶段配置），设置 thresholds（`http_req_duration: p(95)<500`）。
2. 连接到数据库查询块，配置 `SELECT * FROM users WHERE id = {{user_id}}`，验证结果。
3. 连接到 For 块，循环执行子工作流（3 次）。
4. 前端提交测试配置（JSON 格式），调用 `/api/execute-k6-performance` 端点。
5. 后端根据配置生成统一的 k6 脚本模板，创建性能测试记录，添加到 asyncq 队列。
6. 队列处理器通过 Tekton TaskRun 调度高性能 Pod 执行大规模负载测试。
7. 执行结果实时输出到 Elasticsearch，前端通过 Elasticsearch API 获取实时性能指标。

## 5. 错误处理

- **前端**：使用 MUI 的 `Alert` 组件显示 k6 执行错误（断言失败、超时、接口测试阈值未达标）。
- **后端**：解析 k6 输出中的错误信息，记录到日志和 `TestResult` 表，返回标准错误响应（如 `{ "error": "k6 execution failed: threshold exceeded" }`）。
- **k6 特定错误**：处理 `http_req_failed` 和 `thresholds` 失败，记录详细信息。

## 6. 安全性

- 使用 HTTPS 保护通信。
- 实现 JWT 用户认证。
- 加密存储 API 密钥（在 `Node.Config` 的 JSONB 字段中使用加密）。
- 定期更新 k6 及其依赖，修复安全漏洞。
- 限制 k6 脚本的执行环境，使用 Docker 沙盒运行 k6 脚本，防止恶意脚本。

## 7. 挑战与风险

| 挑战                     | 描述                                     | 缓解措施                     |
|--------------------------|------------------------------------------|------------------------------|
| React Flow 集成          | 确保多种测试对象节点无缝集成             | 开发原型，测试兼容性         |
| k6 集成                  | 确保 k6 脚本动态生成和执行的稳定性       | 测试 k6 CLI 和嵌入式运行时   |
| 场景管理                 | 支持复杂场景的可视化编排                 | 优化 React Flow 组件库       |
| 数据驱动测试             | 支持大规模测试数据的高效加载             | 优化 CSV/JSON 解析和数据库查询 |
| 实时协作                 | 实现无冲突的多用户同步                   | 使用成熟 WebSocket 库        |
| JavaScript 执行          | 确保后端安全执行 JavaScript 代码         | 使用沙盒环境（如 goja）      |
| PostgreSQL 性能          | 确保复杂测试数据的高效存储和查询         | 优化索引，使用 GORM 缓存     |
| 性能指标解析             | 解析和展示 k6 的复杂性能指标             | 使用 JSON 输出，集成 Chart.js|
| Tekton 集成              | 确保 Tekton TaskRun 调度 k6 Pod 的稳定性 | 测试 Tekton Pipeline 和 Pod 配置 |
| asyncq 队列管理          | 确保异步队列的高效处理和故障恢复         | 实现队列监控和重试机制       |
| 多执行模式协调           | 协调 API 调试、定时任务、性能压测三种模式 | 统一任务调度和结果管理       |

## 8. 功能实现优先级

| 功能类别         | 功能描述                     | 优先级 | 实现复杂度 |
|------------------|------------------------------|--------|------------|
| 核心构建元素     | 测试对象（API、数据库）      | 高     | 中         |
| 场景管理         | 可视化场景编排               | 高     | 高         |
| 性能测试         | 七种压测模式和自定义配置     | 高     | 高         |
| 接口测试         | 定时测试和断言功能           | 高     | 中         |
| k6 执行引擎      | API 调试、定时任务、性能压测  | 高     | 高         |
| Tekton 集成      | TaskRun 调度和 Pod 管理      | 高     | 高         |
| asyncq 队列      | 异步任务调度和队列管理       | 中     | 中         |
| AI 辅助          | 自然语言生成和调试           | 中     | 高         |
| 组织与协作       | 颜色高亮、注释、分组、协作   | 中     | 中         |
| 高级功能         | JavaScript、JsonPath/XPath/jq | 低     | 高         |