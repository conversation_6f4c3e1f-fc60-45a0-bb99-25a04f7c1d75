
# API Flows


## 核心构建元素


### 块 (Blocks)

- 内置 (Default) 块
  - Start 块
    - 流程入口：每个新流程模块自动包含，是流程运行的起点
    - 数据接收：可配置输入以接收来自场景（如 Webhook 调用）或 Flow Module 块的数据
  - Output 块
    - 流程出口：将当前流程模块的结果发送给引用它的 Flow Module 块
    - 数据定义：其“输入”端口实际上定义了该流程的“输出”数据结构
- 动作 (Actions) 块
  - HTTP Request 块
    - API 调用：执行 接口文档中已有的 HTTP 请求
    - 参数化：可将上游数据连接到请求变量（如路径变量、查询参数、请求体）
    - 结果处理：区分成功 (2xx) 和失败 (非 2xx 或测试失败) 输出端口
  - Flow Module 块
    - 模块化与复用：引用并运行工作区中其他已保存的流程模块
    - 函数式编程：允许将复杂逻辑封装为可重用的“函数”
    - 数据传递：通过输入端口传入数据给被引用流程的 Start 块，通过输出端口接收被引用流程的 Output 块结果
    - 状态通知：提供“Done”输出端口，指示被引用流程是否完成
- 逻辑 (Logic) 块
  - Evaluate 块
    - 数据加工：实现流程中数据处理
  - If 块
    - 分支控制：简单的If条件逻辑路径
  - Switch 块
    - 分支控制Condition：根据布尔条件执行不同的逻辑路径
  - Delay 块
    - 延迟等待
- 循环 (Loop) 块
  - For 块
    - 列表迭代：遍历数组或列表中的每个元素
  - Repeat 块
    - 执行固定次数的重试、数据生成等
  - Collection 块
    - 收集循环输出数据
- 数据 (Data) 块
  - String 块
    - 创建和输出文本字符串
  - Bool 块
    - 创建和输出布尔值 (True/False)
  - Number 块
    - 创建和输出数字（整数或小数）
  - Null 块
    - 输出 null 值
  - Now 块
    - 当前时间戳
  - Date 块
    - 创建和输出日期
  - Date & Time 块：创建和输出日期时间
  - List 块
    - 创建和管理数据列表（数组），可包含不同类型的块
  - Object 块
    - 创建和管理键值对的结构化数据（对象），可嵌套
  - Select 块
    - JsonPath/Xpath引用上个节点的报文
  - Create Variable 块
    - 定义并为变量赋值，使其可在流程中全局访问
  - Get Variable 块
    - 在流程的任何位置获取已定义的变量值
- 可视化 (Visualize) 块
- AI 块

### 连接 (Connections)

- 块之间的数据流向和动作触发机制
- 单向：数据和触发信号单向流动
- 一对多/多对一：一个输出可连接多个输入，多个输出可连接一个输入

## 高级功能与开发


### JavaScript 支持

- 在某些块（如 Evaluate）中编写自定义逻辑和复杂数据操作

### JsonPath（jq）/Xpath

- 高效地从复杂 JSON / XML 结构中提取、过滤和重塑数据

## 组织与协作


### 颜色高亮：为单个块或组着色，增强视觉区分度


### 文件夹：在侧边栏中组织流程模块，便于工作区管理和导航


## AI 辅助


### AI Agent，可通过自然语言辅助流程构建


### AI Request：调用AI请求

