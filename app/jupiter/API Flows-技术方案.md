# API Flows 技术方案

API Flows 是一个专注于接口测试和性能压测的平台，通过可视化工作流编排支持多种测试对象（API、数据库）、场景管理、性能测试和接口测试，旨在加速产品交付周期、保证交付质量。

## 1. 前端技术

- **React**：使用 JSX 和现代 JavaScript 构建单页应用。
- **React Flow**：节点式工作流编辑器，支持多种测试对象（API、数据库）、场景管理节点和拖放交互。
- **MUI**：Material-UI 组件库，提供一致、美观且响应式的用户界面。
- **k6 Integration**：动态生成 k6 JavaScript 脚本，支持接口测试（断言、参数化）、性能测试（六种压测模式、thresholds）。

## 2. 后端技术

- **Golang**：核心编程语言，提供高性能和类型安全。
- **Gin**：RESTful API 框架，暴露测试执行、结果查询和场景管理端点。
- **PostgreSQL**：关系型数据库，存储工作流、节点、连接、测试用例、测试数据和性能指标。
- **GORM**：ORM 工具，简化数据库操作。
- **jsonpath**：使用 `github.com/oliveagle/jsonpath` 实现 JsonPath 查询，用于断言和数据提取。
- **gojq**：使用 `github.com/itchyny/gojq` 实现 jq 查询，支持复杂数据处理。
- **xpath**：支持 XML 数据解析和 XPath 查询。
- **k6**：通过 `k6/http` 模块实现 HTTP 请求测试，支持接口测试断言和性能压测，集成 k6 CLI。
- **xk6-output-elasticsearch**：使用 `github.com/elastic/xk6-output-elasticsearch` 插件，将压测结果实时输出到 Elasticsearch。
- **telegraf**：使用 https://github.com/influxdata/telegraf 开源指标采集代理实现施压机性能指标监控
## 3. 实现细节

### 3.1 前端

- **工作流编辑器**：
  - 使用 React Flow 实现拖放式画布，支持测试对象节点（HTTP 请求、数据库查询）、场景管理节点（条件、循环、分支）和性能测试节点。
  - **测试对象**：
    - **API 请求**：支持配置 URL、方法、headers、body、断言（状态码、响应内容、响应时间），区分成功 (2xx) 和失败 (非 2xx 或测试失败) 输出端口。
    - **数据库查询**：支持 SQL 查询配置，验证数据库操作结果。
  - **场景管理**：
    - 提供可视化场景组件库（Start、Output、HTTP Request、逻辑块、循环块、数据块等），通过拖放和连接实现复杂测试场景。
  - **性能测试**：
    - 支持七种压测模式：
      - **并发模式**：固定虚拟用户数，持续执行指定时间
      - **轮次模式**：固定请求次数，每个虚拟用户执行指定轮次
      - **阶梯模式**：逐步增加/减少虚拟用户数，支持复杂的负载曲线
      - **错误率模式**：基于错误率动态调整负载，自动控制测试强度
      - **响应时间模式**：基于响应时间调整负载，确保性能目标
      - **每秒应答数模式**：基于 RPS（每秒请求数）控制负载
      - **自定义模式**：支持用户自定义负载模型和阶段配置
    - 配置负载参数（vus、duration、stages、thresholds、customConfig），使用 MUI 组件动态渲染。
  - **接口测试**：
    - 支持创建接口测试脚本，基于真实场景，支持定时执行（使用 MUI 的时间选择器配置）。
    - 提供丰富的断言界面，支持 JsonPath、XPath、jq 表达式。
  - 提供配置面板，使用 MUI 组件动态渲染节点属性（如 URL、断言规则、负载模式）。
  - 支持颜色高亮、注释和分组，通过 React Flow 的节点和边属性管理。
- **数据流可视化**：
  - 使用 React Flow 的边动画显示测试数据流和场景执行顺序。
- **结果面板**：
  - 显示接口测试结果（通过/失败的断言）、性能测试指标（响应时间、吞吐量、错误率）。
  - 使用 Chart.js 渲染实时性能指标曲线图（如 `http_req_duration`、请求成功率）。
  - 支持导出测试报告为 JSON、HTML 或 PDF 格式（使用 `react-pdf`）。
- **侧边栏**：
  - 显示文件夹、模板库和测试用例集，使用 MUI 树形视图（TreeView）支持层级导航。
- **测试配置提交**：
  - 前端通过约定的 JSON 数据格式与后端交互，包含测试对象配置、场景逻辑、断言和性能测试参数。
  - 根据执行场景类型提交不同的配置数据：
    - **API 调试 Run**：提交单次执行的测试配置，用于快速验证 API 功能。
    - **定时任务执行**：提交可重复执行的测试配置，支持参数化测试。
    - **性能压测**：提交包含复杂负载模型的测试配置，支持多种压测模式。
  - 示例测试配置提交逻辑：
    ```jsx
    // TestConfigSubmitter.jsx: 提交测试配置到后端
    import React, { useState } from 'react';
    import { Button, Box, FormControl, InputLabel, Select, MenuItem } from '@mui/material';

    const TestConfigSubmitter = ({ nodes, edges, executionType }) => {
      const [submitStatus, setSubmitStatus] = useState('');

      // 根据执行类型提交不同的测试配置
      const submitTestConfig = () => {
        let testConfig = {};
        
        switch (executionType) {
          case 'debug':
            // API 调试 Run - 单次执行，快速验证
            testConfig = generateDebugConfig(nodes);
            break;
          case 'scheduled':
            // 定时任务执行 - 可重复执行，支持参数化
            testConfig = generateScheduledConfig(nodes);
            break;
          case 'performance':
            // 性能压测 - 复杂负载模型
            testConfig = generatePerformanceConfig(nodes);
            break;
          default:
            testConfig = generateDefaultConfig(nodes);
        }
        
        executeTestConfig(testConfig, executionType);
      };

      // API 调试 Run 配置生成
      const generateDebugConfig = (nodes) => {
        return {
          executionType: 'debug',
          workflowId: nodes[0]?.workflowId,
          testConfig: {
            vus: 1,
            duration: '10s',
            thresholds: {
              'http_req_duration': 'p(95)<1000',
              'http_req_failed': 'rate<0.01'
            },
            requests: extractRequestConfigs(nodes)
          }
        };
      };

      // 定时任务执行配置生成
      const generateScheduledConfig = (nodes) => {
        return {
          executionType: 'scheduled',
          workflowId: nodes[0]?.workflowId,
          schedule: nodes[0]?.data?.config?.schedule || '0 0 * * *',
          testData: nodes[0]?.data?.config?.testData || '',
          testConfig: {
            vus: nodes[0]?.data?.config?.vus || 5,
            duration: nodes[0]?.data?.config?.duration || '60s',
            thresholds: {
              'http_req_duration': 'p(95)<2000',
              'http_req_failed': 'rate<0.05'
            },
            requests: extractRequestConfigs(nodes)
          }
        };
      };

      // 性能压测配置生成
      const generatePerformanceConfig = (nodes) => {
        return {
          executionType: 'performance',
          workflowId: nodes[0]?.workflowId,
          testConfig: {
            loadModel: nodes[0]?.data?.config?.loadModel || 'ramping-vus',
            vus: nodes[0]?.data?.config?.vus || 50,
            duration: nodes[0]?.data?.config?.duration || '300s',
            thresholds: nodes[0]?.data?.config?.thresholds || {
              'http_req_duration': 'p(95)<500',
              'http_req_failed': 'rate<0.01',
              'http_reqs': 'rate>100'
            },
            requests: extractRequestConfigs(nodes)
          }
        };
      };

      // 提取请求配置
      const extractRequestConfigs = (nodes) => {
        return nodes
          .filter(node => node.type === 'httpRequest')
          .map(node => ({
            url: node.data.config.url,
            method: node.data.config.method,
            headers: node.data.config.headers || {},
            body: node.data.config.body || null,
            timeout: node.data.config.timeout || 60000,
            assertions: node.data.config.assertions || [],
            tags: node.data.config.tags || {}
          }));
      };

      // 调用后端 API 执行测试配置
      const executeTestConfig = async (testConfig, executionType) => {
        const endpoint = getExecutionEndpoint(executionType);
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(testConfig),
        });
        const result = await response.json();
        setSubmitStatus(result.message || '配置提交成功');
        console.log(result);
      };

      // 根据执行类型获取对应的 API 端点
      const getExecutionEndpoint = (type) => {
        switch (type) {
          case 'debug':
            return '/api/execute-k6-debug';
          case 'scheduled':
            return '/api/execute-k6-scheduled';
          case 'performance':
            return '/api/execute-k6-performance';
          default:
            return '/api/execute-k6';
        }
      };

      return (
        <Box sx={{ p: 2 }}>
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>执行类型</InputLabel>
            <Select
              value={executionType}
              label="执行类型"
              onChange={(e) => setExecutionType(e.target.value)}
            >
              <MenuItem value="debug">API 调试 Run</MenuItem>
              <MenuItem value="scheduled">定时任务执行</MenuItem>
              <MenuItem value="performance">性能压测</MenuItem>
            </Select>
          </FormControl>
          <Button variant="contained" onClick={submitTestConfig}>提交测试配置</Button>
          {submitStatus && (
            <Box sx={{ mt: 2, p: 1, bgcolor: 'success.light', borderRadius: 1 }}>
              {submitStatus}
            </Box>
          )}
        </Box>
      );
    };

    export default TestConfigSubmitter;
    ```

### 3.2 后端

- **工作流编排**：
  - 使用自定义的工作流引擎定义测试工作流，包含测试对象（API、数据库）、场景管理和接口测试逻辑。
  - **测试对象**：
    - **API 请求**：支持 HTTP 请求，集成 k6 的 `http` 模块，区分成功/失败输出端口。
    - **数据库查询**：支持 SQL 查询，验证数据库操作结果。
  - **场景管理**：
    - 支持可视化场景编排，通过自定义引擎实现节点间的流程控制（条件、循环、分支）。
  - **性能测试**：
    - 支持七种压测模式（并发、轮次、阶梯、错误率、响应时间、每秒应答数、自定义），通过 k6 的 `scenarios` 配置实现。
    - 提供实时性能指标监控，通过 Elasticsearch 实时输出和存储。
  - **接口测试**：
    - 支持定时任务（使用 `github.com/robfig/cron`）。
    - 支持丰富的断言功能，基于 k6 的 `check` 模块。

- **k6 执行引擎**：
  - **API 调试 Run**：服务端直接通过 k6 CLI 执行，适用于快速验证和调试场景。
    ```go
    // k6DebugExecutor.go: API 调试 Run 执行器
    package main

    import (
      "encoding/json"
      "fmt"
      "io/ioutil"
      "os"
      "os/exec"
      "time"
      "github.com/gin-gonic/gin"
      "github.com/google/uuid"
    )

    // K6DebugExecutor 处理 API 调试 Run 的 k6 执行
    type K6DebugExecutor struct {
      db *gorm.DB
    }

    func (e *K6DebugExecutor) ExecuteDebug(c *gin.Context) {
      var request struct {
        ExecutionType string                 `json:"executionType"`
        WorkflowID    string                 `json:"workflowId"`
        TestConfig    map[string]interface{} `json:"testConfig"`
      }
      
      if err := c.ShouldBindJSON(&request); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
      }

      // 根据测试配置生成统一的 k6 脚本
      k6Script, err := e.generateK6Script(request.TestConfig, "debug")
      if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
      }

      // 生成临时 k6 脚本文件
      scriptFile, err := ioutil.TempFile("", "k6-debug-*.js")
      if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
      }
      defer os.Remove(scriptFile.Name())
      
      if _, err := scriptFile.Write([]byte(k6Script)); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
      }
      scriptFile.Close()

      // 直接执行 k6 CLI，使用 elasticsearch 输出插件
      cmd := exec.Command("k6", "run", scriptFile.Name(), "--quiet", "--out", "elasticsearch=http://elasticsearch:9200")
      output, err := cmd.CombinedOutput()
      
      if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{
          "error": fmt.Sprintf("k6 execution failed: %v, output: %s", err, string(output)),
        })
        return
      }

      // 由于使用 elasticsearch 输出，不再需要解析 JSON 输出
      // 结果会实时写入 elasticsearch
      result := map[string]interface{}{
        "message": "Test executed successfully, results sent to Elasticsearch",
        "elasticsearch_url": "http://elasticsearch:9200",
      }

      // 保存调试结果到数据库（只保存执行状态）
      debugResult := TestResult{
        ID:         uuid.New().String(),
        WorkflowID: request.WorkflowID,
        Type:       "debug",
        Data:       "Results sent to Elasticsearch",
        CreatedAt:  time.Now(),
      }
      
      if err := e.db.Create(&debugResult).Error; err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{
          "error": fmt.Sprintf("failed to save debug result: %v", err),
        })
        return
      }

      c.JSON(http.StatusOK, result)
    }

    // generateK6Script 根据测试配置生成统一的 k6 脚本
    func (e *K6DebugExecutor) generateK6Script(testConfig map[string]interface{}, executionType string) (string, error) {
      // 提取配置参数
      vus := testConfig["vus"].(int)
      duration := testConfig["duration"].(string)
      thresholds := testConfig["thresholds"].(map[string]interface{})
      requests := testConfig["requests"].([]interface{})
      
      // 根据执行类型调整配置
      var loadModel string
      var stages []map[string]interface{}
      
      switch executionType {
      case "debug":
        loadModel = "constant-vus"
        stages = []map[string]interface{}{
          {"duration": "10s", "target": vus},
        }
      case "scheduled":
        loadModel = "constant-vus"
        stages = []map[string]interface{}{
          {"duration": duration, "target": vus},
        }
      case "performance":
        loadModel = testConfig["loadModel"].(string)
        if loadModel == "custom" {
          // 使用自定义配置
          customConfig := testConfig["customConfig"].(map[string]interface{})
          stages = customConfig["stages"].([]map[string]interface{})
        } else {
          // 使用默认的阶梯模式
          stages = []map[string]interface{}{
            {"duration": "30s", "target": vus / 3},
            {"duration": duration, "target": vus},
            {"duration": "30s", "target": 0},
          }
        }
      default:
        return "", fmt.Errorf("unsupported execution type: %s", executionType)
      }

      // 生成统一的 k6 脚本模板
      script := fmt.Sprintf(`
        import http from 'k6/http';
        import { check } from 'k6';
        import { SharedArray } from 'k6/data';

        // 支持数据驱动测试（定时任务和性能测试）
        const testData = new SharedArray('testData', function() {
          return JSON.parse(__ENV.TEST_DATA || '[]');
        });

        export const options = {
          scenarios: {
            load_test: {
              executor: '%s',
              startVUs: 0,
              stages: %s,
              gracefulRampDown: '10s',
            },
          },
          thresholds: %s,
        };

        export default function () {
          // 获取测试数据（如果有）
          const data = testData.length > 0 ? testData[__VU %% testData.length] : {};
      `, loadModel, e.formatStages(stages), e.formatThresholds(thresholds))

      // 添加请求逻辑
      for _, req := range requests {
        request := req.(map[string]interface{})
        script += e.generateRequestCode(request)
      }

      script += `
        }
      `
      return script, nil
    }

    // generateRequestCode 生成请求代码
    func (e *K6DebugExecutor) generateRequestCode(request map[string]interface{}) string {
      url := request["url"].(string)
      method := request["method"].(string)
      headers := request["headers"].(map[string]interface{})
      body := request["body"]
      timeout := request["timeout"].(int)
      assertions := request["assertions"].([]interface{})
      tags := request["tags"].(map[string]interface{})

      code := fmt.Sprintf(`
          const res = http.%s('%s', %s, {
            headers: %s,
            tags: %s,
            timeout: %d
          });
          check(res, {
      `, strings.ToLower(method), url, e.formatBody(body), e.formatJSON(headers), e.formatJSON(tags), timeout)

      // 添加断言
      for _, assertion := range assertions {
        ass := assertion.(map[string]interface{})
        name := ass["name"].(string)
        expression := ass["expression"].(string)
        code += fmt.Sprintf(`            '%s': (r) => %s,`, name, expression)
      }

      code += `
          });
      `
      return code
    }

    // formatStages 格式化阶段配置
    func (e *K6DebugExecutor) formatStages(stages []map[string]interface{}) string {
      return e.formatJSON(stages)
    }

    // formatThresholds 格式化阈值
    func (e *K6DebugExecutor) formatThresholds(thresholds map[string]interface{}) string {
      return e.formatJSON(thresholds)
    }

    // formatBody 格式化请求体
    func (e *K6DebugExecutor) formatBody(body interface{}) string {
      if body == nil {
        return "null"
      }
      return e.formatJSON(body)
    }

    // formatJSON 格式化 JSON
    func (e *K6DebugExecutor) formatJSON(data interface{}) string {
      jsonBytes, _ := json.Marshal(data)
      return string(jsonBytes)
    }
    ```

  - **定时任务执行**：通过 Tekton TaskRun 调度包含 k6 的 Pod 执行，使用 asyncq 实现队列机制。
    ```go
    // k6ScheduledExecutor.go: 定时任务执行器
    package main

    import (
      "encoding/json"
      "fmt"
      "time"
      "github.com/gin-gonic/gin"
      "github.com/google/uuid"
      "github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
      tektonv1beta1 "github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
      metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
    )

    // K6ScheduledExecutor 处理定时任务的 k6 执行
    type K6ScheduledExecutor struct {
      db           *gorm.DB
      tektonClient *tekton.Clientset
      asyncq       *AsyncQueue
    }

    func (e *K6ScheduledExecutor) ExecuteScheduled(c *gin.Context) {
      var request struct {
        ExecutionType string                 `json:"executionType"`
        WorkflowID    string                 `json:"workflowId"`
        Schedule      string                 `json:"schedule"`
        TestData      string                 `json:"testData"`
        TestConfig    map[string]interface{} `json:"testConfig"`
      }
      
      if err := c.ShouldBindJSON(&request); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
      }

      // 根据测试配置生成统一的 k6 脚本
      k6Script, err := e.generateK6Script(request.TestConfig, "scheduled")
      if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
      }

      // 创建定时任务记录
      scheduledTask := ScheduledTask{
        ID:         uuid.New().String(),
        WorkflowID: request.WorkflowID,
        Script:     k6Script,
        Schedule:   request.Schedule,
        TestData:   request.TestData,
        Status:     "pending",
        CreatedAt:  time.Now(),
      }
      
      if err := e.db.Create(&scheduledTask).Error; err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
      }

      // 添加到 asyncq 队列
      job := &K6Job{
        ID:           scheduledTask.ID,
        Script:       k6Script,
        WorkflowID:   request.WorkflowID,
        TestData:     request.TestData,
        ExecutionType: "scheduled",
      }
      
      e.asyncq.Enqueue(job)

      c.JSON(http.StatusOK, gin.H{
        "message": "Scheduled task queued successfully",
        "taskId":  scheduledTask.ID,
      })
    }

    // AsyncQueue 异步队列实现
    type AsyncQueue struct {
      jobs chan *K6Job
      db   *gorm.DB
    }

    func NewAsyncQueue(db *gorm.DB) *AsyncQueue {
      q := &AsyncQueue{
        jobs: make(chan *K6Job, 100),
        db:   db,
      }
      go q.processJobs()
      return q
    }

    func (q *AsyncQueue) Enqueue(job *K6Job) {
      q.jobs <- job
    }

    func (q *AsyncQueue) processJobs() {
      for job := range q.jobs {
        go q.executeK6Job(job)
      }
    }

    func (q *AsyncQueue) executeK6Job(job *K6Job) {
      // 创建 Tekton TaskRun，配置 elasticsearch 输出
      taskRun := &tektonv1beta1.TaskRun{
        ObjectMeta: metav1.ObjectMeta{
          Name:      fmt.Sprintf("k6-scheduled-%s", job.ID),
          Namespace: "api-flows",
        },
        Spec: tektonv1beta1.TaskRunSpec{
          TaskRef: &tektonv1beta1.TaskRef{
            Name: "k6-scheduled-task",
          },
          Params: []tektonv1beta1.Param{
            {
              Name:  "script",
              Value: tektonv1beta1.ArrayOrString{StringVal: job.Script},
            },
            {
              Name:  "test-data",
              Value: tektonv1beta1.ArrayOrString{StringVal: job.TestData},
            },
            {
              Name:  "workflow-id",
              Value: tektonv1beta1.ArrayOrString{StringVal: job.WorkflowID},
            },
            {
              Name:  "elasticsearch-url",
              Value: tektonv1beta1.ArrayOrString{StringVal: "http://elasticsearch:9200"},
            },
          },
        },
      }

      // 创建 TaskRun
      _, err := q.tektonClient.TektonV1beta1().TaskRuns("api-flows").Create(
        context.Background(), taskRun, metav1.CreateOptions{},
      )
      
      if err != nil {
        // 更新任务状态为失败
        q.db.Model(&ScheduledTask{}).Where("id = ?", job.ID).Update("status", "failed")
        return
      }

      // 更新任务状态为执行中
      q.db.Model(&ScheduledTask{}).Where("id = ?", job.ID).Update("status", "running")
    }
    ```

  - **性能压测执行**：通过 Tekton TaskRun 调度包含 k6 的 Pod 执行，支持大规模负载测试。
    ```go
    // k6PerformanceExecutor.go: 性能压测执行器
    package main

    import (
      "encoding/json"
      "fmt"
      "time"
      "github.com/gin-gonic/gin"
      "github.com/google/uuid"
      "github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
      tektonv1beta1 "github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
      metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
    )

    // K6PerformanceExecutor 处理性能压测的 k6 执行
    type K6PerformanceExecutor struct {
      db           *gorm.DB
      tektonClient *tekton.Clientset
      asyncq       *AsyncQueue
    }

    func (e *K6PerformanceExecutor) ExecutePerformance(c *gin.Context) {
      var request struct {
        ExecutionType string                 `json:"executionType"`
        WorkflowID    string                 `json:"workflowId"`
        TestConfig    map[string]interface{} `json:"testConfig"`
      }
      
      if err := c.ShouldBindJSON(&request); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
      }

      // 根据测试配置生成统一的 k6 脚本
      k6Script, err := e.generateK6Script(request.TestConfig, "performance")
      if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
      }

      // 创建性能测试记录
      performanceTest := PerformanceTest{
        ID:         uuid.New().String(),
        WorkflowID: request.WorkflowID,
        Script:     k6Script,
        LoadConfig: request.TestConfig["loadConfig"].(map[string]interface{}),
        Thresholds: request.TestConfig["thresholds"].(map[string]string),
        Status:     "pending",
        CreatedAt:  time.Now(),
      }
      
      if err := e.db.Create(&performanceTest).Error; err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
      }

      // 添加到 asyncq 队列
      job := &K6Job{
        ID:           performanceTest.ID,
        Script:       k6Script,
        WorkflowID:   request.WorkflowID,
        LoadConfig:   request.TestConfig["loadConfig"].(map[string]interface{}),
        Thresholds:   request.TestConfig["thresholds"].(map[string]string),
        ExecutionType: "performance",
      }
      
      e.asyncq.Enqueue(job)

      c.JSON(http.StatusOK, gin.H{
        "message": "Performance test queued successfully",
        "testId":  performanceTest.ID,
      })
    }

    func (e *K6PerformanceExecutor) executePerformanceJob(job *K6Job) {
      // 创建高性能的 Tekton TaskRun，配置 elasticsearch 输出
      taskRun := &tektonv1beta1.TaskRun{
        ObjectMeta: metav1.ObjectMeta{
          Name:      fmt.Sprintf("k6-performance-%s", job.ID),
          Namespace: "api-flows",
        },
        Spec: tektonv1beta1.TaskRunSpec{
          TaskRef: &tektonv1beta1.TaskRef{
            Name: "k6-performance-task",
          },
          Params: []tektonv1beta1.Param{
            {
              Name:  "script",
              Value: tektonv1beta1.ArrayOrString{StringVal: job.Script},
            },
            {
              Name:  "workflow-id",
              Value: tektonv1beta1.ArrayOrString{StringVal: job.WorkflowID},
            },
            {
              Name:  "load-config",
              Value: tektonv1beta1.ArrayOrString{StringVal: json.Marshal(job.LoadConfig)},
            },
            {
              Name:  "thresholds",
              Value: tektonv1beta1.ArrayOrString{StringVal: json.Marshal(job.Thresholds)},
            },
            {
              Name:  "elasticsearch-url",
              Value: tektonv1beta1.ArrayOrString{StringVal: "http://elasticsearch:9200"},
            },
          },
          // 配置资源限制
          PodTemplate: &tektonv1beta1.PodTemplate{
            Spec: corev1.PodSpec{
              Containers: []corev1.Container{
                {
                  Name: "k6",
                  Resources: corev1.ResourceRequirements{
                    Requests: corev1.ResourceList{
                      corev1.ResourceCPU:    resource.MustParse("2"),
                      corev1.ResourceMemory: resource.MustParse("4Gi"),
                    },
                    Limits: corev1.ResourceList{
                      corev1.ResourceCPU:    resource.MustParse("8"),
                      corev1.ResourceMemory: resource.MustParse("16Gi"),
                    },
                  },
                },
              },
            },
          },
        },
      }

      // 创建 TaskRun
      _, err := e.tektonClient.TektonV1beta1().TaskRuns("api-flows").Create(
        context.Background(), taskRun, metav1.CreateOptions{},
      )
      
      if err != nil {
        e.db.Model(&PerformanceTest{}).Where("id = ?", job.ID).Update("status", "failed")
        return
      }

      e.db.Model(&PerformanceTest{}).Where("id = ?", job.ID).Update("status", "running")
    }
    ```

  - **块类型实现**：
    - **Start 块**：流程入口，接收外部输入数据（如 Webhook 或 Flow Module 传入）。
      ```go
      package main

      // StartTool 是流程的起点，自动包含在每个流程模块中，用于接收外部输入
      // 功能：接收来自场景（如 Webhook）或 Flow Module 块的数据，作为流程的入口
      // 输入：外部传入的数据（如 JSON 对象）
      // 输出：直接传递输入数据到下游节点
      type StartTool struct {
        BaseTool
      }

      func (t *StartTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        return input, nil
      }
      ```
    - **Output 块**：流程出口，定义流程的输出数据结构。
      ```go
      package main

      // OutputTool 是流程的出口，将当前流程的结果发送给引用它的 Flow Module 块
      // 功能：定义流程的输出数据结构，传递给父流程
      // 输入：上游节点的数据
      // 输出：封装后的输出数据
      type OutputTool struct {
        BaseTool
      }

      func (t *OutputTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        return map[string]interface{}{"output": input}, nil
      }
      ```
    - **HTTP Request 块**：执行 API 调用，支持参数化和成功/失败输出端口。
      ```go
      package main

      import (
        "encoding/json"
        "fmt"
        "io/ioutil"
        "os"
        "os/exec"
        "strings"
        "github.com/oliveagle/jsonpath"
      )

      // APITool 执行 HTTP 请求，支持接口测试和性能压测
      // 功能：执行接口文档中定义的 HTTP 请求，支持参数化（路径变量、查询参数、请求体）
      // 输入：上游节点的数据，用于参数化请求
      // 输出：
      //   - success：HTTP 状态码为 2xx 且断言通过
      //   - failure：HTTP 状态码非 2xx 或断言失败
      type APITool struct {
        BaseTool
        URL         string            // HTTP 请求的目标 URL
        Method      string            // HTTP 方法（GET、POST 等）
        Headers     map[string]string // 请求头
        Body        string            // 请求体（JSON 格式）
        Timeout     int               // 超时时间（毫秒）
        VUs         int               // k6 虚拟用户数
        Duration    string            // k6 测试持续时间
        Tags        map[string]string // k6 自定义标签
        Assertions  []Assertion       // 断言规则
        DataSource  string            // 数据源（CSV/JSON 文件路径或数据库查询）
        LoadModel   string            // 负载模型（ramping-vus、constant-arrival-rate、custom 等）
        Thresholds  map[string]string // 性能阈值
        CustomConfig map[string]interface{} // 自定义配置
      }

      // Assertion 定义断言规则
      type Assertion struct {
        Name       string // 断言名称
        Expression string // 断言表达式（如 "r.status === 200"）
      }

      func (t *APITool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        // 从输入中获取动态配置（若有）
        url, ok := input["url"].(string)
        if !ok {
          url = t.URL
        }
        method, ok := input["method"].(string)
        if !ok {
          method = t.Method
        }
        headers := t.Headers
        if inputHeaders, ok := input["headers"].(map[string]interface{}); ok {
          headers = make(map[string]string)
          for k, v := range inputHeaders {
            headers[k] = v.(string)
          }
        }
        body := t.Body
        if inputBody, ok := input["body"].(string); ok {
          body = inputBody
        }

        // 处理数据驱动测试
        var testData []map[string]interface{}
        if t.DataSource != "" {
          testData, err := loadTestData(t.DataSource)
          if err != nil {
            return nil, fmt.Errorf("failed to load test data: %v", err)
          }
        } else {
          testData = []map[string]interface{}{input}
        }

        // 生成 k6 脚本
        k6Script := `
          import http from 'k6/http';
          import { check } from 'k6';

          export const options = {
            scenarios: {
              load_test: {
                executor: '` + t.LoadModel + `',
                startVUs: 0,
                stages: [
                  { duration: '10s', target: ` + fmt.Sprintf("%d", t.VUs/2) + ` },
                  { duration: '` + t.Duration + `', target: ` + fmt.Sprintf("%d", t.VUs) + ` },
                  { duration: '10s', target: 0 },
                ],
                gracefulRampDown: '5s',
              },
            },
            thresholds: ` + json.Marshal(t.Thresholds) + `,
          };

          export default function () {
        `

        // 为每个测试数据生成请求
        success := true
        results := []map[string]interface{}{}
        for _, data := range testData {
          resolvedURL := resolveTemplate(url, data)
          resolvedBody := resolveTemplate(body, data)
          k6Script += `
            const res = http.` + strings.ToLower(method) + `('` + resolvedURL + `', ` + resolvedBody + `, {
              headers: ` + json.Marshal(headers) + `,
              tags: ` + json.Marshal(t.Tags) + `,
              timeout: ` + fmt.Sprintf("%d", t.Timeout) + `
            });
            check(res, {
              ${t.Assertions.map(a => `'` + a.Name + `': (r) => ` + a.Expression).join(',\n')}
            }, (result) => {
              if (!result) {
                console.log('Assertion failed for ${resolvedURL}');
                return false;
              }
              return true;
            });
          `
        }

        k6Script += `
          }
        `

        // 将 k6 脚本写入临时文件
        scriptFile, err := ioutil.TempFile("", "k6-script-*.js")
        if err != nil {
          return nil, err
        }
        defer os.Remove(scriptFile.Name())
        if _, err := scriptFile.Write([]byte(k6Script)); err != nil {
          return nil, err
        }
        scriptFile.Close()

        // 执行 k6 脚本
        cmd := exec.Command("k6", "run", scriptFile.Name(), "--quiet", "--out", "json=-")
        output, err := cmd.CombinedOutput()
        if err != nil {
          return map[string]interface{}{
            "failure": true,
            "error":   fmt.Sprintf("k6 execution failed: %v, output: %s", err, string(output)),
          }, nil
        }

        // 解析 k6 输出（JSON 格式）
        var k6Result map[string]interface{}
        if err := json.Unmarshal(output, &k6Result); err != nil {
          return map[string]interface{}{
            "failure": true,
            "error":   fmt.Sprintf("failed to parse k6 output: %v", err),
          }, nil
        }

        // 检查断言结果
        metrics, ok := k6Result["metrics"].(map[string]interface{})
        if !ok || metrics["checks"] == nil {
          success = false
        } else {
          checks := metrics["checks"].(map[string]interface{})
          success = checks["passes"].(float64) > 0
        }

        // 返回结果，区分成功/失败输出端口
        outputPort := "success"
        if !success {
          outputPort = "failure"
        }
        return map[string]interface{}{
          outputPort: true,
          "data":     k6Result,
          "metrics":  k6Result["metrics"],
          "testData": testData,
        }, nil
      }

      // resolveTemplate 替换模板中的占位符
      func resolveTemplate(template string, data map[string]interface{}) string {
        for key, value := range data {
          placeholder := fmt.Sprintf("{{.%s}}", key)
          template = strings.ReplaceAll(template, placeholder, fmt.Sprintf("%v", value))
        }
        return template
      }

      // loadTestData 从 CSV/JSON 或数据库加载测试数据
      func loadTestData(source string) ([]map[string]interface{}, error) {
        if strings.HasSuffix(source, ".csv") {
          return parseCSV(source)
        } else if strings.HasSuffix(source, ".json") {
          return parseJSON(source)
        } else {
          return queryTestDataFromDB(source)
        }
      }
      ```
    - **Flow Module 块**：引用其他流程模块，实现模块化复用。
      ```go
      package main

      import (
        "github.com/xeipuuv/eino"
      )

      // FlowModuleTool 引用并运行工作区中其他已保存的流程模块
      // 功能：封装复杂逻辑为可重用的"函数"，支持数据传递和状态通知
      // 输入：外部传入的数据，传递给被引用流程的 Start 块
      // 输出：
      //   - result：被引用流程的 Output 块结果
      //   - done：指示被引用流程是否完成
      type FlowModuleTool struct {
        BaseTool
        WorkflowID string
      }

      func (t *FlowModuleTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        chain, err := loadWorkflow(t.WorkflowID)
        if err != nil {
          return nil, err
        }
        result, err := chain.Execute(input)
        if err != nil {
          return nil, err
        }
        return map[string]interface{}{
          "result": result,
          "done":   true,
        }, nil
      }
      ```
    - **Evaluate 块**：实现数据处理逻辑。
      ```go
      package main

      import (
        "github.com/robertkrimen/otto"
        "github.com/xeipuuv/eino"
      )

      // EvaluateTool 执行自定义 JavaScript 脚本，实现数据处理
      // 功能：在流程中执行复杂数据操作
      // 输入：上游节点的数据
      // 输出：脚本执行结果
      type EvaluateTool struct {
        BaseTool
        Script string
      }

      func (t *EvaluateTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        vm := otto.New()
        for k, v := range input {
          vm.Set(k, v)
        }
        value, err := vm.Run(t.Script)
        if err != nil {
          return nil, err
        }
        result, _ := value.Export()
        return map[string]interface{}{"result": result}, nil
      }
      ```
    - **If 块**：实现简单的条件分支。
      ```go
      package main

      import (
        "github.com/robertkrimen/otto"
        "github.com/xeipuuv/eino"
      )

      // IfTool 执行简单的条件分支逻辑
      // 功能：根据布尔条件选择执行路径
      // 输入：上游节点的数据
      // 输出：布尔结果（true/false）
      type IfTool struct {
        BaseTool
        Condition string
      }

      func (t *IfTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        vm := otto.New()
        for k, v := range input {
          vm.Set(k, v)
        }
        value, err := vm.Run(t.Condition)
        if err != nil {
          return nil, err
        }
        result, _ := value.ToBoolean()
        return map[string]interface{}{"result": result}, nil
      }
      ```
    - **Switch 块**：实现多条件分支。
      ```go
      package main

      import (
        "github.com/robertkrimen/otto"
        "github.com/xeipuuv/eino"
      )

      // SwitchTool 根据多个条件执行不同的逻辑路径
      // 功能：根据条件选择执行路径
      // 输入：上游节点的数据
      // 输出：匹配的条件键
      type SwitchTool struct {
        BaseTool
        Cases map[string]string
      }

      func (t *SwitchTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        vm := otto.New()
        for k, v := range input {
          vm.Set(k, v)
        }
        for key, script := range t.Cases {
          value, err := vm.Run(script)
          if err != nil {
            return nil, err
          }
          if ok, _ := value.ToBoolean(); ok {
            return map[string]interface{}{"case": key}, nil
          }
        }
        return map[string]interface{}{}, nil
      }
      ```
    - **Delay 块**：实现延迟等待。
      ```go
      package main

      import (
        "time"
        "github.com/xeipuuv/eino"
      )

      // DelayTool 实现流程中的延迟等待
      // 功能：暂停执行指定时间
      // 输入：上游节点的数据
      // 输出：直接传递输入数据
      type DelayTool struct {
        BaseTool
        Duration int64
      }

      func (t *DelayTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        time.Sleep(time.Duration(t.Duration) * time.Millisecond)
        return input, nil
      }
      ```
    - **For 块**：遍历数组或列表。
      ```go
      package main

      import (
        "fmt"
        "github.com/xeipuuv/eino"
      )

      // ForTool 遍历数组或列表中的每个元素
      // 功能：对输入数组进行迭代，执行子工作流
      // 输入：包含数组的输入数据
      // 输出：迭代结果列表
      type ForTool struct {
        BaseTool
        ArrayKey string
      }

      func (t *ForTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        array, ok := input[t.ArrayKey].([]interface{})
        if !ok {
          return nil, fmt.Errorf("invalid array input for key: %s", t.ArrayKey)
        }
        results := []interface{}{}
        for _, item := range array {
          subChain, err := eino.NewChain[map[string]interface{}, *eino.Message]()
          if err != nil {
            return nil, err
          }
          subChain.AddNode("iteration", &GenericTool{Input: item})
          result, err := subChain.Execute(map[string]interface{}{"item": item})
          if err != nil {
            return nil, err
          }
          results = append(results, result)
        }
        return map[string]interface{}{"results": results}, nil
      }
      ```
    - **Repeat 块**：执行固定次数的循环。
      ```go
      package main

      import (
        "github.com/xeipuuv/eino"
      )

      // RepeatTool 执行固定次数的循环
      // 功能：重复执行子工作流指定次数
      // 输入：上游节点的数据
      // 输出：循环结果列表
      type RepeatTool struct {
        BaseTool
        Count int
      }

      func (t *RepeatTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        results := []interface{}{}
        for i := 0; i < t.Count; i++ {
          subChain, err := eino.NewChain[map[string]interface{}, *eino.Message]()
          if err != nil {
            return nil, err
          }
          result, err := subChain.Execute(input)
          if err != nil {
            return nil, err
          }
          results = append(results, result)
        }
        return map[string]interface{}{"results": results}, nil
      }
      ```
    - **Collection 块**：收集循环输出数据。
      ```go
      package main

      import (
        "fmt"
        "github.com/xeipuuv/eino"
      )

      // CollectionTool 收集循环节点的输出数据
      // 功能：将循环产生的多个结果聚合成一个列表
      // 输入：循环节点的输出
      // 输出：聚合后的结果列表
      type CollectionTool struct {
        BaseTool
      }

      func (t *CollectionTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        results, ok := input["results"].([]interface{})
        if !ok {
          return nil, fmt.Errorf("invalid results input")
        }
        return map[string]interface{}{"collected": results}, nil
      }
      ```
    - **String 块**：创建文本字符串。
      ```go
      package main

      import (
        "github.com/xeipuuv/eino"
      )

      // StringTool 创建和输出文本字符串
      // 功能：生成静态字符串值
      // 输入：无
      // 输出：配置的字符串
      type StringTool struct {
        BaseTool
        Value string
      }

      func (t *StringTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        return map[string]interface{}{"value": t.Value}, nil
      }
      ```
    - **Bool 块**：创建布尔值。
      ```go
      package main

      import (
        "github.com/xeipuuv/eino"
      )

      // BoolTool 创建和输出布尔值
      // 功能：生成静态布尔值（true/false）
      // 输入：无
      // 输出：配置的布尔值
      type BoolTool struct {
        BaseTool
        Value bool
      }

      func (t *BoolTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        return map[string]interface{}{"value": t.Value}, nil
      }
      ```
    - **Number 块**：创建数字。
      ```go
      package main

      import (
        "github.com/xeipuuv/eino"
      )

      // NumberTool 创建和输出数字
      // 功能：生成静态整数或小数值
      // 输入：无
      // 输出：配置的数字
      type NumberTool struct {
        BaseTool
        Value float64
      }

      func (t *NumberTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        return map[string]interface{}{"value": t.Value}, nil
      }
      ```
    - **Null 块**：输出 null 值。
      ```go
      package main

      import (
        "github.com/xeipuuv/eino"
      )

      // NullTool 输出 null 值
      // 功能：生成空值
      // 输入：无
      // 输出：null
      type NullTool struct {
        BaseTool
      }

      func (t *NullTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        return map[string]interface{}{"value": nil}, nil
      }
      ```
    - **Now 块**：生成当前时间戳。
      ```go
      package main

      import (
        "time"
        "github.com/xeipuuv/eino"
      )

      // NowTool 生成当前时间戳
      // 功能：输出当前 Unix 时间戳
      // 输入：无
      // 输出：当前时间戳
      type NowTool struct {
        BaseTool
      }

      func (t *NowTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        return map[string]interface{}{"value": time.Now().Unix()}, nil
      }
      ```
    - **Date 块**：创建日期。
      ```go
      package main

      import (
        "time"
        "github.com/xeipuuv/eino"
      )

      // DateTool 创建和输出日期
      // 功能：生成指定格式的日期
      // 输入：无
      // 输出：配置的日期值
      type DateTool struct {
        BaseTool
        Value string
      }

      func (t *DateTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        date, err := time.Parse("2006-01-02", t.Value)
        if err != nil {
          return nil, err
        }
        return map[string]interface{}{"value": date}, nil
      }
      ```
    - **Date & Time 块**：创建日期时间。
      ```go
      package main

      import (
        "time"
        "github.com/xeipuuv/eino"
      )

      // DateTimeTool 创建和输出日期时间
      // 功能：生成指定格式的日期时间
      // 输入：无
      // 输出：配置的日期时间值
      type DateTimeTool struct {
        BaseTool
        Value string
      }

      func (t *DateTimeTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        dateTime, err := time.Parse("2006-01-02T15:04:05", t.Value)
        if err != nil {
          return nil, err
        }
        return map[string]interface{}{"value": dateTime}, nil
      }
      ```
    - **List 块**：创建和管理数据列表。
      ```go
      package main

      import (
        "github.com/xeipuuv/eino"
      )

      // ListTool 创建和管理数据列表
      // 功能：生成包含不同类型数据的数组
      // 输入：无
      // 输出：配置的列表
      type ListTool struct {
        BaseTool
        Values []interface{}
      }

      func (t *ListTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        return map[string]interface{}{"value": t.Values}, nil
      }
      ```
    - **Object 块**：创建和管理键值对数据。
      ```go
      package main

      import (
        "encoding/json"
        "encoding/xml"
        "fmt"
        "github.com/antchfx/xpath"
        "github.com/itchyny/gojq"
        "github.com/oliveagle/jsonpath"
        "github.com/xeipuuv/eino"
      )

      // ObjectTool 创建和管理键值对的结构化数据
      // 功能：支持 JSON/XML 数据的提取和处理，使用 JsonPath/XPath/jq 查询
      // 输入：上游节点的 JSON/XML 数据
      // 输出：查询结果或配置的对象
      type ObjectTool struct {
        BaseTool
        Values    map[string]interface{}
        QueryType string
        Query     string
      }

      func (t *ObjectTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        data, ok := input["data"]
        if !ok {
          return map[string]interface{}{"value": t.Values}, nil
        }

        var result interface{}
        switch t.QueryType {
        case "jsonpath":
          result, err := jsonpath.JsonPathLookup(data, t.Query)
          if err != nil {
            return nil, err
          }
        case "xpath":
          xmlData, err := xml.Marshal(data)
          if err != nil {
            return nil, err
          }
          expr, err := xpath.Compile(t.Query)
          if err != nil {
            return nil, err
          }
          result, err = expr.Evaluate(string(xmlData))
          if err != nil {
            return nil, err
          }
        case "jq":
          query, err := gojq.Parse(t.Query)
          if err != nil {
            return nil, err
          }
          iter := query.Run(data)
          if result, ok = iter.Next(); !ok {
            return nil, fmt.Errorf("no result from jq query")
          }
        default:
          result = t.Values
        }

        return map[string]interface{}{"value": result}, nil
      }
      ```
    - **Select 块**：从 JSON/XML 数据中提取字段。
      ```go
      package main

      import (
        "fmt"
        "github.com/oliveagle/jsonpath"
        "github.com/xeipuuv/eino"
      )

      // SelectTool 使用 JsonPath 提取上游节点的报文数据
      // 功能：从 JSON 数据中选择特定字段
      // 输入：上游节点的 JSON 数据
      // 输出：提取的字段值
      type SelectTool struct {
        BaseTool
        Path string
      }

      func (t *SelectTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        data, ok := input["data"]
        if !ok {
          return nil, fmt.Errorf("no data provided")
        }
        result, err := jsonpath.JsonPathLookup(data, t.Path)
        if err != nil {
          return nil, err
        }
        return map[string]interface{}{"value": result}, nil
      }
      ```
    - **Create Variable 块**：定义全局变量。
      ```go
      package main

      import (
        "github.com/xeipuuv/eino"
      )

      // CreateVariableTool 定义并为变量赋值
      // 功能：在流程中创建全局可访问的变量
      // 输入：无
      // 输出：变量值
      type CreateVariableTool struct {
        BaseTool
        Name  string
        Value interface{}
      }

      func (t *CreateVariableTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        globalVars[t.Name] = t.Value
        return map[string]interface{}{"value": t.Value}, nil
      }
      ```
    - **Get Variable 块**：获取全局变量值。
      ```go
      package main

      import (
        "fmt"
        "github.com/xeipuuv/eino"
      )

      // GetVariableTool 获取已定义的变量值
      // 功能：在流程中读取全局变量
      // 输入：无
      // 输出：变量值
      type GetVariableTool struct {
        BaseTool
        Name string
      }

      func (t *GetVariableTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        value, exists := globalVars[t.Name]
        if !exists {
          return nil, fmt.Errorf("variable %s not found", t.Name)
        }
        return map[string]interface{}{"value": value}, nil
      }
      ```
    - **Visualize 块**：可视化数据。
      ```go
      package main

      import (
        "github.com/xeipuuv/eino"
      )

      // VisualizeTool 显示数据可视化结果
      // 功能：将数据格式化为指定格式，用于前端展示
      // 输入：上游节点的数据
      // 输出：格式化的数据和展示格式
      type VisualizeTool struct {
        BaseTool
        Format string
      }

      func (t *VisualizeTool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        return map[string]interface{}{"data": input, "format": t.Format}, nil
      }
      ```
    - **AI 块**：调用 AI 模型处理数据。
      ```go
      package main

      import (
        "github.com/xeipuuv/eino"
      )

      // AITool 调用 AI 模型处理数据
      // 功能：通过自然语言提示生成或处理数据
      // 输入：上游节点的数据
      // 输出：AI 处理结果
      type AITool struct {
        BaseTool
        Prompt string
      }

      func (t *AITool) Execute(input map[string]interface{}) (map[string]interface{}, error) {
        chatModel := eino.NewChatModel(&eino.ChatModelConfig{
          SystemPrompt: t.Prompt,
        })
        result, err := chatModel.Execute(input)
        if err != nil {
          return nil, err
        }
        return map[string]interface{}{"result": result}, nil
      }
      ```
- **API 端点**：
  - `POST /api/workflows`：保存工作流 JSON。
  - `GET /api/workflows`：列出所有工作流。
  - `GET /api/workflows/:id`：获取特定工作流。
  - `POST /api/execute`：执行工作流，返回结果。
  - **k6 执行端点**：
    - `POST /api/execute-k6-debug`：API 调试 Run，直接执行 k6 CLI。
    - `POST /api/execute-k6-scheduled`：定时任务执行，通过 Tekton TaskRun 调度。
    - `POST /api/execute-k6-performance`：性能压测执行，通过 Tekton TaskRun 调度。
    - `GET /api/k6-results/:id`：获取 k6 执行结果。
    - `GET /api/scheduled-tasks`：获取定时任务列表。
    - `GET /api/performance-tests`：获取性能测试列表。

## 4. 数据库表结构设计

### 4.1 核心表结构

#### 4.1.1 Workflow 表 - 工作流定义
```sql
CREATE TABLE workflows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    version VARCHAR(50) DEFAULT '1.0.0',
    status VARCHAR(20) DEFAULT 'active', -- active, inactive, archived
    created_by VARCHAR(100) DEFAULT 'anonymous',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tags JSONB, -- 标签信息
    metadata JSONB -- 元数据信息
);

-- 示例数据
INSERT INTO workflows (id, name, description, created_by) VALUES 
('550e8400-e29b-41d4-a716-************', 'API接口测试流程', '测试用户登录和注册接口', 'anonymous'),
('550e8400-e29b-41d4-a716-************', '性能压测流程', '用户系统性能压测', 'anonymous');
```

#### 4.1.2 Node 表 - 节点定义
```sql
CREATE TABLE nodes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID NOT NULL REFERENCES workflows(id) ON DELETE CASCADE,
    type VARCHAR(100) NOT NULL, -- start, output, http_request, database_query, evaluate, if, switch, for, repeat, collection, ai, visualize, etc.
    name VARCHAR(255) NOT NULL,
    config JSONB NOT NULL, -- 节点配置信息
    position_x DOUBLE PRECISION DEFAULT 0,
    position_y DOUBLE PRECISION DEFAULT 0,
    width INTEGER DEFAULT 200,
    height INTEGER DEFAULT 100,
    color VARCHAR(7) DEFAULT '#ffffff', -- 节点颜色
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 示例数据：HTTP请求节点
INSERT INTO nodes (id, workflow_id, type, name, config, position_x, position_y) VALUES 
('node-001', '550e8400-e29b-41d4-a716-************', 'http_request', '用户登录API', 
'{
  "url": "https://api.example.com/login",
  "method": "POST",
  "headers": {"Content-Type": "application/json"},
  "body": "{\"username\": \"{{username}}\", \"password\": \"{{password}}\"}",
  "timeout": 60000,
  "vus": 10,
  "duration": "30s",
  "tags": {"test": "api-flow"},
  "assertions": [
    {"name": "status_check", "expression": "r.status === 200"},
    {"name": "response_time", "expression": "r.timings.duration < 500"}
  ],
  "dataSource": "test_data.csv",
  "loadModel": "ramping-vus",
  "thresholds": {"http_req_duration": "p(95)<500", "http_req_failed": "rate<0.01"},
  "customConfig": {
    "stages": [
      {"duration": "60s", "target": 50},
      {"duration": "300s", "target": 100},
      {"duration": "60s", "target": 0}
    ],
    "executor": "ramping-vus",
    "gracefulRampDown": "30s"
  },
  "elasticsearch": {
    "url": "http://elasticsearch:9200",
    "index": "k6-results",
    "username": "",
    "password": ""
  }
}', 100, 100);

-- 示例数据：数据库查询节点
INSERT INTO nodes (id, workflow_id, type, name, config, position_x, position_y) VALUES 
('node-002', '550e8400-e29b-41d4-a716-************', 'database_query', '验证用户数据', 
'{
  "query": "SELECT * FROM users WHERE id = {{user_id}}",
  "assertions": [
    {"name": "user_exists", "expression": "result.length > 0"}
  ]
}', 300, 100);
```

#### 4.1.3 Connection 表 - 连接定义
```sql
CREATE TABLE connections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID NOT NULL REFERENCES workflows(id) ON DELETE CASCADE,
    source_node_id UUID NOT NULL REFERENCES nodes(id) ON DELETE CASCADE,
    target_node_id UUID NOT NULL REFERENCES nodes(id) ON DELETE CASCADE,
    source_port VARCHAR(100) DEFAULT 'output', -- 源端口
    target_port VARCHAR(100) DEFAULT 'input', -- 目标端口
    condition TEXT, -- 条件表达式
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 示例数据
INSERT INTO connections (id, workflow_id, source_node_id, target_node_id) VALUES 
('conn-001', '550e8400-e29b-41d4-a716-************', 'node-001', 'node-002');
```

#### 4.1.4 TestCase 表 - 测试用例
```sql
CREATE TABLE test_cases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID NOT NULL REFERENCES workflows(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    data_source VARCHAR(255), -- CSV/JSON文件路径或数据库查询
    schedule VARCHAR(100), -- cron表达式
    parameters JSONB, -- 测试参数
    status VARCHAR(20) DEFAULT 'active', -- active, inactive
    created_by VARCHAR(100) DEFAULT 'anonymous',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 示例数据
INSERT INTO test_cases (id, workflow_id, name, description, data_source, schedule, created_by) VALUES 
('test-001', '550e8400-e29b-41d4-a716-************', '用户登录测试', '测试用户登录功能', 'test_data.csv', '0 0 * * *', 'anonymous');
```

#### 4.1.5 TestResult 表 - 测试结果
```sql
CREATE TABLE test_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID NOT NULL REFERENCES workflows(id) ON DELETE CASCADE,
    test_case_id UUID REFERENCES test_cases(id),
    type VARCHAR(50) NOT NULL, -- debug, scheduled, performance
    status VARCHAR(20) NOT NULL, -- pending, running, completed, failed
    data JSONB NOT NULL, -- 测试结果数据
    error_message TEXT,
    execution_time INTEGER, -- 执行时间（毫秒）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 示例数据
INSERT INTO test_results (id, workflow_id, test_case_id, type, status, data) VALUES 
('result-001', '550e8400-e29b-41d4-a716-************', 'test-001', 'debug', 'completed', 
'{
  "http_req_duration": {"avg": 150, "p95": 300, "p99": 500},
  "http_req_failed": 0.01,
  "http_reqs": 1000,
  "iterations": 100,
  "assertions": [
    {"name": "status_check", "passed": true},
    {"name": "response_time", "passed": true}
  ]
}');
```

#### 4.1.6 ScheduledTask 表 - 定时任务
```sql
CREATE TABLE scheduled_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID NOT NULL REFERENCES workflows(id) ON DELETE CASCADE,
    test_case_id UUID REFERENCES test_cases(id),
    name VARCHAR(255) NOT NULL,
    script TEXT NOT NULL, -- k6脚本内容
    schedule VARCHAR(100) NOT NULL, -- cron表达式
    test_data TEXT, -- 测试数据
    parameters JSONB, -- 任务参数
    status VARCHAR(20) DEFAULT 'pending', -- pending, running, completed, failed, cancelled
    last_run_at TIMESTAMP,
    next_run_at TIMESTAMP,
    created_by VARCHAR(100) DEFAULT 'anonymous',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 示例数据
INSERT INTO scheduled_tasks (id, workflow_id, test_case_id, name, script, schedule, created_by) VALUES 
('task-001', '550e8400-e29b-41d4-a716-************', 'test-001', '每日登录测试', 
'import http from "k6/http";
import { check } from "k6";
import { Rate } from "k6/metrics";

const errorRate = new Rate("errors");

export const options = {
  vus: 10,
  duration: "30s",
  thresholds: {
    "http_req_duration": ["p(95)<500"],
    "errors": ["rate<0.01"]
  }
};

export default function() {
  const response = http.post("https://api.example.com/login", 
    JSON.stringify({username: "test", password: "test"}),
    {headers: {"Content-Type": "application/json"}}
  );
  
  check(response, {
    "status is 200": (r) => r.status === 200,
    "response time < 500ms": (r) => r.timings.duration < 500
  });
}', '0 0 * * *', 'anonymous');
```

#### 4.1.7 PerformanceTest 表 - 性能测试
```sql
CREATE TABLE performance_tests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID NOT NULL REFERENCES workflows(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    script TEXT NOT NULL, -- k6脚本内容
    load_config JSONB, -- 负载配置
    thresholds JSONB, -- 性能阈值
    status VARCHAR(20) DEFAULT 'pending', -- pending, running, completed, failed
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    results JSONB, -- 测试结果
    created_by VARCHAR(100) DEFAULT 'anonymous',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 示例数据
INSERT INTO performance_tests (id, workflow_id, name, script, load_config, thresholds, created_by) VALUES 
('perf-001', '550e8400-e29b-41d4-a716-************', '用户系统性能压测', 
'import http from "k6/http";
import { check } from "k6";

export const options = {
  stages: [
    { duration: "60s", target: 50 },
    { duration: "300s", target: 100 },
    { duration: "60s", target: 0 }
  ],
  thresholds: {
    "http_req_duration": ["p(95)<500"],
    "http_req_failed": ["rate<0.01"]
  }
};

export default function() {
  const response = http.get("https://api.example.com/users");
  check(response, {
    "status is 200": (r) => r.status === 200
  });
}', 
'{
  "stages": [
    {"duration": "60s", "target": 50},
    {"duration": "300s", "target": 100},
    {"duration": "60s", "target": 0}
  ],
  "executor": "ramping-vus",
  "gracefulRampDown": "30s"
}', 
'{
  "http_req_duration": "p(95)<500",
  "http_req_failed": "rate<0.01"
}', 'anonymous');
```

#### 4.1.8 Folder 表 - 文件夹
```sql
CREATE TABLE folders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES folders(id) ON DELETE CASCADE,
    color VARCHAR(7) DEFAULT '#ffffff',
    created_by VARCHAR(100) DEFAULT 'anonymous',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 示例数据
INSERT INTO folders (id, name, description, parent_id, created_by) VALUES 
('folder-001', 'API测试', 'API接口测试相关', NULL, 'anonymous'),
('folder-002', '性能测试', '性能压测相关', NULL, 'anonymous'),
('folder-003', '登录模块', '用户登录相关测试', 'folder-001', 'anonymous');
```

#### 4.1.9 Template 表 - 模板
```sql
CREATE TABLE templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100), -- API测试, 性能测试, 数据库测试等
    data JSONB NOT NULL, -- 模板数据
    tags JSONB, -- 标签
    is_public BOOLEAN DEFAULT false, -- 是否公开
    created_by VARCHAR(100) DEFAULT 'anonymous',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 示例数据
INSERT INTO templates (id, name, description, category, data, created_by) VALUES 
('template-001', 'REST API测试模板', '标准的REST API测试模板', 'API测试', 
'{
  "nodes": [
    {
      "id": "start-001",
      "type": "start",
      "name": "开始",
      "config": {},
      "position": {"x": 100, "y": 100}
    },
    {
      "id": "http-001", 
      "type": "http_request",
      "name": "API请求",
      "config": {
        "url": "{{api_url}}",
        "method": "{{method}}",
        "headers": {"Content-Type": "application/json"},
        "assertions": [
          {"name": "status_check", "expression": "r.status === 200"}
        ]
      },
      "position": {"x": 300, "y": 100}
    }
  ],
  "connections": [
    {
      "source": "start-001",
      "target": "http-001"
    }
  ]
}', 'anonymous');
```



### 4.2 索引设计

```sql
-- 工作流相关索引
CREATE INDEX idx_workflows_status ON workflows(status);
CREATE INDEX idx_workflows_created_at ON workflows(created_at);

-- 节点相关索引
CREATE INDEX idx_nodes_workflow_id ON nodes(workflow_id);
CREATE INDEX idx_nodes_type ON nodes(type);
CREATE INDEX idx_nodes_position ON nodes(position_x, position_y);

-- 连接相关索引
CREATE INDEX idx_connections_workflow_id ON connections(workflow_id);
CREATE INDEX idx_connections_source ON connections(source_node_id);
CREATE INDEX idx_connections_target ON connections(target_node_id);

-- 测试相关索引
CREATE INDEX idx_test_cases_workflow_id ON test_cases(workflow_id);
CREATE INDEX idx_test_cases_status ON test_cases(status);
CREATE INDEX idx_test_cases_schedule ON test_cases(schedule);

-- 结果相关索引
CREATE INDEX idx_test_results_workflow_id ON test_results(workflow_id);
CREATE INDEX idx_test_results_type ON test_results(type);
CREATE INDEX idx_test_results_status ON test_results(status);
CREATE INDEX idx_test_results_created_at ON test_results(created_at);

-- 任务相关索引
CREATE INDEX idx_scheduled_tasks_workflow_id ON scheduled_tasks(workflow_id);
CREATE INDEX idx_scheduled_tasks_status ON scheduled_tasks(status);
CREATE INDEX idx_scheduled_tasks_next_run ON scheduled_tasks(next_run_at);

-- 性能测试相关索引
CREATE INDEX idx_performance_tests_workflow_id ON performance_tests(workflow_id);
CREATE INDEX idx_performance_tests_status ON performance_tests(status);
CREATE INDEX idx_performance_tests_created_at ON performance_tests(created_at);

-- 文件夹相关索引
CREATE INDEX idx_folders_parent_id ON folders(parent_id);

-- 模板相关索引
CREATE INDEX idx_templates_category ON templates(category);
CREATE INDEX idx_templates_is_public ON templates(is_public);
```

### 4.3 外键约束

```sql
-- 添加外键约束
ALTER TABLE nodes ADD CONSTRAINT fk_nodes_workflow 
    FOREIGN KEY (workflow_id) REFERENCES workflows(id) ON DELETE CASCADE;

ALTER TABLE connections ADD CONSTRAINT fk_connections_workflow 
    FOREIGN KEY (workflow_id) REFERENCES workflows(id) ON DELETE CASCADE;

ALTER TABLE connections ADD CONSTRAINT fk_connections_source_node 
    FOREIGN KEY (source_node_id) REFERENCES nodes(id) ON DELETE CASCADE;

ALTER TABLE connections ADD CONSTRAINT fk_connections_target_node 
    FOREIGN KEY (target_node_id) REFERENCES nodes(id) ON DELETE CASCADE;

ALTER TABLE test_cases ADD CONSTRAINT fk_test_cases_workflow 
    FOREIGN KEY (workflow_id) REFERENCES workflows(id) ON DELETE CASCADE;

ALTER TABLE test_results ADD CONSTRAINT fk_test_results_workflow 
    FOREIGN KEY (workflow_id) REFERENCES workflows(id) ON DELETE CASCADE;

ALTER TABLE test_results ADD CONSTRAINT fk_test_results_test_case 
    FOREIGN KEY (test_case_id) REFERENCES test_cases(id) ON DELETE SET NULL;

ALTER TABLE scheduled_tasks ADD CONSTRAINT fk_scheduled_tasks_workflow 
    FOREIGN KEY (workflow_id) REFERENCES workflows(id) ON DELETE CASCADE;

ALTER TABLE scheduled_tasks ADD CONSTRAINT fk_scheduled_tasks_test_case 
    FOREIGN KEY (test_case_id) REFERENCES test_cases(id) ON DELETE SET NULL;

ALTER TABLE performance_tests ADD CONSTRAINT fk_performance_tests_workflow 
    FOREIGN KEY (workflow_id) REFERENCES workflows(id) ON DELETE CASCADE;

ALTER TABLE folders ADD CONSTRAINT fk_folders_parent 
    FOREIGN KEY (parent_id) REFERENCES folders(id) ON DELETE CASCADE;
```

### 4.4 触发器

```sql
-- 更新时间戳触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表添加更新时间戳触发器
CREATE TRIGGER update_workflows_updated_at BEFORE UPDATE ON workflows FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_nodes_updated_at BEFORE UPDATE ON nodes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_connections_updated_at BEFORE UPDATE ON connections FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_test_cases_updated_at BEFORE UPDATE ON test_cases FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_test_results_updated_at BEFORE UPDATE ON test_results FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_scheduled_tasks_updated_at BEFORE UPDATE ON scheduled_tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_performance_tests_updated_at BEFORE UPDATE ON performance_tests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_folders_updated_at BEFORE UPDATE ON folders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_templates_updated_at BEFORE UPDATE ON templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 4.5 视图

```sql
-- 工作流统计视图
CREATE VIEW workflow_stats AS
SELECT 
    w.id,
    w.name,
    COUNT(DISTINCT n.id) as node_count,
    COUNT(DISTINCT c.id) as connection_count,
    COUNT(DISTINCT tc.id) as test_case_count,
    COUNT(DISTINCT tr.id) as test_result_count,
    MAX(tr.created_at) as last_test_run
FROM workflows w
LEFT JOIN nodes n ON w.id = n.workflow_id
LEFT JOIN connections c ON w.id = c.workflow_id
LEFT JOIN test_cases tc ON w.id = tc.workflow_id
LEFT JOIN test_results tr ON w.id = tr.workflow_id
GROUP BY w.id, w.name;

-- 测试结果统计视图
CREATE VIEW test_result_stats AS
SELECT 
    tr.type,
    tr.status,
    COUNT(*) as count,
    AVG(tr.execution_time) as avg_execution_time,
    MAX(tr.created_at) as last_run
FROM test_results tr
GROUP BY tr.type, tr.status;

-- 性能测试统计视图
CREATE VIEW performance_test_stats AS
SELECT 
    pt.status,
    COUNT(*) as count,
    AVG(EXTRACT(EPOCH FROM (pt.completed_at - pt.started_at))) as avg_duration_seconds,
    MAX(pt.created_at) as last_created
FROM performance_tests pt
GROUP BY pt.status;
```

这个数据库表结构设计提供了完整的API Flows系统数据存储方案，支持工作流管理、测试执行、结果存储和模板管理等核心功能。