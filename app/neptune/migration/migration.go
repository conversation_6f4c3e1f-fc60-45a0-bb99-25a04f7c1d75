package migration

import (
	"database/sql"
	"embed"
	"errors"
	"fmt"
	"io/fs"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/migration"
	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/postgres"
	"github.com/golang-migrate/migrate/v4/source/iofs"
)

//go:embed sql/*.sql
var migrations embed.FS

// Init 初始化数据
func Init() {
	if !configs.Config.Db.Migrate {
		return
	}

	// 从 embed.FS 创建迁移源
	migrationFS, err := fs.Sub(migrations, "sql")
	if err != nil {
		panic(fmt.Errorf("无法获取嵌入的 migrations 文件夹: %w", err))
	}

	// 使用自定义的文件系统包装器处理文件名转换
	versionFS := &migration.VersionFileNameFS{FS: migrationFS}

	// 创建 iofs 驱动以支持 go-migrate
	sourceDriver, err := iofs.New(versionFS, ".")
	if err != nil {
		panic(fmt.Errorf("无法创建迁移源驱动: %w", err))
	}

	// 初始化 migrate 实例
	dbUrl, err := migration.ConvertDSNToURL(configs.Config.Db.Dsn)
	if err != nil {
		panic(fmt.Errorf("无法转换DSN为URL: %w", err))
	}

	// 打开数据库连接
	db, err := sql.Open("postgres", dbUrl)
	if err != nil {
		panic(fmt.Errorf("无法打开数据库连接: %w", err))
	}

	// 配置自定义的migrations表名
	config := &postgres.Config{
		MigrationsTable: "mantis_neptune_migrations",
	}

	// 创建postgres驱动实例
	dbDriver, err := postgres.WithInstance(db, config)
	if err != nil {
		panic(fmt.Errorf("无法创建postgres驱动实例: %w", err))
	}

	// 创建migrate实例
	m, err := migrate.NewWithInstance("iofs", sourceDriver, "postgres", dbDriver)
	if err != nil {
		panic(fmt.Errorf("无法初始化迁移实例: %w", err))
	}
	defer m.Close()

	// 执行所有迁移（向上迁移）
	err = m.Up()
	if err != nil {
		if errors.Is(err, migrate.ErrNoChange) {
			// 没有需要应用的迁移
			return
		}
		panic(fmt.Errorf("迁移失败: %w", err))
	}
}
