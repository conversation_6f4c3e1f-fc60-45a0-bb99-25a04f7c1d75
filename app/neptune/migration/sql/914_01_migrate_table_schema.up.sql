-- Create table neptune_artifact_scan_plan
CREATE TABLE neptune_artifact_scan_plan (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    plan_name varchar NOT NULL,
    plan_type smallint NOT NULL,
    is_default smallint,
    description varchar,
    white_cve text,
    company_id varchar,
    PRIMARY KEY (id)
);

CREATE INDEX idx_planname ON neptune_artifact_scan_plan USING btree (plan_name);

COMMENT ON COLUMN neptune_artifact_scan_plan.plan_name IS '方案名称';

COMMENT ON COLUMN neptune_artifact_scan_plan.plan_type IS '方案类型 1-安全漏洞扫描,2-代码规约扫描,3-单测测试覆盖率扫描';

COMMENT ON COLUMN neptune_artifact_scan_plan.is_default IS '是否默认 1-是,0-否';

COMMENT ON COLUMN neptune_artifact_scan_plan.description IS '方案描述';

COMMENT ON COLUMN neptune_artifact_scan_plan.white_cve IS '漏洞白名单';

COMMENT ON COLUMN neptune_artifact_scan_plan.company_id IS '公司id';

-- Create table neptune_artifact_scan_report
CREATE TABLE neptune_artifact_scan_report (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    report_type smallint,
    foreign_id bigint NOT NULL,
    report_status smallint,
    conditions varchar,
    report_url varchar,
    u_name varchar,
    PRIMARY KEY (id)
);

CREATE INDEX "idx_foreignId" ON neptune_artifact_scan_report USING btree (foreign_id);

COMMENT ON COLUMN neptune_artifact_scan_report.report_type IS '报告类型';

COMMENT ON COLUMN neptune_artifact_scan_report.foreign_id IS '任务id';

COMMENT ON COLUMN neptune_artifact_scan_report.report_status IS '报告开关 1-下载完成 0-下载中 2-下载失败';

COMMENT ON COLUMN neptune_artifact_scan_report.conditions IS '导出类型';

COMMENT ON COLUMN neptune_artifact_scan_report.report_url IS '报告地址';

COMMENT ON COLUMN neptune_artifact_scan_report.u_name IS '用户名';

-- Create table neptune_artifact_scan_task
CREATE TABLE neptune_artifact_scan_task (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    repo_instance_id varchar,
    plan_id bigint,
    plan_type smallint NOT NULL,
    task_name varchar NOT NULL,
    product_type varchar,
    task_from varchar,
    repo varchar,
    scan_range smallint,
    scan_range_info text,
    timer_switch smallint,
    timer_corn varchar,
    quality_gate_switch smallint,
    quality_gate text,
    company_id varchar,
    last_exec_id bigint,
    call_back_url varchar,
    repo_names varchar,
    PRIMARY KEY (id)
);

CREATE INDEX idx_repo ON neptune_artifact_scan_task USING btree (repo);

CREATE INDEX idx_taskname ON neptune_artifact_scan_task USING btree (task_name);

COMMENT ON COLUMN neptune_artifact_scan_task.repo_instance_id IS '仓库实例id';

COMMENT ON COLUMN neptune_artifact_scan_task.plan_id IS '方案id';

COMMENT ON COLUMN neptune_artifact_scan_task.plan_type IS '方案类型 1-制品安全漏洞扫描';

COMMENT ON COLUMN neptune_artifact_scan_task.task_name IS '任务名称';

COMMENT ON COLUMN neptune_artifact_scan_task.product_type IS '制品类型Generic|Docker';

COMMENT ON COLUMN neptune_artifact_scan_task.task_from IS '任务来源';

COMMENT ON COLUMN neptune_artifact_scan_task.repo IS '仓库';

COMMENT ON COLUMN neptune_artifact_scan_task.scan_range IS '扫描范围 1-指定制品 2-最新制品 3-规则扫描';

COMMENT ON COLUMN neptune_artifact_scan_task.scan_range_info IS '扫描范围规则';

COMMENT ON COLUMN neptune_artifact_scan_task.timer_switch IS '定时开关 1-开 0-关闭';

COMMENT ON COLUMN neptune_artifact_scan_task.timer_corn IS '定时corn表达式';

COMMENT ON COLUMN neptune_artifact_scan_task.quality_gate_switch IS '质量门禁开关 1-开 0-关闭';

COMMENT ON COLUMN neptune_artifact_scan_task.quality_gate IS '质量门禁规则';

COMMENT ON COLUMN neptune_artifact_scan_task.company_id IS '公司id';

COMMENT ON COLUMN neptune_artifact_scan_task.last_exec_id IS '最近一次执行id';

COMMENT ON COLUMN neptune_artifact_scan_task.call_back_url IS '回调地址';

-- Create table neptune_artifact_scan_task_detail
CREATE TABLE neptune_artifact_scan_task_detail (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    task_id bigint NOT NULL,
    exec_id bigint NOT NULL,
    status smallint,
    instance_id varchar,
    repo_name varchar,
    product_name varchar,
    product_version varchar,
    scan_result varchar,
    start_time timestamp without time zone,
    end_time timestamp without time zone,
    result jsonb,
    err_msg varchar,
    k8s_task_id varchar,
    product_path varchar,
    product_download_url varchar,
    PRIMARY KEY (id)
);

CREATE INDEX "idx_execId" ON neptune_artifact_scan_task_detail USING btree (exec_id);

CREATE INDEX "idx_taskId" ON neptune_artifact_scan_task_detail USING btree (task_id);

COMMENT ON COLUMN neptune_artifact_scan_task_detail.task_id IS '任务id';

COMMENT ON COLUMN neptune_artifact_scan_task_detail.exec_id IS '执行id';

COMMENT ON COLUMN neptune_artifact_scan_task_detail.status IS '任务状态 0-排队中 1-进行中 2-成功 3-失败 ';

COMMENT ON COLUMN neptune_artifact_scan_task_detail.instance_id IS '实例id';

COMMENT ON COLUMN neptune_artifact_scan_task_detail.repo_name IS '仓库名称';

COMMENT ON COLUMN neptune_artifact_scan_task_detail.product_name IS '制品名称';

COMMENT ON COLUMN neptune_artifact_scan_task_detail.product_version IS '制品版本';

COMMENT ON COLUMN neptune_artifact_scan_task_detail.scan_result IS '汇总结果';

COMMENT ON COLUMN neptune_artifact_scan_task_detail.start_time IS '执行时间';

COMMENT ON COLUMN neptune_artifact_scan_task_detail.end_time IS '执行时间';

COMMENT ON COLUMN neptune_artifact_scan_task_detail.result IS '扫描报告';

COMMENT ON COLUMN neptune_artifact_scan_task_detail.err_msg IS '错误信息';

COMMENT ON COLUMN neptune_artifact_scan_task_detail.k8s_task_id IS 'k8s任务id';

COMMENT ON COLUMN neptune_artifact_scan_task_detail.product_path IS '制品路径';

COMMENT ON COLUMN neptune_artifact_scan_task_detail.product_download_url IS '制品下载地址';

-- Create table neptune_artifact_scan_task_exec_his
CREATE TABLE neptune_artifact_scan_task_exec_his (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    task_id bigint NOT NULL,
    task_status smallint,
    scan_result varchar,
    product_count bigint,
    repo varchar,
    trigger_time timestamp without time zone,
    start_time timestamp without time zone,
    end_time timestamp without time zone,
    err_msg varchar,
    exec_user varchar,
    app_name varchar,
    PRIMARY KEY (id)
);

COMMENT ON COLUMN neptune_artifact_scan_task_exec_his.task_id IS '任务id';

COMMENT ON COLUMN neptune_artifact_scan_task_exec_his.task_status IS '任务状态 0-待执行 4-排队中 1-进行中 2-执行成功 3-执行失败 ';

COMMENT ON COLUMN neptune_artifact_scan_task_exec_his.scan_result IS '扫描结果';

COMMENT ON COLUMN neptune_artifact_scan_task_exec_his.product_count IS '制品数量';

COMMENT ON COLUMN neptune_artifact_scan_task_exec_his.repo IS '仓库';

COMMENT ON COLUMN neptune_artifact_scan_task_exec_his.trigger_time IS '触发时间';

COMMENT ON COLUMN neptune_artifact_scan_task_exec_his.start_time IS '执行时间';

COMMENT ON COLUMN neptune_artifact_scan_task_exec_his.end_time IS '执行时间';

COMMENT ON COLUMN neptune_artifact_scan_task_exec_his.err_msg IS '错误信息';

COMMENT ON COLUMN neptune_artifact_scan_task_exec_his.exec_user IS '执行人';

COMMENT ON COLUMN neptune_artifact_scan_task_exec_his.app_name IS '应用名';

-- Create table neptune_scan_jacoco_coverage_exec_his
CREATE TABLE neptune_scan_jacoco_coverage_exec_his (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    app_id varchar(128),
    from_id bigint,
    env varchar(100),
    stage varchar(100),
    "from" varchar(20),
    project_no varchar(100),
    request text,
    status integer,
    mode varchar(20),
    branch varchar(200),
    line_coverage real,
    class_coverage real,
    branch_coverage real,
    report_url varchar(200),
    k8s_task_id varchar(100),
    old_exec_path varchar(500),
    exec_path varchar(500),
    PRIMARY KEY (id)
);

CREATE INDEX idx_stage ON neptune_scan_jacoco_coverage_exec_his USING btree (stage);

CREATE INDEX idx_env ON neptune_scan_jacoco_coverage_exec_his USING btree (env);

CREATE INDEX idx_from_id ON neptune_scan_jacoco_coverage_exec_his USING btree (from_id);

CREATE INDEX idx_app_id ON neptune_scan_jacoco_coverage_exec_his USING btree (app_id);

COMMENT ON COLUMN neptune_scan_jacoco_coverage_exec_his.old_exec_path IS '之前执行的exec文件在oss中的路径';

COMMENT ON COLUMN neptune_scan_jacoco_coverage_exec_his.exec_path IS '本次执行的exec文件在oss中的路径';

-- Create table neptune_scan_language
CREATE TABLE neptune_scan_language (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    "key" varchar,
    name varchar,
    PRIMARY KEY (id)
);

-- Create table neptune_scan_profile_template
CREATE TABLE neptune_scan_profile_template (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    "key" varchar,
    name varchar,
    language varchar,
    is_built_in boolean,
    PRIMARY KEY (id)
);

-- Create table neptune_scan_programme
CREATE TABLE neptune_scan_programme (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    name varchar,
    describe varchar,
    is_default integer,
    company_id varchar,
    PRIMARY KEY (id)
);

COMMENT ON COLUMN neptune_scan_programme.company_id IS '公司id';

-- Create table neptune_scan_programme_profile
CREATE TABLE neptune_scan_programme_profile (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    programme_id bigint,
    profile_template_ids varchar,
    language varchar,
    cus_name varchar,
    cus_key varchar,
    is_active boolean,
    PRIMARY KEY (id)
);

-- Create table neptune_scan_quality_gates
CREATE TABLE neptune_scan_quality_gates (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    task_id bigint,
    is_active boolean,
    blocker_violations integer,
    critical_violations integer,
    major_violations integer,
    minor_violations integer,
    PRIMARY KEY (id)
);

-- Create table neptune_scan_task_filter_config
CREATE TABLE neptune_scan_task_filter_config (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    relation_id text,
    "type" varchar,
    description varchar,
    config_paths text,
    company_id varchar,
    PRIMARY KEY (id)
);

COMMENT ON COLUMN neptune_scan_task_filter_config.company_id IS '公司id';

-- Create table neptune_scan_test_exec_his
CREATE TABLE neptune_scan_test_exec_his (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    mode varchar,
    task_id bigint,
    oss_download_url varchar,
    branch varchar,
    commit_base_id varchar,
    commit_compare_id varchar,
    call_back_url varchar,
    ship_params varchar,
    sonar_url varchar,
    status integer,
    status_msg varchar,
    pdf_url varchar,
    lines integer,
    comment_lines integer,
    complexity integer,
    ncloc integer,
    blocker_violations integer,
    critical_violations integer,
    major_violations integer,
    minor_violations integer,
    info_violations integer,
    line_coverage real,
    test_success_density real,
    comment_lines_density real,
    duplicated_lines_density real,
    branch_coverage real,
    vulnerabilities integer,
    total_warnings integer,
    fixed_warnings integer,
    operator varchar,
    stage varchar,
    lines_to_cover integer,
    k8s_task_id varchar,
    quality_gates smallint,
    quality_gates_params varchar,
    sonar_project_key varchar,
    PRIMARY KEY (id)
);

-- Create table neptune_scan_test_task
CREATE TABLE neptune_scan_test_task (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    task_name varchar,
    app_id varchar(128),
    app_name varchar,
    programme_id bigint,
    branch varchar,
    pdf_gen boolean,
    operator varchar,
    ship_pid varchar,
    language varchar,
    exec_type varchar,
    code_url varchar,
    job_type varchar,
    last_scan_test_exec_his bigint,
    last_scan_test_exec_time timestamp without time zone,
    company_id varchar,
    "from" varchar,
    mode varchar,
    commit_time_frame varchar,
    commit_base_id varchar,
    commit_compare_id varchar,
    PRIMARY KEY (id)
);

COMMENT ON COLUMN neptune_scan_test_task.task_name IS '任务名称';

COMMENT ON COLUMN neptune_scan_test_task.app_id IS '应用id';

COMMENT ON COLUMN neptune_scan_test_task.app_name IS '应用名';

COMMENT ON COLUMN neptune_scan_test_task.programme_id IS '方案id';

COMMENT ON COLUMN neptune_scan_test_task.branch IS '代码分支';

COMMENT ON COLUMN neptune_scan_test_task.pdf_gen IS '是否生成PDF';

COMMENT ON COLUMN neptune_scan_test_task.operator IS '执行人';

COMMENT ON COLUMN neptune_scan_test_task.ship_pid IS '发布单id';

COMMENT ON COLUMN neptune_scan_test_task.language IS '语言';

COMMENT ON COLUMN neptune_scan_test_task.exec_type IS '应用类型 FRONTEND-前端 BACKEND-后端';

COMMENT ON COLUMN neptune_scan_test_task.code_url IS '仓库git地址';

COMMENT ON COLUMN neptune_scan_test_task.job_type IS '任务类型 scan-扫描 unit-单测 coverage-覆盖率';

COMMENT ON COLUMN neptune_scan_test_task.last_scan_test_exec_his IS '最后一次执行历史id';

COMMENT ON COLUMN neptune_scan_test_task.last_scan_test_exec_time IS '最后一次执行时间';

COMMENT ON COLUMN neptune_scan_test_task.company_id IS '公司id';

COMMENT ON COLUMN neptune_scan_test_task."from" IS '任务来源 pipeline-流水线任务 web-离线任务';

COMMENT ON COLUMN neptune_scan_test_task.mode IS '扫描模式';

COMMENT ON COLUMN neptune_scan_test_task.commit_time_frame IS '选取分支的时间范围';

COMMENT ON COLUMN neptune_scan_test_task.commit_base_id IS '基线分支id';

COMMENT ON COLUMN neptune_scan_test_task.commit_compare_id IS '比较分支id';