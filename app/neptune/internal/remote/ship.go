package remote

import (
	"compress/gzip"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/avast/retry-go"
	"github.com/duke-git/lancet/v2/netutil"
)

// ShipInvoke 提供对Ship服务的调用接口
type ShipInvoke struct{}

// ShipCallBack 调用Ship接口并进行重试，不返回结果
func (s ShipInvoke) ShipCallBack(url string, body any) error {
	requestJSON, _ := json.Marshal(body)
	logger.Logger.Infof("回调Ship请求: URL=%s, 参数=%s", url, string(requestJSON))

	const maxRetries = 3
	err := retry.Do(
		func() error {
			httpClient := netutil.NewHttpClient()
			request := &netutil.HttpRequest{
				RawURL: url,
				Method: http.MethodPost,
				Body:   requestJSON,
			}
			resp, err := httpClient.SendRequest(request)
			if err != nil {
				return err
			}

			logger.Logger.Infof("回调Ship响应: URL=%s, 结果=%s", url, resp.Body)
			return nil
		},
		retry.Attempts(maxRetries),
		retry.LastErrorOnly(true),
		retry.OnRetry(func(n uint, err error) {
			logger.Logger.Infof("回调Ship重试(第%d次): %s", n, err.Error())
		}),
	)
	if err != nil {
		return fmt.Errorf("调用接口[%s]失败: %s", url, err.Error())
	}
	return nil
}

// ShipCallBackWithReturn 调用Ship接口并返回结果
func (s ShipInvoke) ShipCallBackWithReturn(url string, body interface{}) (map[string]interface{}, error) {
	requestJSON, _ := json.Marshal(body)
	logger.Logger.Infof("Ship请求: URL=%s, 参数=%s", url, string(requestJSON))

	httpClient := netutil.NewHttpClient()
	request := &netutil.HttpRequest{
		RawURL: url,
		Method: http.MethodPost,
		Body:   requestJSON,
	}
	resp, err := httpClient.SendRequest(request)
	if err != nil {
		return nil, fmt.Errorf("调用接口[%s]失败: %s", url, err.Error())
	}

	res := make(map[string]interface{})
	err = httpClient.DecodeResponse(resp, &res)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %s", err.Error())
	}
	logger.Logger.Infof("Ship响应: URL=%s, 结果=%v", url, res)
	return res, nil
}

// GetBranchListByCodePath 通过代码路径获取分支列表
func (ShipInvoke) GetBranchListByCodePath(ctx *commoncontext.MantisContext, hubUrl, search string, page, pageSize int64) []dto.GitBranchDTO {
	start := time.Now().UnixMilli()

	// 解码仓库URL
	hub, err := url.QueryUnescape(hubUrl)
	if err != nil {
		logger.Logger.Panicf("解码仓库URL失败, hubUrl=%s, 错误: %s", hubUrl, err.Error())
	}

	// 解析仓库路径
	code, hubName, _ := strings.Cut(hub, "/")
	uri := fmt.Sprintf("/ship/api/v1/scm/%s/proxy/api/v4/projects/%s/repository/branches", code, url.QueryEscape(hubName))

	// 构建查询参数
	params := buildQueryParams(search, page, pageSize)

	// 构建完整请求URL
	requestURL := configs.Config.Domain.Deckjob + uri
	if len(params) > 0 {
		requestURL += "?" + params.Encode()
	}
	logger.Logger.Infof("Ship请求: URL=%s, 方法=%s", requestURL, http.MethodGet)

	// 发送HTTP请求
	httpClient := netutil.NewHttpClient()
	request := &netutil.HttpRequest{
		RawURL:      requestURL,
		Method:      http.MethodGet,
		Headers:     ctx.Header,
		QueryParams: params,
	}

	resp, err := httpClient.SendRequest(request)
	if err != nil {
		logger.Logger.Panicf("发送请求到Ship服务失败, URL=%s, 错误: %v", uri, err)
	}

	// 处理HTTP状态码
	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusBadRequest ||
			resp.StatusCode == http.StatusUnauthorized ||
			resp.StatusCode == http.StatusForbidden {
			logger.Logger.Panicf("GitLab未授权, 状态码: %d", resp.StatusCode)
		}
		logger.Logger.Panicf("请求返回非200状态码: %d", resp.StatusCode)
	}

	// 处理响应
	branches := parseGitBranchResponse(resp)

	// 记录执行耗时
	logger.Logger.Infof("获取分支列表完成, 路径=%s, 耗时=%dms", hubUrl, time.Now().UnixMilli()-start)
	return branches
}

// buildQueryParams 构建查询参数
func buildQueryParams(search string, page, pageSize int64) url.Values {
	params := url.Values{}
	if search != "" {
		params.Add("search", search)
	}
	if page != 0 {
		params.Add("page", fmt.Sprintf("%d", page))
	}
	if pageSize != 0 {
		params.Add("per_page", fmt.Sprintf("%d", pageSize))
	}
	return params
}

// parseGitBranchResponse 解析Git分支响应数据
func parseGitBranchResponse(resp *http.Response) []dto.GitBranchDTO {
	// 处理压缩响应
	var reader io.Reader = resp.Body
	if isGzipEncoded(resp.Header) {
		gr, err := gzip.NewReader(resp.Body)
		if err != nil {
			logger.Logger.Panicf("解压gzip响应失败: %s", err.Error())
		}
		defer gr.Close()
		reader = gr
	}

	// 解析JSON
	branches := make([]dto.GitBranchDTO, 0)
	err := json.NewDecoder(reader).Decode(&branches)
	if err != nil {
		logger.Logger.Panicf("解析分支数据失败: %s", err.Error())
	}

	// 记录结果预览
	logResponsePreview(branches)

	return branches
}

// isGzipEncoded 检查响应是否为gzip编码
func isGzipEncoded(headers http.Header) bool {
	contentEncoding := headers.Get("Content-Encoding")
	return strings.ToLower(contentEncoding) == "gzip"
}

// logResponsePreview 记录响应预览
func logResponsePreview(branches []dto.GitBranchDTO) {
	previewCount := 5
	if len(branches) < previewCount {
		previewCount = len(branches)
	}

	preview := branches
	if len(branches) > previewCount {
		preview = branches[:previewCount]
	}

	responseJSON, _ := json.Marshal(preview)
	logger.Logger.Infof("Ship响应: 获取到%d个分支, 前%d个分支=%s", len(branches), previewCount, string(responseJSON))
}
