package help

import (
	"context"
	"fmt"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/third_party/cmdb"
)

// GitHelper Git操作相关
type GitHelper struct{}

// cloneCmd Git克隆命令模板
const cloneCmd = "git clone -b %s %s"

// vcsModel CMDB中的版本控制系统模型名称
const vcsModel = "vcs_model"

// GitCloneCmd 构建Git克隆命令
func (h GitHelper) GitCloneCmd(gitUrl string, branch string) string {
	// 获取Git仓库信息
	gitInfo := h.GetGitInfoByUrl(gitUrl)
	if gitInfo == nil {
		logger.Logger.Panicf("没有找到对应的git配置，请检查配置文件上的git配置")
	}

	// 将凭证插入到URL中
	authUrl := strings.ReplaceAll(gitUrl, "//", fmt.Sprintf("//%s:%s@", gitInfo.User, gitInfo.Token))

	// 格式化克隆命令
	return fmt.Sprintf(cloneCmd, branch, authUrl)
}

// GetGitInfoByUrl 通过URL获取Git仓库信息
func (GitHelper) GetGitInfoByUrl(url string) *configs.GitInfo {
	// 从CMDB获取版本控制系统信息
	result := make([]map[string]any, 0)
	err := cmdb.Client.GetModelInstances(context.Background(), vcsModel, &result, nil)
	if err != nil || len(result) == 0 {
		logger.Logger.Panicf("获取git信息错误, err=%s", err.Error())
	}

	// 遍历所有VCS配置，查找匹配的Git仓库
	for _, v := range result {
		meta, ok := v["meta"].(map[string]interface{})
		if !ok {
			continue
		}

		baseUrl, ok := meta["address"].(string)
		if !ok || baseUrl == "" {
			continue
		}

		// 检查URL前缀匹配
		if strings.HasPrefix(url, baseUrl) {
			// 获取用户名，如果为空则使用默认值
			user, _ := meta["user_name"].(string)
			if user == "" {
				user = "user"
			}

			// 获取token
			token, _ := meta["token"].(string)

			return &configs.GitInfo{
				Domain: baseUrl,
				User:   user,
				Token:  token,
			}
		}
	}

	logger.Logger.Panicf("%s 没有匹配到对应的git仓库", url)
	return nil
}

// ValidateGitUrl 规范化Git仓库URL
func (GitHelper) ValidateGitUrl(gitUrl string) string {
	const gitSuffix = ".git"

	if !strings.HasSuffix(gitUrl, gitSuffix) {
		return gitUrl + gitSuffix
	}
	return gitUrl
}
