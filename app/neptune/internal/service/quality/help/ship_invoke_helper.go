package help

import (
	"fmt"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/remote"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/third_party/cmdb"
)

// ShipInvokeHelper Ship服务调用助手
type ShipInvokeHelper struct{}

// shipInvoke Ship服务调用实例
var shipInvoke remote.ShipInvoke

// InvokeStaticPublicShip 调用公共静态Ship服务
func (s ShipInvokeHelper) InvokeStaticPublicShip(ctx *commoncontext.MantisContext, hisDTO dto.ScanTestExecHisDTO) {
	// 如果回调URL为空，则不执行回调
	if hisDTO.CallBackUrl == "" {
		return
	}
	s.invokeShipByScanTestExecHisDTO(ctx, hisDTO)
}

// invokeShipByScanTestExecHisDTO 根据扫描测试执行历史调用Ship服务
func (s ShipInvokeHelper) invokeShipByScanTestExecHisDTO(ctx *commoncontext.MantisContext, execHisDto dto.ScanTestExecHisDTO) {
	// 只有任务状态为终态（成功或失败）时才进行回调
	if !task.CheckFinalStatusCode(execHisDto.Status) {
		logger.Logger.Infof("静态扫描任务:hisId:%d的状态为%s，无需回调", execHisDto.Id, execHisDto.StatusMsg)
		return
	}

	// 查询任务基本信息
	scanTestTask := quality.ScanTestTask{}
	scanTestTask.Id = execHisDto.TaskId
	gormx.SelectOneByConditionX(ctx, &scanTestTask)

	// 获取任务相关参数
	mode := execHisDto.Mode
	execType := scanTestTask.ExecType
	jobType := scanTestTask.JobType

	// 根据不同的模式和执行类型组装回调数据
	var resData map[string]interface{}
	if mode == constants.StandardTotal {
		// 全量模式
		if execType == constants.ExecTypeBack {
			// 后端执行
			resData = s.invokeTotalAfterStaticForShip(ctx, execHisDto, jobType)
		} else {
			// 前端执行
			resData = s.invokeTotalFrontStaticForShip(ctx, execHisDto)
		}
	} else {
		// 增量模式
		if execType == constants.ExecTypeBack {
			// 后端执行
			resData = s.invokeMixAfterStaticForShip(ctx, execHisDto, jobType)
		} else {
			// 前端执行
			resData = s.invokeMixFrontStaticForShip(ctx, execHisDto)
		}
	}

	logger.Logger.Infof("ShipInvokeHelper, 回调ship请求参数:%+v", resData)
	s.restInvokeShipForStatic(resData, execHisDto.Id)
}

// invokeTotalAfterStaticForShip 处理全量模式后端静态扫描结果
func (s ShipInvokeHelper) invokeTotalAfterStaticForShip(ctx *commoncontext.MantisContext, hisDTO dto.ScanTestExecHisDTO, jobType string) map[string]interface{} {
	// 获取公共回调数据
	res := s.getStaticPublicResult(ctx, hisDTO)

	// 只有成功状态下才添加额外指标
	if hisDTO.Status == task.TaskStatusSuccess {
		if jobType == constants.ScanJobType {
			// 扫描任务添加违规指标
			res["blockerViolations"] = hisDTO.BlockerViolations
			res["criticalViolations"] = hisDTO.CriticalViolations
			res["majorViolations"] = hisDTO.MajorViolations
			res["minorViolations"] = hisDTO.MinorViolations
			res["infoViolations"] = hisDTO.InfoViolations
		} else {
			// 单元测试任务添加覆盖率指标
			res["coverage"] = hisDTO.LineCoverage
			res["testSuccessDensity"] = hisDTO.TestSuccessDensity
		}
	}
	return res
}

// invokeTotalFrontStaticForShip 处理全量模式前端静态扫描结果
func (s ShipInvokeHelper) invokeTotalFrontStaticForShip(ctx *commoncontext.MantisContext, execHisDto dto.ScanTestExecHisDTO) map[string]interface{} {
	// 获取公共回调数据
	res := s.getStaticPublicResult(ctx, execHisDto)

	// 只有成功状态下才添加额外指标
	if execHisDto.Status == task.TaskStatusSuccess {
		logger.Logger.Infof("组装pid:%d、appName:%v回调ship前端应用静态扫描全量指标", execHisDto.ShipPid, res["appName"])

		// 添加违规指标
		res["blockerViolations"] = execHisDto.BlockerViolations
		res["criticalViolations"] = execHisDto.CriticalViolations
		res["majorViolations"] = execHisDto.MajorViolations
		res["minorViolations"] = execHisDto.MinorViolations
		res["infoViolations"] = execHisDto.InfoViolations
	}
	return res
}

// invokeMixAfterStaticForShip 处理混合模式后端静态扫描结果
func (s ShipInvokeHelper) invokeMixAfterStaticForShip(ctx *commoncontext.MantisContext, hisDto dto.ScanTestExecHisDTO, jobType string) map[string]interface{} {
	// 获取公共回调数据
	res := s.getStaticPublicResult(ctx, hisDto)

	// 只有成功状态下才添加额外指标
	if hisDto.Status == task.TaskStatusSuccess {
		logger.Logger.Infof("组装pid:%d、appName:%v回调ship后端应用静态扫描混合指标", hisDto.ShipPid, res["appName"])

		if jobType == constants.ScanJobType {
			// 扫描任务添加违规指标和代码质量指标
			res["blockerViolations"] = hisDto.BlockerViolations
			res["criticalViolations"] = hisDto.CriticalViolations
			res["majorViolations"] = hisDto.MajorViolations
			res["minorViolations"] = hisDto.MinorViolations
			res["infoViolations"] = hisDto.InfoViolations
			res["commentLinesDensity"] = hisDto.CommentLinesDensity
			res["duplicatedLinesDensity"] = hisDto.DuplicatedLinesDensity

			// 增量违规指标
			res["newBlockerViolations"] = hisDto.BlockerViolations
			res["newCriticalViolations"] = hisDto.CriticalViolations
			res["newMajorViolations"] = hisDto.MajorViolations
			res["newMinorViolations"] = hisDto.MinorViolations
			res["newInfoViolations"] = hisDto.InfoViolations
		} else {
			// 单元测试任务添加覆盖率指标
			res["coverage"] = hisDto.LineCoverage

			// 计算增量覆盖率
			newLines := hisDto.LinesToCover
			newCoverage := hisDto.LineCoverage
			if newLines == 0 {
				newCoverage = 100
			}
			res["newCoverage"] = newCoverage
			res["testSuccessDensity"] = hisDto.TestSuccessDensity
		}
	}
	return res
}

// invokeMixFrontStaticForShip 处理混合模式前端静态扫描结果
func (s ShipInvokeHelper) invokeMixFrontStaticForShip(ctx *commoncontext.MantisContext, hisDto dto.ScanTestExecHisDTO) map[string]interface{} {
	// 获取公共回调数据
	res := s.getStaticPublicResult(ctx, hisDto)

	// 只有成功状态下才添加额外指标
	if hisDto.Status == task.TaskStatusSuccess {
		logger.Logger.Infof("组装pid:%d、appName:%v回调ship前端应用静态扫描混合指标", hisDto.ShipPid, res["appName"])

		// 添加全量和增量违规指标
		res["blockerViolations"] = hisDto.BlockerViolations
		res["criticalViolations"] = hisDto.CriticalViolations
		res["majorViolations"] = hisDto.MajorViolations
		res["minorViolations"] = hisDto.MinorViolations
		res["infoViolations"] = hisDto.InfoViolations

		// 增量违规指标
		res["newBlockerViolations"] = hisDto.BlockerViolations
		res["newCriticalViolations"] = hisDto.CriticalViolations
		res["newMajorViolations"] = hisDto.MajorViolations
		res["newMinorViolations"] = hisDto.MinorViolations
		res["newInfoViolations"] = hisDto.InfoViolations
	}
	return res
}

// getStaticPublicResult 获取静态扫描公共回调结果
func (s ShipInvokeHelper) getStaticPublicResult(ctx *commoncontext.MantisContext, execHisDTO dto.ScanTestExecHisDTO) map[string]interface{} {
	// 初始化结果对象
	res := make(map[string]interface{})

	// 解析Ship参数
	shipParam := make(map[string]interface{})
	jsonx.UnMarshal([]byte(execHisDTO.ShipParams), &shipParam)

	// 设置基础信息
	res["pid"] = execHisDTO.ShipPid
	res["appName"] = shipParam["appName"]
	res["status"] = s.changeShipResultStatus(execHisDTO.Status)
	res["statusMsg"] = execHisDTO.StatusMsg

	// 查询任务信息
	scanTestTask := quality.ScanTestTask{}
	scanTestTask.Id = execHisDTO.TaskId
	scanTestTask.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &scanTestTask)

	// 查询应用信息
	appInfo, err := cmdb.Client.GetAppById(ctx, scanTestTask.AppId)
	if err != nil {
		logger.Logger.Panicf("查询应用错误, err=%s", err.Error())
	}

	// 设置报告URL
	if scanTestTask.JobType == constants.ScanJobType {
		res["zaccReportUrl"] = fmt.Sprintf(constants.QualityScanReportPath,
			configs.Config.Domain.Cube,
			execHisDTO.TaskId,
			scanTestTask.TaskName,
			appInfo.Name,
			scanTestTask.AppId)
	} else {
		res["zaccReportUrl"] = fmt.Sprintf(constants.QualityUnitTestReportPath,
			configs.Config.Domain.Cube,
			execHisDTO.TaskId,
			appInfo.Name,
			scanTestTask.AppId)
	}

	// 设置回调地址和模式
	res["callBackAddr"] = execHisDTO.CallBackUrl
	res["mode"] = execHisDTO.Mode

	return res
}

// changeShipResultStatus 转换任务状态为Ship服务状态
func (ShipInvokeHelper) changeShipResultStatus(status int8) string {
	switch status {
	case task.TaskStatusSuccess:
		return constants.SuccessStatus
	case task.TaskStatusFail:
		return constants.FailureStatus
	default:
		return constants.AbortedStatus
	}
}

// restInvokeShipForStatic 通过REST接口调用Ship服务
func (ShipInvokeHelper) restInvokeShipForStatic(jsonObj map[string]interface{}, hisId int64) {
	logger.Logger.Info("开始调用 ShipInvokeHelper.restInvokeShip...")

	// 获取回调URL
	callBackUrl := jsonObj["callBackAddr"]
	logger.Logger.Infof("=====Mantis->Devops：回调url======:%s", callBackUrl)

	// 使用defer处理可能出现的异常
	defer func() {
		if err := recover(); err != nil {
			logger.Logger.Infof("任务id:%d，调用ship出现异常，e=%v", hisId, err)
		}
		logger.Logger.Info("结束 ShipInvokeHelper.restInvokeShip...")
	}()

	// 执行回调
	invokeResult, err := shipInvoke.ShipCallBackWithReturn(fmt.Sprintf("%s", callBackUrl), jsonObj)
	if err != nil {
		logger.Logger.Panicf("ShipInvokeHelper.restInvokeShipForStatic error: %v", err)
	}
	logger.Logger.Infof("=====Mantis->Devops: 回调返回=====:%+v", invokeResult)
}
