package help

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/netutil"
)

// GitLabHelper GitLab操作相关
type GitLabHelper struct {
	Domain string // GitLab服务域名
	Token  string // GitLab访问令牌
}

// httpClient HTTP客户端实例
var httpClient = netutil.NewHttpClient()

// GitCommitInfo GitLab提交信息
type GitCommitInfo struct {
	Id            string `json:"id"`             // 提交ID
	Message       string `json:"message"`        // 提交消息
	AuthorName    string `json:"author_name"`    // 作者名称
	CommittedDate string `json:"committed_date"` // 提交日期
}

// QueryBranchInfoByCodePath 查询代码路径的分支信息
func (g GitLabHelper) QueryBranchInfoByCodePath(codePath string, page int64, pageSize int64, search string) []dto.GitBranchDTO {
	logger.Logger.Info("开始执行 GitlabHelper.queryBranchInfo...")
	start := time.Now().UnixMilli()

	// 构建基础URL
	baseUrl := fmt.Sprintf("/api/v4/projects/%s/repository/branches?sort=updated_desc", codePath)

	// 添加搜索条件
	if search != "" {
		baseUrl = baseUrl + "&search=" + search
	}

	// 分页参数模板
	pageParam := "&page=%d&per_page=" + strconv.FormatInt(pageSize, 10)

	// 结果列表
	result := make([]dto.GitBranchDTO, 0, 128)

	// 逐页查询
	for i := int64(1); i <= page; i++ {
		// 构建完整URL
		pageUrl := baseUrl + fmt.Sprintf(pageParam, i)

		// 发送请求
		response := g.gitHttpRequest(pageUrl, nil, http.MethodGet)
		if response.StatusCode != http.StatusOK {
			logger.Logger.Errorf("获取分支信息失败, 状态码: %d", response.StatusCode)
			break
		}

		// 解析响应
		branchList := make([]dto.GitBranchDTO, 0)
		err := httpClient.DecodeResponse(response, &branchList)
		if err != nil {
			logger.Logger.Errorf("解析分支信息失败: %v", err)
			break
		}

		// 处理结果
		for j := range branchList {
			branchList[j].CommitId = branchList[j].Commit.Id
		}
		result = append(result, branchList...)

		// 如果返回的结果小于页大小，说明已经到达最后一页
		if int64(len(branchList)) < pageSize {
			break
		}
	}

	logger.Logger.Infof("结束 GitlabHelper.queryBranchInfo, 共获取%d个分支, 耗时=%dms",
		len(result), time.Now().UnixMilli()-start)
	return result
}

// QueryProjectCommitInfo 查询项目提交信息
func (g GitLabHelper) QueryProjectCommitInfo(codePath string, branch string, page int64, pageSize int64, since string, until string) []GitCommitInfo {
	// 构建基本URL
	url := fmt.Sprintf("/api/v4/projects/%s/repository/commits", codePath)

	// 构建查询参数
	params := make(map[string]string)
	if page != 0 {
		params["page"] = strconv.FormatInt(page, 10)
	}
	if pageSize != 0 {
		params["per_page"] = strconv.FormatInt(pageSize, 10)
	}
	if branch != "" {
		params["ref_name"] = branch
	}
	if since != "" {
		params["since"] = since
	}
	if until != "" {
		params["until"] = until
	}

	// 将参数添加到URL
	if len(params) != 0 {
		url += "?"
		queryParams := make([]string, 0, len(params))
		for k, v := range params {
			queryParams = append(queryParams, fmt.Sprintf("%s=%s", k, v))
		}
		url += strings.Join(queryParams, "&")
	}

	// 发送请求
	response := g.gitHttpRequest(url, nil, http.MethodGet)
	if response.StatusCode != http.StatusOK {
		logger.Logger.Panicf("向GitLab发送请求失败，状态码: %d", response.StatusCode)
	}

	// 解析响应
	result := make([]GitCommitInfo, 0)
	err := httpClient.DecodeResponse(response, &result)
	if err != nil {
		logger.Logger.Panicf("解析GitLab提交信息失败: %v", err)
	}

	return result
}

// gitHttpRequest 发送GitLab HTTP请求
func (g GitLabHelper) gitHttpRequest(path string, requestBody map[string]interface{}, method string) *http.Response {
	// 构建完整URL
	url := g.Domain + path

	// 特殊处理: 如果是cube.anlink.com/code域名，将http替换为https
	if g.Domain == "http://cube.anlink.com/code" {
		url = strings.ReplaceAll(url, "http", "https")
	}

	logger.Logger.Infof("GitlabHelper 请求: URL=%s", url)

	// 设置请求头
	headers := http.Header{}
	headers.Set("Content-Type", "application/json; charset=UTF-8")
	headers.Set("PRIVATE-TOKEN", g.Token)

	// 构建请求
	request := &netutil.HttpRequest{
		RawURL:  url,
		Method:  method,
		Headers: headers,
		Body:    jsonx.Marshal(&requestBody),
	}

	// 发送请求
	resp, err := httpClient.SendRequest(request)
	if err != nil {
		logger.Logger.Panicf("发送请求到GitLab失败: %v, URL: %s", err, url)
	}

	return resp
}
