package quality

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

// ScanQualityGatesService 扫描质量门禁服务
type ScanQualityGatesService struct{}

// 质量门禁状态常量
const (
	QualityGateFailStatus = 1 // 不通过
	QualityGatePassStatus = 2 // 通过
)

// InsertTaskConfig 插入或更新任务质量门禁配置
func (ScanQualityGatesService) InsertTaskConfig(ctx *commoncontext.MantisContext, gatesDto dto.ScanQualityGatesDTO, user commondto.UserInfo) int64 {
	// 初始化质量门禁实体
	gates := quality.ScanQualityGates{}
	gates.GmtCreated = times.Now()
	gates.GmtModified = times.Now()
	gates.Creator = user.AdAccount
	gates.Modifier = user.AdAccount
	gates.IsDeleted = commonconstants.DeleteNo
	gates.TaskId = gatesDto.TaskId
	gates.IsActive = gatesDto.IsActive
	gates.BlockerViolations = gatesDto.BlockerViolations
	gates.CriticalViolations = gatesDto.CriticalViolations
	gates.MajorViolations = gatesDto.MajorViolations
	gates.MinorViolations = gatesDto.MinorViolations

	// 查询是否已存在该任务的质量门禁
	queryGates := quality.ScanQualityGates{
		TaskId: gatesDto.TaskId,
	}
	gormx.SelectOneByConditionX(ctx, &queryGates)

	// 如果已存在，则使用原有ID进行更新
	if queryGates.Id != 0 {
		gates.Id = queryGates.Id
	}

	// 插入或更新质量门禁配置
	gormx.InsertUpdateOneX(ctx, &gates)
	return gates.Id
}

// SelectByTaskId 根据任务ID查询质量门禁
func (ScanQualityGatesService) SelectByTaskId(ctx *commoncontext.MantisContext, taskId int64) quality.ScanQualityGates {
	gates := quality.ScanQualityGates{}
	gates.TaskId = taskId
	gates.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &gates)
	return gates
}

// SelectBatchByTaskIds 批量查询多个任务的质量门禁
func (ScanQualityGatesService) SelectBatchByTaskIds(ctx *commoncontext.MantisContext, taskIds []int64) []quality.ScanQualityGates {
	results := make([]quality.ScanQualityGates, 0)
	gormx.SelectByParamBuilderX(
		ctx,
		gormx.NewParamBuilder().
			Model(&quality.ScanQualityGates{}).
			In("task_id", taskIds).
			Eq("is_deleted", commonconstants.DeleteNo),
		&results,
	)
	return results
}

// CompareThreshold 比较值与阈值，返回质量门禁状态
func (ScanQualityGatesService) CompareThreshold(actual, threshold int32) int32 {
	if actual > threshold {
		return QualityGateFailStatus
	}
	return QualityGatePassStatus
}
