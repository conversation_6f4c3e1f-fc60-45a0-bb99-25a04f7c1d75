package quality

import (
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/convert"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality/help"
	common_constants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	common_model "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	shipUtils "git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
	set "github.com/duke-git/lancet/v2/datastructure/set"
	"github.com/duke-git/lancet/v2/slice"
)

// ScanProgrammeService 扫描方案服务
type ScanProgrammeService struct{}

var scanProgrammeProfileDao dao.ScanProgrammeProfileDao

// CreateScanProgramme 创建扫描方案
func (s ScanProgrammeService) CreateScanProgramme(ctx *commoncontext.MantisContext, scanProgrammeDto dto.ScanProgrammeDTO, user commondto.UserInfo) int64 {
	logger.Logger.Info("开始创建扫描方案")

	// 检查方案名称是否重复
	programmeName := scanProgrammeDto.Name
	scanProgrammes := make([]quality.ScanProgramme, 0)

	builder := gormx.NewParamBuilder().
		Model(&quality.ScanProgramme{}).
		Eq("name", programmeName).
		Eq("is_deleted", common_constants.DeleteNo)

	// 如果有公司ID，添加公司过滤条件
	if user.CompanyID != "" {
		builder.Eq("company_id", user.CompanyID)
	}

	gormx.SelectByParamBuilderX(ctx, builder, &scanProgrammes)
	if len(scanProgrammes) != 0 {
		logger.Logger.Panic("方案名称重复，请使用其他名称")
	}

	// 创建扫描方案对象
	scanProgramme := quality.ScanProgramme{
		Addons: common_model.Addons{
			Creator:     user.AdAccount,
			Modifier:    user.AdAccount,
			GmtCreated:  times.Now(),
			GmtModified: times.Now(),
			IsDeleted:   common_constants.DeleteNo,
		},
		Name:      scanProgrammeDto.Name,
		Describe:  scanProgrammeDto.Describe,
		IsDefault: scanProgrammeDto.IsDefault,
		CompanyId: shipUtils.IDString(user.CompanyID),
	}

	// 如果是默认方案，将其他默认方案设为普通方案
	if scanProgramme.IsDefault == constants.IsDefault {
		oldDefaultProgramme := quality.ScanProgramme{}
		oldDefaultProgramme.IsDefault = constants.IsDefault
		oldDefaultProgramme.IsDeleted = common_constants.DeleteNo
		oldDefaultProgramme.CompanyId = shipUtils.IDString(user.CompanyID)

		// 查询当前默认方案
		err := gormx.SelectOneByCondition(ctx, &oldDefaultProgramme)
		if err == nil && oldDefaultProgramme.Id != 0 {
			// 将旧的默认方案设置为非默认
			oldDefaultProgramme.IsDefault = constants.NotDefault
			gormx.InsertUpdateOneX(ctx, &oldDefaultProgramme)
		}
	}

	// 保存方案
	gormx.InsertUpdateOneX(ctx, &scanProgramme)
	scanProgrammeId := scanProgramme.Id
	scanProgrammeDto.Id = scanProgrammeId

	logger.Logger.Infof("保存扫描方案成功，ID: %d", scanProgrammeId)

	// 处理规则配置文件
	s.dealRuleProfile(ctx, scanProgrammeDto, user)

	logger.Logger.Info("扫描方案创建完成")
	return scanProgrammeId
}

// QueryScanProgramme 查询扫描方案详情
func (s ScanProgrammeService) QueryScanProgramme(ctx *commoncontext.MantisContext, id int64) *dto.ScanProgrammeDTO {
	// 查询扫描方案基本信息
	scanProgramme := quality.ScanProgramme{}
	scanProgramme.Id = id
	gormx.SelectOneByConditionX(ctx, &scanProgramme)

	// 检查方案是否存在或已删除
	if scanProgramme.IsDeleted == "" || scanProgramme.IsDeleted == common_constants.DeleteYes {
		return nil
	}

	// 查询方案关联的规则配置文件
	scanProgrammeProfiles := make([]quality.ScanProgrammeProfile, 0)
	gormx.SelectByParamBuilderX(
		ctx,
		gormx.NewParamBuilder().
			Model(&quality.ScanProgrammeProfile{}).
			Eq("programme_id", id).
			Eq("is_deleted", common_constants.DeleteNo),
		&scanProgrammeProfiles,
	)

	// 构建语言与规则模板映射
	scanLanguageProfileDtos := make([]dto.ScanLanguageProfileTemplateDTO, 0)
	languageProfileTemplatesMap := make(map[string][]int64)
	languageKeys := set.New[string]()

	if len(scanProgrammeProfiles) != 0 {
		for _, scanProgrammeProfile := range scanProgrammeProfiles {
			// 解析规则模板ID列表
			templateIds := make([]int64, 0)
			jsonx.UnMarshal([]byte(scanProgrammeProfile.ProfileTemplateIdsStr), &templateIds)

			// 添加语言到集合
			languageKeys.Add(scanProgrammeProfile.Language)

			// 初始化语言对应的模板ID列表
			if languageProfileTemplatesMap[scanProgrammeProfile.Language] == nil {
				languageProfileTemplatesMap[scanProgrammeProfile.Language] = make([]int64, 0)
			}

			// 添加模板ID到语言对应的列表
			languageProfileTemplatesMap[scanProgrammeProfile.Language] = append(
				languageProfileTemplatesMap[scanProgrammeProfile.Language],
				templateIds...,
			)
		}
	}

	// 查询语言信息
	languages := make([]quality.ScanLanguage, 0, languageKeys.Size())
	gormx.SelectByParamBuilderX(
		ctx,
		gormx.NewParamBuilder().
			Model(&quality.ScanLanguage{}).
			In("key", languageKeys.ToSlice()).
			Eq("is_deleted", common_constants.DeleteNo),
		&languages,
	)

	// 构建语言键值与名称的映射
	languageIdNameMap := make(map[string]string)
	for _, language := range languages {
		languageIdNameMap[language.Key] = language.Name
	}

	// 构建语言配置DTO列表
	for langKey, templateIds := range languageProfileTemplatesMap {
		scanLanguageProfileDtos = append(scanLanguageProfileDtos, dto.ScanLanguageProfileTemplateDTO{
			Key:                   langKey,
			Name:                  languageIdNameMap[langKey],
			SelectProfileTemplate: templateIds,
		})
	}

	// 构建返回结果
	result := convert.ScanProgrammeModelToDTO(scanProgramme)
	result.LanguageList = languageKeys.ToSlice()
	result.LanguageProfileTemplate = scanLanguageProfileDtos

	return &result
}

// DeleteScanProgramme 删除扫描方案
func (s ScanProgrammeService) DeleteScanProgramme(ctx *commoncontext.MantisContext, id int64) bool {
	// 查询方案信息
	scanProgramme := quality.ScanProgramme{}
	scanProgramme.Id = id
	gormx.SelectOneByConditionX(ctx, &scanProgramme)

	// 默认方案不允许删除
	if scanProgramme.IsDefault == constants.IsDefault {
		logger.Logger.Panic("默认方案不允许删除！")
	}

	// 检查是否有关联的任务
	_, taskNameList := s.QueryScanTaskNameList(ctx, id, gormx.PageRequest{Page: 1, PageSize: 100})
	if len(taskNameList) > 0 {
		logger.Logger.Panic("该方案已关联任务，不允许删除")
	}

	// 逻辑删除方案
	scanProgramme.IsDeleted = common_constants.DeleteYes
	gormx.UpdateOneByConditionX(ctx, &scanProgramme)

	// 异步删除方案绑定的规则集
	goroutine.Run(func() {
		logger.Logger.Infof("开始删除方案(ID:%d)绑定的规则集", id)

		profiles := make([]quality.ScanProgrammeProfile, 0)
		gormx.SelectByParamBuilderX(
			ctx,
			gormx.NewParamBuilder().
				Model(&quality.ScanProgrammeProfile{}).
				Eq("programme_id", id).
				Eq("is_deleted", common_constants.DeleteNo),
			&profiles,
		)

		s.deleteProfileRuleRelation(profiles)
		logger.Logger.Infof("方案(ID:%d)绑定的规则集删除完成", id)
	})

	return true
}

// QueryScanProgrammeList 查询扫描方案列表
func (s ScanProgrammeService) QueryScanProgrammeList(ctx *commoncontext.MantisContext, name string) []dto.ScanProgrammeDTO {
	// 查询方案列表
	scanProgrammes := make([]quality.ScanProgramme, 0)
	builder := gormx.NewParamBuilder().
		Model(&quality.ScanProgramme{}).
		Eq("is_deleted", common_constants.DeleteNo)

	// 添加名称过滤条件
	if name != "" {
		builder.Like("name", "%"+name+"%")
	}

	// 排序：默认方案优先，然后按修改时间倒序
	builder.OrderByDesc("is_default").OrderByDesc("gmt_modified")

	// 添加公司过滤条件
	if ctx.User.CompanyID != "" {
		builder.Eq("company_id", shipUtils.IDString(ctx.User.CompanyID))
	}

	gormx.SelectByParamBuilderX(ctx, builder, &scanProgrammes)

	// 构建返回结果
	result := make([]dto.ScanProgrammeDTO, 0, len(scanProgrammes))
	for _, scanProgramme := range scanProgrammes {
		// 查询方案关联的语言
		languageKeys := set.New[string]()
		programmeProfiles := make([]quality.ScanProgrammeProfile, 0)

		gormx.SelectByParamBuilderX(
			ctx,
			gormx.NewParamBuilder().
				Model(&quality.ScanProgrammeProfile{}).
				Eq("programme_id", scanProgramme.Id).
				Eq("is_deleted", common_constants.DeleteNo),
			&programmeProfiles,
		)

		// 收集方案关联的语言
		for _, programmeProfile := range programmeProfiles {
			languageKeys.Add(programmeProfile.Language)
		}

		// 构建方案DTO
		scanProgrammeDto := convert.ScanProgrammeModelToDTO(scanProgramme)
		scanProgrammeDto.LanguageList = languageKeys.ToSlice()

		result = append(result, scanProgrammeDto)
	}

	return result
}

// QueryScanTaskNameList 查询扫描方案关联的任务列表
func (s ScanProgrammeService) QueryScanTaskNameList(ctx *commoncontext.MantisContext, id int64, pageReq gormx.PageRequest) (*gormx.PageResult, []quality.ScanTestTask) {
	// 查询方案关联的任务
	pageResult, tasks := taskDao.PageSelectRunningTaskByProgrammeId(ctx, id, pageReq)

	// 查询任务操作者的用户信息
	userIdNameMap := make(map[string]string)
	for _, task := range tasks {
		userIdNameMap[task.Operator] = ""
	}

	// 获取用户名称
	err := cubeBaseRemoteApi.GetUserAdAccountNameMap(userIdNameMap, shipUtils.IDString(ctx.User.CompanyID))
	if err != nil {
		logger.Logger.Warnf("获取用户信息失败: %v", err)
	}

	// 设置任务操作者的名称
	for i, task := range tasks {
		if name, exists := userIdNameMap[task.Operator]; exists {
			tasks[i].Operator = name
		}
	}

	// 设置分页结果的列表
	pageResult.List = tasks

	return pageResult, tasks
}

// QueryProgrammeProfileEnum 查询方案配置文件枚举
func (s ScanProgrammeService) QueryProgrammeProfileEnum(ctx *commoncontext.MantisContext, programmeId int64) []commondto.CodeEnumDTO {
	// 查询方案关联的配置文件
	programmeProfiles := make([]quality.ScanProgrammeProfile, 0)
	gormx.SelectByParamBuilderX(
		ctx,
		gormx.NewParamBuilder().
			Model(&quality.ScanProgrammeProfile{}).
			Eq("programme_id", programmeId).
			Eq("is_deleted", common_constants.DeleteNo),
		&programmeProfiles,
	)

	// 构建返回结果
	result := make([]commondto.CodeEnumDTO, 0, len(programmeProfiles))
	for _, programmeProfile := range programmeProfiles {
		result = append(result, commondto.CodeEnumDTO{
			Value: programmeProfile.CusKey,
			Label: programmeProfile.CusName,
		})
	}

	return result
}

func (s ScanProgrammeService) ModifyScanProgramme(ctx *commoncontext.MantisContext, programmeDTO dto.ScanProgrammeDTO, user commondto.UserInfo) int64 {
	sonarHelper := help.DefaultSonarHelper
	oldProgramme := quality.ScanProgramme{}
	oldProgramme.Id = programmeDTO.Id
	oldProgramme.IsDeleted = common_constants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &oldProgramme)
	programmeName := programmeDTO.Name
	scanProgrammes := make([]quality.ScanProgramme, 0)
	builder := gormx.NewParamBuilder().Model(&quality.ScanProgramme{}).
		Eq("name", programmeName).Eq("is_deleted", common_constants.DeleteNo)
	if user.CompanyID != "" {
		builder.Eq("company_id", user.CompanyID)
	}
	if len(scanProgrammes) != 0 {
		for _, plan := range scanProgrammes {
			if (plan.Name == programmeName) && (plan.Id != programmeDTO.Id) {
				logger.Logger.Panic("方案名称重复")
			}
		}
	}
	scanProgramme := convert.ScanProgrammeDTOToModel(programmeDTO)
	scanProgramme.GmtModified = times.Now()
	scanProgramme.IsDeleted = common_constants.DeleteNo
	scanProgramme.Modifier = user.AdAccount
	if scanProgramme.IsDefault == constants.IsDefault {
		// 将之前的默认方案设为普通方案
		oldDefaultProgramme := quality.ScanProgramme{}
		oldDefaultProgramme.IsDefault = constants.IsDefault
		oldDefaultProgramme.IsDeleted = common_constants.DeleteNo
		oldDefaultProgramme.CompanyId = shipUtils.IDString(user.CompanyID)
		gormx.SelectOneByConditionX(ctx, &oldDefaultProgramme)
		if oldDefaultProgramme.Id != 0 {
			oldDefaultProgramme.IsDefault = constants.NotDefault
			gormx.InsertUpdateOneX(ctx, &oldDefaultProgramme)
		}
	}
	scanProgramme.CompanyId = shipUtils.IDString(user.CompanyID)
	gormx.InsertUpdateOneX(ctx, &scanProgramme)
	oldProfiles := make([]quality.ScanProgrammeProfile, 0)
	gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanProgrammeProfile{}).
		Eq("programme_id", programmeDTO.Id).Eq("is_deleted", common_constants.DeleteNo),
		&oldProfiles)
	// 对已有的规则集全部进行行更名
	if len(oldProfiles) != 0 && oldProgramme.Name != scanProgramme.Name {
		for _, profile := range oldProfiles {
			// 先修改sonar
			newProfileName := strings.ReplaceAll(profile.CusName, oldProgramme.Name, scanProgramme.Name)
			sonarHelper.RenameProfile(profile.CusKey, newProfileName)
			// 修改数据库
			profile.CusName = newProfileName
			gormx.UpdateOneByConditionX(ctx, &profile)
		}
	}
	for i, oldProfile := range oldProfiles {
		tmpIds := make([]int64, 0)
		jsonx.UnMarshal([]byte(oldProfile.ProfileTemplateIdsStr), &tmpIds)
		oldProfiles[i].ProfileTemplateIds = tmpIds
	}
	oldLanguageMap := make(map[string][]int64)
	for _, profile := range oldProfiles {
		if _, ok := oldLanguageMap[profile.Language]; !ok {
			oldLanguageMap[profile.Language] = make([]int64, 0)
		}
		oldLanguageMap[profile.Language] = append(oldLanguageMap[profile.Language], profile.ProfileTemplateIds...)
	}
	newLanguageMap := make(map[string]dto.ScanLanguageProfileTemplateDTO)
	for _, languageProfile := range programmeDTO.LanguageProfileTemplate {
		newLanguageMap[languageProfile.Key] = languageProfile
	}
	oldLanguageSet := set.New[string]()
	for k := range oldLanguageMap {
		oldLanguageSet.Add(k)
	}
	oldLanguages := oldLanguageSet.ToSlice()
	newLanguages := programmeDTO.LanguageList
	// 新增语言
	addLanguages := slice.Difference[string](newLanguages, oldLanguages)
	if len(addLanguages) != 0 {
		logger.Logger.Infof("新增的语言：%v", addLanguages)
		languageProfiles := make([]dto.ScanLanguageProfileTemplateDTO, 0)
		for _, addLanguage := range addLanguages {
			languageProfiles = append(languageProfiles, dto.ScanLanguageProfileTemplateDTO{
				Key:                   addLanguage,
				SelectProfileTemplate: newLanguageMap[addLanguage].SelectProfileTemplate,
			})
		}
		addProgrammeDto := dto.ScanProgrammeDTO{
			Id:                      programmeDTO.Id,
			Name:                    programmeName,
			LanguageProfileTemplate: languageProfiles,
		}
		s.dealRuleProfile(ctx, addProgrammeDto, user)
	}
	// 删除语言
	delLanguages := slice.Difference[string](oldLanguages, newLanguages)
	if len(delLanguages) != 0 {
		logger.Logger.Infof("删除的语言：%v", delLanguages)
		profiles := make([]quality.ScanProgrammeProfile, 0)
		gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanProgrammeProfile{}).
			Eq("programme_id", programmeDTO.Id).In("language", delLanguages).
			Eq("is_deleted", common_constants.DeleteNo), &profiles)
		profileIds := make([]int64, 0)
		for _, profile := range profiles {
			profileIds = append(profileIds, profile.Id)
		}
		s.deleteProfileRuleRelation(profiles)
		// 数据库删除
		gormx.UpdateBatchByParamBuilderAndMapX(ctx, gormx.NewParamBuilder().Model(&quality.ScanProgrammeProfile{}).
			In("id", profileIds), map[string]any{"is_deleted": common_constants.DeleteYes})
	}
	// 获取没有变动的语言（交集）
	mixLanguages := slice.Intersection[string](oldLanguages, newLanguages)
	if len(mixLanguages) != 0 {
		sonarHelper := help.DefaultSonarHelper
		logger.Logger.Infof("检查语言绑定的内置规则集：%v", mixLanguages)
		for _, lan := range mixLanguages {
			n1 := newLanguageMap[lan].SelectProfileTemplate
			o1 := oldLanguageMap[lan]
			if o1 == nil {
				o1 = make([]int64, 0)
			}
			addExcept := slice.Difference[int64](n1, o1)
			delExcept := slice.Difference[int64](o1, n1)
			// 取删除的模版和增加的模版的并集
			allExcept := slice.Concat[int64](addExcept, delExcept)
			templates := make([]quality.ScanProfileTemplate, 0)
			gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanProfileTemplate{}).
				In("id", allExcept).Eq("is_deleted", common_constants.DeleteNo), &templates)
			templateIdMap := make(map[int64]quality.ScanProfileTemplate)
			for _, template := range templates {
				templateIdMap[template.Id] = template
			}
			if len(addExcept) != 0 {
				logger.Logger.Infof("绑定新的规则库！规则语言 id=%v", addExcept)
				scanProgrammeProfile := quality.ScanProgrammeProfile{}
				scanProgrammeProfile.ProgrammeId = programmeDTO.Id
				scanProgrammeProfile.Language = lan
				scanProgrammeProfile.IsDeleted = common_constants.DeleteNo
				gormx.SelectOneByConditionX(ctx, &scanProgrammeProfile)
				if scanProgrammeProfile.Id == 0 { // 查不出中间表，跳过
					continue
				}
				tmpIds := make([]int64, 0)
				jsonx.UnMarshal([]byte(scanProgrammeProfile.ProfileTemplateIdsStr), &tmpIds)
				tmpIds = append(tmpIds, addExcept...)
				scanProgrammeProfile.ProfileTemplateIdsStr = string(jsonx.Marshal(&tmpIds))
				gormx.UpdateOneByConditionX(ctx, &scanProgrammeProfile)
				// 操作sonar激活新增的规则模版的规则
				for _, templateId := range addExcept {
					sonarHelper.ActiveRulesFromTemplate(templateIdMap[templateId].Key, scanProgrammeProfile.CusKey)
				}
			}
			if len(delExcept) != 0 {
				logger.Logger.Infof("删除老规则库！规则库id=%v", delExcept)
				programmeProfile := quality.ScanProgrammeProfile{}
				programmeProfile.ProgrammeId = programmeDTO.Id
				programmeProfile.Language = lan
				programmeProfile.IsDeleted = common_constants.DeleteNo
				gormx.SelectOneByConditionX(ctx, &programmeProfile)
				if programmeProfile.Id == 0 { // 查不出中间表，跳过
					continue
				}
				tmpIds := make([]int64, 0)
				jsonx.UnMarshal([]byte(programmeProfile.ProfileTemplateIdsStr), &tmpIds)
				delIds := slice.Difference[int64](tmpIds, delExcept) // 删除了需要删除的模版后的模版id数组
				programmeProfile.ProfileTemplateIdsStr = string(jsonx.Marshal(&delIds))
				gormx.UpdateOneByConditionX(ctx, &programmeProfile)
				// 操作sonar失活删除的规则模版的规则
				for _, templateId := range delExcept {
					sonarHelper.DeactivateRulesFromTemplate(templateIdMap[templateId].Key, programmeProfile.CusKey, "")
				}
			}
		}
	}
	return programmeDTO.Id
}

// SetProfileActive 关闭实例卡片，不操作sonar
func (s ScanProgrammeService) SetProfileActive(ctx *commoncontext.MantisContext, programmeId int64, programmeProfileId int64, active bool) int32 {
	return scanProgrammeProfileDao.SetProfileActive(ctx, programmeProfileId, active)
}

func (s ScanProgrammeService) QueryScanProgrammeProfileCards(ctx *commoncontext.MantisContext, id int64) dto.ScanProgrammeCardDTO {
	scanProgramme := quality.ScanProgramme{}
	scanProgramme.Id = id
	gormx.SelectOneByConditionX(ctx, &scanProgramme)
	scanProgrammeProfiles := make([]quality.ScanProgrammeProfile, 0)
	gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().
		Model(&quality.ScanProgrammeProfile{}).Eq("programme_id", id).
		Eq("is_deleted", common_constants.DeleteNo),
		&scanProgrammeProfiles)
	profiles := make([]dto.ScanProgrammeCardProfileDTO, 0)
	if len(scanProgrammeProfiles) != 0 {
		profileTemplateIds := make([]int64, 0)
		for _, programmeProfile := range scanProgrammeProfiles {
			tmpIds := make([]int64, 0)
			jsonx.UnMarshal([]byte(programmeProfile.ProfileTemplateIdsStr), &tmpIds)
			profileTemplateIds = append(profileTemplateIds, tmpIds...)
		}
		profileTemplates := make([]quality.ScanProfileTemplate, 0, len(profileTemplateIds))
		gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanProfileTemplate{}).
			In("id", profileTemplateIds), &profileTemplates)
		templateIdMap := make(map[int64]quality.ScanProfileTemplate)
		for _, template := range profileTemplates {
			templateIdMap[template.Id] = template
		}
		for _, programmeProfile := range scanProgrammeProfiles {
			profileResDto := dto.ScanProgrammeCardProfileDTO{
				IsActive:    programmeProfile.IsActive,
				Languages:   []string{programmeProfile.Language},
				ProfileName: programmeProfile.CusName,
				ProfileKey:  programmeProfile.CusKey,
				ProfileId:   programmeProfile.Id,
			}
			tmpIds := make([]int64, 0)
			jsonx.UnMarshal([]byte(programmeProfile.ProfileTemplateIdsStr), &tmpIds)
			templates := make([]dto.ScanProgrammeCardTemplateDTO, 0)
			for _, templateId := range tmpIds {
				template := templateIdMap[templateId]
				templates = append(templates, dto.ScanProgrammeCardTemplateDTO{
					TemplateId:   templateId,
					TemplateName: template.Name,
				})
			}
			profileResDto.Templates = templates
			profiles = append(profiles, profileResDto)
		}
	}
	cardDto := dto.ScanProgrammeCardDTO{}
	cardDto.Id = scanProgramme.Id
	cardDto.Describe = scanProgramme.Describe
	cardDto.Name = scanProgramme.Name
	cardDto.Profiles = profiles
	cardDto.IsDefault = scanProgramme.IsDefault
	return cardDto
}

func (s ScanProgrammeService) dealRuleProfile(ctx *commoncontext.MantisContext, scanProgrammeDto dto.ScanProgrammeDTO, user commondto.UserInfo) {
	profiles := s.saveScanProgrammeProfile(ctx, scanProgrammeDto, user, scanProgrammeDto.Id)
	s.createProfileAndActiveRule(ctx, scanProgrammeDto.Name, profiles, user)
}

func (ScanProgrammeService) saveScanProgrammeProfile(ctx *commoncontext.MantisContext, scanProgrammeDto dto.ScanProgrammeDTO, user commondto.UserInfo, programmeId int64) []quality.ScanProgrammeProfile {
	scanProgrammeProfiles := make([]quality.ScanProgrammeProfile, 0)
	languageProfile := scanProgrammeDto.LanguageProfileTemplate
	for _, scanLanguageProfile := range languageProfile {
		languageId := scanLanguageProfile.Key
		templateIds := scanLanguageProfile.SelectProfileTemplate
		scanProgrammeProfiles = append(scanProgrammeProfiles, quality.ScanProgrammeProfile{
			Addons: common_model.Addons{
				GmtCreated:  times.Now(),
				GmtModified: times.Now(),
				Creator:     user.AdAccount,
				Modifier:    user.AdAccount,
				IsDeleted:   common_constants.DeleteNo,
			},
			ProgrammeId:           programmeId,
			ProfileTemplateIds:    templateIds,
			ProfileTemplateIdsStr: string(jsonx.Marshal(&templateIds)),
			Language:              languageId,
			IsActive:              true,
		})
	}
	gormx.InsertBatchX(ctx, &scanProgrammeProfiles)
	logger.Logger.Info("save scanProgrammeProfile success")
	return scanProgrammeProfiles
}

func (s ScanProgrammeService) createProfileAndActiveRule(ctx *commoncontext.MantisContext, programmeName string, programmeProfiles []quality.ScanProgrammeProfile, user commondto.UserInfo) []string {
	profileNames := make([]string, 0)
	languageIds := make([]string, 0, len(programmeProfiles))
	for _, programmeProfile := range programmeProfiles {
		languageIds = append(languageIds, programmeProfile.Language)
	}
	languages := make([]quality.ScanLanguage, 0)
	gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanLanguage{}).In("key", languageIds), &languages)
	languageMap := make(map[string]quality.ScanLanguage)
	for _, language := range languages {
		languageMap[language.Key] = language
	}
	for _, programmeProfile := range programmeProfiles {
		language := languageMap[programmeProfile.Language]
		profileName := shipUtils.IDString(user.CompanyID) + "_" + programmeName + "_" + language.Key
		profileNames = append(profileNames, profileName)
		// sonar新建规则集实例，并且根据模版激活规则
		key := s.createActiveProfile(ctx, programmeProfile.ProfileTemplateIds, language, profileName)
		scanProgrammeProfileDao.UpdateNameAndKeyByIds(ctx, profileName, key, programmeProfile.Id)
	}
	return profileNames
}

// sonar新建规则集实例，并且根据模版激活规则
func (ScanProgrammeService) createActiveProfile(ctx *commoncontext.MantisContext, templateIds []int64, language quality.ScanLanguage, customProfileName string) string {
	sonarHelper := help.DefaultSonarHelper
	customKey := sonarHelper.CreateProfile(customProfileName, language.Key)
	logger.Logger.Infof("rule绑定profile实例，key=%v", customKey)
	templates := make([]quality.ScanProfileTemplate, 0)
	gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanProfileTemplate{}).In("id", templateIds),
		&templates)
	for _, template := range templates {
		sonarHelper.ActiveRulesFromTemplate(template.Key, customKey)
	}
	return customKey
}

// sonar删除规则集实例
func (s ScanProgrammeService) deleteProfileRuleRelation(profiles []quality.ScanProgrammeProfile) {
	sonarHelper := help.DefaultSonarHelper
	if len(profiles) == 0 {
		return
	}
	cusNameLanguageMap := make(map[string]string)
	for _, profile := range profiles {
		cusNameLanguageMap[profile.CusName] = profile.Language
	}
	logger.Logger.Info("删除规则关联关系！")
	for k, v := range cusNameLanguageMap {
		sonarHelper.DeleteProfile(v, k)
	}
	logger.Logger.Info("end 删除规则关联关系！")
}
