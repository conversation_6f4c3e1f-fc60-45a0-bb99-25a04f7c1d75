package quality

import (
	"slices"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality/help"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

// conditions 定义Sonar质量门禁的条件映射
// 键: 指标名称
// 值: 比较操作符 (LT:小于, GT:大于)
var conditions = map[string]string{
	// 增量指标
	"new_branch_coverage":          "LT", // 分支覆盖率小于阈值
	"new_duplicated_lines_density": "GT", // 重复行密度大于阈值
	"new_line_coverage":            "LT", // 行覆盖率小于阈值
	"new_lines_to_cover":           "GT", // 覆盖的行数大于阈值
	"new_blocker_violations":       "GT", // 阻断性违规大于阈值
	"new_critical_violations":      "GT", // 严重违规大于阈值
	"new_lines":                    "GT", // 新增行数大于阈值
	"new_major_violations":         "GT", // 重要违规大于阈值
	"new_minor_violations":         "GT", // 次要违规大于阈值

	// 全量指标
	"blocker_violations":       "GT", // 阻断性违规大于阈值
	"comment_lines":            "LT", // 注释行数小于阈值
	"comment_lines_density":    "LT", // 注释行密度小于阈值
	"branch_coverage":          "LT", // 分支覆盖率小于阈值
	"critical_violations":      "GT", // 严重违规大于阈值
	"complexity":               "GT", // 复杂度大于阈值
	"duplicated_lines_density": "GT", // 重复行密度大于阈值
	"generated_ncloc":          "GT", // 生成的代码行数大于阈值
	"line_coverage":            "LT", // 行覆盖率小于阈值
	"lines":                    "GT", // 总行数大于阈值
	"ncloc":                    "GT", // 代码行数大于阈值
	"lines_to_cover":           "GT", // 需要覆盖的行数大于阈值
	"major_violations":         "GT", // 重要违规大于阈值
	"minor_violations":         "GT", // 次要违规大于阈值
	"test_success_density":     "LT", // 测试成功密度小于阈值
}

// InitSonarQualityGates 初始化Sonar质量门禁
// 如果名为"online"的质量门禁已存在，则不做任何操作
// 否则，创建质量门禁并设置条件
func InitSonarQualityGates() {
	sonarHelper := help.DefaultSonarHelper

	// 使用defer处理可能出现的异常
	defer func() {
		if err := recover(); err != nil {
			logger.Logger.Errorf("对%s的sonar质量阀初始化失败, err = %v", sonarHelper.Domain, err)
		}
	}()

	// 检查"online"质量门禁是否已存在
	existingGates := sonarHelper.GetQualityGates()
	if slices.Contains(existingGates, "online") {
		// 已存在，不需要创建
		return
	}

	// 创建新的质量门禁
	qualityGateId := sonarHelper.CreateQualityGate()

	// 为每个条件添加规则
	for metricKey, operator := range conditions {
		sonarHelper.AddCondition(qualityGateId, metricKey, operator)
	}

	// 设置为默认质量门禁
	if sonarHelper.SetDefault(qualityGateId) {
		logger.Logger.Infof("对%s的sonar质量阀初始化成功", sonarHelper.Domain)
	} else {
		logger.Logger.Errorf("对%s的sonar质量阀初始化失败", sonarHelper.Domain)
	}
}
