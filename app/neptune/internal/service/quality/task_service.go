package quality

import (
	"context"
	"encoding/base64"
	"fmt"
	"math"
	"net/url"
	"path"
	"strconv"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/convert"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/message/payload"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/remote"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality/help"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/utils"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/pubsub"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	commonremote "git.zhonganinfo.com/zainfo/cube-mantis/pkg/remote"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/third_party/cmdb"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	shipcmdb "git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/cmdb"
	shiputils "git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
	set "github.com/duke-git/lancet/v2/datastructure/set"
	"github.com/duke-git/lancet/v2/xerror"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	corev1 "k8s.io/api/core/v1"
	"knative.dev/pkg/apis"
)

// TaskService 扫描任务服务
type TaskService struct{}

// 依赖的服务组件
var (
	gatesService        ScanQualityGatesService
	filterConfigService ScanTaskFilterConfigService
	gitHelper           help.GitHelper
	cubeBaseRemoteApi   commonremote.CubeBaseRemoteApi
	shipRemoteApi       commonremote.ShipRemoteApi
)

// GetCommitInfo 获取Git提交历史信息
func (t TaskService) GetCommitInfo(ctx *commoncontext.MantisContext, id string, page int64, size int64, timeZone, branch string) []dto.GitCommitInfo {
	// 获取应用的Git信息
	gitMap := t.getAppGitInfo(id)
	gitLabHelper := help.GitLabHelper{
		Domain: gitMap["gitDomain"],
		Token:  gitMap["gitToken"],
	}

	// 设置查询时间范围
	// 默认获取一周内的提交记录
	since := time.Now().AddDate(0, 0, -7).Format(times.ISO8601TimeFormat)
	until := time.Now().Format(times.ISO8601TimeFormat)

	// 如果指定了时间范围，则使用指定的范围
	if timeZone != "" {
		since = times.GetDataFromCommaString(timeZone, 0).Format(times.ISO8601TimeFormat)
		until = times.GetDataFromCommaString(timeZone, 1).Format(times.ISO8601TimeFormat)
	}

	// 查询提交信息
	gitCommitInfos := gitLabHelper.QueryProjectCommitInfo(
		gitMap["codePath"],
		branch,
		page,
		size,
		since,
		until,
	)

	// 转换为DTO对象
	result := make([]dto.GitCommitInfo, 0, len(gitCommitInfos))
	for _, info := range gitCommitInfos {
		result = append(result, dto.GitCommitInfo{
			Id:            info.Id,
			Message:       info.Message,
			AuthorName:    info.AuthorName,
			CommittedDate: times.Parse(times.ISO8601TimeFormatWithColon, info.CommittedDate),
		})
	}

	return result
}

// GetBranchList 获取应用的分支列表
func (t TaskService) GetBranchList(appId string, search string, page, pageSize int64) *gormx.PageResult {
	// 获取应用的Git信息
	gitMap := t.getAppGitInfo(appId)
	gitLabHelper := help.GitLabHelper{
		Domain: gitMap["gitDomain"],
		Token:  gitMap["gitToken"],
	}

	// 查询分支信息
	gitBranchDTOS := gitLabHelper.QueryBranchInfoByCodePath(
		gitMap["codePath"],
		page,
		pageSize,
		search,
	)

	// 转换为通用的代码枚举DTO对象
	codeEnums := make([]commondto.CodeEnumDTO, 0, len(gitBranchDTOS))
	for _, branchDTO := range gitBranchDTOS {
		codeEnums = append(codeEnums, commondto.CodeEnumDTO{
			Label: branchDTO.Name,
			Value: branchDTO.CommitId,
		})
	}

	// 构建分页结果
	return &gormx.PageResult{
		PageSize:    pageSize,
		CurrentPage: page,
		List:        codeEnums,
	}
}

// GetBranchListByCodePath 根据代码路径获取分支列表
func (t TaskService) GetBranchListByCodePath(ctx *commoncontext.MantisContext, hubUrl, search string, page, pageSize int64) *gormx.PageResult {
	// 调用Ship服务获取分支列表
	branchList := remote.ShipInvoke{}.GetBranchListByCodePath(ctx, hubUrl, search, page, pageSize)

	// 转换为通用的代码枚举DTO对象
	codeEnums := make([]commondto.CodeEnumDTO, 0, len(branchList))
	for _, branchDTO := range branchList {
		codeEnums = append(codeEnums, commondto.CodeEnumDTO{
			Label: branchDTO.Name,
			Value: branchDTO.Name,
		})
	}

	// 构建分页结果
	return &gormx.PageResult{
		PageSize:    pageSize,
		CurrentPage: page,
		List:        codeEnums,
	}
}

// GetBranchCommit 获取分支的提交信息
func (t TaskService) GetBranchCommit(appId string, branch string) commondto.CodeEnumDTO {
	// 获取应用的Git信息
	gitMap := t.getAppGitInfo(appId)
	gitLabHelper := help.GitLabHelper{
		Domain: gitMap["gitDomain"],
		Token:  gitMap["gitToken"],
	}

	// 查询分支信息
	var gitBranchDTOS []dto.GitBranchDTO

	// 使用defer-recover捕获可能的异常
	defer func() {
		if err := recover(); err != nil {
			logger.Logger.Warnf("获取应用分支失败, appId:%s, branch:%s, 返回默认分支master, err=%v", appId, branch, err)
			// 如果出错或没有查到分支，默认返回master分支
			if len(gitBranchDTOS) == 0 {
				gitBranchDTOS = append(gitBranchDTOS, dto.GitBranchDTO{Name: "master"})
			}
		}
	}()

	// 查询指定分支的信息
	gitBranchDTOS = gitLabHelper.QueryBranchInfoByCodePath(gitMap["codePath"], 1, 1, branch)

	// 若没有查询结果，返回默认分支master
	if len(gitBranchDTOS) == 0 {
		gitBranchDTOS = append(gitBranchDTOS, dto.GitBranchDTO{Name: "master"})
	}

	// 返回分支信息
	return commondto.CodeEnumDTO{
		Label: gitBranchDTOS[0].Name,
		Value: gitBranchDTOS[0].CommitId,
	}
}

// ExecScanTestTask 执行扫描测试任务
func (t TaskService) ExecScanTestTask(ctx *commoncontext.MantisContext, reqDTO dto.ScanTestWebExecReqDTO, user commondto.UserInfo) {
	// 查询任务信息
	scanTestTask := quality.ScanTestTask{}
	scanTestTask.Id = reqDTO.TaskId
	scanTestTask.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &scanTestTask)

	// 验证任务配置
	if scanTestTask.ProgrammeId == 0 && scanTestTask.JobType == constants.ScanJobType {
		logger.Logger.Panic("未选择扫描方案！")
	}

	// 查询扫描方案
	scanProgramme := quality.ScanProgramme{}
	if scanTestTask.ProgrammeId > 0 {
		scanProgramme.Id = scanTestTask.ProgrammeId
		gormx.SelectOneByConditionX(ctx, &scanProgramme)

		// 如果方案已删除，更新任务并报错
		if scanProgramme.IsDeleted == commonconstants.DeleteYes && scanTestTask.JobType == constants.ScanJobType {
			scanTestTask.ProgrammeId = 0
			gormx.InsertUpdateOneX(ctx, &scanTestTask)
			logger.Logger.Panic("扫描方案已删除，请重新选择方案！")
		}
	}

	// 更新任务参数
	scanTestTask.Mode = reqDTO.Mode
	scanTestTask.CommitTimeFrame = reqDTO.CommitTimeFrame
	scanTestTask.CommitBaseId = reqDTO.CommitBaseId
	scanTestTask.CommitCompareId = reqDTO.CommitCompareId
	gormx.UpdateOneByConditionX(ctx, &scanTestTask)

	// 创建执行历史记录
	execHistory := quality.ScanTestExecHis{}
	execHistory.Branch = scanTestTask.Branch
	execHistory.GmtCreated = times.Now()
	execHistory.GmtModified = times.Now()
	execHistory.Creator = user.AdAccount
	execHistory.Modifier = user.AdAccount
	execHistory.TaskId = scanTestTask.Id

	// 设置执行模式和提交ID
	if reqDTO.Mode == "" {
		execHistory.Mode = scanTestTask.Mode
		execHistory.CommitBaseId = scanTestTask.CommitBaseId
		execHistory.CommitCompareId = scanTestTask.CommitCompareId
	} else {
		execHistory.Mode = reqDTO.Mode
		execHistory.CommitBaseId = reqDTO.CommitBaseId
		execHistory.CommitCompareId = reqDTO.CommitCompareId
	}

	// 设置执行状态和操作者
	execHistory.Status = task.TaskStatusRun
	execHistory.StatusMsg = task.StatusCodeToMemo(execHistory.Status)
	execHistory.Operator = user.AdAccount
	gormx.InsertUpdateOneX(ctx, &execHistory)

	// 更新任务的最后执行记录
	updateTask := quality.ScanTestTask{}
	updateTask.Id = scanTestTask.Id
	updateTask.Operator = user.AdAccount
	updateTask.LastScanTestExecHis = execHistory.Id
	updateTask.LastScanTestExecTime = times.Now()
	gormx.UpdateOneByConditionX(ctx, &updateTask)

	// 查询应用信息
	baseAppInfo, err := cmdb.Client.GetAppById(ctx, scanTestTask.AppId)
	if err != nil {
		logger.Logger.Panicf("查询应用信息失败: %s", err.Error())
	}

	// 查询过滤配置
	scanTaskFilterDTOS := filterConfigService.QueryFilter(
		ctx,
		strconv.FormatInt(scanTestTask.Id, 10),
		constants.FilterConfigTypeTask,
		"",
	)

	// 构建包含和排除路径集合
	exclusionPath := set.New[string]()
	inclusionPath := set.New[string]()

	if scanTestTask.JobType == constants.ScanJobType {
		for _, filterDTO := range scanTaskFilterDTOS {
			if filterDTO != nil {
				filters := filterDTO.ConfigSlices
				for _, config := range filters {
					if config.Type == constants.FilterConfigExclusion {
						exclusionPath.Add(config.Path)
					} else if config.Type == constants.FilterConfigInclusion {
						inclusionPath.Add(config.Path)
					}
				}
			}
		}
	}

	// 查询programme的规则集是不是关闭
	programmeProfile := quality.ScanProgrammeProfile{}
	programmeProfile.ProgrammeId = scanProgramme.Id
	programmeProfile.Language = scanTestTask.Language
	programmeProfile.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &programmeProfile)
	if programmeProfile.IsActive == constants.RuleDeActive && programmeProfile.Id != 0 {
		execHistory.Status = task.TaskStatusFail
		execHistory.StatusMsg = task.StatusCodeToMemo(execHistory.Status)
		gormx.UpdateOneByConditionX(ctx, &execHistory)
		logger.Logger.Panic("关联方案的规则集被关闭")
	}
	// 向队列塞入消息
	payloadData := &payload.QualityScanTaskPayload{}
	gitInfo := gitHelper.GetGitInfoByUrl(scanTestTask.CodeUrl)
	if gitInfo == nil {
		logger.Logger.Errorf("git url is not valid! %v", scanTestTask.CodeUrl)
		return
	}
	payloadData = payloadData.WithBiz(payload.QualityScanBizParam{
		Type:     scanTestTask.JobType,
		Mode:     execHistory.Mode,
		Language: scanTestTask.Language,
		// JavaVersion:   baseAppInfo.LanguageVersion,
		ExclusionPath: strings.Join(exclusionPath.ToSlice(), ","),
		InclusionPath: strings.Join(inclusionPath.ToSlice(), ","),
		AppType:       scanTestTask.ExecType,
		ModulePath:    baseAppInfo.BuildPath,
		ExecHisId:     execHistory.Id,
		CallBack:      fmt.Sprintf(configs.Config.App.Svc+"/rick/openapi/back/sonarUpdateBackCall/%d", execHistory.Id),
	}).WithCode(payload.QualityScanCodeParam{
		CodeUrl:         scanTestTask.CodeUrl,
		GitUser:         gitInfo.User,
		GitToken:        gitInfo.Token,
		Branch:          scanTestTask.Branch,
		BaseCommitId:    execHistory.CommitBaseId,
		CompareCommitId: execHistory.CommitCompareId,
	}).WithSonar(payload.QualityScanSonarParam{
		SonarUrl:      configs.Config.Modules.Neptune.Sonar.Domain,
		SonarToken:    configs.Config.Modules.Neptune.Sonar.Token,
		SonarPdf:      scanTestTask.PdfGen,
		SonarProfile:  programmeProfile.CusName,
		SonarCallback: fmt.Sprintf(configs.Config.Domain.Cube+"/magic/api/rick/openapi/back/sonarUpdateBackCall/%d", execHistory.Id),
	}).WithMvn(payload.QualityScanMvnParam{
		MvnRepo: configs.Config.Modules.Neptune.Maven.Repo,
		MvnUser: configs.Config.Modules.Neptune.Maven.UserName,
		MvnPw:   configs.Config.Modules.Neptune.Maven.Password.String(),
	})
	pubsub.SendToRedisQueue(payloadData, scanTestTask.From)
}

func (t TaskService) ShipRegisterJob(ctx *commoncontext.MantisContext, request dto.ScanTestInfoDTO, jobType string) string {
	// 查询programme
	scanProgramme := quality.ScanProgramme{}
	scanProgramme.IsDefault = constants.IsDefault
	scanProgramme.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &scanProgramme)
	if scanProgramme.Id == 0 && jobType == constants.ScanJobType {
		logger.Logger.Panic("未选择默认方案！")
	}
	if jobType == "unitTest" {
		jobType = constants.UnitTestJobType
	}
	logger.Logger.Infof("ship 请求接受数据: %s", jsonx.Marshal(&request))
	// 搜索并修改或新增task
	scanTestTask := quality.ScanTestTask{}
	scanTestTask.TaskName = constants.SourcePipeline + "_" + strconv.FormatInt(request.Pid, 10) + "_" + request.Stage + request.AppId + "_" + jobType
	scanTestTask.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &scanTestTask)
	baseApplicationDTO, err := cmdb.Client.GetAppById(ctx, request.AppId)
	if err != nil {
		logger.Logger.Panicf("查询应用错误, err=%s", err.Error())
	}
	scanTestTask.AppId = request.AppId
	scanTestTask.ShipPid = strconv.FormatInt(request.Pid, 10)
	scanTestTask.From = constants.SourcePipeline
	scanTestTask.Language = request.Language
	scanTestTask.CodeUrl = baseApplicationDTO.VcsFullPath
	scanTestTask.ExecType = baseApplicationDTO.AppClassify
	scanTestTask.JobType = jobType
	scanTestTask.CompanyId = shiputils.IDString(baseApplicationDTO.CompanyId)
	scanTestTask.Branch = request.Branch
	scanTestTask.Mode = request.Mode
	scanTestTask.CommitBaseId = request.BasicCommitId
	scanTestTask.CommitCompareId = request.CompareCommitId
	scanTestTask.Creator = constants.UserDevops
	scanTestTask.Modifier = constants.UserDevops
	scanTestTask.Operator = constants.UserDevops
	scanTestTask.ProgrammeId = scanProgramme.Id
	if scanTestTask.Id == 0 {
		scanTestTask.GmtCreated = times.Now()
		scanTestTask.GmtModified = times.Now()
		gormx.InsertUpdateOneX(ctx, &scanTestTask)
	} else {
		scanTestTask.GmtModified = times.Now()
		gormx.UpdateOneByConditionX(ctx, &scanTestTask)
	}
	// 插入一条历史记录
	his := quality.ScanTestExecHis{}
	his.GmtCreated = times.Now()
	his.GmtModified = times.Now()
	his.Creator = constants.UserDevops
	his.Modifier = constants.UserDevops
	his.TaskId = scanTestTask.Id
	his.Mode = request.Mode
	his.CommitBaseId = request.BasicCommitId
	his.CommitCompareId = request.CompareCommitId
	his.Status = task.TaskStatusRun
	his.StatusMsg = task.StatusCodeToMemo(his.Status)
	his.CallBackUrl = request.CallBackAddr
	his.ShipParams = string(jsonx.Marshal(&request))
	his.Stage = request.Stage
	his.Operator = constants.UserDevops
	gormx.InsertUpdateOneX(ctx, &his)
	// 更新task
	updateTask := quality.ScanTestTask{}
	updateTask.Id = scanTestTask.Id
	updateTask.Operator = constants.UserDevops
	updateTask.LastScanTestExecHis = his.Id
	updateTask.LastScanTestExecTime = times.Now()
	gormx.UpdateOneByConditionX(ctx, &updateTask)
	scanProfile := ""
	if scanProgramme.Id != 0 {
		// 查询programme的规则集是不是关闭
		programmeProfile := quality.ScanProgrammeProfile{}
		programmeProfile.ProgrammeId = scanProgramme.Id
		programmeProfile.Language = scanTestTask.Language
		gormx.SelectOneByConditionX(ctx, &programmeProfile)
		if programmeProfile.IsActive == constants.RuleDeActive && programmeProfile.Id != 0 {
			his.Status = task.TaskStatusFail
			his.StatusMsg = task.StatusCodeToMemo(his.Status)
			gormx.UpdateOneByConditionX(ctx, &his)
			logger.Logger.Panic("关联方案的规则集被关闭")
		}
		scanProfile = scanProgramme.Name + "_" + scanTestTask.Language
	}
	// 查询exclusion path
	scanTaskFilterDTOS := filterConfigService.QueryFilter(ctx, strconv.FormatInt(scanTestTask.Id, 10), constants.FilterConfigTypeTask, "")
	exclusionPath := set.New[string]()
	inclusionPah := set.New[string]()
	if scanTestTask.JobType == constants.ScanJobType {
		for _, filterDTO := range scanTaskFilterDTOS {
			if filterDTO != nil {
				filters := filterDTO.ConfigSlices
				for _, config := range filters {
					if config.Type == constants.FilterConfigExclusion {
						exclusionPath.Add(config.Path)
					} else if config.Type == constants.FilterConfigInclusion {
						inclusionPah.Add(config.Path)
					}
				}
			}
		}
	} else {
		for t, filterDTO := range scanTaskFilterDTOS {
			if t != constants.FilterConfigTypeApp {
				continue
			}
			if filterDTO != nil {
				filters := filterDTO.ConfigSlices
				for _, config := range filters {
					if config.Type == constants.FilterConfigExclusion {
						exclusionPath.Add(config.Path)
					} else if config.Type == constants.FilterConfigInclusion {
						inclusionPah.Add(config.Path)
					}
				}
			}
		}
	}
	// 向队列塞入消息
	payloadData := &payload.QualityScanTaskPayload{}
	gitInfo := gitHelper.GetGitInfoByUrl(scanTestTask.CodeUrl)
	if gitInfo == nil {
		logger.Logger.Panicf("git url is not valid! %v", scanTestTask.CodeUrl)
	}
	payloadData = payloadData.WithBiz(payload.QualityScanBizParam{
		ExecHisId:     his.Id,
		Type:          jobType,
		Mode:          his.Mode,
		Language:      scanTestTask.Language,
		JavaVersion:   strings.ReplaceAll(request.JdkVersion, "jdk", "Java"),
		ExclusionPath: strings.Join(exclusionPath.ToSlice(), ","),
		InclusionPath: strings.Join(inclusionPah.ToSlice(), ","),
		AppType:       scanTestTask.ExecType,
		ModulePath:    request.DirPath,
		CallBack:      fmt.Sprintf(configs.Config.App.Svc+"/rick/openapi/back/sonarUpdateBackCall/%d", his.Id),
	}).WithCode(payload.QualityScanCodeParam{
		CodeUrl:         scanTestTask.CodeUrl,
		GitUser:         gitInfo.User,
		GitToken:        gitInfo.Token,
		Branch:          scanTestTask.Branch,
		BaseCommitId:    request.BasicCommitId,
		CompareCommitId: request.CompareCommitId,
	}).WithSonar(payload.QualityScanSonarParam{
		SonarUrl:      configs.Config.Modules.Neptune.Sonar.Domain,
		SonarToken:    configs.Config.Modules.Neptune.Sonar.Token,
		SonarPdf:      scanTestTask.PdfGen,
		SonarProfile:  scanProfile,
		SonarCallback: fmt.Sprintf(configs.Config.Domain.Cube+"/magic/api/rick/openapi/back/sonarUpdateBackCall/%d", his.Id),
	}).WithMvn(payload.QualityScanMvnParam{
		MvnRepo: configs.Config.Modules.Neptune.Maven.Repo,
		MvnUser: configs.Config.Modules.Neptune.Maven.UserName,
		MvnPw:   configs.Config.Modules.Neptune.Maven.Password.String(),
	})
	pubsub.SendToRedisQueue(payloadData, scanTestTask.From)
	return fmt.Sprintf("%s/magic/codeScan/task/historyDetail?taskId=%d&hisId=%d&tab=scanResult", configs.Config.Domain.Cube, scanTestTask.Id, his.Id)
}

func (t TaskService) StopScanTestTask(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	// 搜索task
	scanTestTask := quality.ScanTestTask{}
	scanTestTask.Id = id
	scanTestTask.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &scanTestTask)
	// 搜索历史记录, 更新表并且删除tekton task
	if scanTestTask.LastScanTestExecHis != 0 {
		his := quality.ScanTestExecHis{}
		his.Id = scanTestTask.LastScanTestExecHis
		his.IsDeleted = commonconstants.DeleteNo
		gormx.SelectOneByConditionX(ctx, &his)
		goroutine.Run(func() {
			k8sTaskId := his.K8sTaskId
			ctx := context.Background()
			if k8sTaskId != "" {
				provider, _ := driver.NewDriverProvider()
				err := provider.Cancel(ctx, k8sTaskId)
				if err != nil {
					logger.Logger.Errorf("taskId=%v,k8sTaskId: %s,取消任务失败!err=%v", id, k8sTaskId, err)
				}
			}
		})
		updateHis := quality.ScanTestExecHis{}
		updateHis.Id = his.Id
		updateHis.GmtModified = times.Now()
		updateHis.Modifier = user.AdAccount
		updateHis.Status = task.TaskStatusFail
		updateHis.StatusMsg = task.StatusCodeToMemo(updateHis.Status)
		gormx.UpdateOneByConditionX(ctx, &updateHis)
		// 流水线任务，通知ship
		if scanTestTask.From == constants.SourcePipeline {
			hisDTO := ScanTestExecHisService{}.GetScanTestExecHis(ctx, updateHis.Id)
			shipInvokeHelper.InvokeStaticPublicShip(ctx, *hisDTO)
		}
	}
}

func (t TaskService) GetScanTestTask(ctx *commoncontext.MantisContext, taskId int64) dto.ScanTestTaskConfigDTO {
	scanTestTask := quality.ScanTestTask{}
	scanTestTask.Id = taskId
	gormx.SelectOneByConditionX(ctx, &scanTestTask)
	// 查询方案是否存在
	programmes := make([]quality.ScanProgramme, 0)
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(&quality.ScanProgramme{}).Eq("id", scanTestTask.ProgrammeId).
			Eq("is_deleted", commonconstants.DeleteNo).Limit(1),
		&programmes)

	if len(programmes) == 0 {
		scanTestTask.ProgrammeId = 0
	}
	res := dto.ScanTestTaskConfigDTO{
		BaseInfo: scanTestTask,
	}
	if scanTestTask.LastScanTestExecHis != 0 {
		execHis := quality.ScanTestExecHis{}
		execHis.Id = scanTestTask.LastScanTestExecHis
		gormx.SelectOneByConditionX(ctx, &execHis)
	}
	res.TaskFilterInfo = scanTaskFilterConfigService.QueryFilter(ctx, strconv.FormatInt(taskId, 10), constants.FilterConfigTypeTask, "")[constants.FilterConfigTypeTask]
	qualityGatesInfo := gatesService.SelectByTaskId(ctx, taskId)
	if qualityGatesInfo.Id != 0 {
		res.QualityGatesInfo = &dto.ScanQualityGatesDTO{}
		res.QualityGatesInfo.Id = qualityGatesInfo.Id
		res.QualityGatesInfo.TaskId = qualityGatesInfo.TaskId
		res.QualityGatesInfo.IsActive = qualityGatesInfo.IsActive
		res.QualityGatesInfo.BlockerViolations = qualityGatesInfo.BlockerViolations
		res.QualityGatesInfo.CriticalViolations = qualityGatesInfo.CriticalViolations
		res.QualityGatesInfo.MajorViolations = qualityGatesInfo.MajorViolations
		res.QualityGatesInfo.MinorViolations = qualityGatesInfo.MinorViolations
	}
	return res
}

func (t TaskService) InsertTask(ctx *commoncontext.MantisContext, task quality.ScanTestTask, user commondto.UserInfo) int64 {
	appInfo, err := cmdb.Client.GetAppById(ctx, task.AppId)
	if err != nil {
		logger.Logger.Panicf("查询应用错误, err=%s", err.Error())
	}
	task.ExecType = appInfo.AppClassify
	task.Language = appInfo.Language
	task.IsDeleted = commonconstants.DeleteNo
	task.GmtModified = times.Now()
	task.Modifier = user.AdAccount
	task.Operator = user.AdAccount
	task.From = constants.SourceWeb
	task.CodeUrl = appInfo.VcsFullPath
	task.CompanyId = shiputils.IDString(user.CompanyID)
	task.AppName = appInfo.Name
	if task.Id == 0 {
		task.GmtCreated = times.Now()
		task.Creator = user.AdAccount
		gormx.InsertUpdateOneX(ctx, &task)
	} else {
		gormx.InsertUpdateOneX(ctx, &task)
	}
	return task.Id
}

func (t TaskService) DeleteTask(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	// 删除task
	scanTestTask := quality.ScanTestTask{}
	scanTestTask.Id = id
	scanTestTask.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &scanTestTask)
	// 查询是否正在运行
	if scanTestTask.LastScanTestExecHis != 0 {
		his := quality.ScanTestExecHis{}
		his.Id = scanTestTask.LastScanTestExecHis
		his.IsDeleted = commonconstants.DeleteNo
		gormx.SelectOneByConditionX(ctx, &his)
		if task.CheckRunning(his.Status) {
			logger.Logger.Panic("任务正在运行中")
		}
	}
	scanTestTask.IsDeleted = commonconstants.DeleteYes
	scanTestTask.Modifier = user.AdAccount
	scanTestTask.GmtModified = times.Now()
	gormx.UpdateOneByConditionX(ctx, &scanTestTask)
	// 删除质量配置
	scanQualityGate := quality.ScanQualityGates{}
	scanQualityGate.TaskId = id
	scanQualityGate.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &scanQualityGate)
	if scanQualityGate.Id != 0 {
		scanQualityGate.IsDeleted = commonconstants.DeleteYes
		scanQualityGate.GmtModified = times.Now()
		scanQualityGate.Modifier = user.AdAccount
		gormx.UpdateOneByConditionX(ctx, &scanQualityGate)
	}
}

func (t TaskService) SaveTaskConfig(ctx *commoncontext.MantisContext, configDTO dto.ScanTestTaskConfigDTO, user commondto.UserInfo) int64 {
	jobType := constants.ScanJobType
	if configDTO.TaskType != "" {
		jobType = configDTO.TaskType
	}
	configDTO.BaseInfo.JobType = jobType
	taskId := t.InsertTask(ctx, configDTO.BaseInfo, user)
	if jobType == constants.ScanJobType {
		configDTO.QualityGatesInfo.TaskId = taskId
		configDTO.TaskFilterInfo.RelationId = strconv.FormatInt(taskId, 10)
		if configDTO.QualityGatesInfo != nil {
			gatesService.InsertTaskConfig(ctx, *(configDTO.QualityGatesInfo), user)
		}
		configDTO.TaskFilterInfo.RelationId = strconv.FormatInt(configDTO.BaseInfo.Id, 10)
		configDTO.TaskFilterInfo.Type = constants.FilterConfigTypeTask
		if configDTO.TaskFilterInfo != nil {
			configDTO.TaskFilterInfo.RelationId = strconv.FormatInt(taskId, 10)
			filterConfigService.ModifyFilter(ctx, *(configDTO.TaskFilterInfo), user)
		}
	}
	return taskId
}

func (t TaskService) GetScanTestTaskOverview(ctx *commoncontext.MantisContext, taskId int64, hisId int64) dto.ScanTaskOverviewDTO {
	scanTestTask := quality.ScanTestTask{}
	scanTestTask.Id = taskId
	scanTestTask.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &scanTestTask)
	if hisId == 0 {
		logger.Logger.Info("查询全量趋势！")
		hisId = scanTestTask.LastScanTestExecHis
	}
	scanTaskOverviewDTO := dto.ScanTaskOverviewDTO{
		TaskId: taskId,
	}
	if hisId != 0 {
		execHis := quality.ScanTestExecHis{}
		execHis.Id = hisId
		execHis.IsDeleted = commonconstants.DeleteNo
		gormx.SelectOneByConditionX(ctx, &execHis)
		scanTaskOverviewDTO = dto.ScanTaskOverviewDTO{
			HisId:                  execHis.Id,
			TaskId:                 taskId,
			Mode:                   execHis.Mode,
			From:                   scanTestTask.From,
			Status:                 execHis.Status,
			StatusMsg:              execHis.StatusMsg,
			BlockerViolations:      execHis.BlockerViolations,
			CriticalViolations:     execHis.CriticalViolations,
			MajorViolations:        execHis.MajorViolations,
			MinorViolations:        execHis.MinorViolations,
			Complexity:             execHis.Complexity,
			DuplicatedLinesDensity: execHis.DuplicatedLinesDensity,
			QualityGates:           execHis.QualityGates,
			QualityGatesParams:     execHis.QualityGatesParams,
			GmtCreated:             execHis.GmtCreated,
			GmtModified:            execHis.GmtModified,
			Creator:                execHis.Creator,
			Operator:               execHis.Operator,
			OssDownload:            execHis.OssDownloadUrl,
			PdfUrl:                 execHis.PdfUrl,
			SonarUrl:               execHis.SonarUrl,
			LastScanTestExecTime:   scanTestTask.LastScanTestExecTime,
		}
		scanTaskOverviewDTO.CodeDistribution = t.getCodeDistribution(execHis.Lines, execHis.Ncloc, execHis.CommentLines)
		scanTaskOverviewDTO.CodeTrend = t.getCodeTrend(ctx, taskId, hisId)
		if execHis.Id != 0 {
			duration := time.Time(*scanTaskOverviewDTO.GmtModified).Sub(time.Time(*scanTaskOverviewDTO.GmtCreated))
			scanTaskOverviewDTO.TakeTime = fmt.Sprintf("%02d:%02d:%02d", int32(duration.Hours()), int32(duration.Minutes()), int32(duration.Seconds()))
		}
		scanTaskOverviewDTO.TakeTime = times.CalculateDiff(execHis.GmtCreated, execHis.GmtModified)
	}
	taskConfig := quality.ScanQualityGates{}
	taskConfig.TaskId = taskId
	taskConfig.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &taskConfig)
	scanTaskOverviewDTO.TaskConfig = taskConfig
	if taskConfig.Id != 0 && taskConfig.IsActive == constants.RuleActive && scanTaskOverviewDTO.Status == task.TaskStatusSuccess {
		if scanTaskOverviewDTO.BlockerViolations <= taskConfig.BlockerViolations &&
			scanTaskOverviewDTO.CriticalViolations <= taskConfig.CriticalViolations &&
			scanTaskOverviewDTO.MajorViolations <= taskConfig.MajorViolations &&
			scanTaskOverviewDTO.MinorViolations <= taskConfig.MinorViolations {
			scanTaskOverviewDTO.QualityGates = enums.QualityGatePass.Code
			scanTaskOverviewDTO.QualityGatesColumn = enums.QualityGatePass.Name
		} else {
			scanTaskOverviewDTO.QualityGates = enums.QualityGateFail.Code
			scanTaskOverviewDTO.QualityGatesColumn = enums.QualityGateFail.Name
		}
	} else {
		scanTaskOverviewDTO.QualityGates = enums.QualityGateOff.Code
		scanTaskOverviewDTO.QualityGatesColumn = enums.QualityGateOff.Name
	}
	return scanTaskOverviewDTO
}

func (t TaskService) GetScanTestTaskPageList(ctx *commoncontext.MantisContext, searchDTO dto.ScanTestTaskSearchDTO,
	pageRequest gormx.PageRequest,
) *gormx.PageResult {
	user := ctx.User
	apps, err := cubeBaseRemoteApi.GetAppsByUser(user, "")
	if err != nil {
		logger.Logger.Panicf("查询应用错误, %s", err.Error())
	}
	appIds := make([]string, 0, len(apps))
	appIdInfoMap := make(map[string]shipcmdb.CmdbApp)
	for _, app := range apps {
		appIds = append(appIds, shiputils.IDString(app.AppId))
		appIdInfoMap[shiputils.IDString(app.AppId)] = app
	}
	searchDTO.AppIds = appIds
	searchDTO.CompanyId = shiputils.IDString(ctx.User.CompanyID)
	return t.searchTaskPage(ctx, searchDTO, pageRequest, appIdInfoMap)
}

func (t TaskService) GetScanTestTaskPageListInHub(ctx *commoncontext.MantisContext, searchDTO dto.ScanTestTaskSearchDTO,
	pageRequest gormx.PageRequest,
) *gormx.PageResult {
	appIds := make([]string, 0)
	appInfos := shipRemoteApi.GetAppInfosByHubUrl(ctx, searchDTO.HubUrl)
	for _, info := range appInfos {
		appIds = append(appIds, shiputils.IDString(info.AppId))
	}
	searchDTO.AppIds = appIds
	query := ""
	for _, appId := range appIds {
		query += fmt.Sprintf("id='%s'||", appId)
	}
	if query != "" {
		query = query[:len(query)-2]
	}
	apps, err := cmdb.Client.GetAppList(ctx, url.Values{"filter": []string{query}})
	if err != nil {
		logger.Logger.Panicf("查询app错误, %s", err.Error())
	}
	appIdInfoMap := make(map[string]shipcmdb.CmdbApp)
	for _, app := range apps {
		appIdInfoMap[shiputils.IDString(app.AppId)] = app
	}
	searchDTO.CompanyId = shiputils.IDString(ctx.User.CompanyID)
	return t.searchTaskPage(ctx, searchDTO, pageRequest, appIdInfoMap)
}

func (t TaskService) searchTaskPage(ctx *commoncontext.MantisContext, searchDTO dto.ScanTestTaskSearchDTO,
	pageRequest gormx.PageRequest, appIdInfoMap map[string]shipcmdb.CmdbApp,
) *gormx.PageResult {
	result, taskDetailDTOS := taskDao.SelectScanTestTaskList(ctx, searchDTO, pageRequest)
	if len(taskDetailDTOS) == 0 {
		return result
	}
	userIdNameMap := make(map[string]string)
	userIdNameMap[constants.UserDevops] = constants.UserDevops
	taskIds := make([]int64, 0, len(taskDetailDTOS))
	programmeIds := set.New[int64]()
	for _, detailDTO := range taskDetailDTOS {
		if detailDTO.TaskCreator != "" && detailDTO.TaskCreator != constants.UserDevops {
			userIdNameMap[detailDTO.TaskCreator] = ""
		}
		if detailDTO.Operator != "" && detailDTO.Operator != constants.UserDevops {
			userIdNameMap[detailDTO.Operator] = ""
		}
		taskIds = append(taskIds, detailDTO.OriTaskId)
		programmeIds.Add(detailDTO.ProgrammeId)
	}
	// 查询programmeMap
	programmes := make([]quality.ScanProgramme, 0)
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(quality.ScanProgramme{}).In("id", programmeIds.ToSlice()).Eq("is_deleted", commonconstants.DeleteNo),
		&programmes)
	programmeMap := make(map[int64]string)
	for _, programme := range programmes {
		programmeMap[programme.Id] = programme.Name
	}
	// 查询门禁
	gates := gatesService.SelectBatchByTaskIds(ctx, taskIds)
	taskGateMap := make(map[int64]quality.ScanQualityGates)
	for _, gate := range gates {
		taskGateMap[gate.TaskId] = gate
	}
	err := cubeBaseRemoteApi.GetUserAdAccountNameMap(userIdNameMap, shiputils.IDString(ctx.User.CompanyID))
	if err != nil {
		logger.Logger.Panic(err)
	}
	for i, detailDTO := range taskDetailDTOS {
		taskDetailDTOS[i].CreatorName = userIdNameMap[detailDTO.TaskCreator]
		taskDetailDTOS[i].Operator = userIdNameMap[detailDTO.Operator]
		app := appIdInfoMap[detailDTO.AppId]
		taskDetailDTOS[i].AppName = app.Name
		taskDetailDTOS[i].Language = app.Language
		if searchDTO.JobType == constants.ScanJobType {
			gates := int32(0)
			if detailDTO.QualityGates != 0 {
				gates = detailDTO.QualityGates
			}
			gateWay := enums.QualityGatesEnum{}.GetNameByCode(gates)
			taskDetailDTOS[i].QualityGatesColumn = gateWay
		}
		// 写入programme
		taskDetailDTOS[i].Programme = struct {
			Id   int64  "json:\"id\""
			Name string "json:\"name\""
		}{
			Id:   detailDTO.ProgrammeId,
			Name: programmeMap[detailDTO.ProgrammeId],
		}
		// 写入resultInfo
		resultInfo := make(map[string]dto.ScanTestResultDto)
		if detailDTO.JobType == constants.ScanJobType {
			gate, ok := taskGateMap[detailDTO.OriTaskId]
			if gate.Id != 0 && gate.IsActive == constants.RuleActive && detailDTO.Status == task.TaskStatusSuccess {
				if detailDTO.BlockerViolations <= gate.BlockerViolations &&
					detailDTO.CriticalViolations <= gate.CriticalViolations &&
					detailDTO.MajorViolations <= gate.MajorViolations &&
					detailDTO.MinorViolations <= gate.MinorViolations {
					taskDetailDTOS[i].Status = task.TaskGatePass
				} else {
					taskDetailDTOS[i].Status = task.TaskGateNotPass
				}
			}
			taskDetailDTOS[i].StatusMsg = task.StatusCodeToMemo(taskDetailDTOS[i].Status)
			tagFunc := func(actual, gateV int32) int {
				if !ok || !gate.IsActive || actual <= gateV {
					return 0
				} else {
					return 1
				}
			}
			resultInfo["blockerViolations"] = dto.ScanTestResultDto{
				Num: detailDTO.BlockerViolations,
				Tag: tagFunc(detailDTO.BlockerViolations, gate.BlockerViolations),
			}
			resultInfo["criticalViolations"] = dto.ScanTestResultDto{
				Num: detailDTO.CriticalViolations,
				Tag: tagFunc(detailDTO.CriticalViolations, gate.CriticalViolations),
			}
			resultInfo["majorViolations"] = dto.ScanTestResultDto{
				Num: detailDTO.MajorViolations,
				Tag: tagFunc(detailDTO.MajorViolations, gate.MajorViolations),
			}
			resultInfo["minorViolations"] = dto.ScanTestResultDto{
				Num: detailDTO.MinorViolations,
				Tag: tagFunc(detailDTO.MinorViolations, gate.MinorViolations),
			}
		} else if detailDTO.JobType == constants.UnitTestJobType {
			resultInfo["lines"] = dto.ScanTestResultDto{
				Num: detailDTO.Lines,
				Tag: 0,
			}
			resultInfo["testSuccessDensity"] = dto.ScanTestResultDto{
				Num: detailDTO.TestSuccessDensity,
				Tag: 0,
			}
			resultInfo["lineCoverage"] = dto.ScanTestResultDto{
				Num: detailDTO.LineCoverage,
				Tag: 0,
			}
		}
		taskDetailDTOS[i].ScanResultInfo = resultInfo
	}
	result.List = taskDetailDTOS
	return result
}

func (t TaskService) GetScanTestExecHisPageByTaskId(ctx *commoncontext.MantisContext, searchDTO dto.ScanTestTaskSearchDTO,
	request gormx.PageRequest,
) *gormx.PageResult {
	scanTestTask := quality.ScanTestTask{}
	scanTestTask.Id = searchDTO.TaskId
	scanTestTask.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &scanTestTask)
	paramBuilder := gormx.NewParamBuilder().Model(&quality.ScanTestExecHis{}).Eq("task_id", searchDTO.TaskId)
	if searchDTO.GmtModified != "" {
		paramBuilder.Gte("gmt_modified", searchDTO.GetModifyTimeStart()).Lte("gmt_modified", searchDTO.GetModifyTimeEnd())
	}
	paramBuilder.OrderByDesc("gmt_modified")
	list := make([]quality.ScanTestExecHis, 0)
	pageResult := gormx.PageSelectByParamBuilderX(ctx, paramBuilder, &list, request)
	res := make([]dto.ScanTestExecHisDTO, 0, len(list))
	userIdNameMap := make(map[string]string)
	userIdNameMap[constants.UserDevops] = constants.UserDevops
	for _, his := range list {
		if his.Operator != "" && his.Operator != constants.UserDevops {
			userIdNameMap[his.Operator] = ""
		}
	}
	err := cubeBaseRemoteApi.GetUserAdAccountNameMap(userIdNameMap, shiputils.IDString(ctx.User.CompanyID))
	if err != nil {
		logger.Logger.Panic(err)
	}
	gate := quality.ScanQualityGates{}
	gate.TaskId = searchDTO.TaskId
	gate.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &gate)
	for _, his := range list {
		execHisDto := convert.ScanTestExecHisModelToDTO(his)
		execHisDto.Operator = userIdNameMap[execHisDto.Operator]
		// 写入resultInfo
		resultInfo := make(map[string]dto.ScanTestResultDto)
		if scanTestTask.JobType == constants.ScanJobType {
			tagFunc := func(actual, gateV int32) int {
				if !gate.IsActive || actual <= gateV {
					return 0
				} else {
					return 1
				}
			}
			resultInfo["blockerViolations"] = dto.ScanTestResultDto{
				Num: his.BlockerViolations,
				Tag: tagFunc(his.BlockerViolations, gate.BlockerViolations),
			}
			resultInfo["criticalViolations"] = dto.ScanTestResultDto{
				Num: his.CriticalViolations,
				Tag: tagFunc(his.CriticalViolations, gate.CriticalViolations),
			}
			resultInfo["majorViolations"] = dto.ScanTestResultDto{
				Num: his.MajorViolations,
				Tag: tagFunc(his.MajorViolations, gate.MajorViolations),
			}
			resultInfo["minorViolations"] = dto.ScanTestResultDto{
				Num: his.MinorViolations,
				Tag: tagFunc(his.MinorViolations, gate.MinorViolations),
			}
		} else if scanTestTask.JobType == constants.UnitTestJobType {
			resultInfo["lines"] = dto.ScanTestResultDto{
				Num: his.Lines,
				Tag: 0,
			}
			resultInfo["testSuccessDensity"] = dto.ScanTestResultDto{
				Num: his.TestSuccessDensity,
				Tag: 0,
			}
			resultInfo["lineCoverage"] = dto.ScanTestResultDto{
				Num: his.LineCoverage,
				Tag: 0,
			}
		}
		execHisDto.ScanResultInfo = resultInfo
		res = append(res, execHisDto)
	}
	pageResult.List = res
	return pageResult
}

func SonarStatusAndResultLinkUpdate(condition apis.Condition, labels map[string]string) error {
	var status int8
	switch condition.Status {
	case corev1.ConditionTrue:
		status = task.ProductStatusSucc
	case corev1.ConditionFalse:
		if condition.Reason == v1beta1.TaskRunSpecStatusCancelled {
			status = task.ProductStatusFail
		} else {
			status = task.ProductStatusFail
		}
	case corev1.ConditionUnknown:
		if condition.Reason == "Running" || condition.Reason == "Pending" {
			status = task.TaskStatusRun
		}
		if condition.Reason == v1beta1.TaskRunReasonTimedOut.String() {
			status = task.ProductStatusFail
		}
	default:
		return fmt.Errorf("syncTaskRunRecord invaild taskrun status")
	}
	execIdStr, ok := labels[commonconstants.ExecIdLabel]
	if !ok {
		return fmt.Errorf("error in getting execId")
	}
	execId, err := strconv.ParseInt(execIdStr, 10, 64)
	if err != nil {
		return err
	}
	his := quality.ScanTestExecHis{}
	his.Id = execId
	his.IsDeleted = commonconstants.DeleteNo
	ctx := &commoncontext.MantisContext{}
	if err := gormx.SelectOneByCondition(ctx, &his); err != nil {
		return err
	}
	if his.Status >= status {
		return nil
	}
	his.Status = status
	his.StatusMsg = task.StatusCodeToMemo(status)
	his.GmtModified = times.Now()
	_, err = gormx.UpdateOneByCondition(ctx, &his)
	return err
}

func (t TaskService) GetLogsByExecHisId(ctx *commoncontext.MantisContext, execId int64) map[string]interface{} {
	execHis := quality.ScanTestExecHis{}
	execHis.Id = execId
	execHis.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &execHis)
	// 获取k8s pod 日志
	dp, err := driver.NewDriverProvider()
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in new driver provider"))
	}
	var log string
	log, err = dp.GetLog(context.Background(), execHis.K8sTaskId)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get log"))
	}
	m := make(map[string]interface{})
	m["id"] = execHis.Id
	m["status"] = execHis.Status
	m["log"] = log
	if execHis.Status >= task.TaskStatusSuccess {
		oss := base64.StdEncoding.EncodeToString([]byte(path.Join(commonconstants.TaskRunLogFilePath, fmt.Sprintf("%s.txt", execHis.K8sTaskId))))
		m["osskey"] = oss
	} else {
		m["osskey"] = ""
	}
	return m
}

func (TaskService) getAppGitInfo(appId string) map[string]string {
	appInfo, err := cmdb.Client.GetAppById(context.Background(), appId)
	if err != nil {
		logger.Logger.Panicf("查询应用错误, err=%s", err.Error())
	}
	if appInfo.VcsFullPath == "" {
		logger.Logger.Panic("获取应用分支失败！")
	}
	gitInfo := gitHelper.GetGitInfoByUrl(appInfo.VcsFullPath)
	if gitInfo == nil {
		logger.Logger.Panic("无法获取到对应的git信息")
		return nil
	}
	res := make(map[string]string)
	res["gitDomain"] = gitInfo.Domain
	res["gitToken"] = gitInfo.Token
	res["codePath"] = utils.GetGitCodePath(gitInfo.Domain, appInfo.VcsFullPath)
	return res
}

func (t TaskService) getCodeDistribution(allLines int32, ncloc int32, commentLines int32) string {
	emptyLines := int32(0)
	if abs := math.Abs(float64(allLines - ncloc)); abs > 0 {
		emptyLines = int32(abs)
	}
	codeJson := make(map[string]interface{})
	codeJson["代码"] = ncloc
	codeJson["注释"] = commentLines
	codeJson["空行"] = emptyLines
	return string(jsonx.Marshal(&codeJson))
}

func (t TaskService) getCodeTrend(ctx *commoncontext.MantisContext, taskId int64, hisId int64) string {
	scanTestExecHisList := taskDao.SelectScanHistoryListByTaskIdAndLatestHisId(ctx, taskId, hisId)
	res := make([]map[string]interface{}, 0)
	if len(scanTestExecHisList) != 0 {
		for _, his := range scanTestExecHisList {
			code := make(map[string]interface{})
			code["总数"] = his.Lines
			code["代码"] = his.Ncloc
			code["注释"] = his.CommentLines
			emptyLines := int32(0)
			if abs := math.Abs(float64(his.Lines - his.Ncloc)); abs > 0 {
				emptyLines = int32(abs)
			}
			code["空行"] = emptyLines
			code["time"] = his.GmtModified
			res = append(res, code)
		}
	}
	return string(jsonx.Marshal(&res))
}
