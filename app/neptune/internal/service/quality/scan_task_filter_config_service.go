package quality

import (
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	shipcmdb "git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/cmdb"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
	shiputils "git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
)

type ScanTaskFilterConfigService struct{}

var scanTaskFilterConfigService ScanTaskFilterConfigService

// ModifyFilter 保存除app以外的配置
func (ScanTaskFilterConfigService) ModifyFilter(ctx *commoncontext.MantisContext, filterDto dto.ScanTaskFilterDTO, user commondto.UserInfo) bool {
	if filterDto.RelationId != "" {
		filter := quality.ScanTaskFilterConfig{}
		filter.Type = filterDto.Type
		filter.Modifier = user.AdAccount
		filter.GmtModified = times.Now()
		filter.IsDeleted = commonconstants.DeleteNo
		filter.RelationId = filterDto.RelationId
		filter.ConfigPaths = string(jsonx.Marshal(&filterDto.Configs))
		filter.ConfigPaths = strings.TrimSpace(filter.ConfigPaths)
		filter.CompanyId = shiputils.IDString(user.CompanyID)
		oldFilter := quality.ScanTaskFilterConfig{}
		oldFilter.RelationId = filterDto.RelationId
		oldFilter.Type = filterDto.Type
		gormx.SelectOneByConditionX(ctx, &oldFilter)
		if oldFilter.Id == 0 {
			filter.GmtCreated = times.Now()
			filter.Creator = user.AdAccount
			gormx.InsertUpdateOneX(ctx, &filter)
		} else {
			filter.Id = oldFilter.Id
			gormx.UpdateOneByConditionX(ctx, &filter)
		}
		return true
	} else {
		return false
	}
}

// SaveAppFilter 只保存app的配置
func (ScanTaskFilterConfigService) SaveAppFilter(ctx *commoncontext.MantisContext, filterDTO dto.AppScanTaskFilterDTO) int64 {
	filterConfig := quality.ScanTaskFilterConfig{}
	filterConfig.Type = constants.FilterConfigTypeApp
	filterConfig.Modifier = ctx.User.AdAccount
	filterConfig.GmtModified = times.Now()
	filterConfig.IsDeleted = commonconstants.DeleteNo
	filterConfig.RelationId = filterDTO.AppId
	filterConfig.ConfigPaths = string(jsonx.Marshal(&(filterDTO.Configs)))
	filterConfig.ConfigPaths = strings.TrimSpace(filterConfig.ConfigPaths)
	filterConfig.Description = filterDTO.Description
	filterConfig.CompanyId = utils.IDString(ctx.User.CompanyID)
	oldFilter := quality.ScanTaskFilterConfig{}
	oldFilter.Id = filterDTO.Id
	oldFilter.RelationId = filterConfig.RelationId
	oldFilter.Type = filterConfig.Type
	oldFilter.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &oldFilter)
	if filterDTO.Id == 0 && oldFilter.Id != 0 {
		// 新增的时候存在同一个app的配置，抛错
		logger.Logger.Panic("该应用的配置已存在！")
	}
	if oldFilter.Id == 0 {
		filterConfig.GmtCreated = times.Now()
		filterConfig.Creator = ctx.User.AdAccount
		gormx.InsertUpdateOneX(ctx, &filterConfig)
	} else {
		filterConfig.Id = oldFilter.Id
		gormx.UpdateOneByConditionX(ctx, &filterConfig)
	}
	return filterConfig.Id
}

func (ScanTaskFilterConfigService) DeleteAppFilter(ctx *commoncontext.MantisContext, id int64) {
	config := quality.ScanTaskFilterConfig{}
	config.Id = id
	config.IsDeleted = commonconstants.DeleteYes
	gormx.UpdateOneByConditionX(ctx, &config)
}

// QueryFilter 查询除app以外的配置
func (s ScanTaskFilterConfigService) QueryFilter(ctx *commoncontext.MantisContext, relationId string, filterType string, mode string) map[string]*dto.ScanTaskFilterDTO {
	res := make(map[string]*dto.ScanTaskFilterDTO)
	res[constants.FilterConfigTypeApp] = nil
	res[constants.FilterConfigTypeProgramme] = nil
	res[constants.FilterConfigTypeTask] = nil
	scanTaskFilterConfig := quality.ScanTaskFilterConfig{}
	scanTaskFilterConfig.RelationId = relationId
	scanTaskFilterConfig.Type = filterType
	scanTaskFilterConfig.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &scanTaskFilterConfig)
	if scanTaskFilterConfig.Id != 0 {
		filterDTO := dto.ScanTaskFilterDTO{
			Id:         scanTaskFilterConfig.Id,
			RelationId: relationId,
			Type:       filterType,
		}
		if filterType == constants.FilterConfigTypeApp {
			configs := make(map[string][]dto.ScanFilter)
			jsonx.UnMarshal([]byte(scanTaskFilterConfig.ConfigPaths), &configs)
			filterDTO.Configs = configs[mode]
			filterDTO.ConfigSlices = configs[mode]
		} else {
			configs := make([]dto.ScanFilter, 0)
			jsonx.UnMarshal([]byte(scanTaskFilterConfig.ConfigPaths), &configs)
			filterDTO.Configs = configs
			filterDTO.ConfigSlices = configs
		}
		res[filterType] = &filterDTO
	}
	if filterType == constants.FilterConfigTypeTask {
		scanTestTask := quality.ScanTestTask{}
		taskId, err := strconv.ParseInt(relationId, 10, 64)
		if err != nil {
			logger.Logger.Panicf("parse task relation [%s] error", relationId)
		}
		scanTestTask.Id = taskId
		gormx.SelectOneByConditionX(ctx, &scanTestTask)
		if scanTestTask.AppId != "" {
			res[constants.FilterConfigTypeApp] = s.QueryFilter(ctx, scanTestTask.AppId, constants.FilterConfigTypeApp, scanTestTask.JobType)[constants.FilterConfigTypeApp]
		}
		if scanTestTask.ProgrammeId != 0 {
			res[constants.FilterConfigTypeProgramme] = s.QueryFilter(ctx, strconv.FormatInt(scanTestTask.ProgrammeId, 10), constants.FilterConfigTypeProgramme, "")[constants.FilterConfigTypeProgramme]
		}
	}
	return res
}

// QueryAppFilter 只查询app配置
func (s ScanTaskFilterConfigService) QueryAppFilter(ctx *commoncontext.MantisContext, appId string, pageRequest gormx.PageRequest) *gormx.PageResult {
	filters := make([]quality.ScanTaskFilterConfig, 0)
	user := ctx.User
	apps, err := cubeBaseRemoteApi.GetAppsByUser(user, "")
	if err != nil {
		logger.Logger.Panicf("查询应用错误:%v", err.Error())
	}
	appIds := make([]string, 0)
	appIdInfoMap := make(map[string]shipcmdb.CmdbApp)
	for _, app := range apps {
		appIds = append(appIds, shiputils.IDString(app.AppId))
		appIdInfoMap[shiputils.IDString(app.AppId)] = app
	}
	paramBuilder := gormx.NewParamBuilder().Model(&quality.ScanTaskFilterConfig{}).
		Eq("type", constants.FilterConfigTypeApp).Eq("is_deleted", commonconstants.DeleteNo).In("relation_id", appIds)
	if appId != "" {
		paramBuilder.Eq("relation_id", appId)
	}
	if user.CompanyID != "" {
		paramBuilder.Eq("company_id", user.CompanyID)
	}
	pageResult := gormx.PageSelectByParamBuilderX(ctx, paramBuilder,
		&filters, pageRequest)
	userIdNameMap := make(map[string]string)
	for _, filter := range filters {
		userIdNameMap[filter.Creator] = ""
	}
	err = cubeBaseRemoteApi.GetUserAdAccountNameMap(userIdNameMap, shiputils.IDString(ctx.User.CompanyID))
	if err != nil {
		logger.Logger.Panic(err)
	}
	res := make([]dto.AppScanTaskFilterDTO, 0)
	for _, filter := range filters {
		configs := make(map[string][]dto.ScanFilter)
		jsonx.UnMarshal([]byte(filter.ConfigPaths), &configs)
		res = append(res, dto.AppScanTaskFilterDTO{
			Id:          filter.Id,
			AppId:       filter.RelationId,
			AppName:     appIdInfoMap[filter.RelationId].Name,
			CompanyName: appIdInfoMap[filter.RelationId].CompanyName,
			CreatorName: userIdNameMap[filter.Creator],
			GmtCreated:  filter.GmtCreated,
			GmtModified: filter.GmtModified,
			Description: filter.Description,
			Configs:     configs,
		})
	}
	pageResult.List = res
	return pageResult
}

func (s ScanTaskFilterConfigService) QueryAppFilterInfo(ctx *commoncontext.MantisContext, id int64) dto.ScanTaskFilterDTO {
	taskFilterConfig := quality.ScanTaskFilterConfig{}
	taskFilterConfig.Id = id
	taskFilterConfig.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &taskFilterConfig)
	configs := make(map[string][]dto.ScanFilter)
	jsonx.UnMarshal([]byte(taskFilterConfig.ConfigPaths), &configs)
	return dto.ScanTaskFilterDTO{
		Id:         taskFilterConfig.Id,
		RelationId: taskFilterConfig.RelationId,
		Type:       taskFilterConfig.Type,
		Configs:    configs,
	}
}

func (s ScanTaskFilterConfigService) QueryAppFilterInfoByAppId(ctx *commoncontext.MantisContext, id string) dto.ScanTaskFilterDTO {
	taskFilterConfig := quality.ScanTaskFilterConfig{}
	taskFilterConfig.RelationId = id
	taskFilterConfig.Type = constants.FilterConfigTypeApp
	taskFilterConfig.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &taskFilterConfig)
	configs := make(map[string][]dto.ScanFilter)
	if taskFilterConfig.ConfigPaths != "" {
		jsonx.UnMarshal([]byte(taskFilterConfig.ConfigPaths), &configs)
	}
	return dto.ScanTaskFilterDTO{
		Id:         taskFilterConfig.Id,
		RelationId: taskFilterConfig.RelationId,
		Type:       taskFilterConfig.Type,
		Configs:    configs,
	}
}

func (s ScanTaskFilterConfigService) UpdateAppFilterDescription(ctx *commoncontext.MantisContext, id int64, description string) {
	config := quality.ScanTaskFilterConfig{}
	config.Id = id
	config.Description = description
	gormx.UpdateOneByConditionX(ctx, &config)
}
