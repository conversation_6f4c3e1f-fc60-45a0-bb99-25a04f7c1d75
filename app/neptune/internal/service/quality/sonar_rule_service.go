package quality

import (
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality/help"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

// SonarRuleService 提供Sonar规则相关功能的服务
type SonarRuleService struct{}

// SearchRulesInTemplate 搜索规则模板中的规则
func (SonarRuleService) SearchRulesInTemplate(req help.SonarRuleSearchReq, ctx *commoncontext.MantisContext) *gormx.PageResult {
	// 验证必填参数
	if req.ProfileKey == "" {
		logger.Logger.Panic("请选择规则集！")
	}
	if req.IsActive == "" {
		logger.Logger.Panic("启用状态必填！")
	}

	// 设置默认分页参数
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 100
	}

	// 调用Sonar助手搜索规则
	sonarHelper := help.DefaultSonarHelper
	rules, total := sonarHelper.SearchRules(req)

	// 设置规则活动状态
	isActive, _ := strconv.ParseBool(req.IsActive)
	for i := range rules {
		rules[i].IsActive = isActive
	}

	// 构建并返回分页结果
	return &gormx.PageResult{
		Pages:       total / req.PageSize, // 总页数
		Total:       total,                // 总记录数
		PageSize:    req.PageSize,         // 每页大小
		CurrentPage: req.Page,             // 当前页码
		List:        rules,                // 规则列表
	}
}

// ActiveOrDeActiveTemplateRule 启用或禁用模板规则
func (SonarRuleService) ActiveOrDeActiveTemplateRule(req dto.ActiveDeActiveRuleReqDTO, ctx *commoncontext.MantisContext) {
	sonarHelper := help.DefaultSonarHelper

	if req.IsActive {
		// 启用规则
		sonarHelper.ActiveProfileRule(req.ProfileKey, req.RuleKey, req.Severity)
	} else {
		// 禁用规则
		sonarHelper.DeActiveProfileRule(req.ProfileKey, req.RuleKey)
	}
}

// GetLanguages 获取支持的扫描语言列表
func (SonarRuleService) GetLanguages(ctx *commoncontext.MantisContext) []dto.ScanLanguageDTO {
	// 查询未删除的语言记录
	languages := make([]quality.ScanLanguage, 0)
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().
			Model(&quality.ScanLanguage{}).
			Eq("is_deleted", commonconstants.DeleteNo).
			OrderByAsc("id"),
		&languages)

	// 转换为DTO对象
	result := make([]dto.ScanLanguageDTO, 0, len(languages))
	for _, language := range languages {
		result = append(result, dto.ScanLanguageDTO{
			Key:  language.Key,
			Name: language.Name,
		})
	}

	return result
}

// ShowRuleDetail 显示规则详情
func (SonarRuleService) ShowRuleDetail(key string) dto.SonarRuleDTO {
	sonarHelper := help.DefaultSonarHelper
	return sonarHelper.ShowRule(key)
}
