package quality

import (
	"fmt"
	"mime/multipart"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality/help"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/common_util"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/xmlx"
)

// ScanProfileTemplateService 扫描规则集模板服务
type ScanProfileTemplateService struct{}

// SaveScanProfileTemplate 保存扫描规则集模板
func (ScanProfileTemplateService) SaveScanProfileTemplate(ctx *commoncontext.MantisContext, template quality.ScanProfileTemplate, user commondto.UserInfo) int64 {
	sonarHelper := help.DefaultSonarHelper

	// 检查规则集名称是否重复
	profiles := sonarHelper.SearchQualityProfiles(help.SonarQualityProfilesSearchReq{QualityProfile: template.Name})
	if len(profiles) != 0 {
		logger.Logger.Panic("规则集名称重复，请重新命名！")
	}

	if template.Id != 0 {
		// 更新已有模板
		oldTemplate := quality.ScanProfileTemplate{}
		oldTemplate.Id = template.Id
		gormx.SelectOneByConditionX(ctx, &oldTemplate)

		// 更新数据库记录
		template.GmtModified = times.Now()
		template.Modifier = user.AdAccount
		gormx.UpdateOneByConditionX(ctx, &template)

		// 更新Sonar中的规则集
		sonarHelper.RenameProfile(oldTemplate.Key, template.Name)
	} else {
		// 创建新模板
		// 在Sonar中创建规则集
		key := sonarHelper.CreateProfile(template.Name, template.Language)

		// 在数据库中创建记录
		template.Creator = user.AdAccount
		template.Modifier = user.AdAccount
		template.GmtCreated = times.Now()
		template.GmtModified = times.Now()
		template.IsDeleted = commonconstants.DeleteNo
		template.Key = key
		gormx.InsertUpdateOneX(ctx, &template)
	}

	return template.Id
}

// CopyTemplate 复制规则集模板
func (ScanProfileTemplateService) CopyTemplate(ctx *commoncontext.MantisContext, id int64, name string, user commondto.UserInfo) int64 {
	sonarHelper := help.DefaultSonarHelper

	// 查询源模板
	sourceTemplate := quality.ScanProfileTemplate{}
	sourceTemplate.Id = id
	sourceTemplate.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &sourceTemplate)

	// 在Sonar中复制规则集
	newKey := sonarHelper.CopyProfile(sourceTemplate.Key, name)

	// 创建新模板记录
	newTemplate := sourceTemplate // 复制源模板属性
	newTemplate.Id = 0            // 重置ID以创建新记录
	newTemplate.Name = name
	newTemplate.Key = newKey
	newTemplate.Creator = user.AdAccount
	newTemplate.Modifier = user.AdAccount
	newTemplate.GmtCreated = times.Now()
	newTemplate.GmtModified = times.Now()

	gormx.InsertUpdateOneX(ctx, &newTemplate)
	return newTemplate.Id
}

// DeleteTemplate 删除规则集模板
func (ScanProfileTemplateService) DeleteTemplate(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	sonarHelper := help.DefaultSonarHelper

	// 查询要删除的模板
	template := quality.ScanProfileTemplate{}
	template.Id = id
	template.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &template)

	// 在Sonar中删除规则集
	sonarHelper.DeleteProfile(template.Language, template.Name)

	// 逻辑删除数据库记录
	template.IsDeleted = commonconstants.DeleteYes
	template.Modifier = user.AdAccount
	template.GmtModified = times.Now()
	gormx.UpdateOneByConditionX(ctx, &template)
}

// GetTemplatesByLanguage 获取指定语言的规则集模板列表
func (ScanProfileTemplateService) GetTemplatesByLanguage(ctx *commoncontext.MantisContext, language string) []quality.ScanProfileTemplate {
	sonarHelper := help.DefaultSonarHelper

	// 从Sonar获取所有指定语言的规则集
	profilesInSonar := sonarHelper.SearchQualityProfiles(help.SonarQualityProfilesSearchReq{Language: language})

	// 构建规则集键值映射
	profileKeyMap := make(map[string]quality.ScanProfileTemplate)
	for _, profile := range profilesInSonar {
		profileKeyMap[profile.Key] = profile
	}

	// 从数据库获取指定语言的模板
	profilesInDB := make([]quality.ScanProfileTemplate, 0)
	gormx.SelectByParamBuilderX(
		ctx,
		gormx.NewParamBuilder().
			Model(&quality.ScanProfileTemplate{}).
			Eq("language", language).
			Eq("is_deleted", commonconstants.DeleteNo),
		&profilesInDB,
	)

	// 合并Sonar和数据库中的信息
	for i, template := range profilesInDB {
		if sonarProfile, exists := profileKeyMap[template.Key]; exists {
			profilesInDB[i].LanguageName = sonarProfile.LanguageName
			profilesInDB[i].ActiveRuleCount = sonarProfile.ActiveRuleCount
			profilesInDB[i].ActiveDeprecatedRuleCount = sonarProfile.ActiveDeprecatedRuleCount
			profilesInDB[i].IsDefault = sonarProfile.IsDefault
		}
	}

	return profilesInDB
}

// ImportProfileToTemplate 导入规则集模板
func (ScanProfileTemplateService) ImportProfileToTemplate(ctx *commoncontext.MantisContext, file *multipart.FileHeader, user commondto.UserInfo) int64 {
	sonarHelper := help.DefaultSonarHelper

	// 读取文件内容
	fileContent := common_util.ReadFileToString(file)

	// 解析XML获取规则集名称和语言
	name, err := xmlx.GetDataByXpath(fileContent, "/profile/name")
	if err != nil {
		logger.Logger.Panicf("解析XML失败，无法获取规则集名称: %v", err)
	}

	language, err := xmlx.GetDataByXpath(fileContent, "/profile/language")
	if err != nil {
		logger.Logger.Panicf("解析XML失败，无法获取语言: %v", err)
	}

	// 检查Sonar中是否已存在同名规则集
	logger.Logger.Infof("导入规则集: name=%s, language=%s", name, language)
	existingProfiles := sonarHelper.SearchQualityProfiles(help.SonarQualityProfilesSearchReq{
		QualityProfile: fmt.Sprintf("%s", name),
		Language:       fmt.Sprintf("%s", language),
	})

	if len(existingProfiles) != 0 {
		logger.Logger.Panicf("规则集已经存在！name=%s, language=%s", name, language)
	}

	// 打开文件并导入到Sonar
	stream, err := file.Open()
	if err != nil {
		logger.Logger.Panicf("打开文件失败: %v", err)
	}

	// 导入规则集到Sonar
	profileKey := ""
	func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Logger.Panicf("规则集导入Sonar失败: %v", r)
			}
		}()
		profileKey = sonarHelper.ImportProfiles(stream, file.Filename)
	}()

	// 创建数据库记录
	logger.Logger.Infof("规则集模板 %s 正在入库", name)
	template := quality.ScanProfileTemplate{}
	template.Key = profileKey
	template.Name = fmt.Sprintf("%s", name)
	template.Language = fmt.Sprintf("%s", language)
	template.IsBuiltIn = false
	template.GmtCreated = times.Now()
	template.GmtModified = times.Now()
	template.Creator = user.AdAccount
	template.Modifier = user.AdAccount
	template.IsDeleted = commonconstants.DeleteNo

	gormx.InsertUpdateOneX(ctx, &template)
	return template.Id
}

// ExportProfileTemplate 导出规则集模板
func (ScanProfileTemplateService) ExportProfileTemplate(ctx *commoncontext.MantisContext, templateId int64) (string, string) {
	sonarHelper := help.DefaultSonarHelper

	// 查询模板
	template := quality.ScanProfileTemplate{}
	template.Id = templateId
	gormx.SelectOneByConditionX(ctx, &template)

	// 生成文件名和下载内容
	fileName := template.Name + "_" + template.LanguageName + ".xml"
	content := sonarHelper.DownloadProfiles(template.Name, template.Language)

	return fileName, content
}

// SearchRulesInTemplate 在规则集模板中搜索规则
func (ScanProfileTemplateService) SearchRulesInTemplate(req help.SonarRuleSearchReq) *gormx.PageResult {
	sonarHelper := help.DefaultSonarHelper
	rules, total := sonarHelper.SearchRules(req)

	return &gormx.PageResult{
		Pages:       total / req.PageSize,
		Total:       total,
		PageSize:    req.PageSize,
		CurrentPage: req.Page,
		List:        rules,
	}
}

// InitDefaultProfileToTemplate 初始化默认规则集模板
// 将Sonar中的内置规则集同步到数据库
func InitDefaultProfileToTemplate() {
	sonarHelper := help.DefaultSonarHelper

	// 使用defer捕获可能的异常
	defer func() {
		if err := recover(); err != nil {
			logger.Logger.Errorf("初始化规则集模板失败: %v", err)
		}
	}()

	// 检查数据库中是否已有内置模板
	existingTemplates := make([]quality.ScanProfileTemplate, 0)
	gormx.SelectByParamBuilderX(
		&commoncontext.MantisContext{},
		gormx.NewParamBuilder().
			Model(&quality.ScanProfileTemplate{}).
			Eq("is_deleted", commonconstants.DeleteNo).
			Eq("is_built_in", true),
		&existingTemplates,
	)

	// 如果已存在内置模板，则不需要初始化
	if len(existingTemplates) != 0 {
		return
	}

	// 获取Sonar中的所有规则集
	allProfiles := sonarHelper.SearchQualityProfiles(help.SonarQualityProfilesSearchReq{})

	// 筛选内置规则集
	builtInProfiles := make([]quality.ScanProfileTemplate, 0)
	for i := range allProfiles {
		if allProfiles[i].IsBuiltIn {
			// 设置创建和修改信息
			allProfiles[i].Creator = ""
			allProfiles[i].Modifier = ""
			allProfiles[i].GmtCreated = times.Now()
			allProfiles[i].GmtModified = times.Now()
			builtInProfiles = append(builtInProfiles, allProfiles[i])
		}
	}

	// 批量插入内置规则集
	if len(builtInProfiles) > 0 {
		gormx.InsertBatchX(&commoncontext.MantisContext{}, builtInProfiles)
	}
}
