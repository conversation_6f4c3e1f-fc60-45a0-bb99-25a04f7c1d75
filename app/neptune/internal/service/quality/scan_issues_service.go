package quality

import (
	"fmt"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality/help"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

// ScanIssuesService 扫描问题服务
type ScanIssuesService struct{}

// QueryScanIssuesList 查询扫描问题列表
func (ScanIssuesService) QueryScanIssuesList(ctx *commoncontext.MantisContext, reqDto dto.ScanIssuesReqDTO, request gormx.PageRequest) *gormx.PageResult {
	logger.Logger.Infof("查询扫描问题请求: %+v", reqDto)

	// 验证任务ID
	if reqDto.TaskId == 0 {
		logger.Logger.Panic("未指定任务!")
	}

	// 构建搜索请求
	searchReq := help.SearchIssuesReq{
		Severity: reqDto.Severity,
		Page:     request.Page,
		PageSize: request.PageSize,
	}

	// 获取扫描任务信息
	scanTask := quality.ScanTestTask{}
	scanTask.Id = reqDto.TaskId
	gormx.SelectOneByConditionX(ctx, &scanTask)

	// 检查是否有执行记录
	if scanTask.LastScanTestExecHis == 0 {
		return buildEmptyPageResult(request.Page, request.PageSize)
	}

	// 获取执行历史
	execHistory := quality.ScanTestExecHis{}
	execHistory.Id = scanTask.LastScanTestExecHis
	execHistory.IsDeleted = constants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &execHistory)

	// 验证执行历史状态
	if execHistory.Status < task.TaskStatusSuccess || execHistory.SonarProjectKey == "" {
		logger.Logger.Infof("任务状态异常: %v, sonarKey: %v", execHistory.Status, execHistory.SonarProjectKey)
		return buildEmptyPageResult(request.Page, request.PageSize)
	}

	// 设置Sonar项目键和模式
	searchReq.ProjectKey = execHistory.SonarProjectKey
	searchReq.Mode = execHistory.Mode

	// 处理创建日期过滤
	if reqDto.CreationDate != "" {
		creationDate := make([]*times.Time, 2)
		creationDate[0] = times.GetDataFromCommaString(reqDto.CreationDate, 0)
		creationDate[1] = times.GetDataFromCommaString(reqDto.CreationDate, 1)
		searchReq.CreationDate = creationDate
	}

	// 调用Sonar助手搜索问题
	sonarHelper := help.DefaultSonarHelper
	list, total := sonarHelper.SearchIssues(searchReq)

	// 构建分页结果
	return &gormx.PageResult{
		Total:       total,
		PageSize:    searchReq.PageSize,
		CurrentPage: searchReq.Page,
		List:        list,
	}
}

// QueryUnitComponentList 查询单元组件列表
func (ScanIssuesService) QueryUnitComponentList(ctx *commoncontext.MantisContext, reqDto dto.SearchComponentReqDTO, request gormx.PageRequest) *gormx.PageResult {
	// 验证任务ID
	if reqDto.TaskId == 0 {
		logger.Logger.Panic("未指定任务!")
	}

	// 构建搜索请求
	searchReq := help.SearchComponentReq{
		Page:       request.Page,
		PageSize:   request.PageSize,
		OrderBy:    reqDto.OrderBy,
		MetricSort: reqDto.MetricSort,
	}

	// 获取扫描任务信息
	scanTask := quality.ScanTestTask{}
	scanTask.Id = reqDto.TaskId
	gormx.SelectOneByConditionX(ctx, &scanTask)

	// 检查是否有执行记录
	if scanTask.LastScanTestExecHis == 0 {
		return buildEmptyPageResult(request.Page, request.PageSize)
	}

	// 获取执行历史
	execHistory := quality.ScanTestExecHis{}
	execHistory.Id = scanTask.LastScanTestExecHis
	execHistory.IsDeleted = constants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &execHistory)

	// 验证执行历史状态
	if execHistory.Status != task.TaskStatusSuccess && execHistory.Status != task.TaskStatusFail {
		return buildEmptyPageResult(request.Page, request.PageSize)
	}

	// 设置组件搜索条件
	searchReq.Component = execHistory.SonarProjectKey

	// 调用Sonar助手搜索组件
	sonarHelper := help.DefaultSonarHelper
	componentList, total := sonarHelper.SearchComponents(searchReq)

	// 转换并过滤结果
	resultList := make([]dto.SonarComponentRes, 0, len(componentList))
	for _, component := range componentList {
		// 按路径过滤
		if reqDto.Search != "" && !strings.Contains(component.Path, reqDto.Search) {
			continue
		}

		// 构建组件响应对象
		componentRes := dto.SonarComponentRes{
			Key:      component.Key,
			Name:     component.Name,
			Path:     component.Path,
			Language: component.Language,
			Url: fmt.Sprintf(
				"%s/component_measures?id=%s&metric=coverage&view=list&selected=%s",
				configs.Config.Modules.Neptune.Sonar.Domain,
				searchReq.Component,
				component.Key,
			),
		}

		// 处理指标数据
		for _, measure := range component.Measures {
			switch measure.Metric {
			case "uncovered_lines":
				componentRes.UncoveredLines = measure.Value
			case "coverage":
				componentRes.Coverage = measure.Value
			case "uncovered_conditions":
				componentRes.UncoveredConditions = measure.Value
			}
		}

		resultList = append(resultList, componentRes)
	}

	// 构建分页结果
	return &gormx.PageResult{
		Total:       total,
		PageSize:    searchReq.PageSize,
		CurrentPage: searchReq.Page,
		List:        resultList,
	}
}

// buildEmptyPageResult 构建空的分页结果
func buildEmptyPageResult(page, pageSize int64) *gormx.PageResult {
	return &gormx.PageResult{
		Total:       0,
		PageSize:    pageSize,
		CurrentPage: page,
		List:        nil,
	}
}
