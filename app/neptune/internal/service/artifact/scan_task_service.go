package artifact

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	task2 "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/utils"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/common_util"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type ScanTaskService struct{}

// 依赖的服务
var (
	taskExecHisService = ScanTaskExecHisService{}
	planService        = ScanPlanService{}
	detailService      = ScanTaskDetailService{}
)

// List 查询任务列表
func (s ScanTaskService) List(ctx *commoncontext.MantisContext, request *commondto.QueryWithOrderDTO, user commondto.UserInfo) *gormx.PageResult {
	logger.Logger.Infof("ScanTaskService.List...request: %+v ", request)

	// 构建基础查询条件
	paramBuilder := gormx.NewParamBuilder().
		Model(&artifact.ScanTask{}).
		Eq("is_deleted", commonconstants.DeleteNo).
		Eq("company_id", user.CompanyID)

	// 添加搜索条件
	if search := request.SearchKey; search != "" {
		paramBuilder.Or(
			gormx.NewParamBuilder().ILike("repo_names", "%"+search+"%"),
			gormx.NewParamBuilder().ILike("task_name", "%"+search+"%"),
		)
	}

	// 添加计划类型过滤
	if planType := request.GetQueryParam("planType"); planType != nil && reflect.TypeOf(planType).Kind() == reflect.String {
		paramBuilder.Eq("plan_type", planType)
	}

	// 添加产品类型过滤
	if productType := request.GetQueryParam("productType"); productType != nil && reflect.TypeOf(productType).Kind() == reflect.String {
		paramBuilder.Eq("product_type", productType)
	}

	// 添加任务来源过滤
	if taskFrom := request.GetQueryParam("taskFrom"); taskFrom != nil && reflect.TypeOf(taskFrom).Kind() == reflect.String {
		paramBuilder.Eq("task_from", taskFrom)
	}

	// 添加排序
	if request.OrderField != "" {
		paramBuilder.Order(request.OrderField, request.OrderTypeFormat())
	} else {
		paramBuilder.Order("id", "desc")
	}

	// 查询任务列表
	tasks := make([]artifact.ScanTask, 0)
	page, err := gormx.PageSelectByParamBuilder(
		ctx,
		paramBuilder,
		&tasks,
		gormx.PageRequest{
			PageSize: request.PageSize,
			Page:     request.Page,
		},
	)
	if err != nil {
		logger.Logger.Panicf("查询任务列表失败! err=%v", err)
	}

	// 如果没有数据，直接返回
	if len(tasks) == 0 {
		return page
	}

	// 收集关联ID，用于批量查询
	planIds := make([]int64, len(tasks))
	execIds := make([]int64, len(tasks))
	for i, task := range tasks {
		planIds[i] = task.PlanId
		execIds[i] = task.LastExecId
	}

	// 查询关联的计划信息
	planList := make([]*artifact.ScanPlan, 0)
	gormx.SelectByParamBuilderX(
		ctx,
		gormx.NewParamBuilder().
			Model(&artifact.ScanPlan{}).
			In("id", planIds).
			Eq("is_deleted", commonconstants.DeleteNo),
		&planList,
	)
	planMap := common_util.ListToMap(planList)

	// 查询关联的执行历史
	execList := make([]*artifact.ScanTaskExecHis, 0)
	gormx.SelectByParamBuilderX(
		ctx,
		gormx.NewParamBuilder().
			Model(&artifact.ScanTaskExecHis{}).
			In("id", execIds),
		&execList,
	)
	execMap := common_util.ListToMap(execList)

	// 处理任务数据
	for i, task := range tasks {
		// 设置计划名称
		if plan, ok := planMap[task.PlanId]; ok {
			task.PlanName = plan.PlanName
		}

		// 解析质量门禁配置
		if task.QualityGate != "" {
			if err := json.Unmarshal([]byte(task.QualityGate), &task.QualityGateInfo); err != nil {
				logger.Logger.Panicf("质量阀对象转化失败! err=%v", err)
			}
		}

		// 处理执行记录信息
		if exec, ok := execMap[task.LastExecId]; ok {
			// 设置最后执行信息
			task.LastExecUserName = exec.Creator
			task.LastTriggerTime = exec.TriggerTime
			task.LastTaskStatus = task2.StatusCodeToMemo(exec.TaskStatus)
			task.LastTaskStatusCode = exec.TaskStatus

			// 计算质量门禁结果
			viewScanResult, _ := utils.CalGateQualityGate(
				constants.IsFalse,
				exec.TaskStatus,
				exec.ScanResult,
				task.QualityGateInfo,
			)
			task.LastScanResultInfo = viewScanResult
			task.ScanProductCount = &exec.ProductCount
		} else {
			// 没有执行记录时设置默认值
			task.LastExecUserName = "-"
			task.LastTaskStatus = task2.StatusCodeToMemo(task2.TaskStatusWait)
			task.LastScanResultInfo = nil
			task.LastTriggerTime = nil
		}

		// 解析仓库信息
		if task.Repo != "" {
			if err := json.Unmarshal([]byte(task.Repo), &task.RepoInfo); err != nil {
				logger.Logger.Panicf("仓库对象转化失败! err=%v", err)
			}
		}

		tasks[i] = task
	}

	return page
}

// Info 获取任务详情
func (s ScanTaskService) Info(ctx *commoncontext.MantisContext, id, execId int64) artifact.ScanTask {
	logger.Logger.Infof("ScanTaskService.Info...id: %d", id)

	// 查询任务基本信息
	paramBuilder := gormx.NewParamBuilder().
		Model(&artifact.ScanTask{}).
		Eq("id", id).
		Eq("is_deleted", commonconstants.DeleteNo)

	var task artifact.ScanTask
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &task)

	// 解析质量门禁配置
	if task.QualityGate != "" {
		if err := json.Unmarshal([]byte(task.QualityGate), &task.QualityGateInfo); err != nil {
			logger.Logger.Panicf("质量阀对象转化失败! err=%v", err)
		}
	}

	// 解析仓库信息
	if task.Repo != "" {
		if err := json.Unmarshal([]byte(task.Repo), &task.RepoInfo); err != nil {
			logger.Logger.Panicf("仓库对象转化失败! err=%v", err)
		}
	}

	// 处理自定义扫描范围
	if task.ScanRange == task2.ScanRangeCusProduct && task.ScanRangeInfo != "" {
		if err := json.Unmarshal([]byte(task.ScanRangeInfo), &task.ProductList); err != nil {
			logger.Logger.Panicf("制品列表对象转化失败! err=%v", err)
		}
	}

	// 查询关联的计划名称
	var planName string
	gormx.RawX(ctx, "SELECT plan_name FROM neptune_artifact_scan_plan WHERE id=?", &planName, task.PlanId)
	task.PlanName = planName

	// 处理执行记录信息
	if execId != 0 {
		// 设置扫描次数
		if task.Id != execId {
			task.ScanCount = taskExecHisService.GetScanCount(ctx, task.Id)
		} else {
			// 历史详情，无需展示次数
			var scanCount int64 = 1
			task.ScanCount = &scanCount
		}

		// 设置最新执行记录信息
		execRecord := taskExecHisService.GetExecRecord(ctx, execId)
		task.LastExecUserName = execRecord.Creator
		task.LastTriggerTime = execRecord.TriggerTime
		task.LastTaskStatus = task2.StatusCodeToMemo(execRecord.TaskStatus)
		task.LastTaskStatusCode = execRecord.TaskStatus

		// 计算质量门禁结果
		viewScanResult, _ := utils.CalGateQualityGate(
			task.QualityGateSwitch,
			execRecord.TaskStatus,
			execRecord.ScanResult,
			task.QualityGateInfo,
		)
		task.LastScanResultInfo = viewScanResult
		task.ScanProductCount = &execRecord.ProductCount
	}

	return task
}

// DeleteTask 删除任务
func (s ScanTaskService) DeleteTask(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	// 执行逻辑删除
	task := artifact.ScanTask{}
	task.Id = id
	task.IsDeleted = commonconstants.DeleteYes
	task.GmtModified = times.Now()
	task.Modifier = user.AdAccount

	gormx.UpdateOneByConditionX(ctx, &task)
}

// AddOrUpdateTask 新增或更新任务
func (s ScanTaskService) AddOrUpdateTask(ctx *commoncontext.MantisContext, task *artifact.ScanTask, user commondto.UserInfo) int64 {
	logger.Logger.Infof("ScanTaskService.AddOrUpdateTask...task: %+v ", task)

	// 设置基本字段
	task.Modifier = user.AdAccount
	task.GmtModified = times.Now()

	// 处理质量门禁配置
	if task.QualityGateSwitch == 1 {
		qualityGateJSON, _ := json.Marshal(task.QualityGateInfo)
		task.QualityGate = string(qualityGateJSON)
	}

	// 处理扫描范围
	if task.ScanRange == task2.ScanRangeCusProduct {
		// 指定制品扫描
		if task.ProductList == nil {
			logger.Logger.Panicf("选择指定制品扫描时，制品信息必填")
		}

		// 序列化制品列表
		productListJSON, _ := json.Marshal(task.ProductList)
		task.ScanRangeInfo = string(productListJSON)
	}

	// 处理新增或修改逻辑
	if task.Id == 0 {
		// 新增任务
		task.CompanyID = fmt.Sprintf("%v", user.CompanyID)
		task.Creator = user.AdAccount
		task.GmtCreated = times.Now()
	}

	// 处理仓库信息
	if task.ScanRange == task2.ScanRangeCusProduct && task.ScanRangeInfo != "" {
		// 自定义制品扫描时，提取仓库信息
		uniqueRepos := distantRepo(task.ProductList)
		repoJSON, _ := json.Marshal(uniqueRepos)
		task.Repo = string(repoJSON)

		// 构建仓库名称列表
		repoNames := ""
		lastRepo := ""
		for _, product := range *task.ProductList {
			if lastRepo == product.RepoInfo.Name {
				continue
			}
			lastRepo = product.RepoInfo.Name
			repoNames += product.RepoInfo.Name + "\u0001"
		}
		task.RepoNames = repoNames
	} else if task.ScanRange == task2.ScanRangeLastProduct {
		// 最新制品扫描
		repoJSON, _ := json.Marshal(task.RepoInfo)
		task.Repo = string(repoJSON)

		// 构建仓库名称列表
		repoNames := ""
		for _, repo := range *task.RepoInfo {
			repoNames += repo.Name + "\u0001"
		}
		task.RepoNames = repoNames
	}

	// 保存任务
	gormx.InsertUpdateOneX(ctx, task)

	logger.Logger.Infof("ScanTaskService.AddOrUpdateTask...id: %d", task.Id)
	return task.Id
}

// distantRepo 遍历制品列表，获取仓库信息并去重
func distantRepo(productList *[]dto.ViewProductInfo) []dto.RepoDto {
	repoMap := make(map[string]dto.RepoDto)

	// 去重仓库信息
	for _, product := range *productList {
		if _, exists := repoMap[product.RepoInfo.Name]; !exists {
			repoMap[product.RepoInfo.Name] = *product.RepoInfo
		}
	}

	// 转换为列表
	repos := make([]dto.RepoDto, 0, len(repoMap))
	for _, repo := range repoMap {
		repos = append(repos, repo)
	}

	return repos
}

// Exec 执行任务
func (s ScanTaskService) Exec(ctx *commoncontext.MantisContext, taskId int64, user commondto.UserInfo, appName string) int64 {
	logger.Logger.Infof("ScanTaskService.Exec...taskId: %d", taskId)

	// 查询任务信息
	taskQuery := gormx.NewParamBuilder().
		Model(&artifact.ScanTask{}).
		Eq("id", taskId).
		Eq("is_deleted", commonconstants.DeleteNo)

	var task artifact.ScanTask
	gormx.SelectByParamBuilderX(ctx, taskQuery, &task)

	// 检查任务是否存在
	if task.Id == 0 {
		logger.Logger.Panicf("任务不存在或已被删除! taskId=%d", taskId)
	}

	// 获取关联的方案，查看CVE白名单
	cveWhiteList := planService.whiteInfos(ctx, task.PlanId)

	// 生成任务执行记录
	execId := taskExecHisService.InitTaskExecHis(ctx, task.Id, task.Repo, user, appName)

	// 更新任务最新执行ID
	gormx.ExecX(ctx, "UPDATE neptune_artifact_scan_task SET last_exec_id=? WHERE id=?", execId, taskId)

	// 异步处理任务明细
	goroutine.Run(func() {
		detailService.GenTaskDetail(ctx, &task, execId, user, cveWhiteList)
	})

	return execId
}

// Abort 中止任务
func (s ScanTaskService) Abort(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	logger.Logger.Infof("ScanTaskService.Abort...id: %d, user=%v", id, user.AdAccount)

	// 查询最新执行ID
	var execId int64
	gormx.RawX(ctx, "SELECT last_exec_id FROM neptune_artifact_scan_task WHERE id=?", &execId, id)

	if execId == 0 {
		logger.Logger.Panicf("id: %d 没有执行记录!", id)
	}

	// 更新执行历史状态为失败
	gormx.ExecX(ctx,
		"UPDATE neptune_artifact_scan_task_exec_his SET task_status=?, gmt_modified=?, modifier=? WHERE id=?",
		task2.TaskStatusFail, times.Now(), user.AdAccount, execId,
	)

	// 查询需要中止的K8S任务ID
	var k8sTaskIds []string
	gormx.RawX(ctx,
		"SELECT k8s_task_id FROM neptune_artifact_scan_task_detail WHERE exec_id=? AND status=?",
		&k8sTaskIds, execId, task2.TaskStatusRun,
	)

	logger.Logger.Infof("需要取消的k8s task有 %d 个!", len(k8sTaskIds))

	// 异步取消K8S任务
	goroutine.Run(func() {
		bgCtx := context.Background()

		for _, k8sTaskId := range k8sTaskIds {
			if k8sTaskId != "" {
				provider, _ := driver.NewDriverProvider()

				if err := provider.Cancel(bgCtx, k8sTaskId); err != nil {
					logger.Logger.Errorf("取消K8S任务失败: taskId=%v, k8sTaskId=%s, err=%v",
						id, k8sTaskId, err,
					)
				}
			}
		}
	})

	// 更新任务明细状态
	gormx.ExecX(ctx,
		"UPDATE neptune_artifact_scan_task_detail SET status=?, gmt_modified=?, modifier=? "+
			"WHERE exec_id=? AND status IN (0, 1, 4)",
		task2.TaskStatusFail, times.Now(), user.AdAccount, execId,
	)
}
