package artifact

import (
	"encoding/json"
	"mime/multipart"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type ResultService struct{}

// DealResult 处理上传的扫描结果文件
func (r ResultService) DealResult(ctx *commoncontext.MantisContext, file multipart.File, fileSize int64, params map[string]interface{}) {
	// 读取文件内容
	fileContent := make([]byte, fileSize)
	_, err := file.Read(fileContent)
	if err != nil {
		logger.Logger.Panicf("读取文件失败: %v", err)
	}

	// 根据扫描类型处理不同的结果
	scanType := params["type"].(string)
	taskId := params["id"].(int64)

	switch scanType {
	case enums.ScanTypeProductsScan:
		logger.Logger.Infof("开始处理扫描类型: %v 的结果文件", scanType)
		dealProductResult(ctx, taskId, fileContent)

		// 异步处理最终状态
		goroutine.Run(func() {
			FinallyStatusDeal(ctx, taskId)
		})
	default:
		logger.Logger.Infof("未知的扫描类型: %v，不处理", scanType)
	}
}

// 处理制品扫描结果
func dealProductResult(ctx *commoncontext.MantisContext, taskDetailId int64, fileContent []byte) {
	// 解析扫描结果JSON
	var scanResult dto.RProduct
	err := json.Unmarshal(fileContent, &scanResult)
	if err != nil {
		logger.Logger.Errorf("解析文件内容失败，文件内容: %v", string(fileContent))
		logger.Logger.Panicf("JSON解析异常: %v", err)
	}

	// 统计漏洞信息
	resultSummary := calculateVulnerabilitySummary(scanResult.Vulnerabilities)

	// 序列化漏洞汇总结果
	resultSummaryJSON, err := json.Marshal(resultSummary)
	if err != nil {
		logger.Logger.Errorf("序列化漏洞汇总结果失败: %v", resultSummary)
		logger.Logger.Panicf("JSON序列化异常: %v", err)
	}

	// 更新扫描任务详情
	const sqlUpdateTaskDetail = "update neptune_artifact_scan_task_detail set scan_result=?, result=?, status=2, gmt_modified=?, end_time=? where id=?"
	now := times.Now()
	gormx.ExecX(
		ctx,
		sqlUpdateTaskDetail,
		string(resultSummaryJSON),
		string(fileContent),
		now,
		now,
		taskDetailId,
	)

	logger.Logger.Infof("任务 ID: %v 的扫描结果已更新", taskDetailId)
}

// 计算漏洞统计信息
func calculateVulnerabilitySummary(vulnerabilities []dto.RProductVul) artifact.ScanResultInfo {
	var summary artifact.ScanResultInfo
	summary.VulnerabilityCount = int64(len(vulnerabilities))

	// 按严重程度统计漏洞数量
	for _, vulnerability := range vulnerabilities {
		severity := strings.ToLower(vulnerability.Severity)
		switch severity {
		case enums.GateCritical:
			summary.Critical++
		case enums.GateHigh:
			summary.High++
		case enums.GateMedium:
			summary.Medium++
		case enums.GateLow:
			summary.Low++
		case enums.GateNone:
			summary.None++
		}
	}

	return summary
}

// FinallyStatusDeal 处理任务最终状态
func FinallyStatusDeal(ctx *commoncontext.MantisContext, detailId int64) {
	logger.Logger.Infof("开始处理任务最终状态，扫描任务详情ID: %v", detailId)

	// 获取执行ID和任务ID
	var taskDetail artifact.ScanTaskDetail
	const sqlGetTaskDetail = "select exec_id, task_id from neptune_artifact_scan_task_detail where id=?"
	gormx.RawX(ctx, sqlGetTaskDetail, &taskDetail, detailId)

	// 获取任务来源
	var taskFrom string
	const sqlGetTaskFrom = "select task_from from neptune_artifact_scan_task where id=?"
	gormx.RawX(ctx, sqlGetTaskFrom, &taskFrom, taskDetail.TaskId)

	logger.Logger.Infof("处理任务状态: 详情ID=%v, 执行ID=%v, 任务来源=%v",
		detailId, taskDetail.ExecId, taskFrom)

	// 更新最终状态和结果
	ScanTaskDetailService{}.FinallyStatusAndResultUpdate(ctx, detailId, taskDetail.ExecId, taskFrom)

	logger.Logger.Infof("任务状态处理完成: 详情ID=%v", detailId)
}
