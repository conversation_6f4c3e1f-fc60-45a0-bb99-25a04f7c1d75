package artifact

import (
	"encoding/json"
	"fmt"
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/utils"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type ScanTaskExecHisService struct{}

// InitTaskExecHis 初始化任务执行历史,返回执行ID
func (s *ScanTaskExecHisService) InitTaskExecHis(ctx *commoncontext.MantisContext, taskId int64, repo string, user commondto.UserInfo, appName string) int64 {
	// 创建执行历史记录
	taskExecHis := artifact.ScanTaskExecHis{
		TaskId:      taskId,
		TaskStatus:  task.TaskStatusRun, // 初始状态为运行中
		Repo:        repo,
		TriggerTime: times.Now(),
		AppName:     appName,
		ExecUser:    user.AdAccount,
	}

	// 设置审计字段
	taskExecHis.SetTimeNowAndUser(user.AdAccount)

	// 保存执行历史记录
	gormx.InsertUpdateOneX(ctx, &taskExecHis)

	return taskExecHis.Id
}

// GetProductExecResult 获取制品扫描结果
func (s *ScanTaskExecHisService) GetProductExecResult(ctx *commoncontext.MantisContext, execId int64, request commondto.QueryDTO) *gormx.PageResult {
	// 查询任务信息和质量门禁配置
	var scanTask artifact.ScanTask
	taskQuery := `
		SELECT t2.quality_gate, t2.quality_gate_switch, t2.id 
		FROM neptune_artifact_scan_task_exec_his t1 
		LEFT JOIN neptune_artifact_scan_task t2 ON t1.task_id = t2.id 
		WHERE t1.id = ? 
		LIMIT 1
	`
	gormx.RawX(ctx, taskQuery, &scanTask, execId)

	// 如果任务不存在，返回空结果
	if scanTask.Id == 0 {
		return &gormx.PageResult{
			Total:       0,
			List:        []artifact.ScanTaskDetail{},
			PageSize:    request.PageSize,
			CurrentPage: request.Page,
		}
	}

	// 解析质量门禁配置
	var qualityGate artifact.QualityGateDto
	if scanTask.QualityGateSwitch == constants.IsTrue && scanTask.QualityGate != "" {
		if err := json.Unmarshal([]byte(scanTask.QualityGate), &qualityGate); err != nil {
			logger.Logger.Panicf("质量阀反序列化失败：%v", err)
		}
	}

	// 构建基础查询条件
	baseCondition := "where exec_id = ?"

	// 添加搜索条件
	if request.SearchKey != "" {
		baseCondition += " and product_name ilike ?"
	}

	// 构建查询字段
	baseFields := "id, repo_name, product_name, product_version, product_path, product_download_url, scan_result, start_time, end_time, status"
	successFields := ", scan_result::jsonb ->> 'critical' as critical" +
		", scan_result::jsonb ->> 'high' as high" +
		", scan_result::jsonb ->> 'medium' as medium" +
		", scan_result::jsonb ->> 'none' as none" +
		", scan_result::jsonb ->> 'low' as low"

	// 构建基础查询SQL
	baseSQL := fmt.Sprintf("SELECT %s %%s FROM neptune_artifact_scan_task_detail %s", baseFields, baseCondition)

	// 处理状态过滤
	statusValue, err := strconv.Atoi(fmt.Sprintf("%v", request.GetQueryParam("statusCode")))

	querySQL := baseSQL

	if err == nil {
		status := int8(statusValue)

		// 处理质量门禁过滤
		if scanTask.QualityGateSwitch == constants.IsFalse && (status == task.ProductStatusGateNotPass || status == task.ProductStatusGatePass) {
			logger.Logger.Infof("质量阈值未开启，选择了质量门禁，直接返回空结果")
			return &gormx.PageResult{}
		}

		// 根据不同状态构建不同的查询
		switch status {
		case task.ProductStatusGatePass:
			// 质量门禁通过
			baseCondition += fmt.Sprintf(" and status = %d", task.ProductStatusSucc)
			querySQL = fmt.Sprintf(baseSQL, successFields)
			querySQL = fmt.Sprintf(`
				SELECT id, repo_name, product_name, product_version, scan_result, start_time, end_time, status 
				FROM (%s) t0
				WHERE critical::int <= %d AND high::int <= %d AND medium::int <= %d AND low::int <= %d AND none::int <= %d
			`, querySQL, qualityGate.Critical, qualityGate.High, qualityGate.Medium, qualityGate.Low, qualityGate.None)

		case task.ProductStatusGateNotPass:
			// 质量门禁未通过
			baseCondition += fmt.Sprintf(" and status = %d", task.ProductStatusSucc)
			querySQL = fmt.Sprintf(baseSQL, successFields)
			querySQL = fmt.Sprintf(`
				SELECT id, repo_name, product_name, product_version, scan_result, start_time, end_time, status 
				FROM (%s) t0
				WHERE critical::int > %d OR high::int > %d OR medium::int > %d OR low::int > %d OR none::int > %d
			`, querySQL, qualityGate.Critical, qualityGate.High, qualityGate.Medium, qualityGate.Low, qualityGate.None)

		default:
			// 其他状态过滤
			baseCondition += fmt.Sprintf(" and status = %d", status)
			querySQL = fmt.Sprintf(baseSQL, "")
		}
	} else {
		// 无状态过滤
		querySQL = fmt.Sprintf(baseSQL, "")
	}

	// 查询总记录数
	var total int64
	countSQL := fmt.Sprintf("SELECT count(*) FROM (%s) t1", querySQL)

	// 执行查询，处理不同参数情况
	if request.SearchKey != "" {
		gormx.RawX(ctx, countSQL, &total, strconv.FormatInt(execId, 10), "%"+request.SearchKey+"%")
	} else {
		gormx.RawX(ctx, countSQL, &total, strconv.FormatInt(execId, 10))
	}

	// 如果没有记录，返回空结果
	if total == 0 {
		return &gormx.PageResult{}
	}

	// 计算分页参数
	offset := (request.Page - 1) * request.PageSize

	// 添加排序和分页条件
	querySQL += " ORDER BY id DESC LIMIT ? OFFSET ?"

	// 查询详细记录
	details := make([]artifact.ScanTaskDetail, 0)

	if request.SearchKey != "" {
		gormx.RawX(ctx, querySQL, &details, strconv.FormatInt(execId, 10), "%"+request.SearchKey+"%", request.PageSize, offset)
	} else {
		gormx.RawX(ctx, querySQL, &details, strconv.FormatInt(execId, 10), request.PageSize, offset)
	}

	// 处理每条记录的状态和展示信息
	for i, detail := range details {
		result, resultStatus := utils.CalGateQualityGate(scanTask.QualityGateSwitch, detail.Status, detail.ScanResult, qualityGate)
		detail.StatusCode = resultStatus
		detail.ViewScanResultInfo = result
		details[i] = detail
	}

	// 返回分页结果
	return &gormx.PageResult{
		Total:       total,
		List:        details,
		PageSize:    request.PageSize,
		CurrentPage: request.Page,
	}
}

// GetScanCount 获取扫描次数
func (s *ScanTaskExecHisService) GetScanCount(ctx *commoncontext.MantisContext, taskId int64) *int64 {
	var count int64

	countQuery := `
		SELECT count(*) 
		FROM neptune_artifact_scan_task_exec_his 
		WHERE task_id = ?
	`

	gormx.RawX(ctx, countQuery, &count, taskId)
	return &count
}

// GetExecRecord 获取原生执行结果
func (s *ScanTaskExecHisService) GetExecRecord(ctx *commoncontext.MantisContext, execId int64) artifact.ScanTaskExecHis {
	var execRecord artifact.ScanTaskExecHis

	recordQuery := `
		SELECT * 
		FROM neptune_artifact_scan_task_exec_his 
		WHERE id = ?
	`

	gormx.RawX(ctx, recordQuery, &execRecord, execId)
	return execRecord
}

// GetExecHistList 查询执行历史记录
func (s *ScanTaskExecHisService) GetExecHistList(ctx *commoncontext.MantisContext, taskId int64, request commondto.QueryDTO) *gormx.PageResult {
	// 构建查询条件
	queryBuilder := gormx.NewParamBuilder().
		Model(&artifact.ScanTaskExecHis{}).
		SelectMany("id", "repo", "trigger_time", "start_time", "end_time", "exec_user", "task_status").
		Eq("task_id", taskId).
		Order("trigger_time", "desc")

	// 查询执行历史记录
	historyRecords := make([]artifact.ScanTaskExecHis, 0)
	page, err := gormx.PageSelectByParamBuilder(
		ctx,
		queryBuilder,
		&historyRecords,
		gormx.PageRequest{
			Page:     request.Page,
			PageSize: request.PageSize,
		},
	)
	if err != nil {
		logger.Logger.Panicf("分页查询任务执行历史失败: %v", err)
	}

	// 处理每条记录的额外信息
	for i, record := range historyRecords {
		// 设置状态描述
		record.StatusMemo = task.StatusCodeToMemo(record.TaskStatus)

		// 解析仓库信息
		if record.Repo != "" {
			if err := json.Unmarshal([]byte(record.Repo), &record.RepoInfo); err != nil {
				logger.Logger.Warnf("解析仓库信息失败: %v", err)
			}
		}

		historyRecords[i] = record
	}

	return page
}

// CalExecResult 计算执行记录下的所有制品扫描结果的汇总
func (s *ScanTaskExecHisService) CalExecResult(ctx *commoncontext.MantisContext, execId int64) artifact.ScanResultInfo {
	// 构建汇总查询SQL
	summaryQuery := `
		SELECT 
			SUM(critical::int) AS critical,
			SUM(high::int) AS high,
			SUM(medium::int) AS medium,
			SUM(none::int) AS none,
			SUM(low::int) AS low,
			SUM(vulnerabilityCount::int) AS vulnerabilityCount 
		FROM (
			SELECT 
				r ->> 'critical' AS critical,
				r ->> 'high' AS high,
				r ->> 'medium' AS medium,
				r ->> 'none' AS none,
				r ->> 'low' AS low,
				r ->> 'vulnerabilityCount' AS vulnerabilityCount 
			FROM (
				SELECT scan_result::jsonb AS r 
				FROM neptune_artifact_scan_task_detail 
				WHERE exec_id = ?
			)
		) t1
	`

	var result artifact.ScanResultInfo
	gormx.RawX(ctx, summaryQuery, &result, execId)
	return result
}

// CheckFinality 检查任务是否处于终态（成功或失败）
func (s *ScanTaskExecHisService) CheckFinality(ctx *commoncontext.MantisContext, execId int64) bool {
	var status int8

	statusQuery := `
		SELECT task_status 
		FROM neptune_artifact_scan_task_exec_his 
		WHERE id = ?
	`

	gormx.RawX(ctx, statusQuery, &status, execId)

	// 判断是否为终态（成功或失败）
	return status == task.ProductStatusSucc || status == task.ProductStatusFail
}
