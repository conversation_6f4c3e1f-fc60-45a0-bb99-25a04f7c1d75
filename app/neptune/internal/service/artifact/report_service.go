package artifact

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"

	dto "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/s3store"

	task2 "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/xuri/excelize/v2"
)

type ReportService struct{}

const (
	// Excel导出行数限制
	sheetRowLimit = 2000

	// 下载状态常量
	downloadStatusDone      int8 = 1
	downloadStatusInProcess int8 = 0
	downloadStatusError     int8 = 2
)

// Excel表头定义
var excelHeaders = []string{
	"制品名称", "制品版本", "仓库名称", "制品路径", "扫描状态", "扫描结果",
	"开始时间", "结束时间", "漏洞编号", "漏洞所在库", "漏洞等级",
	"当前使用版本", "漏洞修复版本", "描述", "解决方案",
}

// Detail 查询漏洞详情
func (r ReportService) Detail(ctx *commoncontext.MantisContext, detailID int64, req commondto.QueryDTO) *gormx.PageResult {
	// 构建基础查询SQL
	baseSQL := `
		SELECT v ->> 'vulnerabilityID' AS vulnerabilityID, 
		       v ->> 'title' AS title, 
		       v ->> 'pkgName' as pkgName,
		       v ->> 'pkgID' AS pkg_id,
		       v ->> 'pkgPath' AS pkgPath,
		       v ->> 'installedVersion' as installedVersion,
		       v ->> 'fixedVersion' AS fixedVersion,
		       v ->> 'status' AS status, 
		       v ->> 'description' as description,
		       v ->> 'severity' AS severity,
		       v ->> 'solution' AS solution 
		FROM (select result from neptune_artifact_scan_task_detail where id=?) t0,
		     jsonb_array_elements(result::jsonb -> 'vulnerabilities') AS v
	`

	// 构建查询条件
	condition := "where 1=1 "

	// 处理安全等级过滤
	severityList, ok := req.GetQueryParam("severity").([]string)
	if ok && len(severityList) > 0 {
		severityParams := make([]string, len(severityList))
		for i, severity := range severityList {
			severityParams[i] = "'" + severity + "'"
		}
		condition += "and t1.severity in (" + strings.Join(severityParams, ",") + ")"
	}

	// 处理搜索关键词
	if key := req.SearchKey; key != "" {
		condition += fmt.Sprintf(" and (t1.vulnerabilityID ilike '%%%s%%' or t1.pkgName ilike '%%%s%%')", key, key)
	}

	// 完整查询SQL
	querySQL := fmt.Sprintf("select %%v from (%s) t1 %s", baseSQL, condition)

	// 查询总数
	var total int64 = 0
	gormx.RawX(ctx, fmt.Sprintf(querySQL, "count(*)"), &total, detailID)

	// 如果没有数据，直接返回空结果
	if total == 0 {
		return &gormx.PageResult{
			Total:       total,
			List:        []dto.RProductVul{},
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		}
	}

	// 计算分页
	offset := (req.Page - 1) * req.PageSize

	// 查询详情数据
	vulnerabilities := make([]dto.RProductVul, 0)
	gormx.RawX(
		ctx,
		fmt.Sprintf(querySQL, "*")+" order by t1.vulnerabilityID offset ? limit ?",
		&vulnerabilities,
		detailID,
		offset,
		req.PageSize,
	)

	return &gormx.PageResult{
		Total:       total,
		List:        vulnerabilities,
		CurrentPage: req.Page,
		PageSize:    req.PageSize,
	}
}

// ExecHisExport 按执行记录ID导出报告
func (r ReportService) ExecHisExport(ctx *commoncontext.MantisContext, execId int64, scanType int, req dto.ProductExportDto, user commondto.UserInfo) {
	// 查询是否有正在下载中的报告
	var scanReport artifact.ScanReport
	gormx.RawX(
		ctx,
		"select * from neptune_artifact_scan_report where foreign_id=? and report_type=? and report_status=?",
		&scanReport,
		execId,
		scanType,
		downloadStatusInProcess,
	)

	// 如果存在正在下载的报告，提示用户稍后再试
	if scanReport.Id != 0 {
		logger.Logger.Panicf("存在下载中的报告,id=%v,请稍后再试!", scanReport.Id)
	}

	// 创建新的报告记录
	scanReport = artifact.ScanReport{
		ReportType:   scanType,
		ReportStatus: downloadStatusInProcess,
		UName:        user.AdAccount,
		ForeignId:    execId,
	}

	// 序列化导出条件
	conditionsJSON, _ := json.Marshal(&req)
	scanReport.Conditions = string(conditionsJSON)

	// 设置创建时间和创建人
	scanReport.SetTimeNowAndUser(user.AdAccount)

	// 保存报告记录
	gormx.InsertUpdateOneX(ctx, &scanReport)

	// 异步生成报告
	go asyncExecHisExport(ctx, execId, scanReport.Id, req.Cves)
}

// List 获取报告列表
func (r ReportService) List(ctx *commoncontext.MantisContext, id int64, req commondto.QueryDTO) *gormx.PageResult {
	// 构建查询条件
	query := gormx.NewParamBuilder().
		Model(&artifact.ScanReport{}).
		Eq("is_deleted", commonconstants.DeleteNo).
		Eq("foreign_id", id).
		Order("id", "desc")

	// 查询报告列表
	reportList := make([]artifact.ScanReport, 0)
	page, err := gormx.PageSelectByParamBuilder(
		ctx,
		query,
		&reportList,
		gormx.PageRequest{
			Page:     req.Page,
			PageSize: req.PageSize,
		},
	)
	if err != nil {
		logger.Logger.Panicf("查询报告列表失败! err=%v", err)
	}

	// 处理报告条件信息
	for i, report := range reportList {
		if report.Conditions != "" {
			var exportConditions dto.ProductExportDto
			if err := json.Unmarshal([]byte(report.Conditions), &exportConditions); err == nil {
				report.ConditionInfo = exportConditions
				reportList[i] = report
			}
		}
	}

	return page
}

// asyncExecHisExport 异步生成并导出报告
func asyncExecHisExport(ctx *commoncontext.MantisContext, execId, reportId int64, cves []string) {
	// 捕获并处理异常，更新报告状态
	defer func() {
		if err := recover(); err != nil {
			logger.Logger.Errorf("导出报告异常: %v", err)
			gormx.ExecX(
				ctx,
				"update neptune_artifact_scan_report set report_status=? where id=?",
				downloadStatusError,
				reportId,
			)
		}
	}()

	logger.Logger.Info("开始导出报告")
	startTime := time.Now().UnixMilli()

	// 过滤并格式化CVE列表
	formattedCVEs := formatCves(cves)

	// 计算数据分布，确保每个sheet不超过行数限制
	idGroups := calData(ctx, execId, formattedCVEs)

	// 构建查询SQL
	baseSQL := `
		select %v v ->> 'vulnerabilityID' as vulnerabilityID,
		       v ->> 'pkgName' as pkgName,
		       v ->> 'installedVersion' as installedVersion, 
		       v ->> 'fixedVersion' AS fixedVersion, 
		       v ->> 'severity' AS severity,
		       v ->> 'solution' AS solution,
		       v ->> 'description' AS description 
		FROM (
			select %v result from neptune_artifact_scan_task_detail 
			where exec_id=? and id in (?)
		) t, jsonb_array_elements(result::jsonb -> 'vulnerabilities') AS v
	`

	prefixFields := "id,product_name,product_version,product_path,repo_name,status,scan_result,start_time,end_time,"

	// 添加CVE过滤条件
	condition := ""
	if len(formattedCVEs) > 0 {
		cveParams := make([]string, len(formattedCVEs))
		for i, cve := range formattedCVEs {
			cveParams[i] = "'" + cve + "'"
		}
		condition = "and t1.vulnerabilityID in (" + strings.Join(cveParams, ",") + ")"
	}

	querySQL := fmt.Sprintf(
		"select *, case "+
			"when severity = 'Critical' then '严重' "+
			"when severity = 'High' then '高危' "+
			"when severity = 'Medium' then '中危' "+
			"when severity = 'Low' then '低危' "+
			"when severity = 'Node' then '未知' "+
			"end as severity from (%s) t1 where 1=1 %s order by id",
		fmt.Sprintf(baseSQL, prefixFields, prefixFields),
		condition,
	)

	// 创建Excel文件
	filePath := fmt.Sprintf("/mantis/product-scan/%d", execId)
	filename := fmt.Sprintf("%d.xlsx", time.Now().UnixNano())

	if err := createExcel(filePath, filename); err != nil {
		logger.Logger.Panicf("创建excel文件失败: %v", err)
	}

	// 打开Excel文件
	excelFile, err := excelize.OpenFile(filePath + "/" + filename)
	if err != nil {
		logger.Logger.Panicf("打开excel文件失败: %v", err)
	}

	// 处理每个ID组，创建对应的Sheet并填充数据
	for sheetIndex, idGroup := range idGroups {
		// 如果ID组为空，跳过
		if len(idGroup) == 0 {
			continue
		}

		// 查询漏洞数据
		vulData := make([]dto.VulExcel, 0)
		gormx.RawX(ctx, querySQL, &vulData, execId, idGroup)

		// 如果没有数据，跳过
		if len(vulData) == 0 {
			continue
		}

		// 保存数据到Excel
		saveData(excelFile, vulData, sheetIndex)
	}

	// 保存Excel文件
	if err := excelFile.Save(); err != nil {
		logger.Logger.Panicf("保存Excel文件失败: %v", err)
	}

	// 上传到S3存储
	s3store.UploadFile(filePath+"/"+filename, filePath+"/"+filename)

	// 生成下载链接
	objKey := strings.TrimPrefix(path.Join(filePath, filename), "/")
	objKeyBase64 := base64.StdEncoding.EncodeToString([]byte(objKey))
	reportDownloadUrl := configs.Config.Domain.Cube + "/magic/api/common/api/v1/biz/file/download?obj_key=" + objKeyBase64

	// 更新报告状态和下载链接
	gormx.ExecX(
		ctx,
		"update neptune_artifact_scan_report set report_url=?, report_status=?, gmt_modified=now() where id=?",
		reportDownloadUrl,
		downloadStatusDone,
		reportId,
	)

	logger.Logger.Infof(
		"导出报告完成, 耗时=%v ms, 下载链接=%s",
		time.Now().UnixMilli()-startTime,
		reportDownloadUrl,
	)
}

// createExcel 创建Excel文件
func createExcel(filepath, filename string) error {
	// 检查并创建目录
	if _, err := os.Stat(filepath); err != nil {
		if os.IsNotExist(err) {
			if err := os.MkdirAll(filepath, 0o755); err != nil {
				return fmt.Errorf("创建目录失败: %v", err)
			}
		} else {
			return fmt.Errorf("检查目录失败: %v", err)
		}
	}

	// 创建新Excel文件
	f := excelize.NewFile()
	defer f.Close()

	// 保存文件
	return f.SaveAs(filepath + "/" + filename)
}

// calData 计算数据分布，确保每个sheet不超过行数限制
func calData(ctx *commoncontext.MantisContext, execId int64, cves []string) [][]int64 {
	var idCounts []dto.IdCount

	// 构建基础SQL
	baseSQL := `
		select t1.id, count(t1.id) as count 
		from (
			SELECT id, v ->> 'vulnerabilityID' as vulnerabilityID 
			FROM (
				select id, result from neptune_artifact_scan_task_detail where exec_id=?
			) t, jsonb_array_elements(result::jsonb -> 'vulnerabilities') AS v
		) t1 where 1=1 %s group by id
	`

	// 根据是否有CVE过滤条件构建SQL
	if len(cves) == 0 {
		gormx.RawX(ctx, fmt.Sprintf(baseSQL, ""), &idCounts, execId)
	} else {
		gormx.RawX(ctx, fmt.Sprintf(baseSQL, "and t1.vulnerabilityID in ?"), &idCounts, execId, cves)
	}

	// 分组处理ID，确保每个Sheet的数据量不超过限制
	idGroups := make([][]int64, 0)
	currentCount := 0
	currentGroup := make([]int64, 0)

	// 遍历处理每个ID及其对应的记录数
	for _, idCount := range idCounts {
		// 如果单个ID的记录数超过限制，单独一组
		if idCount.Count > sheetRowLimit {
			idGroups = append(idGroups, []int64{idCount.Id})
			continue
		}

		// 累计记录数
		currentCount += idCount.Count
		currentGroup = append(currentGroup, idCount.Id)

		// 如果当前组累计记录数超过限制，创建新组
		if currentCount > sheetRowLimit {
			idGroups = append(idGroups, currentGroup)
			currentGroup = make([]int64, 0)
			currentCount = 0
		}
	}

	// 添加最后一组（如果有数据）
	if len(currentGroup) > 0 {
		idGroups = append(idGroups, currentGroup)
	}

	return idGroups
}

// saveData 将数据保存到Excel的指定Sheet
func saveData(f *excelize.File, data []dto.VulExcel, sheetIndex int) {
	// 创建新Sheet
	sheetName := "sheet" + strconv.Itoa(sheetIndex+1)
	if _, err := f.NewSheet(sheetName); err != nil {
		logger.Logger.Errorf("创建sheet失败: %v", err)
		return
	}

	// 设置表头
	f.SetSheetRow(sheetName, "A1", &excelHeaders)

	// 跟踪每个ID的记录数，用于后续合并单元格
	idRecordCounts := make(map[int64]int)

	// 填充数据
	for rowIndex, record := range data {
		// 累计每个ID的记录数
		if count, exists := idRecordCounts[record.Id]; exists {
			idRecordCounts[record.Id] = count + 1
		} else {
			idRecordCounts[record.Id] = 1
		}

		// 根据任务状态设置不同的数据
		cellIndex := strconv.Itoa(rowIndex + 2) // +2 因为第1行是表头

		if record.Status == task2.TaskStatusSuccess {
			// 成功状态，展示完整数据
			var scanResult artifact.QualityGateDto
			if err := json.Unmarshal([]byte(record.ScanResult), &scanResult); err != nil {
				logger.Logger.Errorf("解析扫描结果失败: %v", err)
				continue
			}

			// 格式化时间
			startTime := "-"
			if record.StartTime != nil {
				startTime = record.StartTime.ToString()
			}

			endTime := "-"
			if record.EndTime != nil {
				endTime = record.EndTime.ToString()
			}

			// 设置行数据
			f.SetSheetRow(sheetName, "A"+cellIndex, &[]string{
				record.ProductName,
				record.ProductVersion,
				record.RepoName,
				record.ProductPath,
				task2.StatusCodeToMemo(record.Status),
				fmt.Sprintf("%d/%d/%d/%d/%d", scanResult.Critical, scanResult.High, scanResult.Medium, scanResult.Low, scanResult.None),
				startTime,
				endTime,
				record.VulnerabilityID,
				record.PkgName,
				record.Severity,
				record.InstalledVersion,
				record.FixedVersion,
				record.Description,
				record.Solution,
			})
		} else {
			// 非成功状态，仅展示基础信息
			f.SetSheetRow(sheetName, "A"+cellIndex, &[]string{
				record.ProductName,
				record.ProductVersion,
				record.RepoName,
				record.ProductPath,
				task2.StatusCodeToMemo(record.Status),
				"-", "-", "-", "-", "-", "-", "-", "-", "-", "-",
			})
		}
	}

	// 创建单元格样式
	style, err := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	if err != nil {
		logger.Logger.Errorf("创建Excel样式失败: %v", err)
		return
	}

	// 合并相同ID的前7列单元格
	cellColumns := []string{"A", "B", "C", "D", "E", "F", "G"}
	startRow := 2 // 从第2行开始（第1行是表头）

	for _, count := range idRecordCounts {
		if count <= 1 {
			startRow++
			continue // 不需要合并单行
		}

		// 合并各列的单元格
		for _, column := range cellColumns {
			topCell := column + strconv.Itoa(startRow)
			bottomCell := column + strconv.Itoa(startRow+count-1)
			mergeAndSetCell(sheetName, topCell, bottomCell, style, f)
		}

		startRow += count
	}
}

// mergeAndSetCell 合并单元格并设置样式
func mergeAndSetCell(sheetName, topLeftCell, bottomRightCell string, style int, f *excelize.File) {
	// 合并单元格
	if err := f.MergeCell(sheetName, topLeftCell, bottomRightCell); err != nil {
		logger.Logger.Errorf("合并单元格失败: %v", err)
		return
	}

	// 设置单元格样式
	if err := f.SetCellStyle(sheetName, topLeftCell, bottomRightCell, style); err != nil {
		logger.Logger.Errorf("设置单元格样式失败: %v", err)
	}
}

// formatCves 过滤空的CVE项
func formatCves(cves []string) []string {
	if cves == nil {
		return cves
	}

	filteredCVEs := make([]string, 0, len(cves))
	for _, cve := range cves {
		if strings.TrimSpace(cve) != "" {
			filteredCVEs = append(filteredCVEs, cve)
		}
	}

	return filteredCVEs
}
