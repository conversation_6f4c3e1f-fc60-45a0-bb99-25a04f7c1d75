package artifact

import (
	"fmt"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	taskEnum "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type PipelineService struct{}

// ScanProduct 处理产品扫描请求并返回报告URL
func (s PipelineService) ScanProduct(ctx *commoncontext.MantisContext, req *dto.PipelineProductScanRequestDto) map[string]interface{} {
	logger.Logger.Infof("pipeline-req: %+v", req)

	// 获取默认扫描方案
	var plan artifact.ScanPlan
	const sqlGetDefaultPlan = "select * from neptune_artifact_scan_plan where company_id=? and is_default=1 and plan_type = 1 and is_deleted ='N' order by id desc limit 1"
	gormx.RawX(ctx, sqlGetDefaultPlan, &plan, req.CompanyID)
	if plan.Id == 0 {
		logger.Logger.Panicf("没有找到默认漏洞扫描方案,请先设置默认方案!")
		return make(map[string]interface{})
	}

	// 设置流水线默认用户
	user := commondto.UserInfo{
		CompanyID: req.CompanyID,
		AdAccount: constants.UserDevops,
		Username:  constants.UserDevops,
	}

	// 查找或准备任务
	var task artifact.ScanTask
	const sqlFindTask = "select * from neptune_artifact_scan_task where task_name=? and company_id=? and plan_type = 1 and task_from =? order by id desc limit 1"
	gormx.RawX(ctx, sqlFindTask, &task, req.TaskName, req.CompanyID, taskEnum.TaskFromPipeline)

	// 设置任务公共属性
	task.GmtModified = times.Now()
	task.CallBackUrl = req.CallBackUrl
	task.ProductList = &[]dto.ViewProductInfo{{
		RepoInfo:    &dto.RepoDto{Name: req.RepoName},
		ProductInfo: &dto.MinProductDto{Name: req.ProductName},
		VersionInfo: &dto.ProductVersionDto{Version: req.ProductVersion},
		PathInfo:    &dto.ProductPathDto{Path: req.ProductPath, DownloadUrl: req.ProductDownloadUrl},
	}}

	// 处理新任务创建
	if task.Id == 0 {
		logger.Logger.Infof("创建一个新的task,taskName=%v", req.TaskName)
		task.TaskName = req.TaskName
		task.ScanRange = taskEnum.ScanRangeCusProduct
		task.CompanyID = req.CompanyID
		task.PlanId = plan.Id
		task.PlanType = 1
		task.ProductType = req.ProductType
		task.TaskFrom = taskEnum.TaskFromPipeline
		task.QualityGateSwitch = constants.IsFalse // 流水线触发,关闭质量门禁
		task.RepoInfo = &[]dto.RepoDto{{Name: req.RepoName}}
		task.Creator = constants.UserDevops
		task.Modifier = constants.UserDevops
		task.GmtCreated = times.Now()
		task.RepoInstanceId = req.InstanceId
	} else {
		logger.Logger.Infof("task已经存在,更新制品信息,taskName=%v", req.TaskName)
	}

	// 保存任务并执行
	taskId := taskService.AddOrUpdateTask(ctx, &task, user)
	logger.Logger.Infof("task id is %d", taskId)
	execId := taskService.Exec(ctx, taskId, user, req.AppName)

	// 生成并返回报告URL
	return map[string]interface{}{
		"reportUrl": fmt.Sprintf(constants.ProductScanRunningReportPath, configs.Config.Domain.Cube, taskId, execId),
		"appName":   req.AppName,
	}
}
