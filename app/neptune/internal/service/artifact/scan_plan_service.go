package artifact

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type ScanPlanService struct{}

var taskService = ScanTaskService{}

// List 查看方案列表
func (s ScanPlanService) List(ctx *commoncontext.MantisContext, request *dto.QueryWithOrderDTO, user dto.UserInfo) *gormx.PageResult {
	logger.Logger.Infof("ScanPlanService.List...request: %+v ", request)

	// 构建查询条件
	query := gormx.NewParamBuilder().
		Model(&artifact.ScanPlan{}).
		Eq("is_deleted", commonconstants.DeleteNo).
		Eq("company_id", utils.IDString(user.CompanyID))

	// 添加搜索条件
	if searchKey := request.SearchKey; searchKey != "" {
		query.ILike("plan_name", "%"+searchKey+"%")
	}

	// 添加方案类型过滤
	if planType := request.GetQueryParam("planType"); planType != nil && reflect.TypeOf(planType).Kind() == reflect.String {
		query.Eq("plan_type", planType)
	}

	// 添加排序条件
	if request.OrderField != "" {
		query.Order(request.OrderField, request.OrderTypeFormat())
	} else {
		query.Order("gmt_created", "desc")
	}

	// 查询数据
	planList := make([]artifact.ScanPlan, 0)
	page, err := gormx.PageSelectByParamBuilder(ctx, query, &planList, gormx.PageRequest{
		Page:     request.Page,
		PageSize: request.PageSize,
	})
	if err != nil {
		logger.Logger.Panicf("查询方案列表失败! err=%v", err)
	}

	// 处理漏洞白名单
	for i, plan := range planList {
		if plan.WhiteCVE == "" {
			continue
		}

		if err := json.Unmarshal([]byte(plan.WhiteCVE), &plan.WhiteCVEInfos); err != nil {
			logger.Logger.Panicf("漏洞白名单对象转化失败! err=%v", err)
		}

		planList[i] = plan
	}

	return page
}

// Info 查看方案详情
func (s ScanPlanService) Info(ctx *commoncontext.MantisContext, id int64) artifact.ScanPlan {
	logger.Logger.Infof("ScanPlanService.Info...id: %d", id)

	// 构建查询条件
	query := gormx.NewParamBuilder().
		Model(&artifact.ScanPlan{}).
		Eq("id", id).
		Eq("is_deleted", commonconstants.DeleteNo)

	// 查询方案详情
	var plan artifact.ScanPlan
	gormx.SelectByParamBuilderX(ctx, query, &plan)

	// 处理漏洞白名单
	if plan.WhiteCVE != "" {
		if err := json.Unmarshal([]byte(plan.WhiteCVE), &plan.WhiteCVEInfos); err != nil {
			logger.Logger.Panicf("漏洞白名单对象转化失败! err=%v", err)
		}
	}

	return plan
}

// AddOrUpdatePlan 新增或修改方案
func (s ScanPlanService) AddOrUpdatePlan(ctx *commoncontext.MantisContext, plan *artifact.ScanPlan, user dto.UserInfo) int64 {
	logger.Logger.Infof("ScanPlanService.AddOrUpdatePlan...plan: %+v ", plan)

	// 检查方案名称是否重复
	checkExist(ctx, plan.PlanName, fmt.Sprintf("%v", user.CompanyID), plan.Id)

	// 更新基础信息
	plan.Modifier = user.AdAccount
	plan.GmtModified = times.Now()

	// 序列化漏洞白名单
	if len(plan.WhiteCVEInfos) > 0 {
		whiteCVEJSON, _ := json.Marshal(plan.WhiteCVEInfos)
		plan.WhiteCVE = string(whiteCVEJSON)
	}

	// 新增或更新方案
	if plan.Id == 0 {
		// 新增方案
		plan.CompanyID = fmt.Sprintf("%v", user.CompanyID)
		plan.Creator = user.AdAccount
		plan.GmtCreated = times.Now()
	}

	// 保存方案
	gormx.InsertUpdateOneX(ctx, plan)

	// 如果是默认方案，设置为默认
	if plan.IsDefault == constants.IsDefault {
		s.Default(ctx, plan.Id, strconv.Itoa(constants.IsDefault), user)
	}

	logger.Logger.Infof("ScanPlanService.AddOrUpdatePlan...id: %d", plan.Id)
	return plan.Id
}

// checkExist 检查方案名称是否已存在
func checkExist(ctx *commoncontext.MantisContext, planName string, companyId string, id int64) {
	// 构建查询条件
	existingPlan := artifact.ScanPlan{}
	existingPlan.PlanName = planName
	existingPlan.CompanyID = companyId
	existingPlan.IsDeleted = commonconstants.DeleteNo

	// 查询是否存在同名方案
	gormx.SelectOneByConditionX(ctx, &existingPlan)

	// 新增方案时，检查是否有同名方案
	if id == 0 && existingPlan.Id > 0 {
		logger.Logger.Panicf("新增方案验证: 名称[%v]重复, 请更换名称！", planName)
	}

	// 修改方案时，检查是否有其他同名方案
	if id > 0 && existingPlan.Id > 0 && existingPlan.Id != id {
		logger.Logger.Panicf("修改方案验证: 名称[%v]重复, 请更换名称！", planName)
	}
}

// Default 设置或取消默认方案
func (s ScanPlanService) Default(ctx *commoncontext.MantisContext, id int64, v string, user dto.UserInfo) {
	logger.Logger.Infof("ScanPlanService.Default...id: %d", id)

	// 取消默认
	if v == "0" {
		gormx.ExecX(
			ctx,
			"update neptune_artifact_scan_plan set is_default=?, modifier=?, gmt_modified=? where id=?",
			constants.IsFalse, user.AdAccount, times.Now(), id,
		)
		return
	}

	// 设置为默认方案 (在事务中执行)
	companyId := user.CompanyID
	gormx.Transaction(ctx, func() error {
		// 先将所有方案设为非默认
		gormx.ExecX(
			ctx,
			"update neptune_artifact_scan_plan set is_default=? where is_deleted='N' and is_default=? and company_id=?",
			constants.IsFalse, constants.IsDefault, companyId,
		)

		// 将当前方案设为默认
		gormx.ExecX(
			ctx,
			"update neptune_artifact_scan_plan set is_default=?, modifier=?, gmt_modified=? where id=?",
			constants.IsDefault, user.AdAccount, times.Now(), id,
		)

		return nil
	})
}

// DeletePlan 删除方案
func (s ScanPlanService) DeletePlan(ctx *commoncontext.MantisContext, id int64, user dto.UserInfo) {
	logger.Logger.Infof("ScanPlanService.DeletePlan...id: %d", id)

	// 检查是否有关联的任务正在执行
	activeTaskCount := checkBindTask(ctx, id)
	if activeTaskCount > 0 {
		logger.Logger.Panicf("方案[id=%d]下有任务正在执行或等待执行，请先中止任务或等待任务执行完成!", id)
	}

	// 执行逻辑删除
	plan := artifact.ScanPlan{}
	plan.Id = id
	plan.IsDeleted = commonconstants.DeleteYes
	plan.GmtModified = times.Now()
	plan.Modifier = user.AdAccount

	gormx.UpdateOneByConditionX(ctx, &plan)
}

// checkBindTask 检查是否有关联的任务正在执行
func checkBindTask(ctx *commoncontext.MantisContext, planId int64) int64 {
	// 查询关联的任务状态
	query := `
		select count(*) from neptune_artifact_scan_task t1 
		left join neptune_artifact_scan_task_exec_his t2 on t1.last_exec_id = t2.id
		where plan_id = ? and t1.is_deleted = 'N' and t2.task_status in (0, 1) 
		limit 1
	`

	var activeTaskCount int64 = 0
	gormx.RawX(ctx, query, &activeTaskCount, planId)

	return activeTaskCount
}

// whiteInfos 获取所有的漏洞白名单
func (s ScanPlanService) whiteInfos(ctx *commoncontext.MantisContext, planId int64) []artifact.WhiteCVEInfo {
	// 查询方案信息
	query := gormx.NewParamBuilder().
		Model(&artifact.ScanPlan{}).
		Eq("id", planId).
		Eq("is_deleted", commonconstants.DeleteNo)

	var plan artifact.ScanPlan
	gormx.SelectByParamBuilderX(ctx, query, &plan)

	// 检查方案是否存在
	if plan.Id == 0 {
		logger.Logger.Panicf("方案[id=%d]不存在或已被删除!", planId)
	}

	// 解析漏洞白名单
	cveInfoList := make([]artifact.WhiteCVEInfo, 0)
	if plan.WhiteCVE != "" {
		if err := json.Unmarshal([]byte(plan.WhiteCVE), &cveInfoList); err != nil {
			logger.Logger.Panicf("漏洞白名单解析失败! err=%v", err)
		}
	}

	return cveInfoList
}

// BindTask 查询绑定的task列表
func (s ScanPlanService) BindTask(ctx *commoncontext.MantisContext, req *dto.QueryDTO, planId int64) *gormx.PageResult {
	// SQL查询模板
	baseSQL := `
		from neptune_artifact_scan_task t1
		left join neptune_artifact_scan_task_exec_his t2 on t1.last_exec_id = t2.id
		where plan_id = ? and t1.is_deleted = 'N' and t2.task_status in (?)
	`

	// 解析状态参数
	statusParam := req.GetQueryParam("status")
	statusStrings, ok := statusParam.([]string)
	if !ok {
		logger.Logger.Panicf("参数格式错误，status应为数组!")
	}

	statusList := make([]int, len(statusStrings))
	for i, statusStr := range statusStrings {
		statusVal, err := strconv.Atoi(statusStr)
		if err != nil {
			logger.Logger.Panicf("参数格式错误，status应为整数数组! err=%v", err)
		}
		statusList[i] = statusVal
	}

	// 查询总记录数
	var total int64 = 0
	countSQL := "select count(*) " + baseSQL
	gormx.RawX(ctx, countSQL, &total, planId, statusList)

	// 如果没有记录，直接返回空结果
	if total == 0 {
		return &gormx.PageResult{
			Total:       total,
			List:        []map[string]interface{}{},
			CurrentPage: req.Page,
			PageSize:    req.PageSize,
		}
	}

	// 查询列表数据
	offset := (req.Page - 1) * req.PageSize
	dataSQL := `
		select 
			t1.id,
			t1.task_name as name,
			to_char(t2.trigger_time, 'YYYY-MM-DD HH24:MI:SS') as time,
			t1.last_exec_id as lastExecId,
			t2.exec_user as uname,
			t2.task_status as status
	` + baseSQL + " order by t1.id offset ? limit ?"

	tasks := make([]map[string]interface{}, 0)
	gormx.RawX(ctx, dataSQL, &tasks, planId, statusList, offset, req.PageSize)

	// 修正返回字段名称
	for _, task := range tasks {
		if lastExecId, exists := task["lastexecid"]; exists {
			task["lastExecId"] = lastExecId
			delete(task, "lastexecid")
		}
	}

	return &gormx.PageResult{
		Total:       total,
		List:        tasks,
		CurrentPage: req.Page,
		PageSize:    req.PageSize,
	}
}
