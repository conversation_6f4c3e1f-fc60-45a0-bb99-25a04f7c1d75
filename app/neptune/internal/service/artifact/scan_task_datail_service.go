package artifact

import (
	"encoding/json"
	"fmt"
	"strconv"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/pubsub"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"knative.dev/pkg/apis"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/remote"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/message/payload"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/repo"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	taskEnum "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	corev1 "k8s.io/api/core/v1"
)

type ScanTaskDetailService struct{}

type PipeLineRs struct {
	Num       int64  `json:"num"`
	ReportUrl string `json:"reportUrl"`
}

var (
	resultPath     = "/neptune/openapi/v1/result/%v/%v/form"
	ship           = remote.ShipInvoke{}
	execHisService = ScanTaskExecHisService{}
)

func (s ScanTaskDetailService) GenTaskDetail(ctx *commoncontext.MantisContext,
	task *artifact.ScanTask, execId int64, user commondto.UserInfo, cveArr []artifact.WhiteCVEInfo,
) {
	defer errCallBack(ctx, task, execId)
	products := make([]dto.ViewProductInfo, 0)
	switch task.ScanRange {
	// 定制化制品
	case taskEnum.ScanRangeCusProduct:
		err := json.Unmarshal([]byte(task.ScanRangeInfo), &products)
		if err != nil {
			logger.Logger.Panicf("定制的制品转换失败！")
		}
	// 最新制品
	case taskEnum.ScanRangeLastProduct:
		if task.Repo != "" {
			repos := make([]dto.RepoDto, 0)
			err := json.Unmarshal([]byte(task.Repo), &repos)
			if err != nil {
				logger.Logger.Panicf("反序列化制品仓库失败！taskId=%v", task.Id)
				return
			}
			companyId := utils.IDString(user.CompanyID)
			repoService := repo.GetServiceById(ctx, companyId, task.RepoInstanceId)
			for _, repoItem := range repos {
				repoProducts := repoService.LatestAllProducts(ctx, repoItem.Name, repoItem.Id, repoItem.RepoType)
				logger.Logger.Infof("获取仓库=%v 下所有的最新制品,共计%v个", repoItem.Name, len(repoProducts))
				products = append(products, repoProducts...)
			}
		}
	case taskEnum.ScanRangeRuleProduct:
		// todo 规则制品
	}
	logger.Logger.Infof("taskId=%d 下共计有%v个制品", task.Id, len(products))
	details, err := insertBatch(ctx, products, task, execId, user)
	if err != nil {
		logger.Logger.Panicf("插入数据失败!,err=%v", err)
		return
	}
	companyId := utils.IDString(user.CompanyID)
	instance, err := repo.GetRepoInstanceInfo(ctx, companyId, task.RepoInstanceId)
	if err != nil {
		logger.Logger.Errorf("从 deckjob 获取仓库实例信息失败,err=%v", err)
	}
	sendMsg(&details, *instance, execId, cveArr, task)
	// 更新制品总数
	gormx.ExecX(ctx, "update neptune_artifact_scan_task_exec_his set product_count=? where id=?", len(details), execId)
}

func sendMsg(details *[]artifact.ScanTaskDetail, repoInstance configs.RepoInstance, execId int64, cveArr []artifact.WhiteCVEInfo, task *artifact.ScanTask) {
	whiteCVEs := make([]string, len(cveArr))
	for i, cve := range cveArr {
		whiteCVEs[i] = cve.Code
	}
	for index, detail := range *details {
		msgPayload := payload.NewArtifactScanTaskPayload(
			repoInstance,
			detail.Id,
			execId,
			task.ProductType,
			detail.RepoName,
			detail.ProductName,
			detail.ProductVersion,
			fmt.Sprintf(resultPath, enums.ScanTypeProductsScan, detail.Id),
			detail.ProductDownloadUrl,
		).WithWhiteCVE(whiteCVEs).WithTaskFrom(task.TaskFrom)
		if index == 0 {
			msgPayload.IsFirst = constants.IsTrue
		}
		pubsub.SendToRedisQueue(*msgPayload, task.TaskFrom)
	}
}

// insertBatch 使用数据库事务插入数据
func insertBatch(ctx *commoncontext.MantisContext,
	products []dto.ViewProductInfo, task *artifact.ScanTask, execId int64, user commondto.UserInfo,
) ([]artifact.ScanTaskDetail, error) {
	details := make([]artifact.ScanTaskDetail, len(products))
	for i, product := range products {
		if product.PathInfo == nil {
			product.PathInfo = &dto.ProductPathDto{}
		}
		detail := artifact.ScanTaskDetail{
			TaskId:             task.Id,
			ExecId:             execId,
			ProductName:        product.ProductInfo.Name,
			ProductVersion:     product.VersionInfo.Version,
			RepoName:           product.RepoInfo.Name,
			Status:             taskEnum.ProductStatusWait,
			InstanceId:         task.RepoInstanceId,
			ProductPath:        product.PathInfo.Path,
			ProductDownloadUrl: product.PathInfo.DownloadUrl,
		}
		detail.Creator = user.AdAccount
		detail.Modifier = user.AdAccount
		detail.GmtCreated = times.Now()
		detail.GmtModified = times.Now()
		detail.Result = "{}"
		detail.ScanResult = "{}"
		details[i] = detail
	}
	// 使用事务批量插入数据
	gormx.Transaction(ctx, func() error {
		const batchSize = 30
		if len(details) < batchSize {
			gormx.InsertBatchX(ctx, &details)
			return nil
		}
		totalCount := len(details)
		for i := 0; i < totalCount; i += batchSize {
			endIndex := i + batchSize
			if endIndex > totalCount {
				endIndex = totalCount
			}
			detailsBatch := details[i:endIndex]
			gormx.InsertBatchX(ctx, &detailsBatch)
		}
		return nil
	})
	return details, nil
}

// errCallBack 处理异常回调ship
func errCallBack(ctx *commoncontext.MantisContext, task *artifact.ScanTask, execId int64) {
	err := recover()
	if err == nil {
		return
	}
	logger.Logger.Errorf("生成任务详情失败,err=%v", err)
	gormx.ExecX(ctx, "update neptune_artifact_scan_task_exec_his set task_status=? ,err_msg=? where id=?", taskEnum.TaskStatusFail, err, execId)
	if task.TaskFrom == taskEnum.TaskFromPipeline {
		// 流水线任务特殊处理（目前为空）
	}
}

// ArtifactStatusAndResultLinkUpdate 状态及结果联动更新
func ArtifactStatusAndResultLinkUpdate(condition apis.Condition, labels map[string]string) error {
	var status int8
	switch condition.Status {
	case corev1.ConditionTrue:
		status = task.ProductStatusSucc
	case corev1.ConditionFalse:
		status = task.ProductStatusFail
	case corev1.ConditionUnknown:
		if condition.Reason == "Running" || condition.Reason == "Pending" {
			status = task.TaskStatusRun
		}
		if condition.Reason == v1beta1.TaskRunReasonTimedOut.String() {
			status = task.ProductStatusFail
		}
	default:
		return fmt.Errorf("syncTaskRunRecord invaild taskrun status")
	}

	// 获取isFirst参数
	isFirstStr, ok := labels[commonconstants.IsFirstLabel]
	if !ok {
		return fmt.Errorf("error in getting isFirst")
	}
	isFirst, err := strconv.Atoi(isFirstStr)
	if err != nil {
		return fmt.Errorf("error in parsing isFirst: %v", err)
	}

	// 获取execId参数
	execIdStr, ok := labels[commonconstants.ExecIdLabel]
	if !ok {
		return fmt.Errorf("error in getting execId")
	}
	execId, err := strconv.ParseInt(execIdStr, 10, 64)
	if err != nil {
		return fmt.Errorf("error in parsing execId: %v", err)
	}

	// 获取detailId参数
	detailIdStr, ok := labels[commonconstants.DetailIdLabel]
	if !ok {
		return fmt.Errorf("error in getting detailId")
	}
	detailId, err := strconv.ParseInt(detailIdStr, 10, 64)
	if err != nil {
		return fmt.Errorf("error in parsing detailId: %v", err)
	}

	ctx := &commoncontext.MantisContext{}
	finality := execHisService.CheckFinality(ctx, execId)
	if finality {
		return nil
	}

	// 处理detail
	if status == taskEnum.ProductStatusRun {
		_, err := gormx.Exec(ctx, "update neptune_artifact_scan_task_detail set status=?,start_time=? where id=? and status < ?",
			status, times.Now(), detailId, taskEnum.ProductStatusSucc)
		if err != nil {
			return err
		}
	} else {
		_, err := gormx.Exec(ctx, "update neptune_artifact_scan_task_detail set status=?,end_time=?,err_msg=? where id=? and status < ?",
			status, times.Now(), err, detailId, taskEnum.ProductStatusSucc)
		if err != nil {
			return err
		}
	}

	// 处理执行历史
	if isFirst == constants.IsTrue && status == taskEnum.ProductStatusFail {
		_, err := gormx.Exec(ctx, "update neptune_artifact_scan_task_exec_his set start_time=?,task_status=? where id =? ",
			times.Now(), taskEnum.TaskStatusFail, execId)
		return err
	}
	return nil
}

// FinallyStatusAndResultUpdate 任务终态处理
func (s ScanTaskDetailService) FinallyStatusAndResultUpdate(ctx *commoncontext.MantisContext, detailId, execId int64, taskFrom string) {
	finality := execHisService.CheckFinality(ctx, execId)
	if finality {
		return
	}

	// 判断是否还有执行中的任务，如果没有，则表示整个任务结果，需要汇总结构，保存到exechis中
	var unfinishedId int64
	gormx.RawX(ctx, "select id from neptune_artifact_scan_task_detail where exec_id =? and status in ? limit 1",
		&unfinishedId, execId, []int8{taskEnum.ProductStatusWait, taskEnum.ProductStatusRun})
	if unfinishedId > 0 {
		logger.Logger.Infof("execId=%d 下还有未完成的任务,暂不汇总结果！", execId)
		return
	}

	logger.Logger.Infof("execId=%d 的所有制品扫描完成,汇总结果！", execId)

	// 检查是否有失败的任务
	execStatus := taskEnum.TaskStatusSuccess
	var failId int64
	gormx.RawX(ctx, "select id from neptune_artifact_scan_task_detail where exec_id =? and status = ? limit 1",
		&failId, execId, taskEnum.ProductStatusFail)
	if failId > 0 {
		execStatus = taskEnum.TaskStatusFail
	}

	// 汇总扫描结果
	sqlSummarize := `select sum(critical::int) as critical,sum(high::int) as high ,sum(medium::int) as medium,sum(none::int) as none,
		sum(low::int) as low ,sum(vulnerabilityCount::int) as vulnerabilityCount from (
		select scan_result::jsonb ->> 'critical' as critical, scan_result::jsonb ->> 'high' as high,scan_result::jsonb ->> 'medium' as medium ,
		scan_result::jsonb ->> 'none' as none ,scan_result::jsonb ->> 'low' as low ,scan_result::jsonb ->> 'vulnerabilityCount' as vulnerabilityCount
		from neptune_artifact_scan_task_detail where exec_id=? and status=? ) t `

	var allResult artifact.ScanResultInfo
	gormx.RawX(ctx, sqlSummarize, &allResult, execId, taskEnum.ProductStatusSucc)

	// 更新执行历史记录
	hTaskStatus := int8(execStatus)
	resultBytes, err2 := json.Marshal(allResult)
	errMsg := ""
	if err2 != nil {
		logger.Logger.Errorf("汇总数据转换为json字符串异常！！！execId=%d,err=%v", execId, err2)
		resultBytes = []byte("{}")
		hTaskStatus = taskEnum.TaskStatusFail
		errMsg = "汇总数据转换为json字符串异常！！！"
	}

	gormx.ExecX(ctx, "update neptune_artifact_scan_task_exec_his set task_status=?,end_time=?,scan_result=?,err_msg=? where id=?",
		hTaskStatus, times.Now(), string(resultBytes), errMsg, execId)

	if taskFrom == taskEnum.TaskFromPipeline {
		goroutine.Run(func() {
			DeckJobHelper(ctx, execId, detailId)
		})
	}
}

func DeckJobHelper(ctx *commoncontext.MantisContext, execId, detailId int64) {
	// 查询任务信息
	taskInfoList := make([]map[string]interface{}, 0)
	sqlQuery := `select t1.task_status,t1.scan_result,t1.app_name,t1.task_id,t2.task_from ,t2.call_back_url 
		from neptune_artifact_scan_task_exec_his t1
		left join neptune_artifact_scan_task t2 on t1.task_id =t2.id where t1.id=?`
	gormx.RawX(ctx, sqlQuery, &taskInfoList, execId)

	// 检查任务信息是否存在
	if len(taskInfoList) == 0 {
		return
	}

	taskInfo := taskInfoList[0]

	// 检查回调URL是否存在
	callBackURL, ok := taskInfo["call_back_url"].(string)
	if !ok || callBackURL == "" {
		logger.Logger.Errorf("pipeline task no call-back-url !!! execId={%v}", execId)
		return
	}

	// 准备回调数据
	body := make(map[string]interface{})
	body["appName"] = taskInfo["app_name"]

	// 获取任务状态
	statusValue, _ := strconv.ParseInt(fmt.Sprintf("%v", taskInfo["task_status"]), 10, 32)

	// 根据任务状态准备不同的回调内容
	if statusValue == int64(taskEnum.TaskStatusSuccess) {
		body["status"] = constants.IsTrue

		// 解析扫描结果数据
		var data artifact.QualityGateDto
		err := json.Unmarshal([]byte(fmt.Sprintf("%v", taskInfo["scan_result"])), &data)
		if err != nil {
			logger.Logger.Panicf("转换数据异常,err=%v", err)
		}

		// 准备抽屉参数
		drawerParam := map[string]interface{}{
			"severity":  "%v",
			"productId": detailId,
		}
		drawerParamByte, _ := json.Marshal(drawerParam)
		drawerParamStr := string(drawerParamByte)

		// 设置各个级别的结果
		body["low"] = PipeLineRs{
			Num: data.Low,
			ReportUrl: fmt.Sprintf(constants.ProductScanDoneReportPath,
				configs.Config.Domain.Cube, taskInfo["task_id"], execId, fmt.Sprintf(drawerParamStr, "Low")),
		}
		body["high"] = PipeLineRs{
			Num: data.High,
			ReportUrl: fmt.Sprintf(constants.ProductScanDoneReportPath,
				configs.Config.Domain.Cube, taskInfo["task_id"], execId, fmt.Sprintf(drawerParamStr, "High")),
		}
		body["medium"] = PipeLineRs{
			Num: data.Medium,
			ReportUrl: fmt.Sprintf(constants.ProductScanDoneReportPath,
				configs.Config.Domain.Cube, taskInfo["task_id"], execId, fmt.Sprintf(drawerParamStr, "Medium")),
		}
		body["none"] = PipeLineRs{
			Num: data.Medium, // 注意：使用的是Medium的值
			ReportUrl: fmt.Sprintf(constants.ProductScanDoneReportPath,
				configs.Config.Domain.Cube, taskInfo["task_id"], execId, fmt.Sprintf(drawerParamStr, "None")),
		}
		body["critical"] = PipeLineRs{
			Num: data.Critical,
			ReportUrl: fmt.Sprintf(constants.ProductScanDoneReportPath,
				configs.Config.Domain.Cube, taskInfo["task_id"], execId, fmt.Sprintf(drawerParamStr, "Critical")),
		}
	} else {
		body["status"] = constants.IsFalse

		// 所有级别使用相同的报告URL
		reportUrl := fmt.Sprintf(constants.ProductScanRunningReportPath, configs.Config.Domain.Cube, taskInfo["task_id"], execId)
		body["low"] = PipeLineRs{ReportUrl: reportUrl}
		body["high"] = PipeLineRs{ReportUrl: reportUrl}
		body["medium"] = PipeLineRs{ReportUrl: reportUrl}
		body["none"] = PipeLineRs{ReportUrl: reportUrl}
		body["critical"] = PipeLineRs{ReportUrl: reportUrl}
	}

	// 发送回调请求
	logger.Logger.Infof("回调 deckjob ,请求url=%v,body=%+v", callBackURL, body)
	ship.ShipCallBack(callBackURL, body)
}
