package jacoco

import (
	"fmt"
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/message/payload"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver/request"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/uuid"
)

func RunJacocoCoverageTask(ctx *commoncontext.MantisContext, payload payload.JacocoCoveragePayload) error {
	// 创建任务唯一标识
	taskUUID := fmt.Sprintf("cube-mantis-jacoco-%s", uuid.NewUUIDSha256())

	// 构建运行请求参数
	req := request.RunRequest{
		UUID: taskUUID,
		Type: commonconstants.JacocoTaskType,
		Labels: map[string]string{
			commonconstants.PlatformLabel: "cube-mantis",
			commonconstants.ExecIdLabel:   payload.TaskNo,
			commonconstants.DetailIdLabel: "0",
			commonconstants.IsFirstLabel:  "0",
			commonconstants.TaskFromLabel: "",
		},
		Params: map[string]interface{}{
			// 基本参数
			"mode":        payload.Mode,
			"app-name":    payload.AppName,
			"ip":          payload.Ip,
			"port":        payload.Port,
			"packages":    payload.Packages,
			"exclude":     payload.Exclude,
			"module-name": payload.ModuleName,
			"task-no":     payload.TaskNo,

			// Git相关参数
			"git-url":   payload.Git.GitUrl,
			"git-user":  payload.Git.User,
			"git-token": payload.Git.Token,
			"branch":    payload.Git.Branch,

			// OSS相关参数
			"oss-path":        payload.Oss.Path,
			"oss-endpoint":    payload.Oss.Endpoint,
			"oss-bucket-name": payload.Oss.Bucket,
			"oss-access-id":   payload.Oss.AccessId,
			"oss-access-key":  payload.Oss.AccessKey,
			"oss-path-style":  strconv.FormatBool(payload.Oss.PathStyle),

			// 提交对比和回调参数
			"base-commit-id":    payload.BaseCommitId,
			"compare-commit-id": payload.CompareCommitId,
			"mantis-call-back":  payload.MantisCallBack,
			"old-exec-path":     payload.OldExecPath,
		},
	}

	// 获取驱动提供者并执行任务
	driverProvider, err := driver.NewDriverProvider()
	if err != nil {
		return err
	}

	if err = driverProvider.Run(ctx.Context, req); err != nil {
		return err
	}

	// 更新任务详情关联的TaskRun
	execHis := quality.JacocoCoverageExecHis{}
	execHis.Addons.Id = payload.HisId
	execHis.Addons.IsDeleted = commonconstants.DeleteNo

	// 查询历史记录并更新K8s任务ID
	gormx.SelectOneByConditionX(ctx, &execHis)
	execHis.K8sTaskId = taskUUID
	gormx.UpdateOneByConditionX(ctx, &execHis)

	return nil
}
