package sonar

import (
	"fmt"
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/message/payload"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver/request"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/uuid"
)

func RunSonarScannerTask(ctx *commoncontext.MantisContext, qualityScanTaskPayload payload.QualityScanTaskPayload) error {
	// 创建任务唯一标识
	taskUUID := fmt.Sprintf("cube-mantis-sonar-%s", uuid.NewUUIDSha256())
	execHisId := strconv.FormatInt(qualityScanTaskPayload.BizParam.ExecHisId, 10)

	// 构建运行请求参数
	req := request.RunRequest{
		UUID: taskUUID,
		Type: commonconstants.SonarTaskType,
		Labels: map[string]string{
			commonconstants.PlatformLabel: "cube-mantis",
			commonconstants.ExecIdLabel:   execHisId,
			commonconstants.DetailIdLabel: "0",
			commonconstants.IsFirstLabel:  "0",
			commonconstants.TaskFromLabel: "",
		},
		Params: map[string]interface{}{
			// 业务基础参数
			"call-back":       qualityScanTaskPayload.BizParam.CallBack,
			"type":            qualityScanTaskPayload.BizParam.Type,
			"mode":            qualityScanTaskPayload.BizParam.Mode,
			"language":        qualityScanTaskPayload.BizParam.Language,
			"java-version":    qualityScanTaskPayload.BizParam.JavaVersion,
			"module-path":     qualityScanTaskPayload.BizParam.ModulePath,
			"app-type":        qualityScanTaskPayload.BizParam.AppType,
			"sonar-exclusion": qualityScanTaskPayload.BizParam.ExclusionPath,
			"sonar-inclusion": qualityScanTaskPayload.BizParam.InclusionPath,

			// 代码参数
			"code-url":          qualityScanTaskPayload.CodeParam.CodeUrl,
			"git-user":          qualityScanTaskPayload.CodeParam.GitUser,
			"git-token":         qualityScanTaskPayload.CodeParam.GitToken,
			"branch":            qualityScanTaskPayload.CodeParam.Branch,
			"base-commit-id":    qualityScanTaskPayload.CodeParam.BaseCommitId,
			"compare-commit-id": qualityScanTaskPayload.CodeParam.CompareCommitId,

			// Sonar参数
			"sonar-url":      qualityScanTaskPayload.SonarParam.SonarUrl,
			"sonar-token":    qualityScanTaskPayload.SonarParam.SonarToken,
			"sonar-pdf":      strconv.FormatBool(qualityScanTaskPayload.SonarParam.SonarPdf),
			"sonar-profile":  qualityScanTaskPayload.SonarParam.SonarProfile,
			"sonar-callback": qualityScanTaskPayload.SonarParam.SonarCallback,

			// Maven参数
			"mvn-repo": qualityScanTaskPayload.MvnParam.MvnRepo,
			"mvn-user": qualityScanTaskPayload.MvnParam.MvnUser,
			"mvn-pw":   qualityScanTaskPayload.MvnParam.MvnPw,
		},
	}

	// 获取驱动提供者并执行任务
	driverProvider, err := driver.NewDriverProvider()
	if err != nil {
		return err
	}

	if err = driverProvider.Run(ctx.Context, req); err != nil {
		return err
	}

	// 更新任务详情关联的TaskRun
	execHis := quality.ScanTestExecHis{}
	execHis.Addons.Id = qualityScanTaskPayload.BizParam.ExecHisId
	execHis.Addons.IsDeleted = commonconstants.DeleteNo

	// 查询历史记录并更新K8s任务ID
	gormx.SelectOneByConditionX(ctx, &execHis)
	execHis.K8sTaskId = taskUUID
	gormx.UpdateOneByConditionX(ctx, &execHis)

	return nil
}
