package repo

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"path"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
	"github.com/duke-git/lancet/v2/netutil"
)

type DeckjobRepoService struct {
	httpClient *netutil.HttpClient
	instanceId string
}

// RepoInfos 根据实例id获取所有的仓库名称
func (s DeckjobRepoService) RepoInfos(ctx *context.MantisContext, repoName, format string) []dto.RepoDto {
	format = strings.ToLower(format)
	logger.Logger.Infof("enter RepoInfos, repoName=%s, format=%s", repoName, format)
	reqData := make(map[string]interface{})
	reqData["page_index"] = 1
	reqData["page_size"] = 100
	reqFilter := make([]map[string]interface{}, 2)
	reqFilter[0] = map[string]interface{}{"filter_key": "name", "filter_val": repoName}
	reqFilter[1] = map[string]interface{}{"filter_key": "format", "filter_val": format}
	reqData["extend_filter"] = reqFilter
	reqDataByte, _ := json.Marshal(reqData)
	companyId := utils.IDString(ctx.User.CompanyID)
	request := &netutil.HttpRequest{
		RawURL: fmt.Sprintf("%s/ship/api/v1/artifact/company/%s/node/%s/repository/list",
			configs.Config.Domain.Deckjob, companyId, s.instanceId),
		Method:  "POST",
		Headers: ctx.Header,
		Body:    reqDataByte,
	}
	var rs ShipProductDto
	err := respDeal("RepoInfos", s.httpClient, request, &rs)
	if err != nil {
		logger.Logger.Panicf("error in RepoInfos, err=%v", err)
	}
	resp := make([]dto.RepoDto, len(rs.Item))
	for i, rInfo := range rs.Item {
		resp[i] = dto.RepoDto{
			Id:       rInfo.Uud,
			Name:     rInfo.Name,
			RepoType: format,
		}
	}
	return resp
}

// LatestAllProducts 查询仓库下所有最新的制品
func (s DeckjobRepoService) LatestAllProducts(ctx *context.MantisContext, repoName string, repoId string, format string) []dto.ViewProductInfo {
	format = strings.ToLower(format)
	reqData := make(map[string]interface{})
	reqFilter := make([]map[string]interface{}, 3)
	reqFilter[0] = map[string]interface{}{"filter_key": "token", "filter_val": ""}
	reqFilter[1] = map[string]interface{}{"filter_key": "name", "filter_val": repoName}
	reqFilter[2] = map[string]interface{}{"filter_key": "repository_name", "filter_val": repoName}
	reqData["extend_filter"] = reqFilter
	reqDataByte, _ := json.Marshal(reqData)
	companyId := utils.IDString(ctx.User.CompanyID)
	request := &netutil.HttpRequest{
		RawURL: fmt.Sprintf("%s/ship/api/v1/artifact/nexus/format/%s/company/%s/node/%s/assets/latest",
			configs.Config.Domain.Deckjob, format, companyId, s.instanceId),
		Method:  "POST",
		Headers: ctx.Header,
		Body:    reqDataByte,
	}
	var rs ShipProductDto
	err := respDeal("RepoInfos", s.httpClient, request, &rs)
	if err != nil {
		logger.Logger.Panicf("error in RepoInfos, err=%v", err)
	}
	items := make([]ShipItemDto, len(rs.Item))
	items = append(items, rs.Item...)
	for {
		if rs.Token != "" {
			reqFilter[0] = map[string]interface{}{"filter_key": "token", "filter_val": rs.Token}
			err := respDeal("RepoInfos", s.httpClient, request, &rs)
			if err != nil {
				logger.Logger.Panicf("error in RepoInfos, err=%v", err)
			}
			items = append(items, rs.Item...)
		} else {
			break
		}
	}
	infos := make([]dto.ViewProductInfo, len(items))
	for idx, item := range items {
		infos[idx] = dto.ViewProductInfo{
			ProductInfo: &dto.MinProductDto{
				Name: item.Name,
			},
			VersionInfo: &dto.ProductVersionDto{
				Version: item.Version,
			},
			RepoInfo: &dto.RepoDto{Name: repoName},
			PathInfo: &dto.ProductPathDto{
				DownloadUrl: dealDownloadUrl(item.DownloadUrl),
				Path:        item.Path,
			},
		}
	}
	return infos
}

// Products 根据仓库名称和仓库id查询制品
func (s DeckjobRepoService) Products(ctx *context.MantisContext, repoName string, repoId string, productName string, format string) []dto.MinProductDto {
	format = strings.ToLower(format)
	reqData := make(map[string]interface{})
	reqData["page_size"] = 100
	reqFilter := make([]map[string]interface{}, 3)
	reqFilter[0] = map[string]interface{}{"filter_key": "token", "filter_val": ctx.Header.Get("pageToken")}
	reqFilter[1] = map[string]interface{}{"filter_key": "name", "filter_val": productName}
	reqFilter[2] = map[string]interface{}{"filter_key": "repository_name", "filter_val": repoName}
	reqData["extend_filter"] = reqFilter
	reqDataByte, _ := json.Marshal(reqData)
	companyId := utils.IDString(ctx.User.CompanyID)
	request := &netutil.HttpRequest{
		RawURL: fmt.Sprintf("%s/ship/api/v1/artifact/nexus/format/%s/company/%s/node/%s/packages",
			configs.Config.Domain.Deckjob, format, companyId, s.instanceId),
		Method:  "POST",
		Headers: ctx.Header,
		Body:    reqDataByte,
	}
	var rs ShipProductDto
	err := respDeal("RepoInfos", s.httpClient, request, &rs)
	if err != nil {
		logger.Logger.Panicf("error in RepoInfos, err=%v", err)
	}
	resp := make([]dto.MinProductDto, len(rs.Item))
	for i, pInfo := range rs.Item {
		if strings.Contains(pInfo.Name, "/") {
			pInfo.Name = pInfo.Name[strings.LastIndex(pInfo.Name, "/")+1:]
		}
		resp[i] = dto.MinProductDto{
			Id:        pInfo.Id,
			Name:      pInfo.Name,
			PageToken: rs.Token,
		}
	}
	return resp
}

// Versions 查询版本
func (s DeckjobRepoService) Versions(ctx *context.MantisContext, repoName string, versionName string, productId string, productName string, format string) []dto.ProductVersionDto {
	format = strings.ToLower(format)
	reqData := make(map[string]interface{})
	reqData["page_size"] = 999
	reqData["page_index"] = 1
	reqFilter := make([]map[string]interface{}, 3)
	reqFilter[0] = map[string]interface{}{"filter_key": "id", "filter_val": productId}
	reqFilter[1] = map[string]interface{}{"filter_key": "type", "filter_val": format}
	reqFilter[2] = map[string]interface{}{"filter_key": "version", "filter_val": versionName}
	reqData["extend_filter"] = reqFilter
	reqDataByte, _ := json.Marshal(reqData)
	companyId := utils.IDString(ctx.User.CompanyID)
	request := &netutil.HttpRequest{
		RawURL: fmt.Sprintf("%s/ship/api/v1/artifact/nexus/format/%s/company/%s/node/%s/versions",
			configs.Config.Domain.Deckjob, format, companyId, s.instanceId),
		Method:  "POST",
		Headers: ctx.Header,
		Body:    reqDataByte,
	}
	var rs ShipProductDto
	err := respDeal("RepoInfos", s.httpClient, request, &rs)
	if err != nil {
		logger.Logger.Panicf("error in RepoInfos, err=%v", err)
	}
	resp := make([]dto.ProductVersionDto, len(rs.Item))
	for i, vInfo := range rs.Item {
		resp[i] = dto.ProductVersionDto{
			Version: vInfo.Version,
			Id:      vInfo.Id,
		}
	}
	return resp
}

func (s DeckjobRepoService) Path(ctx *context.MantisContext, versionId, format string) []dto.ProductPathDto {
	format = strings.ToLower(format)
	reqData := make(map[string]interface{})
	reqData["page_size"] = 999
	reqData["page_index"] = 1
	reqFilter := make([]map[string]interface{}, 1)
	reqFilter[0] = map[string]interface{}{"filter_key": "id", "filter_val": versionId}
	reqData["extend_filter"] = reqFilter
	reqDataByte, _ := json.Marshal(reqData)
	companyId := utils.IDString(ctx.User.CompanyID)
	request := &netutil.HttpRequest{
		RawURL: fmt.Sprintf("%s/ship/api/v1/artifact/nexus/format/%s/company/%s/node/%s/assets",
			configs.Config.Domain.Deckjob, format, companyId, s.instanceId),
		Method:  "POST",
		Headers: ctx.Header,
		Body:    reqDataByte,
	}
	var rs ShipProductFileDto
	err := respDeal("RepoInfos", s.httpClient, request, &rs)
	if err != nil {
		logger.Logger.Panicf("error in RepoInfos, err=%v", err)
	}
	resp := make([]dto.ProductPathDto, len(rs.Item.AssetList))
	for i, pInfo := range rs.Item.AssetList {
		resp[i] = dto.ProductPathDto{
			Path:        pInfo.Path,
			DownloadUrl: dealDownloadUrl(pInfo.DownloadUrl),
		}
	}
	return resp
}

// GetAllRepoInstance 获取实例列表
func GetAllRepoInstance(ctx *context.MantisContext, companyId string) (*[]configs.RepoInstance, error) {
	// 获取所有公司的实例列表
	request := &netutil.HttpRequest{
		RawURL:  fmt.Sprintf("%s/ship/api/v1/artifact/company/%s/nodes", configs.Config.Domain.Deckjob, companyId),
		Method:  "GET",
		Headers: ctx.Header,
	}
	httpClient := netutil.NewHttpClient()
	var rs []configs.RepoInstance
	err := respDeal("GetRepoInstanceInfo", httpClient, request, &rs)
	return &rs, err
}

// GetRepoInstanceInfo 获取实例详情
func GetRepoInstanceInfo(ctx *context.MantisContext, companyId, id string) (*configs.RepoInstance, error) {
	request := &netutil.HttpRequest{
		RawURL:  fmt.Sprintf("%s/ship/api/v1/artifact/company/%s/node/%s/info", configs.Config.Domain.Deckjob, companyId, id),
		Method:  "GET",
		Headers: ctx.Header,
	}
	httpClient := netutil.NewHttpClient()
	var rs configs.RepoInstance
	err := respDeal("GetRepoInstanceInfo", httpClient, request, &rs)
	return &rs, err
}

func respDeal(methodName string, client *netutil.HttpClient, request *netutil.HttpRequest, target any) error {
	resp, err := client.SendRequest(request)
	if err != nil {
		logger.Logger.Errorf("error in %s, err=%v", methodName, err)
		return err
	}
	if resp.StatusCode != 200 {
		bodyBytes, _ := io.ReadAll(resp.Body)
		logger.Logger.Errorf("error in %s ,deckjob resp code is %v,body is %v", methodName, resp.StatusCode, string(bodyBytes))
		return errors.New(fmt.Sprintf("deckjob resp err,code is %d", resp.StatusCode))
	}
	err = client.DecodeResponse(resp, target)
	if err != nil {
		logger.Logger.Errorf("error in decode response, err=%v", err)
		return err
	}
	return nil
}

type ShipProductDto struct {
	Token string        `json:"token"`
	Item  []ShipItemDto `json:"item"`
}

type ShipProductFileDto struct {
	Token string                 `json:"token"`
	Item  ShipProductFileItemDto `json:"item"`
}

type ShipProductFileItemDto struct {
	Path      string        `json:"path"`
	AssetList []ShipItemDto `json:"asset_list"`
}

type ShipItemDto struct {
	Id          string `json:"id"`
	Uud         string `json:"uuid"`
	Name        string `json:"name"`
	Version     string `json:"version"`
	DownloadUrl string `json:"download_url"`
	Path        string `json:"path"`
}

func dealDownloadUrl(src string) string {
	if src == "" {
		return src
	}
	if !strings.Contains(src, "/ship/openapi/") {
		return src
	}
	n := strings.SplitAfterN(src, "ship", 2)
	return configs.Config.Domain.Deckjob + "/" + path.Join("ship", n[1])
}
