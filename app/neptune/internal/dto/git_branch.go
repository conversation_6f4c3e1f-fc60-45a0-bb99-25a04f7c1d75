package dto

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

// GitBranchDTO Git分支数据传输对象
type GitBranchDTO struct {
	Name     string `json:"name"`
	Commit   Commit `json:"commit"`
	CommitId string `json:"commitId"`
}

// Commit 提交信息数据传输对象
type Commit struct {
	Id          string      `json:"id"`
	CreatedAt   *times.Time `json:"createdAt"`
	Title       string      `json:"title"`
	AuthorName  string      `json:"authorName"`
	AuthorEmail string      `json:"authorEmail"`
}
