package dto

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
)

// ScanTestTaskConfigDTO 扫描测试任务配置数据传输对象
type ScanTestTaskConfigDTO struct {
	BaseInfo         quality.ScanTestTask `json:"baseInfo"`
	QualityGatesInfo *ScanQualityGatesDTO `json:"qualityGatesInfo"`
	TaskFilterInfo   *ScanTaskFilterDTO   `json:"taskFilterInfo"`
	TaskType         string               `json:"taskType"`
}
