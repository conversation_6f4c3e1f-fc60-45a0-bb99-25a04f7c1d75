package dto

// ScanProgrammeDTO 扫描方案数据传输对象
type ScanProgrammeDTO struct {
	Id                      int64                            `json:"id"`
	Name                    string                           `json:"name"`
	Describe                string                           `json:"describe"`
	IsDefault               int32                            `json:"isDefault"`
	LanguageList            []string                         `json:"languageList"`
	LanguageProfileTemplate []ScanLanguageProfileTemplateDTO `json:"languageProfile"`
}
