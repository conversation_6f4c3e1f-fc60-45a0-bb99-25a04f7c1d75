package dto

// SonarRuleDTO Sonar规则数据传输对象
type SonarRuleDTO struct {
	Key         string `json:"key"`
	Repo        string `json:"repo"`
	Name        string `json:"name"`
	CreatedAt   string `json:"createdAt"`
	UpdatedAt   string `json:"updatedAt"`
	HtmlDesc    string `json:"htmlDesc"`
	MdDesc      string `json:"mdDesc"`
	Severity    string `json:"severity"`
	Status      string `json:"status"`
	InternalKey string `json:"internalKey"`
	IsTemplate  bool   `json:"isTemplate"`
	Lang        string `json:"lang"`
	LangName    string `json:"langName"`
	Scope       string `json:"scope"`
	IsExternal  bool   `json:"isExternal"`
	Type        string `json:"type"`
	IsActive    bool   `json:"isActive"`
}
