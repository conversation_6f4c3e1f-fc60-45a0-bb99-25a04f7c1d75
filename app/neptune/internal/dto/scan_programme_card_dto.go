package dto

// ScanProgrammeCardDTO 扫描方案卡片数据传输对象
type ScanProgrammeCardDTO struct {
	Id          int64                         `json:"id"`
	Name        string                        `json:"name"`
	ProfileName string                        `json:"profileName"`
	Describe    string                        `json:"describe"`
	Profiles    []ScanProgrammeCardProfileDTO `json:"profiles"`
	IsDefault   int32                         `json:"isDefault"`
}

// ScanProgrammeCardProfileDTO 扫描方案卡片配置数据传输对象
type ScanProgrammeCardProfileDTO struct {
	Templates   []ScanProgrammeCardTemplateDTO `json:"templates"`
	ProfileName string                         `json:"profileName"`
	ProfileKey  string                         `json:"profileKey"`
	IsActive    bool                           `json:"isActive"`
	Languages   []string                       `json:"languages"`
	ProfileId   int64                          `json:"profileId"`
}

// ScanProgrammeCardTemplateDTO 扫描方案卡片模板数据传输对象
type ScanProgrammeCardTemplateDTO struct {
	TemplateId   int64  `json:"templateId"`
	TemplateName string `json:"templateName"`
}
