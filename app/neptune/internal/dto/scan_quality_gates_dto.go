package dto

// ScanQualityGatesDTO 扫描质量门禁数据传输对象
type ScanQualityGatesDTO struct {
	Id                 int64 `json:"id"`
	TaskId             int64 `json:"taskId"`
	IsActive           bool  `json:"isActive"`
	BlockerViolations  int32 `json:"blockerViolations"`
	CriticalViolations int32 `json:"criticalViolations"`
	MajorViolations    int32 `json:"majorViolations"`
	MinorViolations    int32 `json:"minorViolations"`
}
