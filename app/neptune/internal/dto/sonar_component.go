package dto

// SearchComponentReqDTO 组件搜索请求数据传输对象
type SearchComponentReqDTO struct {
	OrderBy    string `schema:"orderBy"` // 排序方式，asc or desc
	MetricSort string `schema:"metricSort"`
	Search     string `schema:"search"`
	TaskId     int64  `schema:"taskId"` // sonar 项目 key
}

// SonarComponent Sonar组件数据传输对象
type SonarComponent struct {
	Key      string `json:"key"`
	Name     string `json:"name"`
	Path     string `json:"path"`
	Language string `json:"language"`
	Measures []struct {
		Metric string `json:"metric"`
		Value  string `json:"value"`
	} `json:"measures"`
}

// SonarComponentRes Sonar组件响应数据传输对象
type SonarComponentRes struct {
	Key                 string `json:"key"`
	Name                string `json:"name"`
	Path                string `json:"path"`
	Language            string `json:"language"`
	UncoveredLines      string `json:"uncoveredLines"`
	Coverage            string `json:"coverage"`
	UncoveredConditions string `json:"uncoveredConditions"`
	Url                 string `json:"url"`
}
