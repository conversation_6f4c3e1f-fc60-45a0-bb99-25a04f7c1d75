package dto

import commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"

// ScanTestTaskSearchDTO 扫描测试任务搜索条件数据传输对象
type ScanTestTaskSearchDTO struct {
	TaskId    int64    `schema:"taskId"`
	Search    string   `schema:"search"`
	AppIds    []string `schema:"appIds"`
	JobType   string   `schema:"jobType"`
	From      string   `schema:"from"`
	HubUrl    string   `schema:"hubUrl"`
	Branch    string   `schema:"branch"`
	CompanyId string
	commondto.AddonTimeDTO
}
