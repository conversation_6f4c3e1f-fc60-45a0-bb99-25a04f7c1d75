package dto

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

// ScanTaskFilterDTO 扫描任务过滤配置数据传输对象
type ScanTaskFilterDTO struct {
	Id           int64        `json:"id"`
	RelationId   string       `json:"relationId"`
	Type         string       `json:"type"`
	Configs      any          `json:"configs"` // 如果不是app则为[]ScanFilter，如果是app则为map[string][]ScanFilter
	ConfigSlices []ScanFilter `json:"-"`       // 为[]ScanFilter，供内部调用
}

// ScanFilter 扫描过滤条件
type ScanFilter struct {
	Type string `json:"type"`
	Path string `json:"path"`
}

// AppScanTaskFilterDTO 应用扫描任务过滤配置数据传输对象
type AppScanTaskFilterDTO struct {
	Id          int64                   `json:"id"`
	AppId       string                  `json:"appId"`
	AppName     string                  `json:"appName"`
	CompanyName string                  `json:"companyName"`
	CreatorName string                  `json:"creatorName"`
	GmtCreated  *times.Time             `json:"gmtCreated"`
	GmtModified *times.Time             `json:"gmtModified"`
	Description string                  `json:"description"`
	Configs     map[string][]ScanFilter `json:"configs"`
}
