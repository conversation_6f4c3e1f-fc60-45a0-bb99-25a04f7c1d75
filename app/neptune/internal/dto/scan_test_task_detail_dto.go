package dto

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

// ScanTestTaskDetailDTO 扫描测试任务详情数据传输对象
type ScanTestTaskDetailDTO struct {
	OriTaskId   int64  `json:"oriTaskId" gorm:"column:ori_task_id;type:int8"`
	TaskName    string `json:"name" gorm:"column:task_name;type:varchar;size:765"`
	AppId       string `json:"appId" gorm:"column:app_id;type:text"`
	ProgrammeId int64  `json:"-" gorm:"column:programme_id;type:int8"`
	Programme   struct {
		Id   int64  `json:"id"`
		Name string `json:"name"`
	} `json:"programme"`
	Branch               string                       `json:"branch" gorm:"column:branch;type:varchar;size:256"`
	Mode                 string                       `json:"mode" gorm:"column:task_mode"`
	PdfGen               bool                         `json:"pdfGen" gorm:"column:pdf_gen;type:bool"`
	Operator             string                       `json:"operator" gorm:"column:operator;type:varchar;size:256"`
	ShipPid              string                       `json:"shipPid" gorm:"column:ship_pid;type:varchar;size:100"` // ship发布单id
	Language             string                       `json:"language" gorm:"column:language;type:varchar;size:32"`
	ExecType             string                       `json:"execType" gorm:"column:exec_type;type:varchar;size:10"` // 前端：front/后端：back
	CodeUrl              string                       `json:"codeUrl" gorm:"column:code_url;type:varchar;sie:256"`   // 项目git地址
	JobType              string                       `json:"jobType" gorm:"column:job_type;type:varchar;size:20"`   // 扫描：scan/单测：test/覆盖率：coverage
	LastScanTestExecHis  int64                        `json:"lastScanTestExecHis" gorm:"column:last_scan_test_exec_his;type:int8"`
	LastScanTestExecTime *times.Time                  `json:"lastScanTestExecTime" gorm:"column:last_scan_test_exec_time;type:timestamp"`
	From                 string                       `json:"from" gorm:"column:from;type:varchar;size:32"` // 来源 ship/web
	QualityGatesColumn   string                       `json:"qualityGatesColumn" gorm:"-"`
	AppName              string                       `json:"appName" gorm:"column:app_name"`
	CommitTimeFrame      string                       `json:"commitTimeFrame" gorm:"column:commit_time_frame"`
	CommitBaseId         string                       `json:"commitBaseId" gorm:"column:t_commit_base_id"`
	CommitCompareId      string                       `json:"commitCompareId" gorm:"column:t_commit_compare_id"`
	TaskCreator          string                       `json:"-" gorm:"column:task_creator"`
	CreatorName          string                       `json:"creatorName" gorm:"-"`
	Status               int8                         `json:"status" gorm:"column:status;type:int4"`
	StatusMsg            string                       `json:"statusMsg" gorm:"column:status_msg;type:varchar;size:128"`
	TaskGmtCreated       *times.Time                  `json:"taskGmtCreated" gorm:"column:task_gmt_created"`
	Lines                int32                        `json:"-" gorm:"column:lines;type:int4"`
	BlockerViolations    int32                        `json:"-" gorm:"column:blocker_violations;type:int4"`
	CriticalViolations   int32                        `json:"-" gorm:"column:critical_violations;type:int4"`
	MajorViolations      int32                        `json:"-" gorm:"column:major_violations;type:int4"`
	MinorViolations      int32                        `json:"-" gorm:"column:minor_violations;type:int4"`
	LineCoverage         float64                      `json:"-" gorm:"column:line_coverage;type:float4"`
	TestSuccessDensity   float64                      `json:"-" gorm:"column:test_success_density;type:float4"`
	Stage                string                       `json:"stage" gorm:"column:stage;type:varchar;size:128"`
	LinesToCover         int32                        `json:"linesToCover" gorm:"column:lines_to_cover;type:int4"`
	QualityGates         int32                        `json:"qualityGates" gorm:"column:quality_gates;type:int2"`
	SonarProjectKey      string                       `json:"sonarProjectKey" gorm:"column:sonar_project_key;type:varchar;size:100"`
	ScanResultInfo       map[string]ScanTestResultDto `json:"scanResultInfo" gorm:"-"`
}

// ScanTestResultDto 扫描测试结果数据传输对象
type ScanTestResultDto struct {
	Num any `json:"num"`
	Tag int `json:"tag"`
}
