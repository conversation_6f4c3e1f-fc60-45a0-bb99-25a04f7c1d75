package dto

// ScanTestInfo 扫描测试信息数据传输对象
type ScanTestInfo struct {
	Branch          string `json:"branch"`
	Source          string `json:"source"`
	CompareCommitId string `json:"compareCommitId"`
	BasicCommitId   string `json:"basicCommitId"`
	JdkVersion      string `json:"jdkVersion"`
	BuildCommand    string `json:"buildCommand"`
	Language        string `json:"language"`
	Mode            string `json:"mode"`
	DirPath         string `json:"dirPath"`
	Pid             int64  `json:"pid"`
	CallBackAddr    string `json:"callBackAddr"`
	AppId           int64  `json:"appId"`
	Stage           string `json:"stage"`
}
