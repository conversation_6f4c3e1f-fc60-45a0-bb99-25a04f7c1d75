package dto

// RepoDto 仓库数据传输对象
type RepoDto struct {
	Name     string `json:"name"`     // 仓库名称
	Id       string `json:"id"`       // 仓库id
	RepoType string `json:"repoType"` // 仓库类型
}

// MinProductDto 最小制品数据传输对象
type MinProductDto struct {
	Name string `json:"name"` // 制品名称
	Id   string `json:"id"`   // 制品id
	// V2 新增制品path 和 下载url
	PageToken string `json:"pageToken"`
}

// ProductVersionDto 制品版本数据传输对象
type ProductVersionDto struct {
	Version string `json:"version"` // 版本名称
	Id      string `json:"id"`      // 版本名称
}

// ViewProductInfo 查看制品信息数据传输对象
type ViewProductInfo struct {
	RepoInfo    *RepoDto           `json:"repoInfo"`
	ProductInfo *MinProductDto     `json:"productInfo"`
	VersionInfo *ProductVersionDto `json:"versionInfo"`
	PathInfo    *ProductPathDto    `json:"pathInfo"`
}

// ProductPathDto 制品路径数据传输对象
type ProductPathDto struct {
	Path        string `json:"path"`        // 制品路径
	DownloadUrl string `json:"downloadUrl"` // 制品路径
}
