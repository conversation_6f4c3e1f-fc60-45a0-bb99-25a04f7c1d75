package constants

// 布尔标志常量
const (
	NotDefault = 0
	IsDefault  = 1
	IsFalse    = 0
	IsTrue     = 1
)

// UserDevops 业务用户常量
const (
	UserDevops = "DevOps" // 流水线用户
)

// 报告路径格式常量
const (
	ProductScanDoneReportPath    = "%v/magic/productScan/task/%v/%v?drawerId=report&drawerParams=%v" // 制品扫描完成报告地址
	ProductScanRunningReportPath = "%v/magic/productScan/task/%v/%v"                                 // 制品扫描进行中报告地址

	QualityScanReportPath     = "%s/magic/codeScan/task/detail?mode=TaskOverview&taskId=%d&name=%s&appName=%s&appId=%d" // 代码扫描报告地址
	QualityUnitTestReportPath = "%s/magic/codeScan/unit/detail?mode=ScanHistory&taskId=%d&appName=%s&appId=%d"          // 单元测试报告地址
)

// 标准类型常量
const (
	StandardIncrement = "increment" // 增量
	StandardTotal     = "total"     // 全量
)

// 来源类型常量
const (
	SourcePipeline = "pipeline" // 触发来源 - 流水线
	SourceWeb      = "web"      // 触发来源 - Web
)

// 任务状态常量
const (
	SuccessStatus  = "SUCCESS"   // 成功状态
	FailureStatus  = "FAILURE"   // 失败状态
	AbortedStatus  = "ABORTED"   // 中止状态
	FinalizedParse = "FINALIZED" // 最终解析
)

// 任务类型常量
const (
	ScanJobType     = "scan"     // 扫描任务
	UnitTestJobType = "unit"     // 单元测试任务
	CoverageJobType = "coverage" // 覆盖率任务
)

// URL相关常量
const (
	GitUrlSuffix = ".git" // git url 后缀
	Slash        = "/"    // 斜杠
	SlashCode    = "%2F"  // URL编码的斜杠
)

// 规则状态常量
const (
	RuleActive   = true  // 规则激活
	RuleDeActive = false // 规则停用
)

// 执行类型常量
const (
	ExecTypeFront = "FRONTEND" // 前端执行类型
	ExecTypeBack  = "BACKEND"  // 后端执行类型
)

// 过滤配置类型常量
const (
	FilterConfigTypeApp       = "app"       // 应用过滤
	FilterConfigTypeTask      = "task"      // 任务过滤
	FilterConfigTypeProgramme = "programme" // 项目过滤
	FilterConfigExclusion     = "exclusion" // 排除过滤
	FilterConfigInclusion     = "inclusion" // 包含过滤
)
