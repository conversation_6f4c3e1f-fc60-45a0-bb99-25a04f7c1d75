package utils

import (
	"encoding/json"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

// CalGateQualityGate 计算质量门禁结果
func CalGateQualityGate(gateSwitch, status int8, scanResultStr string, qualityGate artifact.QualityGateDto) (*artifact.ViewScanResultInfo, int8) {
	// 检查扫描结果是否为空
	if scanResultStr == "" || scanResultStr == "{}" {
		return nil, status
	}

	// 保持原状态作为默认返回值
	resultStatus := status
	viewResult := artifact.ViewScanResultInfo{}

	// 解析扫描结果
	var scanResult artifact.ScanResultInfo
	err := json.Unmarshal([]byte(scanResultStr), &scanResult)
	if err != nil {
		logger.Logger.Panicf("格式化扫描结果失败: %v", err)
	}

	// 设置漏洞总数(默认不标记)
	viewResult.VulnerabilityCount = artifact.ViewNumTag{Num: scanResult.VulnerabilityCount, Tag: constants.IsFalse}

	// 如果门禁关闭，所有级别漏洞均不标记
	if gateSwitch == constants.IsFalse {
		viewResult.Critical = artifact.ViewNumTag{Num: scanResult.Critical, Tag: constants.IsFalse}
		viewResult.High = artifact.ViewNumTag{Num: scanResult.High, Tag: constants.IsFalse}
		viewResult.Medium = artifact.ViewNumTag{Num: scanResult.Medium, Tag: constants.IsFalse}
		viewResult.Low = artifact.ViewNumTag{Num: scanResult.Low, Tag: constants.IsFalse}
		viewResult.None = artifact.ViewNumTag{Num: scanResult.None, Tag: constants.IsFalse}
	} else if gateSwitch == constants.IsTrue {
		// 门禁开启，根据配置标记各级别漏洞
		viewResult.Critical = artifact.ViewNumTag{Num: scanResult.Critical, Tag: compareThreshold(scanResult.Critical, qualityGate.Critical)}
		viewResult.High = artifact.ViewNumTag{Num: scanResult.High, Tag: compareThreshold(scanResult.High, qualityGate.High)}
		viewResult.Medium = artifact.ViewNumTag{Num: scanResult.Medium, Tag: compareThreshold(scanResult.Medium, qualityGate.Medium)}
		viewResult.Low = artifact.ViewNumTag{Num: scanResult.Low, Tag: compareThreshold(scanResult.Low, qualityGate.Low)}
		viewResult.None = artifact.ViewNumTag{Num: scanResult.None, Tag: compareThreshold(scanResult.None, qualityGate.None)}

		// 判断门禁是否通过
		if isAnyThresholdExceeded(viewResult) {
			resultStatus = task.ProductStatusGateNotPass
		} else {
			resultStatus = task.ProductStatusGatePass
		}
	}

	return &viewResult, resultStatus
}

// compareThreshold 比较实际值与阈值
// 如果实际值超过阈值，返回constants.IsTrue，否则返回constants.IsFalse
func compareThreshold(actual, threshold int64) int {
	if actual > threshold {
		return constants.IsTrue
	}
	return constants.IsFalse
}

// isAnyThresholdExceeded 检查是否有任何级别的漏洞超过阈值
func isAnyThresholdExceeded(result artifact.ViewScanResultInfo) bool {
	return result.Critical.Tag == constants.IsTrue ||
		result.High.Tag == constants.IsTrue ||
		result.Medium.Tag == constants.IsTrue ||
		result.Low.Tag == constants.IsTrue ||
		result.None.Tag == constants.IsTrue
}
