package utils

import (
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
)

// GetGitCodePath 转换Git代码路径为特定格式
// 将完整Git URL转换为适合系统内部使用的格式
func GetGitCodePath(domain string, codeUrl string) string {
	// 去除.git后缀
	if strings.HasSuffix(codeUrl, constants.GitUrlSuffix) {
		codeUrl = codeUrl[:len(codeUrl)-len(constants.GitUrlSuffix)]
	}

	// 移除域名前缀并处理路径格式
	pathWithoutDomain := strings.TrimPrefix(codeUrl, domain)
	if strings.HasPrefix(pathWithoutDomain, "/") {
		pathWithoutDomain = pathWithoutDomain[1:]
	}

	// 将斜杠替换为URL编码格式
	encodedPath := strings.ReplaceAll(pathWithoutDomain, constants.Slash, constants.SlashCode)
	return encodedPath
}
