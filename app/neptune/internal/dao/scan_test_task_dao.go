package dao

import (
	"fmt"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
)

// ScanTestTaskDao 扫描测试任务数据访问对象
type ScanTestTaskDao struct{}

// SelectTaskByLastExecHisId 根据最后一次执行历史ID查询任务
func (ScanTestTaskDao) SelectTaskByLastExecHisId(ctx *commoncontext.MantisContext, execHisId int64) quality.ScanTestTask {
	sql := `SELECT * 
            FROM neptune_scan_test_task 
            WHERE last_scan_test_exec_his = ? 
            AND is_deleted = 'N'`
	task := quality.ScanTestTask{}
	gormx.RawX(ctx, sql, &task, execHisId)
	return task
}

// SelectScanHistoryListByTaskIdAndLatestHisId 根据任务ID和最近历史ID查询扫描历史列表
func (ScanTestTaskDao) SelectScanHistoryListByTaskIdAndLatestHisId(ctx *commoncontext.MantisContext, taskId int64, hisId int64) []quality.ScanTestExecHis {
	sql := `SELECT * FROM (
                SELECT *, 
                       ROW_NUMBER() OVER (
                           PARTITION BY gmt_date
                           ORDER BY gmt_modified DESC
                       ) AS rn 
                FROM (
                    SELECT lines, 
                           comment_lines, 
                           ncloc, 
                           date_trunc('day', gmt_modified) AS gmt_date, 
                           gmt_modified 
                    FROM neptune_scan_test_exec_his 
                    WHERE task_id = ? 
                    AND id < ?
                ) AS his_t 
                LIMIT 20
            ) AS rn_t 
            WHERE rn = 1`

	histories := make([]quality.ScanTestExecHis, 0)
	gormx.RawX(ctx, sql, &histories, taskId, hisId)
	return histories
}

// SelectScanTestTaskList 查询扫描测试任务列表
func (ScanTestTaskDao) SelectScanTestTaskList(ctx *commoncontext.MantisContext, search dto.ScanTestTaskSearchDTO, request gormx.PageRequest) (*gormx.PageResult, []dto.ScanTestTaskDetailDTO) {
	baseSql := `SELECT id AS ori_task_id, 
	                  task_name, 
	                  app_id, 
	                  app_name, 
	                  mode AS task_mode, 
	                  programme_id, 
	                  branch, 
	                  pdf_gen, 
	                  operator, 
	                  ship_pid, 
	                  language, 
	                  exec_type, 
	                  code_url, 
	                  job_type, 
	                  "from", 
	                  last_scan_test_exec_his, 
	                  last_scan_test_exec_time, 
	                  creator AS task_creator, 
	                  gmt_created AS task_gmt_created, 
	                  commit_time_frame, 
	                  commit_base_id AS t_commit_base_id, 
	                  commit_compare_id AS t_commit_compare_id
	           FROM neptune_scan_test_task 
	           WHERE is_deleted = ?`

	params := []interface{}{"N"}

	if search.From != "" {
		baseSql += ` AND "from" = ?`
		params = append(params, search.From)
	}

	if search.JobType != "" {
		baseSql += ` AND job_type = ?`
		params = append(params, search.JobType)
	}

	if search.LastScanTestExecTime != "" {
		baseSql += ` AND last_scan_test_exec_time >= ? AND last_scan_test_exec_time <= ?`
		params = append(params, search.GetExecuteTimeStart(), search.GetExecuteTimeEnd())
	}

	if search.AppIds != nil {
		baseSql += ` AND app_id IN ?`
		params = append(params, search.AppIds)
	}

	if search.Branch != "" {
		baseSql += ` AND branch IN ?`
		params = append(params, strings.Split(search.Branch, ","))
	}

	if search.Search != "" {
		baseSql += ` AND ((task_name ILIKE ?) OR (app_name ILIKE ?))`
		searchPattern := "%" + search.Search + "%"
		params = append(params, searchPattern, searchPattern)
	}

	if search.CompanyId != "" {
		baseSql += ` AND company_id = ?`
		params = append(params, search.CompanyId)
	}

	fullSql := `SELECT * 
	            FROM (%s) t1 
	            LEFT JOIN neptune_scan_test_exec_his t2 
	            ON t1.last_scan_test_exec_his = t2.id 
	            ORDER BY ori_task_id DESC`

	tasks := make([]dto.ScanTestTaskDetailDTO, 0)
	pageResult, err := gormx.PageSelectByRaw(ctx, fmt.Sprintf(fullSql, baseSql), &tasks, request, params...)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "查询数据库出错"))
	}

	return pageResult, tasks
}

// PageSelectRunningTaskByProgrammeId 分页查询指定方案下运行中的任务
func (ScanTestTaskDao) PageSelectRunningTaskByProgrammeId(ctx *commoncontext.MantisContext,
	programmeId int64, pageReq gormx.PageRequest,
) (*gormx.PageResult, []quality.ScanTestTask) {
	sql := `SELECT "task".*, "his".status 
	        FROM "neptune_scan_test_task" AS "task"
	        LEFT JOIN "neptune_scan_test_exec_his" AS "his" 
	        ON "task"."last_scan_test_exec_his" = "his"."id" 
	        WHERE "task"."programme_id" = ? 
	        AND "task"."is_deleted" = 'N' 
	        AND "his"."status" = 1`

	tasks := make([]quality.ScanTestTask, 0)
	result, err := gormx.PageSelectByRaw(ctx, sql, &tasks, pageReq, programmeId)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "查询数据库出错"))
	}

	return result, tasks
}
