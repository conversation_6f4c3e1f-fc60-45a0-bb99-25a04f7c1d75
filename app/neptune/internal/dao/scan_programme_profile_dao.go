package dao

import (
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

// ScanProgrammeProfileDao 扫描方案配置文件数据访问对象
type ScanProgrammeProfileDao struct{}

// UpdateNameAndKeyByIds 根据ID更新配置文件的自定义名称和键值
func (ScanProgrammeProfileDao) UpdateNameAndKeyByIds(ctx *commoncontext.MantisContext, cusName string, cusKey string, id int64) {
	sql := `UPDATE neptune_scan_programme_profile 
            SET cus_name = ?, cus_key = ? 
            WHERE id = ?`
	gormx.ExecX(ctx, sql, cusName, cusKey, id)
}

// SetProfileActive 设置配置文件的激活状态
func (ScanProgrammeProfileDao) SetProfileActive(ctx *commoncontext.MantisContext, profileId int64, active bool) int32 {
	sql := `UPDATE neptune_scan_programme_profile 
            SET is_active = ?, gmt_modified = NOW() 
            WHERE id = ?`
	return int32(gormx.ExecX(ctx, sql, active, profileId))
}
