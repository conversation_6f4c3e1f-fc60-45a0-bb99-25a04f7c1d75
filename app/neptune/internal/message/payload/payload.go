package payload

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
)

// ArtifactScanTaskPayload 制品扫描任务载体
type ArtifactScanTaskPayload struct {
	InstanceInfo    configs.RepoInstance `json:"InstanceInfo"`    // 实例信息
	DetailId        int64                `json:"detailId"`        // 任务ID
	ExecId          int64                `json:"execId"`          // 执行记录ID
	IsFirst         int8                 `json:"isFirst"`         // 是否为本批次任务的第一条 0-否 1-是
	ProductType     string               `json:"productType"`     // 制品类型 Generic|Docker
	RepoName        string               `json:"repoName"`        // 仓库名称 或 projectName(harbor)
	ProductName     string               `json:"productName"`     // 制品名称 或 repoName(harbor)
	ProductVersion  string               `json:"productVersion"`  // 制品版本 或 tagName(harbor)
	UploadResultUrl string               `json:"uploadResultUrl"` // 上传结果文件的URL
	WhiteCVE        []string             `json:"whiteCVE"`        // 漏洞白名单
	TaskFrom        string               `json:"taskFrom"`        // 任务来源 web/pipeline
	DownloadUrl     string               `json:"downloadUrl"`     // 制品下载地址
}

// WithWhiteCVE 设置漏洞白名单
func (p *ArtifactScanTaskPayload) WithWhiteCVE(whiteCVE []string) *ArtifactScanTaskPayload {
	p.WhiteCVE = whiteCVE
	return p
}

// WithTaskFrom 设置任务来源
func (p *ArtifactScanTaskPayload) WithTaskFrom(taskFrom string) *ArtifactScanTaskPayload {
	p.TaskFrom = taskFrom
	return p
}

// Kind 返回消息类型
func (ArtifactScanTaskPayload) Kind() string {
	return "magic-neptune-products"
}

// NewArtifactScanTaskPayload 创建制品扫描任务载体
func NewArtifactScanTaskPayload(instanceInfo configs.RepoInstance, detailId, execId int64, productType, repoName, productName, productVersion, uploadResultUrl, downloadUrl string) *ArtifactScanTaskPayload {
	return &ArtifactScanTaskPayload{
		InstanceInfo:    instanceInfo,
		DetailId:        detailId,
		ExecId:          execId,
		ProductType:     productType,
		RepoName:        repoName,
		ProductName:     productName,
		ProductVersion:  productVersion,
		UploadResultUrl: uploadResultUrl,
		DownloadUrl:     downloadUrl,
	}
}

// QualityScanTaskPayload 质量扫描(sonar-scanner)任务载体
type QualityScanTaskPayload struct {
	BizParam   QualityScanBizParam   `json:"bizParam"`   // 业务参数
	CodeParam  QualityScanCodeParam  `json:"codeParam"`  // 代码参数
	SonarParam QualityScanSonarParam `json:"sonarParam"` // Sonar参数
	MvnParam   QualityScanMvnParam   `json:"mvnParam"`   // Maven参数
}

// Kind 返回消息类型
func (QualityScanTaskPayload) Kind() string {
	return "magic-neptune-quality"
}

// WithBiz 设置业务参数
func (q *QualityScanTaskPayload) WithBiz(biz QualityScanBizParam) *QualityScanTaskPayload {
	q.BizParam = biz
	return q
}

// WithCode 设置代码参数
func (q *QualityScanTaskPayload) WithCode(code QualityScanCodeParam) *QualityScanTaskPayload {
	q.CodeParam = code
	return q
}

// WithSonar 设置Sonar参数
func (q *QualityScanTaskPayload) WithSonar(sonar QualityScanSonarParam) *QualityScanTaskPayload {
	q.SonarParam = sonar
	return q
}

// WithMvn 设置Maven参数
func (q *QualityScanTaskPayload) WithMvn(mvn QualityScanMvnParam) *QualityScanTaskPayload {
	q.MvnParam = mvn
	return q
}

// QualityScanBizParam 质量扫描业务参数
type QualityScanBizParam struct {
	ExecHisId     int64  `json:"execHisId"`     // 任务记录ID
	CallBack      string `json:"callBack"`      // pod内部直接回调主服务地址
	Type          string `json:"type"`          // 任务类型 unit(单测)/scan(扫描)
	Mode          string `json:"mode"`          // 模式: increment(增量)/total(全量)
	Language      string `json:"language"`      // 语言: java
	JavaVersion   string `json:"javaVersion"`   // java版本: Java8/Java11/Java17
	ExclusionPath string `json:"exclusionPath"` // 排除的路径: src/test/java/*
	InclusionPath string `json:"inclusionPath"` // 包含的路径: src/test/java/*
	AppType       string `json:"appType"`       // 前端/后端
	ModulePath    string `json:"modulePath"`    // 模块路径
}

// QualityScanCodeParam 质量扫描代码参数
type QualityScanCodeParam struct {
	CodeUrl         string `json:"codeUrl"`         // 仓库地址: http://xx.xx.git
	GitUser         string `json:"gitUser"`         // 仓库用户名: root
	GitToken        string `json:"gitToken"`        // 仓库密码: 123456
	Branch          string `json:"branch"`          // 分支: master
	BaseCommitId    string `json:"baseCommitId"`    // 基准commitId
	CompareCommitId string `json:"compareCommitId"` // 比较commitId
}

// QualityScanSonarParam 质量扫描Sonar参数
type QualityScanSonarParam struct {
	SonarUrl      string `json:"sonarUrl"`      // sonar地址
	SonarToken    string `json:"sonarToken"`    // sonar token
	SonarPdf      bool   `json:"sonarPdf"`      // 是否生成PDF
	SonarProfile  string `json:"sonarProfile"`  // 规则集名称
	SonarCallback string `json:"sonarCallback"` // 回调地址
}

// QualityScanMvnParam 质量扫描Maven参数
type QualityScanMvnParam struct {
	MvnRepo string `json:"mvnRepo"` // Maven私服仓库地址
	MvnUser string `json:"mvnUser"` // Maven用户名,可为空
	MvnPw   string `json:"mvnPw"`   // Maven密码,可为空
}

// JacocoCoveragePayload Jacoco覆盖率任务载体
type JacocoCoveragePayload struct {
	Mode            string    `json:"mode"`            // 模式
	AppName         string    `json:"appName"`         // 应用名称
	Ip              string    `json:"ip"`              // IP地址
	Port            string    `json:"port"`            // 端口
	Packages        string    `json:"packages"`        // 包路径
	Git             GitConfig `json:"git"`             // Git配置
	Exclude         string    `json:"exclude"`         // 排除路径
	ModuleName      string    `json:"moduleName"`      // 模块名称
	Oss             OssConfig `json:"oss"`             // OSS配置
	TaskNo          string    `json:"taskNo"`          // 任务编号
	BaseCommitId    string    `json:"baseCommitId"`    // 基准commitId
	CompareCommitId string    `json:"compareCommitId"` // 比较commitId
	MantisCallBack  string    `json:"mantisCallBack"`  // Mantis回调地址
	HisId           int64     `json:"hisId"`           // 历史记录ID
	OldExecPath     string    `json:"oldExecPath"`     // 旧执行路径
}

// GitConfig Git配置
type GitConfig struct {
	GitUrl string `json:"gitUrl"` // Git仓库地址
	User   string `json:"user"`   // 用户名
	Token  string `json:"token"`  // 令牌
	Branch string `json:"branch"` // 分支
}

// OssConfig OSS配置
type OssConfig struct {
	Path      string `json:"path"`      // 路径
	Endpoint  string `json:"endpoint"`  // 端点
	Bucket    string `json:"bucket"`    // 桶名称
	AccessId  string `json:"accessId"`  // 访问ID
	AccessKey string `json:"accessKey"` // 访问密钥
	PathStyle bool   `json:"pathStyle"` // 路径风格
}

// Kind 返回消息类型
func (JacocoCoveragePayload) Kind() string {
	return "magic-neptune-jacoco"
}
