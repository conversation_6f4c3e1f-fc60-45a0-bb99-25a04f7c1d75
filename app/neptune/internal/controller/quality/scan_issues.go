package quality

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

// ScanIssuesController 扫描问题控制器
type ScanIssuesController struct {
	*controller.BaseController
}

// 全局变量声明
var (
	// DefaultScanIssuesController 默认扫描问题控制器实例
	DefaultScanIssuesController ScanIssuesController

	// scanIssuesService 扫描问题服务实例
	scanIssuesService quality.ScanIssuesService
)

// List 查询扫描问题列表
func (c *ScanIssuesController) List(req dto.ScanIssuesReqDTO, pageRequest gormx.PageRequest) {
	result := scanIssuesService.QueryScanIssuesList(c.MantisContext, req, pageRequest)
	c.ResSuccessResult(result)
}

// Components 查询单元组件列表
func (c *ScanIssuesController) Components(req dto.SearchComponentReqDTO, request gormx.PageRequest) {
	result := scanIssuesService.QueryUnitComponentList(c.MantisContext, req, request)
	c.ResSuccessResult(result)
}
