package quality

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality/help"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

// SonarRuleKeyInPathReq Sonar规则Key路径参数
type SonarRuleKeyInPathReq struct {
	Key string `path:"key"` // 规则Key
}

// SonarRuleController Sonar规则控制器
type SonarRuleController struct {
	*controller.BaseController
}

// 全局变量声明
var (
	// DefaultSonarRuleController 默认Sonar规则控制器实例
	DefaultSonarRuleController SonarRuleController

	// sonarRuleService Sonar规则服务实例
	sonarRuleService quality.SonarRuleService
)

// SearchTemplateRules 搜索模板规则
func (c *SonarRuleController) SearchTemplateRules(search help.SonarRuleSearchReq) {
	result := sonarRuleService.SearchRulesInTemplate(search, c.MantisContext)
	c.ResSuccessResult(result)
}

// RuleStatusList 获取规则状态列表
func (c *SonarRuleController) RuleStatusList() {
	result := enums.RuleStatusEnum{}.GetRuleStatusList()
	c.ResSuccessResult(result)
}

// RuleTypesList 获取规则类型列表
func (c *SonarRuleController) RuleTypesList() {
	result := enums.RuleTypeEnum{}.GetRuleTypeList()
	c.ResSuccessResult(result)
}

// ActiveOrDeActiveRule 激活或禁用规则
func (c *SonarRuleController) ActiveOrDeActiveRule(req dto.ActiveDeActiveRuleReqDTO) {
	sonarRuleService.ActiveOrDeActiveTemplateRule(req, c.MantisContext)
	c.ResSuccess()
}

// ShowRuleDetail 显示规则详情
func (c *SonarRuleController) ShowRuleDetail(req SonarRuleKeyInPathReq) {
	result := sonarRuleService.ShowRuleDetail(req.Key)
	c.ResSuccessResult(result)
}
