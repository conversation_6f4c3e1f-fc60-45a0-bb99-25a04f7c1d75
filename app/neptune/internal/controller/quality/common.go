package quality

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
)

// QueryBranchListReq 分支列表查询请求参数
type QueryBranchListReq struct {
	Search   string `schema:"search"`   // 搜索关键词
	AppId    string `schema:"appId"`    // 应用ID
	Page     int64  `schema:"page"`     // 页码
	PageSize int64  `schema:"pageSize"` // 每页大小
}

// GetBranchReq 获取单个分支请求参数
type GetBranchReq struct {
	AppId  string `schema:"appId"`  // 应用ID
	Branch string `schema:"branch"` // 分支名称
}

// CommonController 通用控制器，提供枚举列表和基础数据服务
type CommonController struct {
	*controller.BaseController
}

// DefaultCommonController 全局通用控制器实例
var DefaultCommonController CommonController

// GetTaskStatusEnumList 获取任务状态枚举列表
func (c *CommonController) GetTaskStatusEnumList() {
	c.ResSuccessResult(make(map[string]interface{}))
}

// GetLanguages 获取支持的语言列表
func (c *CommonController) GetLanguages() {
	c.ResSuccessResult(sonarRuleService.GetLanguages(c.MantisContext))
}

// GetSeverities 获取严重级别列表
func (c *CommonController) GetSeverities() {
	c.ResSuccessResult(enums.RuleSeverityEnum{}.GetSeverityList())
}

// GetExecTypes 获取执行类型列表
func (c *CommonController) GetExecTypes() {
	c.ResSuccessResult([]dto.CodeEnumDTO{
		{Label: "前端", Value: constants.ExecTypeFront},
		{Label: "后端", Value: constants.ExecTypeBack},
	})
}

// GetScanModes 获取扫描模式列表
func (c *CommonController) GetScanModes() {
	c.ResSuccessResult([]dto.CodeEnumDTO{
		{Label: "全量", Value: constants.StandardTotal},
		{Label: "增量", Value: constants.StandardIncrement},
	})
}

// GetBranchList 获取分支列表
func (c *CommonController) GetBranchList(req QueryBranchListReq) {
	c.ResSuccessResult(taskService.GetBranchList(req.AppId, req.Search, req.Page, req.PageSize))
}

// GetBranchListByVcsPath 根据版本控制路径获取分支列表
func (c *CommonController) GetBranchListByVcsPath(req QueryBranchListReq) {
	c.ResSuccessResult(taskService.GetBranchListByCodePath(
		c.MantisContext,
		c.Request.Header.Get(commonconstants.HubUrlKey),
		req.Search,
		req.Page,
		req.PageSize,
	))
}

// GetBranch 获取特定分支信息
func (c *CommonController) GetBranch(req GetBranchReq) {
	c.ResSuccessResult(taskService.GetBranchCommit(req.AppId, req.Branch))
}

// GetTaskFromEnums 获取任务来源枚举列表
func (c *CommonController) GetTaskFromEnums() {
	c.ResSuccessResult([]dto.CodeEnumDTO{
		{Value: constants.SourceWeb, Label: "手动创建"},
		{Value: constants.SourcePipeline, Label: "自动创建"},
	})
}

// GetTaskTypeEnums 获取任务类型枚举列表
func (c *CommonController) GetTaskTypeEnums() {
	c.ResSuccessResult([]dto.CodeEnumDTO{
		{Label: "代码扫描", Value: constants.ScanJobType},
		{Label: "单元测试", Value: constants.UnitTestJobType},
		{Label: "集成覆盖率", Value: constants.CoverageJobType},
	})
}
