package quality

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

// QueryFilterRuleReq 查询过滤规则请求参数
type QueryFilterRuleReq struct {
	RelationId string `path:"relationId"` // 关联ID
	Type       string `path:"type"`       // 过滤类型
}

// QueryAppFilterRuleReq 查询应用过滤规则请求参数
type QueryAppFilterRuleReq struct {
	AppId string `schema:"appId"` // 应用ID
}

// QueryAppFilterInfoIdReq 通过ID查询应用过滤信息请求参数
type QueryAppFilterInfoIdReq struct {
	Id int64 `path:"id"` // 过滤规则ID
}

// QueryAppFilterInfoAppIdReq 通过应用ID查询应用过滤信息请求参数
type QueryAppFilterInfoAppIdReq struct {
	Id string `path:"id"` // 应用ID
}

// UpdateAppFilterDescriptionReq 更新应用过滤描述请求参数
type UpdateAppFilterDescriptionReq struct {
	Id          int64  `json:"id"`          // 过滤规则ID
	Description string `json:"description"` // 描述内容
}

// ScanFilterRuleController 扫描过滤规则控制器
type ScanFilterRuleController struct {
	*controller.BaseController
}

// 全局变量声明
var (
	// DefaultScanFilterRuleController 默认扫描过滤规则控制器实例
	DefaultScanFilterRuleController ScanFilterRuleController

	// filterConfigService 过滤配置服务实例
	filterConfigService quality.ScanTaskFilterConfigService
)

// QueryFilterRule 查询过滤规则
func (c *ScanFilterRuleController) QueryFilterRule(req QueryFilterRuleReq) {
	result := filterConfigService.QueryFilter(c.MantisContext, req.RelationId, req.Type, "")
	c.ResSuccessResult(result)
}

// CreateFilterRule 创建过滤规则
func (c *ScanFilterRuleController) CreateFilterRule(filterDTO dto.ScanTaskFilterDTO) {
	userInfo := commondto.UserInfo{
		AdAccount: c.GetUser(),
		CompanyID: c.GetCompany(),
	}

	result := filterConfigService.ModifyFilter(c.MantisContext, filterDTO, userInfo)
	c.ResSuccessResult(result)
}

// ModifyFilterRule 修改过滤规则
func (c *ScanFilterRuleController) ModifyFilterRule(filterDTO dto.ScanTaskFilterDTO) {
	userInfo := commondto.UserInfo{
		AdAccount: c.GetUser(),
		CompanyID: c.GetCompany(),
	}

	result := filterConfigService.ModifyFilter(c.MantisContext, filterDTO, userInfo)
	c.ResSuccessResult(result)
}

// QueryAppFilterRule 查询应用过滤规则
func (c *ScanFilterRuleController) QueryAppFilterRule(req QueryAppFilterRuleReq, request gormx.PageRequest) {
	result := filterConfigService.QueryAppFilter(c.MantisContext, req.AppId, request)
	c.ResSuccessResult(result)
}

// QueryAppFilterInfo 查询应用过滤信息
func (c *ScanFilterRuleController) QueryAppFilterInfo(req QueryAppFilterInfoIdReq) {
	result := filterConfigService.QueryAppFilterInfo(c.MantisContext, req.Id)
	c.ResSuccessResult(result)
}

// QueryAppFilterInfoByAppId 通过应用ID查询应用过滤信息
func (c *ScanFilterRuleController) QueryAppFilterInfoByAppId(req QueryAppFilterInfoAppIdReq) {
	result := filterConfigService.QueryAppFilterInfoByAppId(c.MantisContext, req.Id)
	c.ResSuccessResult(result)
}

// SaveAppFilterRule 保存应用过滤规则
func (c *ScanFilterRuleController) SaveAppFilterRule(filterDto dto.AppScanTaskFilterDTO) {
	result := filterConfigService.SaveAppFilter(c.MantisContext, filterDto)
	c.ResSuccessResult(result)
}

// UpdateAppFilterRule 更新应用过滤规则
func (c *ScanFilterRuleController) UpdateAppFilterRule(filterDto dto.AppScanTaskFilterDTO) {
	filterConfigService.SaveAppFilter(c.MantisContext, filterDto)
	c.ResSuccess()
}

// DeleteAppFilterRule 删除应用过滤规则
func (c *ScanFilterRuleController) DeleteAppFilterRule(req QueryAppFilterInfoIdReq) {
	filterConfigService.DeleteAppFilter(c.MantisContext, req.Id)
	c.ResSuccess()
}

// QueryFilterExclusionEnum 查询过滤排除枚举
func (c *ScanFilterRuleController) QueryFilterExclusionEnum() {
	// 返回过滤排除枚举值
	result := []commondto.CodeEnumDTO{
		{Label: "排除", Value: constants.FilterConfigExclusion},
		// {Label: "包含", Value: constants.FilterConfigInclusion},
	}

	c.ResSuccessResult(result)
}

// UpdateDescription 更新应用过滤规则描述
func (c *ScanFilterRuleController) UpdateDescription(description UpdateAppFilterDescriptionReq) {
	filterConfigService.UpdateAppFilterDescription(
		c.MantisContext,
		description.Id,
		description.Description,
	)
	c.ResSuccess()
}
