package openapi

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

// CiJobTypeInPathReq CI任务类型路径参数
type CiJobTypeInPathReq struct {
	Type string `path:"type"` // 任务类型
}

// CiJobRes CI任务响应
type CiJobRes struct {
	ReportUrl string `json:"report_url"` // 报告URL
}

// ScanTestCiController 扫描测试CI控制器
type ScanTestCiController struct {
	*controller.BaseController
}

// 全局变量声明
var (
	// DefaultScanTestCiController 默认CI控制器实例
	DefaultScanTestCiController ScanTestCiController

	// taskService 任务服务
	taskService quality.TaskService
)

// ScanTest 执行扫描测试任务并返回报告URL
func (c *ScanTestCiController) ScanTest(infoDto dto.ScanTestInfoDTO, req CiJobTypeInPathReq) {
	reportUrl := taskService.ShipRegisterJob(c.MantisContext, infoDto, req.Type)
	c.ResSuccessResult(CiJobRes{
		ReportUrl: reportUrl,
	})
}
