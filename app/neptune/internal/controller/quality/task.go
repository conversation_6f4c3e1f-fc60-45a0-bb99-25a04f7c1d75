package quality

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

// TaskControllerIdIdInPathReq 任务ID路径参数
type TaskControllerIdIdInPathReq struct {
	Id int64 `path:"id"` // 任务ID
}

// TaskIdInPathReq 任务ID路径参数
type TaskIdInPathReq struct {
	TaskId int64 `path:"taskId"` // 任务ID
}

// GetCommitInfoSchemaReq 获取提交信息请求参数
type GetCommitInfoSchemaReq struct {
	Id       string `schema:"appId"`    // 应用ID
	Branch   string `schema:"branch"`   // 分支名称
	Page     int64  `schema:"page"`     // 页码
	Size     int64  `schema:"size"`     // 每页大小
	TimeZone string `schema:"timeZone"` // 时区
}

// HisIdInSchemaReq 历史ID查询参数
type HisIdInSchemaReq struct {
	HisId int64 `schema:"hisId"` // 历史记录ID
}

// HisIdInPathReq 历史ID路径参数
type HisIdInPathReq struct {
	HisId int64 `path:"hisId"` // 历史记录ID
}

// TaskController 质量任务控制器
type TaskController struct {
	*controller.BaseController
}

// 服务和控制器实例声明
var (
	// DefaultTaskController 默认任务控制器实例
	DefaultTaskController TaskController

	// 服务实例
	taskService  quality.TaskService             // 任务服务
	gatesService quality.ScanQualityGatesService // 质量门禁服务
)

// GetCommitInfo 获取代码提交信息
func (c *TaskController) GetCommitInfo(schemaReq GetCommitInfoSchemaReq) {
	result := taskService.GetCommitInfo(
		c.MantisContext,
		schemaReq.Id,
		schemaReq.Page,
		schemaReq.Size,
		schemaReq.TimeZone,
		schemaReq.Branch,
	)
	c.ResSuccessResult(result)
}

// ExecuteScanTask 执行代码扫描任务
func (c *TaskController) ExecuteScanTask(reqDto dto.ScanTestWebExecReqDTO) {
	userInfo := commondto.UserInfo{AdAccount: c.GetUser()}
	taskService.ExecScanTestTask(c.MantisContext, reqDto, userInfo)
	c.ResSuccess()
}

// ExecuteTestTask 执行单元测试任务
func (c *TaskController) ExecuteTestTask(reqDto dto.ScanTestWebExecReqDTO) {
	userInfo := commondto.UserInfo{AdAccount: c.GetUser()}
	taskService.ExecScanTestTask(c.MantisContext, reqDto, userInfo)
	c.ResSuccess()
}

// StopScanTask 停止扫描任务
func (c *TaskController) StopScanTask(req TaskControllerIdIdInPathReq) {
	userInfo := commondto.UserInfo{AdAccount: c.GetUser()}
	taskService.StopScanTestTask(c.MantisContext, req.Id, userInfo)
	c.ResSuccess()
}

// StopTestTask 停止测试任务
func (c *TaskController) StopTestTask(req TaskControllerIdIdInPathReq) {
	userInfo := commondto.UserInfo{AdAccount: c.GetUser()}
	taskService.StopScanTestTask(c.MantisContext, req.Id, userInfo)
	c.ResSuccess()
}

// GetScanTestTask 获取扫描测试任务详情
func (c *TaskController) GetScanTestTask(req TaskIdInPathReq) {
	result := taskService.GetScanTestTask(c.MantisContext, req.TaskId)
	c.ResSuccessResult(result)
}

// InsertScanTask 新增扫描任务
func (c *TaskController) InsertScanTask(configDTO dto.ScanTestTaskConfigDTO) {
	// 设置任务类型为扫描任务
	configDTO.TaskType = constants.ScanJobType

	userInfo := commondto.UserInfo{
		AdAccount: c.GetUser(),
		CompanyID: c.GetCompany(),
	}

	result := taskService.SaveTaskConfig(c.MantisContext, configDTO, userInfo)
	c.ResSuccessResult(result)
}

// InsertTestTask 新增单元测试任务
func (c *TaskController) InsertTestTask(configDTO dto.ScanTestTaskConfigDTO) {
	// 设置任务类型为单元测试任务
	configDTO.TaskType = constants.UnitTestJobType

	userInfo := commondto.UserInfo{
		AdAccount: c.GetUser(),
		CompanyID: c.GetCompany(),
	}

	result := taskService.SaveTaskConfig(c.MantisContext, configDTO, userInfo)
	c.ResSuccessResult(result)
}

// DeleteScanTask 删除扫描任务
func (c *TaskController) DeleteScanTask(req TaskControllerIdIdInPathReq) {
	userInfo := commondto.UserInfo{AdAccount: c.GetUser()}
	taskService.DeleteTask(c.MantisContext, req.Id, userInfo)
	c.ResSuccess()
}

// DeleteTestTask 删除单元测试任务
func (c *TaskController) DeleteTestTask(req TaskControllerIdIdInPathReq) {
	userInfo := commondto.UserInfo{AdAccount: c.GetUser()}
	taskService.DeleteTask(c.MantisContext, req.Id, userInfo)
	c.ResSuccess()
}

// UpdateScanTask 更新扫描任务
func (c *TaskController) UpdateScanTask(configDTO dto.ScanTestTaskConfigDTO) {
	// 设置任务类型为扫描任务
	configDTO.TaskType = constants.ScanJobType

	userInfo := commondto.UserInfo{AdAccount: c.GetUser()}
	taskService.SaveTaskConfig(c.MantisContext, configDTO, userInfo)
	c.ResSuccess()
}

// UpdateTestTask 更新单元测试任务
func (c *TaskController) UpdateTestTask(configDTO dto.ScanTestTaskConfigDTO) {
	// 设置任务类型为单元测试任务
	configDTO.TaskType = constants.UnitTestJobType

	userInfo := commondto.UserInfo{AdAccount: c.GetUser()}
	taskService.SaveTaskConfig(c.MantisContext, configDTO, userInfo)
	c.ResSuccess()
}

// QueryQualityGate 查询质量门禁
func (c *TaskController) QueryQualityGate(req TaskIdInPathReq) {
	result := gatesService.SelectByTaskId(c.MantisContext, req.TaskId)
	c.ResSuccessResult(result)
}

// QueryTaskOverview 查询任务概览
func (c *TaskController) QueryTaskOverview(pathReq TaskIdInPathReq, schemaReq HisIdInSchemaReq) {
	result := taskService.GetScanTestTaskOverview(c.MantisContext, pathReq.TaskId, schemaReq.HisId)
	c.ResSuccessResult(result)
}

// QueryScanTestTaskPage 分页查询扫描测试任务
func (c *TaskController) QueryScanTestTaskPage(search dto.ScanTestTaskSearchDTO, req gormx.PageRequest) {
	// 检查是否有Git仓库URL请求头
	hubUrl := c.Request.Header.Get(commonconstants.GitRepoUrlKey)

	if hubUrl != "" {
		// 如果有仓库URL，查询仓库内的任务
		search.HubUrl = hubUrl
		result := taskService.GetScanTestTaskPageListInHub(c.MantisContext, search, req)
		c.ResSuccessResult(result)
	} else {
		// 否则查询所有任务
		result := taskService.GetScanTestTaskPageList(c.MantisContext, search, req)
		c.ResSuccessResult(result)
	}
}

// QueryScanTestExecHisPageByTaskId 分页查询任务执行历史
func (c *TaskController) QueryScanTestExecHisPageByTaskId(search dto.ScanTestTaskSearchDTO, req gormx.PageRequest) {
	pageRequest := gormx.PageRequest{
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	result := taskService.GetScanTestExecHisPageByTaskId(c.MantisContext, search, pageRequest)
	c.ResSuccessResult(result)
}

// GetLogsByHisId 获取执行历史日志
func (c *TaskController) GetLogsByHisId(req HisIdInPathReq) {
	result := taskService.GetLogsByExecHisId(c.MantisContext, req.HisId)
	c.ResSuccessResult(result)
}
