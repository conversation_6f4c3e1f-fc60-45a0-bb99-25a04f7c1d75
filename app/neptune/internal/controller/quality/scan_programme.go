package quality

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

// ScanProgrammeIdInPathReq 扫描方案ID路径参数
type ScanProgrammeIdInPathReq struct {
	Id int64 `path:"id"` // 方案ID
}

// ScanProgrammeSearchReq 扫描方案搜索请求参数
type ScanProgrammeSearchReq struct {
	Name string `schema:"name"` // 方案名称
}

// ScanTaskListReq 扫描任务列表请求参数
type ScanTaskListReq struct {
	Id int64 `schema:"id"` // 方案ID
}

// ScanProgrammeSetProfileDefaultSchemaReq 设置方案配置文件默认状态请求参数
type ScanProgrammeSetProfileDefaultSchemaReq struct {
	ProgrammeId int64 `schema:"programmeId"` // 方案ID
	ProfileId   int64 `schema:"profileId"`   // 配置文件ID
	IsActive    bool  `schema:"isActive"`    // 是否激活
}

// ScanProgrammeCompileReq 方案编译请求参数
type ScanProgrammeCompileReq struct {
	Id      int64 `path:"id"`      // 方案ID
	Compile int32 `path:"compile"` // 编译状态
}

// ScanProgrammeController 扫描方案控制器
type ScanProgrammeController struct {
	*controller.BaseController
}

// 全局变量声明
var (
	// DefaultScanProgrammeController 默认扫描方案控制器实例
	DefaultScanProgrammeController ScanProgrammeController

	// scanProgrammeService 扫描方案服务实例
	scanProgrammeService quality.ScanProgrammeService
)

// Add 添加扫描方案
func (c *ScanProgrammeController) Add(scanProgrammeDto dto.ScanProgrammeDTO) {
	user := commondto.UserInfo{
		AdAccount: c.GetUser(),
		CompanyID: c.GetCompany(),
	}
	result := scanProgrammeService.CreateScanProgramme(c.MantisContext, scanProgrammeDto, user)
	c.ResSuccessResult(result)
}

// Query 查询扫描方案详情
func (c *ScanProgrammeController) Query(req ScanProgrammeIdInPathReq) {
	result := scanProgrammeService.QueryScanProgramme(c.MantisContext, req.Id)
	c.ResSuccessResult(result)
}

// Delete 删除扫描方案
func (c *ScanProgrammeController) Delete(req ScanProgrammeIdInPathReq) {
	result := scanProgrammeService.DeleteScanProgramme(c.MantisContext, req.Id)
	c.ResSuccessResult(result)
}

// List 查询扫描方案列表
func (c *ScanProgrammeController) List(req ScanProgrammeSearchReq) {
	result := scanProgrammeService.QueryScanProgrammeList(c.MantisContext, req.Name)
	c.ResSuccessResult(result)
}

// Modify 修改扫描方案
func (c *ScanProgrammeController) Modify(programmeDTO dto.ScanProgrammeDTO) {
	userInfo := commondto.UserInfo{
		AdAccount: c.GetUser(),
		CompanyID: c.GetCompany(),
	}
	result := scanProgrammeService.ModifyScanProgramme(c.MantisContext, programmeDTO, userInfo)
	c.ResSuccessResult(result)
}

// QueryProfileCards 查询方案配置文件卡片
func (c *ScanProgrammeController) QueryProfileCards(req ScanProgrammeIdInPathReq) {
	result := scanProgrammeService.QueryScanProgrammeProfileCards(c.MantisContext, req.Id)
	c.ResSuccessResult(result)
}

// SetProfileActive 设置配置文件激活状态
func (c *ScanProgrammeController) SetProfileActive(req ScanProgrammeSetProfileDefaultSchemaReq) {
	result := scanProgrammeService.SetProfileActive(c.MantisContext, req.ProgrammeId, req.ProfileId, req.IsActive)
	c.ResSuccessResult(result)
}

// QueryTaskNameList 查询任务名称列表
func (c *ScanProgrammeController) QueryTaskNameList(req ScanTaskListReq, pageReq gormx.PageRequest) {
	res, _ := scanProgrammeService.QueryScanTaskNameList(c.MantisContext, req.Id, pageReq)
	c.ResSuccessResult(res)
}

// QueryProgrammeProfileEnum 查询方案配置文件枚举
func (c *ScanProgrammeController) QueryProgrammeProfileEnum(req ScanProgrammeIdInPathReq) {
	result := scanProgrammeService.QueryProgrammeProfileEnum(c.MantisContext, req.Id)
	c.ResSuccessResult(result)
}
