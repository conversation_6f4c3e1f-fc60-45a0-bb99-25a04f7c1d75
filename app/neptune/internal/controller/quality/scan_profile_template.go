package quality

import (
	"fmt"
	"net/url"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	qualitymodels "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

// ProgrammeIdInPathReq 方案ID路径参数
type ProgrammeIdInPathReq struct {
	ProgrammeId int64 `path:"programmeId"` // 方案ID
}

// ScanProfileTemplateIdInPathReq 扫描配置模板ID路径参数
type ScanProfileTemplateIdInPathReq struct {
	Id int64 `path:"id"` // 模板ID
}

// ScanProfileTemplateLanguageInSchemaReq 扫描配置模板语言查询参数
type ScanProfileTemplateLanguageInSchemaReq struct {
	Language string `schema:"language"` // 语言
}

// ScanProfileProfileIdInPathReq 扫描配置文件ID路径参数
type ScanProfileProfileIdInPathReq struct {
	ProfileId int64 `path:"profileId"` // 配置文件ID
}

// ScanProfileTemplateController 扫描配置模板控制器
type ScanProfileTemplateController struct {
	*controller.BaseController
}

// 全局变量声明
var (
	// DefaultScanProfileTemplateController 默认扫描配置模板控制器实例
	DefaultScanProfileTemplateController ScanProfileTemplateController

	// profileService 扫描配置模板服务实例
	profileService quality.ScanProfileTemplateService
)

// QueryProfileTemplateByLanguage 按语言查询配置模板
func (c *ScanProfileTemplateController) QueryProfileTemplateByLanguage(req ScanProfileTemplateLanguageInSchemaReq) {
	result := profileService.GetTemplatesByLanguage(c.MantisContext, req.Language)
	c.ResSuccessResult(result)
}

// SaveScanProfileTemplate 保存扫描配置模板
func (c *ScanProfileTemplateController) SaveScanProfileTemplate(template qualitymodels.ScanProfileTemplate) {
	userInfo := commondto.UserInfo{AdAccount: c.GetUser()}
	result := profileService.SaveScanProfileTemplate(c.MantisContext, template, userInfo)
	c.ResSuccessResult(result)
}

// CopyScanProfileTemplate 复制扫描配置模板
func (c *ScanProfileTemplateController) CopyScanProfileTemplate(reqDTO dto.CopyProfileTemplateReqDTO) {
	userInfo := commondto.UserInfo{AdAccount: c.GetUser()}
	result := profileService.CopyTemplate(c.MantisContext, reqDTO.Id, reqDTO.Name, userInfo)
	c.ResSuccessResult(result)
}

// DeleteScanProfileTemplate 删除扫描配置模板
func (c *ScanProfileTemplateController) DeleteScanProfileTemplate(req ScanProfileTemplateIdInPathReq) {
	userInfo := commondto.UserInfo{AdAccount: c.GetUser()}
	profileService.DeleteTemplate(c.MantisContext, req.Id, userInfo)
	c.ResSuccess()
}

// ImportProfiles 导入配置文件
func (c *ScanProfileTemplateController) ImportProfiles() {
	request := c.Request
	_, file, err := request.FormFile("file")
	if err != nil {
		logger.Logger.Panicf("获取请求参数错误: %v", err)
	}

	userInfo := commondto.UserInfo{AdAccount: c.GetUser()}
	result := profileService.ImportProfileToTemplate(c.MantisContext, file, userInfo)
	c.ResSuccessResult(result)
}

// ExportProfile 导出配置文件
func (c *ScanProfileTemplateController) ExportProfile(req ScanProfileTemplateIdInPathReq) {
	fileName, export := profileService.ExportProfileTemplate(c.MantisContext, req.Id)

	// 设置响应头
	contentType := "application/octet-stream"
	c.ResponseWriter.Header().Set("Content-Type", contentType)
	fileName = url.QueryEscape(fileName)
	c.ResponseWriter.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	c.ResponseWriter.Header().Set("Content-Transfer-Encoding", "binary")

	// 写入响应体
	c.ResponseWriter.Write([]byte(export))
}
