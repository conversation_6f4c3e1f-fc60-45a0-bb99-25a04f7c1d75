package artifact

import (
	neptunedto "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
)

// ReportIdInPathReq 报告ID请求参数
type ReportIdInPathReq struct {
	Id int64 `path:"id"` // 报告ID
}

// ReportController 报告控制器
type ReportController struct {
	*controller.BaseController
}

// 全局变量声明
var (
	// DefaultReportController 默认报告控制器实例
	DefaultReportController ReportController

	// reportService 报告服务实例
	reportService artifact.ReportService
)

// Detail 获取报告详情
func (r *ReportController) Detail(idReq ReportIdInPathReq, req QueryDTOReq) {
	id := idReq.Id

	// 构建查询请求
	request := dto.QueryDTO{
		PageSize:  req.PageSize,
		Page:      req.Page,
		Q:         req.Q,
		RequestId: req.RequestId,
		SearchKey: req.SearchKey,
	}

	// 查询报告详情
	page := reportService.Detail(r.MantisContext, id, request)
	r.ResSuccessResult(page)
}

// ExecHisExport 导出执行历史
func (r *ReportController) ExecHisExport(idReq ReportIdInPathReq, exportDto neptunedto.ProductExportDto) {
	// 获取用户信息
	userInfo := dto.UserInfo{
		AdAccount: r.GetUser(),
		CompanyID: utils.IDString(r.User.CompanyId),
	}

	id := idReq.Id
	// 导出执行历史，第二个参数1表示导出类型
	reportService.ExecHisExport(r.MantisContext, id, 1, exportDto, userInfo)
	r.ResSuccess()
}

// List 获取报告列表
func (r *ReportController) List(idReq ReportIdInPathReq, req QueryDTOReq) {
	id := idReq.Id

	// 构建查询请求
	request := dto.QueryDTO{
		PageSize:  req.PageSize,
		Page:      req.Page,
		Q:         req.Q,
		RequestId: req.RequestId,
		SearchKey: req.SearchKey,
	}

	// 查询报告列表
	page := reportService.List(r.MantisContext, id, request)
	r.ResSuccessResult(page)
}
