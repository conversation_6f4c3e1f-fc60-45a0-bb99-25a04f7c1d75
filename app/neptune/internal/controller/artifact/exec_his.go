package artifact

import (
	"context"
	"encoding/base64"
	"fmt"
	"path"

	task2 "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	artifactService "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/artifact"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
)

// ExecHisIdInPathReq 执行历史ID请求参数
type ExecHisIdInPathReq struct {
	Id int64 `path:"id"` // 执行历史ID
}

// QueryDTOReq 分页查询请求参数
type QueryDTOReq struct {
	PageSize  int64  `schema:"pageSize"`  // 每页大小
	Page      int64  `schema:"page"`      // 页码
	Q         string `schema:"q"`         // 查询条件
	RequestId string `schema:"requestId"` // 请求ID
	SearchKey string `schema:"searchKey"` // 搜索关键字
}

// ExecHisController 执行历史控制器
type ExecHisController struct {
	*controller.BaseController
}

// 全局变量声明
var (
	// DefaultExecHisController 默认执行历史控制器实例
	DefaultExecHisController ExecHisController

	// scanTaskExecHisService 执行历史服务实例
	scanTaskExecHisService artifactService.ScanTaskExecHisService
)

// GetProductExecResult 获取产品执行结果
func (e *ExecHisController) GetProductExecResult(idReq ExecHisIdInPathReq, req QueryDTOReq) {
	// 构建查询请求
	request := dto.QueryDTO{
		PageSize:  req.PageSize,
		Page:      req.Page,
		Q:         req.Q,
		RequestId: req.RequestId,
		SearchKey: req.SearchKey,
	}

	// 查询产品执行结果
	result := scanTaskExecHisService.GetProductExecResult(e.MantisContext, idReq.Id, request)
	e.ResSuccessResult(result)
}

// GetExecHistList 获取执行历史列表
func (e *ExecHisController) GetExecHistList(idReq ExecHisIdInPathReq, req QueryDTOReq) {
	taskId := idReq.Id

	// 构建查询请求
	request := dto.QueryDTO{
		PageSize:  req.PageSize,
		Page:      req.Page,
		Q:         req.Q,
		RequestId: req.RequestId,
		SearchKey: req.SearchKey,
	}

	// 查询执行历史列表
	result := scanTaskExecHisService.GetExecHistList(e.MantisContext, taskId, request)
	e.ResSuccessResult(result)
}

// GetProductExecLog 获取产品执行日志
func (e *ExecHisController) GetProductExecLog(idReq ExecHisIdInPathReq) {
	detailId := idReq.Id

	// 获取K8S驱动提供者
	driverProvider, err := driver.NewDriverProvider()
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in new driver provider"))
	}

	// 查询任务详情
	var detail artifact.ScanTaskDetail
	detail.Id = detailId
	detail.IsDeleted = commonconstants.DeleteNo

	if err := gormx.SelectOneByCondition(e.MantisContext, &detail); err != nil {
		logger.Logger.Panicf("%+v", xerror.New("error in select ScanTaskDetail"))
	}

	// 获取K8S任务日志
	log, err := driverProvider.GetLog(context.Background(), detail.K8sTaskId)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get log"))
	}

	// 构建响应结果
	result := map[string]interface{}{
		"id":     detailId,
		"status": detail.Status,
		"log":    log,
	}

	// 根据任务状态设置OSS日志文件路径
	if detail.Status >= task2.ProductStatusSucc {
		ossPath := path.Join(commonconstants.TaskRunLogFilePath, fmt.Sprintf("%s.txt", detail.K8sTaskId))
		result["osskey"] = base64.StdEncoding.EncodeToString([]byte(ossPath))
	} else {
		result["osskey"] = ""
	}

	e.ResSuccessResult(result)
}
