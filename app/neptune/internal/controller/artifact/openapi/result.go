package openapi

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

// ResultUploadFormReq 结果上传表单请求参数
type ResultUploadFormReq struct {
	Id   int64  `path:"id"`   // 任务ID
	Type string `path:"type"` // 结果类型
}

// ResultController 结果控制器
type ResultController struct {
	*controller.BaseController
}

// DefaultResultController 默认结果控制器实例
var DefaultResultController ResultController

// resultService 结果服务实例
var resultService artifact.ResultService

// UploadFormResult 上传表单结果文件
func (h *ResultController) UploadFormResult(req ResultUploadFormReq) {
	// 从请求中获取文件
	_, fileHeader, err := h.Request.FormFile("file")
	if err != nil {
		logger.Logger.Panicf("获取文件失败: %v", err)
	}

	// 记录文件信息
	logger.Logger.Infof("获取的文件名: %v, 大小: %v字节", fileHeader.Filename, fileHeader.Size)

	// 打开文件
	file, err := fileHeader.Open()
	if err != nil {
		logger.Logger.Panicf("打开文件失败: %v", err)
	}
	defer file.Close() // 确保文件最终被关闭

	// 准备处理参数
	params := map[string]interface{}{
		"type": req.Type,
		"id":   req.Id,
	}

	// 处理结果并返回成功响应
	resultService.DealResult(h.MantisContext, file, fileHeader.Size, params)
	h.ResSuccess()
}
