package openapi

import (
	"fmt"
	"net/http"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	artifactModel "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/repo"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	pkgDto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type PipelineController struct {
	*controller.BaseController
}

var (
	DefaultPipelineControler PipelineController
	pipelineService          artifact.PipelineService
)

const shipCookie = "zcloud_companyid=%s;zcloud_username=%s"

func (p *PipelineController) ScanProduct(req dto.PipelineProductScanRequestDto) {
	// 获取默认计划
	var plan artifactModel.ScanPlan
	gormx.RawX(p.MantisContext,
		"select * from neptune_artifact_scan_plan where company_id=? and is_default=1 and plan_type = 1 and is_deleted ='N' order by id desc limit 1",
		&plan, req.CompanyID)

	if plan.Id == 0 {
		logger.Logger.Panicf("没有找到默认漏洞扫描方案,请先设置默认方案!")
		return
	}

	// 处理实例ID
	if req.InstanceId == "" && req.InstanceName != "" {
		req.InstanceId = repo.NameRepoInstanceMap[req.InstanceName].ID
	}

	logger.Logger.Infof("pipeline-req: %+v, 使用默认扫描方案 user=%s & company=%s", req, plan.Creator, plan.CompanyID)

	// 设置请求上下文
	p.MantisContext.Header = http.Header{"Cookie": {fmt.Sprintf(shipCookie, req.CompanyID, plan.Creator)}}
	p.MantisContext.User = pkgDto.UserInfo{
		CompanyID: req.CompanyID,
		AdAccount: plan.Creator,
	}

	// 获取仓库服务
	service := repo.GetServiceById(p.MantisContext, req.CompanyID, req.InstanceId)
	if service == nil {
		logger.Logger.Panicf("mantis-neptune 没有配置此仓库！id=%v,name=%s", req.InstanceId, req.InstanceName)
	}

	// 执行扫描并返回结果
	p.ResSuccessResult(pipelineService.ScanProduct(p.MantisContext, &req))
}
