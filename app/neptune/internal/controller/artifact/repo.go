package artifact

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/repo"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
)

// RepoIdInPathReq 仓库ID请求参数
type RepoIdInPathReq struct {
	Id string `path:"id"` // 仓库ID
}

// RepoLatestProductsReq 最新制品请求参数
type RepoLatestProductsReq struct {
	Id       string `path:"id"`       // 仓库实例ID
	RepoId   string `path:"repoId"`   // 仓库ID
	RepoName string `path:"repoName"` // 仓库名称
}

// RepoSchemaReq 仓库查询参数
type RepoSchemaReq struct {
	RepoType  string `schema:"repoType"`  // 仓库类型
	SearchKey string `schema:"searchKey"` // 搜索关键字
}

// RepoVersionsReq 仓库版本请求参数
type RepoVersionsReq struct {
	Id       string `path:"id"`       // 仓库实例ID
	ProdId   string `path:"prodId"`   // 制品ID
	RepoName string `path:"repoName"` // 仓库名称
	ProdName string `path:"prodName"` // 制品名称
}

// RepoVersionPath 版本路径请求参数
type RepoVersionPath struct {
	Id        string `path:"id"`        // 仓库实例ID
	VersionId string `path:"versionId"` // 版本ID
}

// RepoController 仓库控制器
type RepoController struct {
	*controller.BaseController
}

// DefaultRepoController 默认仓库控制器实例
var DefaultRepoController RepoController

// InstanceList 获取仓库实例列表
func (r *RepoController) InstanceList() {
	// 获取当前公司的所有仓库实例
	instanceList, err := repo.GetAllRepoInstance(r.MantisContext, utils.IDString(r.User.CompanyId))
	if err != nil {
		logger.Logger.Panicf("从 deckjob 获取 repo instance 失败!err=%v", err)
	}

	// 构建响应数据
	responseList := make([]map[string]interface{}, 0, len(*instanceList))
	for _, repoInstance := range *instanceList {
		instanceInfo := map[string]interface{}{
			"id":                 repoInstance.ID,
			"instanceName":       repoInstance.InstanceName,
			"supportProductList": repoInstance.SupportProductList,
		}
		responseList = append(responseList, instanceInfo)
	}

	r.ResSuccessResult(responseList)
}

// RepoInfos 获取仓库信息列表
func (r *RepoController) RepoInfos(req RepoIdInPathReq, schema RepoSchemaReq) {
	id := req.Id
	companyId := utils.IDString(r.GetCompany())

	// 获取仓库服务
	service := repo.GetServiceById(r.MantisContext, companyId, id)

	// 查询仓库信息
	repoInfos := service.RepoInfos(r.MantisContext, schema.SearchKey, schema.RepoType)
	r.ResSuccessResult(repoInfos)
}

// LatestProducts 获取最新制品列表
func (r *RepoController) LatestProducts(req RepoLatestProductsReq, schema RepoSchemaReq) {
	id := req.Id
	repoId := req.RepoId
	repoName := req.RepoName
	companyId := utils.IDString(r.GetCompany())

	// 获取仓库服务
	service := repo.GetServiceById(r.MantisContext, companyId, id)

	// 查询最新制品列表
	products := service.LatestAllProducts(r.MantisContext, repoName, repoId, schema.RepoType)
	r.ResSuccessResult(products)
}

// Products 获取制品列表
func (r *RepoController) Products(req RepoLatestProductsReq, schema RepoSchemaReq) {
	id := req.Id
	repoId := req.RepoId
	repoName := req.RepoName
	companyId := utils.IDString(r.GetCompany())

	// 获取仓库服务
	service := repo.GetServiceById(r.MantisContext, companyId, id)

	// 查询制品列表
	products := service.Products(r.MantisContext, repoName, repoId, schema.SearchKey, schema.RepoType)
	r.ResSuccessResult(products)
}

// Versions 获取制品版本列表
func (r *RepoController) Versions(req RepoVersionsReq, schema RepoSchemaReq) {
	id := req.Id
	prodId := req.ProdId
	repoName := req.RepoName
	prodName := req.ProdName
	companyId := utils.IDString(r.GetCompany())

	// 获取仓库服务
	service := repo.GetServiceById(r.MantisContext, companyId, id)

	// 查询版本列表
	versions := service.Versions(r.MantisContext, repoName, schema.SearchKey, prodId, prodName, schema.RepoType)
	r.ResSuccessResult(versions)
}

// VersionPath 获取版本路径
func (r *RepoController) VersionPath(req RepoVersionPath, schema RepoSchemaReq) {
	id := req.Id
	versionId := req.VersionId
	companyId := utils.IDString(r.GetCompany())

	// 获取仓库服务
	service := repo.GetServiceById(r.MantisContext, companyId, id)

	// 查询版本路径
	path := service.Path(r.MantisContext, versionId, schema.RepoType)
	r.ResSuccessResult(path)
}
