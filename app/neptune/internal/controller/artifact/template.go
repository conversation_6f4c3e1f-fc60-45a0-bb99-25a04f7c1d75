package artifact

import (
	"encoding/json"
	"fmt"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/resources"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/common_util"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
	"github.com/xuri/excelize/v2"
)

// 常量定义
const (
	// CveTemplate CVE模板文件路径
	CveTemplate = "template/CVE-template.xlsx"
)

// TemplateTypeInPathReq 模板类型请求参数
type TemplateTypeInPathReq struct {
	Type string `path:"type"` // 模板类型
}

// TemplateController 模板控制器
type TemplateController struct {
	*controller.BaseController
}

// DefaultTemplateController 默认模板控制器实例
var DefaultTemplateController TemplateController

// Format 格式化上传的模板文件
func (t *TemplateController) Format(req TemplateTypeInPathReq) {
	fileType := req.Type

	// 获取上传的文件
	_, fileHeader, err := t.Request.FormFile("file")
	if err != nil {
		logger.Logger.Panic("获取文件异常！")
	}

	openFile, err := fileHeader.Open()
	if err != nil {
		logger.Logger.Panic("打开文件异常！err=", err)
	}
	defer openFile.Close()

	// 根据文件类型处理
	switch fileType {
	case "cve":
		// 处理CVE白名单信息
		existingCveMap := make(map[string]string)
		value := t.Request.PostFormValue("whiteCVEInfos")

		if value != "" {
			var whiteListInfos []artifact.WhiteCVEInfo
			if err := json.Unmarshal([]byte(value), &whiteListInfos); err != nil {
				logger.Logger.Panic("解析字符串数组异常！err=", err)
			}

			// 构建已存在的CVE映射
			for _, info := range whiteListInfos {
				existingCveMap[info.Code] = ""
			}
		}

		// 打开Excel文件
		excelFile, err := excelize.OpenReader(openFile)
		if err != nil {
			logger.Logger.Panicf("%+v", xerror.Wrap(err, "解析excel文件错误，请检查上传文件的格式！"))
		}

		// 获取Sheet1上所有单元格
		contents, err := excelFile.GetRows("Sheet1")
		if err != nil {
			logger.Logger.Panicf("%+v", xerror.Wrap(err, "解析excel文件错误，请检查上传文件的格式！"))
		}

		// 提取新的CVE编码
		newCveList := make([]string, 0)
		for i, line := range contents {
			// 跳过表头
			if i == 0 {
				continue
			}

			for _, cell := range line {
				// 只处理CVE格式的单元格
				if !strings.HasPrefix(cell, "CVE-") {
					continue
				}

				// 如果不在现有白名单中，则添加
				if _, exists := existingCveMap[cell]; !exists {
					newCveList = append(newCveList, cell)
				}
			}
		}

		// 对新提取的CVE列表去重
		uniqueCveList := common_util.StrRemoveDuplicates(newCveList)

		// 构建响应数据
		response := make([]artifact.WhiteCVEInfo, len(uniqueCveList))
		for i, cveCode := range uniqueCveList {
			response[i] = artifact.WhiteCVEInfo{
				Code: cveCode,
			}
		}

		t.ResSuccessResult(response)

	default:
		logger.Logger.Panic("类型不存在，请输入cve")
	}
}

// Download 下载模板文件
func (t *TemplateController) Download(req TemplateTypeInPathReq) error {
	fileType := req.Type

	switch fileType {
	case "cve":
		// 读取CVE模板文件
		fileContent, err := resources.TemplateFS.ReadFile(CveTemplate)
		if err != nil {
			return fmt.Errorf("获取 %s 文件异常, err= %v", CveTemplate, err)
		}

		// 设置响应头
		t.ResponseWriter.Header().Set("Content-Type", "application/octet-stream")
		t.ResponseWriter.Header().Set("Content-Disposition", "attachment; filename=template.xlsx")
		t.ResponseWriter.Header().Set("Content-Transfer-Encoding", "binary")

		// 写入文件内容到响应
		if _, err = t.ResponseWriter.Write(fileContent); err != nil {
			return err
		}

		return nil

	default:
		return fmt.Errorf("类型不存在，请输入cve")
	}
}
