package artifact

import (
	task2 "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	artifactService "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
)

// TaskInfoInPathReq 任务详情请求参数
type TaskInfoInPathReq struct {
	Id     int64 `path:"id"`  // 任务ID
	ExecId int64 `path:"eid"` // 执行记录ID
}

// TaskIdInPathReq 任务ID请求参数
type TaskIdInPathReq struct {
	Id int64 `path:"id"` // 任务ID
}

// TaskController 制品扫描任务控制器
type TaskController struct {
	*controller.BaseController
}

// 全局变量声明
var (
	// DefaultTaskController 默认任务控制器实例
	DefaultTaskController TaskController

	// scanTaskService 任务服务实例
	scanTaskService artifactService.ScanTaskService
)

// List 查询任务列表
func (a *TaskController) List(req QueryWithOrderDTOReq) {
	// 构建查询请求
	request := dto.QueryWithOrderDTO{
		QueryDTO: dto.QueryDTO{
			PageSize:  req.PageSize,
			Page:      req.Page,
			Q:         req.Q,
			RequestId: req.RequestId,
			SearchKey: req.SearchKey,
		},
		OrderField: req.OrderField,
		OrderType:  req.OrderType,
	}

	// 获取用户信息
	userInfo := dto.UserInfo{
		AdAccount: a.GetUser(),
		CompanyID: utils.IDString(a.User.CompanyId),
	}

	// 调用服务查询任务列表
	page := scanTaskService.List(a.MantisContext, &request, userInfo)
	a.ResSuccessResult(page)
}

// Info 获取任务详情
func (a *TaskController) Info(req TaskInfoInPathReq) {
	id := req.Id
	execId := req.ExecId

	// 查询任务详情
	info := scanTaskService.Info(a.MantisContext, id, execId)
	if info.Id == 0 {
		logger.Logger.Panicf("任务详情查询失败: 数据不存在, ID=%d", id)
	}

	a.ResSuccessResult(info)
}

// DeleteTask 删除任务
func (a *TaskController) DeleteTask(req TaskIdInPathReq) {
	// 获取用户信息
	userInfo := dto.UserInfo{
		AdAccount: a.GetUser(),
		CompanyID: utils.IDString(a.User.CompanyId),
	}

	// 删除任务
	scanTaskService.DeleteTask(a.MantisContext, req.Id, userInfo)
	a.ResSuccess()
}

// AddTask 新增任务
func (a *TaskController) AddTask(task artifact.ScanTask) {
	// 获取用户信息
	userInfo := dto.UserInfo{
		AdAccount: a.GetUser(),
		CompanyID: utils.IDString(a.User.CompanyId),
	}

	// 设置任务来源为Web
	task.TaskFrom = task2.TaskFromWeb

	// 保存任务并返回ID
	id := scanTaskService.AddOrUpdateTask(a.MantisContext, &task, userInfo)
	a.ResSuccessResult(id)
}

// UpdateTask 更新任务
func (a *TaskController) UpdateTask(task artifact.ScanTask) {
	// 获取用户信息
	userInfo := dto.UserInfo{
		AdAccount: a.GetUser(),
		CompanyID: utils.IDString(a.User.CompanyId),
	}

	// 设置任务来源为Web
	task.TaskFrom = task2.TaskFromWeb

	// 更新任务并返回ID
	id := scanTaskService.AddOrUpdateTask(a.MantisContext, &task, userInfo)
	a.ResSuccessResult(id)
}

// Exec 执行任务
func (a *TaskController) Exec(req TaskIdInPathReq) {
	// 获取用户信息
	userInfo := dto.UserInfo{
		AdAccount: a.GetUser(),
		CompanyID: utils.IDString(a.User.CompanyId),
	}

	// 执行任务并返回执行ID
	execId := scanTaskService.Exec(a.MantisContext, req.Id, userInfo, "")
	a.ResSuccessResult(execId)
}

// Abort 中止任务
func (a *TaskController) Abort(req TaskIdInPathReq) {
	// 获取用户信息
	userInfo := dto.UserInfo{
		AdAccount: a.GetUser(),
		CompanyID: utils.IDString(a.User.CompanyId),
	}

	// 中止任务
	scanTaskService.Abort(a.MantisContext, req.Id, userInfo)
	a.ResSuccess()
}
