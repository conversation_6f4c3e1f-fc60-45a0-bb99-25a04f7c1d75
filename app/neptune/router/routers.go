package router

import (
	"context"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/auditlog"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/audit"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/router/generated"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	panicHandler "git.zhonganinfo.com/zainfo/cube-mantis/pkg/middleware/panic"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/accesslog"
	authPkg "git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/authentication"
	authoPkg "git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/authorization"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/authority/ua"
	"github.com/go-playground/validator/v10"
	"github.com/gorilla/mux"
	"github.com/sirupsen/logrus"
)

func InstallRouter(ctx context.Context, env *configs.Configs, router *mux.Router) *mux.Router {
	handlerOpt := types.Option{
		Codec:        &codec.DefaultJsonEncoder,
		Validator:    validator.New(),
		LogRouteFunc: types.DefaultLogRouterFunc,
	}
	setMiddleware(env)
	generated.InstallRoutes(router, handlerOpt)
	return router
}

func setMiddleware(env *configs.Configs) {
	generated.SetAccessLogMiddleware(accesslog.WrapLoggingHandler(logrus.StandardLogger().Out))
	generated.SetRecoveryMiddleware(panicHandler.RecoveryHandler())
	generated.SetAuthenticationMiddleware(authPkg.WrapAuthenticationHandler(ua.GetDefaultUA(), authPkg.SsoConfig{
		UserCenterServer: env.Auth.UaUrl,
	}))
	generated.SetAuthorizationMiddleware(authoPkg.WrapAuthorizationHandler())
	ids := []string{"id", "publish_id", "task_id", "app_id", "auditId", "publishId"}
	generated.SetAuditLogMiddleware(auditlog.WrapAuditLogHandler(auditlog.NewCubeAuditLog(audit.GetDefaultAudit(), auditlog.WithDefaultIdSources(ids))))
}
