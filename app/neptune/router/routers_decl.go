package router

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/controller/artifact"
	artifactopenapi "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/controller/artifact/openapi"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/controller/quality"
	qualityopenapi "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/controller/quality/openapi"
	. "git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codegen/router/decl"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/auditlog"
)

const (
	accessLog       = "accessLog"
	recovery        = "recovery"
	authentication  = "authentication"
	authorization   = "authorization"
	auditLog        = "auditLog"
	authenticationx = "authentication-"
	authorizationx  = "authorization-"
	auditLogx       = "auditLog-"
)

const (
	NeptuneAuditResourceLabelKey     = auditlog.AuditResourceLabelKey + "=" + "制品扫描"
	NeptuneRiskAuditResourceLabelKey = auditlog.AuditResourceLabelKey + "=" + "代码扫描"
	MUSTGET                          = auditlog.AuditOpTypeMustGETLabelKey
	NeptuneAuditId                   = "auditlog.id=$resp.traceId"
)

var _ = Path("",
	Path("/neptune",
		Path("/v1",
			Middlewares([]string{recovery, accessLog, authentication, auditLog},
				Labels([]string{NeptuneAuditResourceLabelKey},
					Constructor(artifact.InitializeTemplateController,
						Path("/template",
							Path("/{type}/format", POST(artifact.DefaultTemplateController.Format, NeptuneAuditId)),
							Path("/{type}/download", GET(artifact.DefaultTemplateController.Download)),
						),
					),
					Constructor(artifact.InitializePlanController,
						Middleware(authorization,
							Path("/plan",
								POST(artifact.DefaultPlanController.AddPlan, NeptuneAuditId, "auth.code=MANTIS-NEPTUNE-PRODUCT-PLAN-CREATE"),
								PUT(artifact.DefaultPlanController.UpdatePlan, NeptuneAuditId, "auth.code=MANTIS-NEPTUNE-PRODUCT-PLAN-EDIT"),
								Path("/list", GET(artifact.DefaultPlanController.List, "auth.code=MANTIS-NEPTUNE-PRODUCT-PLAN-FLUSH")),
								Path("/{id}", DELETE(artifact.DefaultPlanController.DeletePlan, NeptuneAuditId, "auth.code=MANTIS-NEPTUNE-PRODUCT-PLAN-DELETE")),
								Path("/{id}/default/{v}", PUT(artifact.DefaultPlanController.Default, NeptuneAuditId, "auth.code=MANTIS-NEPTUNE-PRODUCT-PLAN-DEFAULT")),
								Middleware(authorizationx,
									Path("/{id}", GET(artifact.DefaultPlanController.Info)),
									Path("/{id}/bindTask", GET(artifact.DefaultPlanController.BindTask)),
								),
							),
						),
					),
					Constructor(artifact.InitializeTaskController,
						Path("/task",
							Middleware(authorization,
								POST(artifact.DefaultTaskController.AddTask, NeptuneAuditId, "auth.code=MANTIS-NEPTUNE-PRODUCT-TASK-CREATE"),
								PUT(artifact.DefaultTaskController.UpdateTask, NeptuneAuditId, "auth.code=MANTIS-NEPTUNE-PRODUCT-TASK"),
								Path("/{id}", DELETE(artifact.DefaultTaskController.DeleteTask, NeptuneAuditId, "auth.code=MANTIS-NEPTUNE-PRODUCT-TASK-DELETE")),
								Path("/{id}/exec", POST(artifact.DefaultTaskController.Exec, NeptuneAuditId, "auth.code=MANTIS-NEPTUNE-PRODUCT-TASK-EXEC")),
								Path("/{id}/abort", POST(artifact.DefaultTaskController.Abort, NeptuneAuditId, "auth.code=MANTIS-NEPTUNE-PRODUCT-TASK-STOP")),
							),
							Path("/list", GET(artifact.DefaultTaskController.List)),
							Path("/{id}/{eid}", GET(artifact.DefaultTaskController.Info)),
						),
					),
					Constructor(artifact.InitializeExecHisController,
						Path("/execHis",
							Path("/{id}/detail", GET(artifact.DefaultExecHisController.GetProductExecResult)),
							Path("/{id}/list", GET(artifact.DefaultExecHisController.GetExecHistList)),
							Path("/{id}/detail/log", GET(artifact.DefaultExecHisController.GetProductExecLog)),
						),
					),
					Constructor(artifact.InitializeReportController,
						Path("/report",
							Path("/{id}/detail", GET(artifact.DefaultReportController.Detail)),
							Path("/{id}/execHis/export", POST(artifact.DefaultReportController.ExecHisExport, NeptuneAuditId)),
							Path("/{id}/execHis/list", GET(artifact.DefaultReportController.List)),
						),
					),
					Constructor(artifact.InitializeRepoController,
						Path("/repo",
							Path("/instance/list", GET(artifact.DefaultRepoController.InstanceList)),
							Path("/instance/{id}/repoInfos", GET(artifact.DefaultRepoController.RepoInfos)),
							Path("/instance/{id}/products/{repoId}/{repoName}", GET(artifact.DefaultRepoController.Products)),
							Path("/instance/{id}/versions/{repoName}/{prodId}/{prodName}", GET(artifact.DefaultRepoController.Versions)),
							Path("/instance/{id}/latest/{repoId}/{repoName}", GET(artifact.DefaultRepoController.LatestProducts)),
							Path("/instance/{id}/versionPath/{versionId}", GET(artifact.DefaultRepoController.VersionPath)),
						),
					),
				),
			),
		),
		Path("/openapi/v1",
			Middlewares([]string{recovery, accessLog, auditLog},
				Labels([]string{NeptuneAuditResourceLabelKey, NeptuneAuditId},
					Constructor(artifact.InitializePipelineController,
						Path("/pipeline/trigger/product", POST(artifactopenapi.DefaultPipelineControler.ScanProduct)),
					),
					Constructor(artifact.InitializeResultController,
						Path("/result/{type}/{id}/form", POST(artifactopenapi.DefaultResultController.UploadFormResult)),
					),
				),
			),
		),
	),
	Path("/rick",
		Path("/api",
			Middlewares([]string{recovery, accessLog, authentication, auditLog},
				Labels([]string{NeptuneRiskAuditResourceLabelKey},
					Constructor(quality.InitializeCommonController,
						Path("/common",
							Path("/taskStatus", GET(quality.DefaultCommonController.GetTaskStatusEnumList)),
							Path("/languages", GET(quality.DefaultCommonController.GetLanguages)),
							Path("/severities", GET(quality.DefaultCommonController.GetSeverities)),
							Path("/branchList", GET(quality.DefaultCommonController.GetBranchList)),
							Path("/branchListByVcs", GET(quality.DefaultCommonController.GetBranchListByVcsPath)),
							Path("/branchInfo", GET(quality.DefaultCommonController.GetBranch)),
							Path("/scan/execTypes", GET(quality.DefaultCommonController.GetExecTypes)),
							Path("/scan/modes", GET(quality.DefaultCommonController.GetScanModes)),
							Path("/task/from/enums", GET(quality.DefaultCommonController.GetTaskFromEnums)),
							Path("/task/type/enums", GET(quality.DefaultCommonController.GetTaskTypeEnums)),
						),
					),
					Constructor(quality.InitializeScanProgrammeController,
						Path("/programme",
							Middleware(authorization,
								Path("/add", POST(quality.DefaultScanProgrammeController.Add, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-PLAN-CREATE")),
								Path("/delete/{id}", DELETE(quality.DefaultScanProgrammeController.Delete, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-PLAN-DELETE")),
								Path("/modify", POST(quality.DefaultScanProgrammeController.Modify, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-PLAN-CREATE")),
							),
							Path("/list", GET(quality.DefaultScanProgrammeController.List)),
							Path("/queryProfileCards/{id}", GET(quality.DefaultScanProgrammeController.QueryProfileCards)),
							Path("/query/taskList", GET(quality.DefaultScanProgrammeController.QueryTaskNameList)),
							Path("/query/profiles/{id}", GET(quality.DefaultScanProgrammeController.QueryProgrammeProfileEnum)),
							Path("/profile/active", PUT(quality.DefaultScanProgrammeController.SetProfileActive, NeptuneAuditId)),
							Path("/query/{id}", GET(quality.DefaultScanProgrammeController.Query)),
						),
					),
					Constructor(quality.InitializeScanProfileTemplateController,
						Path("/profileTemplate",
							Path("/query", GET(quality.DefaultScanProfileTemplateController.QueryProfileTemplateByLanguage)),
							Path("/save", POST(quality.DefaultScanProfileTemplateController.SaveScanProfileTemplate, NeptuneAuditId)),
							Path("/copy", POST(quality.DefaultScanProfileTemplateController.CopyScanProfileTemplate, NeptuneAuditId)),
							Path("/import", POST(quality.DefaultScanProfileTemplateController.ImportProfiles, NeptuneAuditId)),
							Path("/export/{id}", GET(quality.DefaultScanProfileTemplateController.ExportProfile)),
							Path("/{id}", DELETE(quality.DefaultScanProfileTemplateController.DeleteScanProfileTemplate, NeptuneAuditId)),
						),
					),
					Constructor(quality.InitializeSonarRuleController,
						Path("/sonarRule",
							Path("/search", GET(quality.DefaultSonarRuleController.SearchTemplateRules)),
							Path("/detail/{key}", GET(quality.DefaultSonarRuleController.ShowRuleDetail)),
							Path("/status/enums", GET(quality.DefaultSonarRuleController.RuleStatusList)),
							Path("/type/enums", GET(quality.DefaultSonarRuleController.RuleTypesList)),
							Path("/updateStatus", POST(quality.DefaultSonarRuleController.ActiveOrDeActiveRule, NeptuneAuditId)),
						),
					),
					Constructor(quality.InitializeScanIssuesController,
						Path("/issues",
							Path("/list", GET(quality.DefaultScanIssuesController.List)),
							Path("/components", GET(quality.DefaultScanIssuesController.Components)),
						),
					),
					Constructor(quality.InitializeTaskController,
						Path("/scanTask",
							Middleware(authorization,
								Path("/task/scan", POST(quality.DefaultTaskController.InsertScanTask, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-TASK-CREATE")),
								Path("/task/unit", POST(quality.DefaultTaskController.InsertTestTask, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-UNIT-TEST-CREATE")),
								Path("/task/scan", PUT(quality.DefaultTaskController.UpdateScanTask, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-TASK-CREATE")),
								Path("/task/unit", PUT(quality.DefaultTaskController.UpdateTestTask, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-UNIT-TEST-CREATE")),
								Path("/task/exec/scan", POST(quality.DefaultTaskController.ExecuteScanTask, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-TASK-EXECUTE")),
								Path("/task/exec/unit", POST(quality.DefaultTaskController.ExecuteTestTask, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-UNIT-TEST-EXECUTE")),
								Path("/task/delete/{id}/scan", DELETE(quality.DefaultTaskController.DeleteScanTask, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-TASK-DELETE")),
								Path("/task/delete/{id}/unit", DELETE(quality.DefaultTaskController.DeleteTestTask, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-UNIT-TEST-DELETE")),
								Path("/task/stop/{id}/scan", POST(quality.DefaultTaskController.StopScanTask, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-TASK-EXECUTE")),
								Path("/task/stop/{id}/unit", POST(quality.DefaultTaskController.StopTestTask, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-UNIT-TEST-EXECUTE")),
							),
							Path("/task/commitInfo", GET(quality.DefaultTaskController.GetCommitInfo)),
							Path("/task/info/{taskId}", GET(quality.DefaultTaskController.GetScanTestTask)),
							Path("/taskConfig/info/{taskId}", GET(quality.DefaultTaskController.QueryQualityGate)),
							Path("/overview/{taskId}", GET(quality.DefaultTaskController.QueryTaskOverview)),
							Path("/query", GET(quality.DefaultTaskController.QueryScanTestTaskPage)),
							Path("/history/query", GET(quality.DefaultTaskController.QueryScanTestExecHisPageByTaskId)),
							Path("/history/logs/{hisId}", GET(quality.DefaultTaskController.GetLogsByHisId)),
						),
					),
					Constructor(quality.InitializeScanFilterRuleController,
						Middleware(authorization,
							Path("/filter", POST(quality.DefaultScanFilterRuleController.CreateFilterRule, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-CONFIG-CREATE"),
								Path("", PUT(quality.DefaultScanFilterRuleController.ModifyFilterRule, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-CONFIG-CREATE")),
								Path("/app", POST(quality.DefaultScanFilterRuleController.SaveAppFilterRule, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-CONFIG-CREATE")),
								Path("/app", PUT(quality.DefaultScanFilterRuleController.UpdateAppFilterRule, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-CONFIG-CREATE")),
								Path("/app/delete/{id}", DELETE(quality.DefaultScanFilterRuleController.DeleteAppFilterRule, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-CONFIG-DELETE")),
								Path("/app/updateDescription", PUT(quality.DefaultScanFilterRuleController.UpdateDescription, NeptuneAuditId, "auth.code=MAGIC-NEPTUNE-CODE-CONFIG-CREATE")),
								Middleware(authorizationx,
									Path("/list/{relationId}/{type}", GET(quality.DefaultScanFilterRuleController.QueryFilterRule)),
									Path("/query/type", GET(quality.DefaultScanFilterRuleController.QueryFilterExclusionEnum)),
									Path("/app/list", GET(quality.DefaultScanFilterRuleController.QueryAppFilterRule)),
									Path("/app/info/{id}", GET(quality.DefaultScanFilterRuleController.QueryAppFilterInfo)),
									Path("/app/infoByAppId/{id}", GET(quality.DefaultScanFilterRuleController.QueryAppFilterInfoByAppId)),
								),
							),
						),
					),
				),
			),
		),
		Path("/openapi",
			Middlewares([]string{recovery, accessLog, auditLog},
				Labels([]string{NeptuneRiskAuditResourceLabelKey},
					Constructor(quality.InitializeBackController,
						Path("/back",
							Path("/sonarUpdateBackCall/{id}", POST(qualityopenapi.DefaultBackController.SonarUpdateBackCall)),
						),
					),
					Constructor(quality.InitializeScanTestCiController,
						Path("/{type}", POST(qualityopenapi.DefaultScanTestCiController.ScanTest)),
					),
					Constructor(quality.InitializeJacocoController,
						Path("/coverage",
							Path("/register", POST(qualityopenapi.DefaultJacocoController.PipelineRegister)),
							Path("/collect", POST(qualityopenapi.DefaultJacocoController.PipelineExec)),
							Path("/reset/auto", POST(qualityopenapi.DefaultJacocoController.AptRegister)),
							Path("/collect/auto", POST(qualityopenapi.DefaultJacocoController.AptExec)),
							Path("/callBack", POST(qualityopenapi.DefaultJacocoController.CallBack)),
						),
					),
				),
			),
		),
	),
)
