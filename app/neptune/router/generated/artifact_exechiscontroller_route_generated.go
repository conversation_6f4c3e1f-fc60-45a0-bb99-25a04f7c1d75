// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/controller/artifact"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// GetProductExecResult 获取产品执行结果
// GET Path("/neptune/v1/execHis/{id}/detail") -> artifact.ExecHisController.GetProductExecResult
func artifactExecHisControllerGetProductExecResultHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetProductExecResult",
		Patten:            "/neptune/v1/execHis/{id}/detail",
		Desc:              "GetProductExecResult 获取产品执行结果",
		ControllerPkgName: "artifact",
		ControllerName:    "ExecHisController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "制品扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := artifact.InitializeExecHisController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		idReq := artifact.ExecHisIdInPathReq{}
		if i, ok := decode.Implements(&idReq); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &idReq); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(idReq); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := artifact.QueryDTOReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetProductExecResult(idReq, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GetExecHistList 获取执行历史列表
// GET Path("/neptune/v1/execHis/{id}/list") -> artifact.ExecHisController.GetExecHistList
func artifactExecHisControllerGetExecHistListHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetExecHistList",
		Patten:            "/neptune/v1/execHis/{id}/list",
		Desc:              "GetExecHistList 获取执行历史列表",
		ControllerPkgName: "artifact",
		ControllerName:    "ExecHisController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "制品扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := artifact.InitializeExecHisController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		idReq := artifact.ExecHisIdInPathReq{}
		if i, ok := decode.Implements(&idReq); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &idReq); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(idReq); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := artifact.QueryDTOReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetExecHistList(idReq, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GetProductExecLog 获取产品执行日志
// GET Path("/neptune/v1/execHis/{id}/detail/log") -> artifact.ExecHisController.GetProductExecLog
func artifactExecHisControllerGetProductExecLogHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetProductExecLog",
		Patten:            "/neptune/v1/execHis/{id}/detail/log",
		Desc:              "GetProductExecLog 获取产品执行日志",
		ControllerPkgName: "artifact",
		ControllerName:    "ExecHisController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "制品扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := artifact.InitializeExecHisController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		idReq := artifact.ExecHisIdInPathReq{}
		if i, ok := decode.Implements(&idReq); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &idReq); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(idReq); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetProductExecLog(idReq)

		}
	}
	return chain.ThenFunc(HandleFunc)
}
