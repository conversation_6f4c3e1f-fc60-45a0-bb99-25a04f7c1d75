// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
	"github.com/gorilla/mux"
	"github.com/justinas/alice"
)

func InstallRoutes(mux *mux.Router, opt types.Option) {
	//generate from Pkg: artifact

	// GetProductExecResult 获取产品执行结果
	mux.Methods("GET").Path("/neptune/v1/execHis/{id}/detail").Handler(artifactExecHisControllerGetProductExecResultHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// GetExecHistList 获取执行历史列表
	mux.Methods("GET").Path("/neptune/v1/execHis/{id}/list").Handler(artifactExecHisControllerGetExecHistListHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// GetProductExecLog 获取产品执行日志
	mux.Methods("GET").Path("/neptune/v1/execHis/{id}/detail/log").Handler(artifactExecHisControllerGetProductExecLogHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/neptune/openapi/v1/pipeline/trigger/product").Handler(artifactPipelineControllerScanProductHandleFunc(alice.New(_recovery, _accessLog, _auditLog), opt))

	//
	mux.Methods("POST").Path("/neptune/v1/plan").Handler(artifactPlanControllerAddPlanHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/neptune/v1/plan").Handler(artifactPlanControllerUpdatePlanHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/plan/list").Handler(artifactPlanControllerListHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/neptune/v1/plan/{id}").Handler(artifactPlanControllerDeletePlanHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/neptune/v1/plan/{id}/default/{v}").Handler(artifactPlanControllerDefaultHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/plan/{id}").Handler(artifactPlanControllerInfoHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/plan/{id}/bindTask").Handler(artifactPlanControllerBindTaskHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	// InstanceList 获取仓库实例列表
	mux.Methods("GET").Path("/neptune/v1/repo/instance/list").Handler(artifactRepoControllerInstanceListHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// RepoInfos 获取仓库信息列表
	mux.Methods("GET").Path("/neptune/v1/repo/instance/{id}/repoInfos").Handler(artifactRepoControllerRepoInfosHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// Products 获取制品列表
	mux.Methods("GET").Path("/neptune/v1/repo/instance/{id}/products/{repoId}/{repoName}").Handler(artifactRepoControllerProductsHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// Versions 获取制品版本列表
	mux.Methods("GET").Path("/neptune/v1/repo/instance/{id}/versions/{repoName}/{prodId}/{prodName}").Handler(artifactRepoControllerVersionsHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// LatestProducts 获取最新制品列表
	mux.Methods("GET").Path("/neptune/v1/repo/instance/{id}/latest/{repoId}/{repoName}").Handler(artifactRepoControllerLatestProductsHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// VersionPath 获取版本路径
	mux.Methods("GET").Path("/neptune/v1/repo/instance/{id}/versionPath/{versionId}").Handler(artifactRepoControllerVersionPathHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	// Detail 获取报告详情
	mux.Methods("GET").Path("/neptune/v1/report/{id}/detail").Handler(artifactReportControllerDetailHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// ExecHisExport 导出执行历史
	mux.Methods("POST").Path("/neptune/v1/report/{id}/execHis/export").Handler(artifactReportControllerExecHisExportHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/report/{id}/execHis/list").Handler(artifactReportControllerListHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	// UploadFormResult 上传表单结果文件
	mux.Methods("POST").Path("/neptune/openapi/v1/result/{type}/{id}/form").Handler(artifactResultControllerUploadFormResultHandleFunc(alice.New(_recovery, _accessLog, _auditLog), opt))

	// AddTask 新增任务
	mux.Methods("POST").Path("/neptune/v1/task").Handler(artifactTaskControllerAddTaskHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// UpdateTask 更新任务
	mux.Methods("PUT").Path("/neptune/v1/task").Handler(artifactTaskControllerUpdateTaskHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// DeleteTask 删除任务
	mux.Methods("DELETE").Path("/neptune/v1/task/{id}").Handler(artifactTaskControllerDeleteTaskHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// Exec 执行任务
	mux.Methods("POST").Path("/neptune/v1/task/{id}/exec").Handler(artifactTaskControllerExecHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// Abort 中止任务
	mux.Methods("POST").Path("/neptune/v1/task/{id}/abort").Handler(artifactTaskControllerAbortHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/task/list").Handler(artifactTaskControllerListHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/task/{id}/{eid}").Handler(artifactTaskControllerInfoHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	// Format 格式化上传的模板文件
	mux.Methods("POST").Path("/neptune/v1/template/{type}/format").Handler(artifactTemplateControllerFormatHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// Download 下载模板文件
	mux.Methods("GET").Path("/neptune/v1/template/{type}/download").Handler(artifactTemplateControllerDownloadHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	//generate from Pkg: quality

	//
	mux.Methods("POST").Path("/rick/openapi/back/sonarUpdateBackCall/{id}").Handler(qualityBackControllerSonarUpdateBackCallHandleFunc(alice.New(_recovery, _accessLog, _auditLog), opt))

	// GetTaskStatusEnumList 获取任务状态枚举列表
	mux.Methods("GET").Path("/rick/api/common/taskStatus").Handler(qualityCommonControllerGetTaskStatusEnumListHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// GetLanguages 获取支持的语言列表
	mux.Methods("GET").Path("/rick/api/common/languages").Handler(qualityCommonControllerGetLanguagesHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// GetSeverities 获取严重级别列表
	mux.Methods("GET").Path("/rick/api/common/severities").Handler(qualityCommonControllerGetSeveritiesHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// GetBranchList 获取分支列表
	mux.Methods("GET").Path("/rick/api/common/branchList").Handler(qualityCommonControllerGetBranchListHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// GetBranchListByVcsPath 根据版本控制路径获取分支列表
	mux.Methods("GET").Path("/rick/api/common/branchListByVcs").Handler(qualityCommonControllerGetBranchListByVcsPathHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// GetBranch 获取特定分支信息
	mux.Methods("GET").Path("/rick/api/common/branchInfo").Handler(qualityCommonControllerGetBranchHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// GetExecTypes 获取执行类型列表
	mux.Methods("GET").Path("/rick/api/common/scan/execTypes").Handler(qualityCommonControllerGetExecTypesHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// GetScanModes 获取扫描模式列表
	mux.Methods("GET").Path("/rick/api/common/scan/modes").Handler(qualityCommonControllerGetScanModesHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// GetTaskFromEnums 获取任务来源枚举列表
	mux.Methods("GET").Path("/rick/api/common/task/from/enums").Handler(qualityCommonControllerGetTaskFromEnumsHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// GetTaskTypeEnums 获取任务类型枚举列表
	mux.Methods("GET").Path("/rick/api/common/task/type/enums").Handler(qualityCommonControllerGetTaskTypeEnumsHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/rick/openapi/coverage/register").Handler(qualityJacocoControllerPipelineRegisterHandleFunc(alice.New(_recovery, _accessLog, _auditLog), opt))
	//
	mux.Methods("POST").Path("/rick/openapi/coverage/collect").Handler(qualityJacocoControllerPipelineExecHandleFunc(alice.New(_recovery, _accessLog, _auditLog), opt))
	//
	mux.Methods("POST").Path("/rick/openapi/coverage/reset/auto").Handler(qualityJacocoControllerAptRegisterHandleFunc(alice.New(_recovery, _accessLog, _auditLog), opt))
	//
	mux.Methods("POST").Path("/rick/openapi/coverage/collect/auto").Handler(qualityJacocoControllerAptExecHandleFunc(alice.New(_recovery, _accessLog, _auditLog), opt))
	//
	mux.Methods("POST").Path("/rick/openapi/coverage/callBack").Handler(qualityJacocoControllerCallBackHandleFunc(alice.New(_recovery, _accessLog, _auditLog), opt))

	// CreateFilterRule 创建过滤规则
	mux.Methods("POST").Path("/rick/api/filter").Handler(qualityScanFilterRuleControllerCreateFilterRuleHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// ModifyFilterRule 修改过滤规则
	mux.Methods("PUT").Path("/rick/api/filter").Handler(qualityScanFilterRuleControllerModifyFilterRuleHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// SaveAppFilterRule 保存应用过滤规则
	mux.Methods("POST").Path("/rick/api/filter/app").Handler(qualityScanFilterRuleControllerSaveAppFilterRuleHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// UpdateAppFilterRule 更新应用过滤规则
	mux.Methods("PUT").Path("/rick/api/filter/app").Handler(qualityScanFilterRuleControllerUpdateAppFilterRuleHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// DeleteAppFilterRule 删除应用过滤规则
	mux.Methods("DELETE").Path("/rick/api/filter/app/delete/{id}").Handler(qualityScanFilterRuleControllerDeleteAppFilterRuleHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// UpdateDescription 更新应用过滤规则描述
	mux.Methods("PUT").Path("/rick/api/filter/app/updateDescription").Handler(qualityScanFilterRuleControllerUpdateDescriptionHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// QueryFilterRule 查询过滤规则
	mux.Methods("GET").Path("/rick/api/filter/list/{relationId}/{type}").Handler(qualityScanFilterRuleControllerQueryFilterRuleHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// QueryFilterExclusionEnum 查询过滤排除枚举
	mux.Methods("GET").Path("/rick/api/filter/query/type").Handler(qualityScanFilterRuleControllerQueryFilterExclusionEnumHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// QueryAppFilterRule 查询应用过滤规则
	mux.Methods("GET").Path("/rick/api/filter/app/list").Handler(qualityScanFilterRuleControllerQueryAppFilterRuleHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// QueryAppFilterInfo 查询应用过滤信息
	mux.Methods("GET").Path("/rick/api/filter/app/info/{id}").Handler(qualityScanFilterRuleControllerQueryAppFilterInfoHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// QueryAppFilterInfoByAppId 通过应用ID查询应用过滤信息
	mux.Methods("GET").Path("/rick/api/filter/app/infoByAppId/{id}").Handler(qualityScanFilterRuleControllerQueryAppFilterInfoByAppIdHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	// List 查询扫描问题列表
	mux.Methods("GET").Path("/rick/api/issues/list").Handler(qualityScanIssuesControllerListHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// Components 查询单元组件列表
	mux.Methods("GET").Path("/rick/api/issues/components").Handler(qualityScanIssuesControllerComponentsHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	// QueryProfileTemplateByLanguage 按语言查询配置模板
	mux.Methods("GET").Path("/rick/api/profileTemplate/query").Handler(qualityScanProfileTemplateControllerQueryProfileTemplateByLanguageHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// SaveScanProfileTemplate 保存扫描配置模板
	mux.Methods("POST").Path("/rick/api/profileTemplate/save").Handler(qualityScanProfileTemplateControllerSaveScanProfileTemplateHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// CopyScanProfileTemplate 复制扫描配置模板
	mux.Methods("POST").Path("/rick/api/profileTemplate/copy").Handler(qualityScanProfileTemplateControllerCopyScanProfileTemplateHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// ImportProfiles 导入配置文件
	mux.Methods("POST").Path("/rick/api/profileTemplate/import").Handler(qualityScanProfileTemplateControllerImportProfilesHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// ExportProfile 导出配置文件
	mux.Methods("GET").Path("/rick/api/profileTemplate/export/{id}").Handler(qualityScanProfileTemplateControllerExportProfileHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// DeleteScanProfileTemplate 删除扫描配置模板
	mux.Methods("DELETE").Path("/rick/api/profileTemplate/{id}").Handler(qualityScanProfileTemplateControllerDeleteScanProfileTemplateHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	// Add 添加扫描方案
	mux.Methods("POST").Path("/rick/api/programme/add").Handler(qualityScanProgrammeControllerAddHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// Delete 删除扫描方案
	mux.Methods("DELETE").Path("/rick/api/programme/delete/{id}").Handler(qualityScanProgrammeControllerDeleteHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// Modify 修改扫描方案
	mux.Methods("POST").Path("/rick/api/programme/modify").Handler(qualityScanProgrammeControllerModifyHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// List 查询扫描问题列表
	mux.Methods("GET").Path("/rick/api/programme/list").Handler(qualityScanProgrammeControllerListHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// QueryProfileCards 查询方案配置文件卡片
	mux.Methods("GET").Path("/rick/api/programme/queryProfileCards/{id}").Handler(qualityScanProgrammeControllerQueryProfileCardsHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// QueryTaskNameList 查询任务名称列表
	mux.Methods("GET").Path("/rick/api/programme/query/taskList").Handler(qualityScanProgrammeControllerQueryTaskNameListHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// QueryProgrammeProfileEnum 查询方案配置文件枚举
	mux.Methods("GET").Path("/rick/api/programme/query/profiles/{id}").Handler(qualityScanProgrammeControllerQueryProgrammeProfileEnumHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// SetProfileActive 设置配置文件激活状态
	mux.Methods("PUT").Path("/rick/api/programme/profile/active").Handler(qualityScanProgrammeControllerSetProfileActiveHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// Query 查询扫描方案详情
	mux.Methods("GET").Path("/rick/api/programme/query/{id}").Handler(qualityScanProgrammeControllerQueryHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	// ScanTest 执行扫描测试任务并返回报告URL
	mux.Methods("POST").Path("/rick/openapi/{type}").Handler(qualityScanTestCiControllerScanTestHandleFunc(alice.New(_recovery, _accessLog, _auditLog), opt))

	// SearchTemplateRules 搜索模板规则
	mux.Methods("GET").Path("/rick/api/sonarRule/search").Handler(qualitySonarRuleControllerSearchTemplateRulesHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// ShowRuleDetail 显示规则详情
	mux.Methods("GET").Path("/rick/api/sonarRule/detail/{key}").Handler(qualitySonarRuleControllerShowRuleDetailHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// RuleStatusList 获取规则状态列表
	mux.Methods("GET").Path("/rick/api/sonarRule/status/enums").Handler(qualitySonarRuleControllerRuleStatusListHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// RuleTypesList 获取规则类型列表
	mux.Methods("GET").Path("/rick/api/sonarRule/type/enums").Handler(qualitySonarRuleControllerRuleTypesListHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// ActiveOrDeActiveRule 激活或禁用规则
	mux.Methods("POST").Path("/rick/api/sonarRule/updateStatus").Handler(qualitySonarRuleControllerActiveOrDeActiveRuleHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	// InsertScanTask 新增扫描任务
	mux.Methods("POST").Path("/rick/api/scanTask/task/scan").Handler(qualityTaskControllerInsertScanTaskHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// InsertTestTask 新增单元测试任务
	mux.Methods("POST").Path("/rick/api/scanTask/task/unit").Handler(qualityTaskControllerInsertTestTaskHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// UpdateScanTask 更新扫描任务
	mux.Methods("PUT").Path("/rick/api/scanTask/task/scan").Handler(qualityTaskControllerUpdateScanTaskHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// UpdateTestTask 更新单元测试任务
	mux.Methods("PUT").Path("/rick/api/scanTask/task/unit").Handler(qualityTaskControllerUpdateTestTaskHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// ExecuteScanTask 执行代码扫描任务
	mux.Methods("POST").Path("/rick/api/scanTask/task/exec/scan").Handler(qualityTaskControllerExecuteScanTaskHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// ExecuteTestTask 执行单元测试任务
	mux.Methods("POST").Path("/rick/api/scanTask/task/exec/unit").Handler(qualityTaskControllerExecuteTestTaskHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// DeleteScanTask 删除扫描任务
	mux.Methods("DELETE").Path("/rick/api/scanTask/task/delete/{id}/scan").Handler(qualityTaskControllerDeleteScanTaskHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// DeleteTestTask 删除单元测试任务
	mux.Methods("DELETE").Path("/rick/api/scanTask/task/delete/{id}/unit").Handler(qualityTaskControllerDeleteTestTaskHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// StopScanTask 停止扫描任务
	mux.Methods("POST").Path("/rick/api/scanTask/task/stop/{id}/scan").Handler(qualityTaskControllerStopScanTaskHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// StopTestTask 停止测试任务
	mux.Methods("POST").Path("/rick/api/scanTask/task/stop/{id}/unit").Handler(qualityTaskControllerStopTestTaskHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	// GetCommitInfo 获取代码提交信息
	mux.Methods("GET").Path("/rick/api/scanTask/task/commitInfo").Handler(qualityTaskControllerGetCommitInfoHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// GetScanTestTask 获取扫描测试任务详情
	mux.Methods("GET").Path("/rick/api/scanTask/task/info/{taskId}").Handler(qualityTaskControllerGetScanTestTaskHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// QueryQualityGate 查询质量门禁
	mux.Methods("GET").Path("/rick/api/scanTask/taskConfig/info/{taskId}").Handler(qualityTaskControllerQueryQualityGateHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// QueryTaskOverview 查询任务概览
	mux.Methods("GET").Path("/rick/api/scanTask/overview/{taskId}").Handler(qualityTaskControllerQueryTaskOverviewHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// QueryScanTestTaskPage 分页查询扫描测试任务
	mux.Methods("GET").Path("/rick/api/scanTask/query").Handler(qualityTaskControllerQueryScanTestTaskPageHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// QueryScanTestExecHisPageByTaskId 分页查询任务执行历史
	mux.Methods("GET").Path("/rick/api/scanTask/history/query").Handler(qualityTaskControllerQueryScanTestExecHisPageByTaskIdHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	// GetLogsByHisId 获取执行历史日志
	mux.Methods("GET").Path("/rick/api/scanTask/history/logs/{hisId}").Handler(qualityTaskControllerGetLogsByHisIdHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

}
