// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/controller/quality"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// InsertScanTask 新增扫描任务
// POST Path("/rick/api/scanTask/task/scan") -> quality.TaskController.InsertScanTask
func qualityTaskControllerInsertScanTaskHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "InsertScanTask",
		Patten:            "/rick/api/scanTask/task/scan",
		Desc:              "InsertScanTask 新增扫描任务",
		ControllerPkgName: "quality",
		ControllerName:    "TaskController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
			"auth.code":         "MAGIC-NEPTUNE-CODE-TASK-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		configDTO := dto.ScanTestTaskConfigDTO{}
		if i, ok := decode.Implements(&configDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &configDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(configDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.InsertScanTask(configDTO)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// InsertTestTask 新增单元测试任务
// POST Path("/rick/api/scanTask/task/unit") -> quality.TaskController.InsertTestTask
func qualityTaskControllerInsertTestTaskHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "InsertTestTask",
		Patten:            "/rick/api/scanTask/task/unit",
		Desc:              "InsertTestTask 新增单元测试任务",
		ControllerPkgName: "quality",
		ControllerName:    "TaskController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
			"auth.code":         "MAGIC-NEPTUNE-CODE-UNIT-TEST-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		configDTO := dto.ScanTestTaskConfigDTO{}
		if i, ok := decode.Implements(&configDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &configDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(configDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.InsertTestTask(configDTO)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// UpdateScanTask 更新扫描任务
// PUT Path("/rick/api/scanTask/task/scan") -> quality.TaskController.UpdateScanTask
func qualityTaskControllerUpdateScanTaskHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateScanTask",
		Patten:            "/rick/api/scanTask/task/scan",
		Desc:              "UpdateScanTask 更新扫描任务",
		ControllerPkgName: "quality",
		ControllerName:    "TaskController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
			"auth.code":         "MAGIC-NEPTUNE-CODE-TASK-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		configDTO := dto.ScanTestTaskConfigDTO{}
		if i, ok := decode.Implements(&configDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &configDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(configDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateScanTask(configDTO)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// UpdateTestTask 更新单元测试任务
// PUT Path("/rick/api/scanTask/task/unit") -> quality.TaskController.UpdateTestTask
func qualityTaskControllerUpdateTestTaskHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateTestTask",
		Patten:            "/rick/api/scanTask/task/unit",
		Desc:              "UpdateTestTask 更新单元测试任务",
		ControllerPkgName: "quality",
		ControllerName:    "TaskController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
			"auth.code":         "MAGIC-NEPTUNE-CODE-UNIT-TEST-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		configDTO := dto.ScanTestTaskConfigDTO{}
		if i, ok := decode.Implements(&configDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &configDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(configDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateTestTask(configDTO)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// ExecuteScanTask 执行代码扫描任务
// POST Path("/rick/api/scanTask/task/exec/scan") -> quality.TaskController.ExecuteScanTask
func qualityTaskControllerExecuteScanTaskHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ExecuteScanTask",
		Patten:            "/rick/api/scanTask/task/exec/scan",
		Desc:              "ExecuteScanTask 执行代码扫描任务",
		ControllerPkgName: "quality",
		ControllerName:    "TaskController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
			"auth.code":         "MAGIC-NEPTUNE-CODE-TASK-EXECUTE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		reqDto := dto.ScanTestWebExecReqDTO{}
		if i, ok := decode.Implements(&reqDto); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &reqDto); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(reqDto); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ExecuteScanTask(reqDto)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// ExecuteTestTask 执行单元测试任务
// POST Path("/rick/api/scanTask/task/exec/unit") -> quality.TaskController.ExecuteTestTask
func qualityTaskControllerExecuteTestTaskHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ExecuteTestTask",
		Patten:            "/rick/api/scanTask/task/exec/unit",
		Desc:              "ExecuteTestTask 执行单元测试任务",
		ControllerPkgName: "quality",
		ControllerName:    "TaskController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
			"auth.code":         "MAGIC-NEPTUNE-CODE-UNIT-TEST-EXECUTE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		reqDto := dto.ScanTestWebExecReqDTO{}
		if i, ok := decode.Implements(&reqDto); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &reqDto); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(reqDto); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ExecuteTestTask(reqDto)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DeleteScanTask 删除扫描任务
// DELETE Path("/rick/api/scanTask/task/delete/{id}/scan") -> quality.TaskController.DeleteScanTask
func qualityTaskControllerDeleteScanTaskHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "DeleteScanTask",
		Patten:            "/rick/api/scanTask/task/delete/{id}/scan",
		Desc:              "DeleteScanTask 删除扫描任务",
		ControllerPkgName: "quality",
		ControllerName:    "TaskController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
			"auth.code":         "MAGIC-NEPTUNE-CODE-TASK-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := quality.TaskControllerIdIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.DeleteScanTask(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DeleteTestTask 删除单元测试任务
// DELETE Path("/rick/api/scanTask/task/delete/{id}/unit") -> quality.TaskController.DeleteTestTask
func qualityTaskControllerDeleteTestTaskHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "DeleteTestTask",
		Patten:            "/rick/api/scanTask/task/delete/{id}/unit",
		Desc:              "DeleteTestTask 删除单元测试任务",
		ControllerPkgName: "quality",
		ControllerName:    "TaskController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
			"auth.code":         "MAGIC-NEPTUNE-CODE-UNIT-TEST-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := quality.TaskControllerIdIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.DeleteTestTask(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// StopScanTask 停止扫描任务
// POST Path("/rick/api/scanTask/task/stop/{id}/scan") -> quality.TaskController.StopScanTask
func qualityTaskControllerStopScanTaskHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "StopScanTask",
		Patten:            "/rick/api/scanTask/task/stop/{id}/scan",
		Desc:              "StopScanTask 停止扫描任务",
		ControllerPkgName: "quality",
		ControllerName:    "TaskController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
			"auth.code":         "MAGIC-NEPTUNE-CODE-TASK-EXECUTE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := quality.TaskControllerIdIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.StopScanTask(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// StopTestTask 停止测试任务
// POST Path("/rick/api/scanTask/task/stop/{id}/unit") -> quality.TaskController.StopTestTask
func qualityTaskControllerStopTestTaskHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "StopTestTask",
		Patten:            "/rick/api/scanTask/task/stop/{id}/unit",
		Desc:              "StopTestTask 停止测试任务",
		ControllerPkgName: "quality",
		ControllerName:    "TaskController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
			"auth.code":         "MAGIC-NEPTUNE-CODE-UNIT-TEST-EXECUTE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := quality.TaskControllerIdIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.StopTestTask(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GetCommitInfo 获取代码提交信息
// GET Path("/rick/api/scanTask/task/commitInfo") -> quality.TaskController.GetCommitInfo
func qualityTaskControllerGetCommitInfoHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetCommitInfo",
		Patten:            "/rick/api/scanTask/task/commitInfo",
		Desc:              "GetCommitInfo 获取代码提交信息",
		ControllerPkgName: "quality",
		ControllerName:    "TaskController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		schemaReq := quality.GetCommitInfoSchemaReq{}
		if i, ok := decode.Implements(&schemaReq); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &schemaReq); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(schemaReq); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetCommitInfo(schemaReq)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GetScanTestTask 获取扫描测试任务详情
// GET Path("/rick/api/scanTask/task/info/{taskId}") -> quality.TaskController.GetScanTestTask
func qualityTaskControllerGetScanTestTaskHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetScanTestTask",
		Patten:            "/rick/api/scanTask/task/info/{taskId}",
		Desc:              "GetScanTestTask 获取扫描测试任务详情",
		ControllerPkgName: "quality",
		ControllerName:    "TaskController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := quality.TaskIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetScanTestTask(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// QueryQualityGate 查询质量门禁
// GET Path("/rick/api/scanTask/taskConfig/info/{taskId}") -> quality.TaskController.QueryQualityGate
func qualityTaskControllerQueryQualityGateHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "QueryQualityGate",
		Patten:            "/rick/api/scanTask/taskConfig/info/{taskId}",
		Desc:              "QueryQualityGate 查询质量门禁",
		ControllerPkgName: "quality",
		ControllerName:    "TaskController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := quality.TaskIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.QueryQualityGate(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// QueryTaskOverview 查询任务概览
// GET Path("/rick/api/scanTask/overview/{taskId}") -> quality.TaskController.QueryTaskOverview
func qualityTaskControllerQueryTaskOverviewHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "QueryTaskOverview",
		Patten:            "/rick/api/scanTask/overview/{taskId}",
		Desc:              "QueryTaskOverview 查询任务概览",
		ControllerPkgName: "quality",
		ControllerName:    "TaskController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		pathReq := quality.TaskIdInPathReq{}
		if i, ok := decode.Implements(&pathReq); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &pathReq); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(pathReq); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		schemaReq := quality.HisIdInSchemaReq{}
		if i, ok := decode.Implements(&schemaReq); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &schemaReq); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(schemaReq); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.QueryTaskOverview(pathReq, schemaReq)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// QueryScanTestTaskPage 分页查询扫描测试任务
// GET Path("/rick/api/scanTask/query") -> quality.TaskController.QueryScanTestTaskPage
func qualityTaskControllerQueryScanTestTaskPageHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "QueryScanTestTaskPage",
		Patten:            "/rick/api/scanTask/query",
		Desc:              "QueryScanTestTaskPage 分页查询扫描测试任务",
		ControllerPkgName: "quality",
		ControllerName:    "TaskController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		search := dto.ScanTestTaskSearchDTO{}
		if i, ok := decode.Implements(&search); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &search); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(search); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := gormx.PageRequest{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.QueryScanTestTaskPage(search, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// QueryScanTestExecHisPageByTaskId 分页查询任务执行历史
// GET Path("/rick/api/scanTask/history/query") -> quality.TaskController.QueryScanTestExecHisPageByTaskId
func qualityTaskControllerQueryScanTestExecHisPageByTaskIdHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "QueryScanTestExecHisPageByTaskId",
		Patten:            "/rick/api/scanTask/history/query",
		Desc:              "QueryScanTestExecHisPageByTaskId 分页查询任务执行历史",
		ControllerPkgName: "quality",
		ControllerName:    "TaskController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		search := dto.ScanTestTaskSearchDTO{}
		if i, ok := decode.Implements(&search); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &search); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(search); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := gormx.PageRequest{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.QueryScanTestExecHisPageByTaskId(search, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GetLogsByHisId 获取执行历史日志
// GET Path("/rick/api/scanTask/history/logs/{hisId}") -> quality.TaskController.GetLogsByHisId
func qualityTaskControllerGetLogsByHisIdHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetLogsByHisId",
		Patten:            "/rick/api/scanTask/history/logs/{hisId}",
		Desc:              "GetLogsByHisId 获取执行历史日志",
		ControllerPkgName: "quality",
		ControllerName:    "TaskController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := quality.HisIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetLogsByHisId(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}
