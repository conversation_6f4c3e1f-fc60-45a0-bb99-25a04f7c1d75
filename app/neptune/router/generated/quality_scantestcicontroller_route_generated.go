// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/controller/quality/openapi"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/controller/quality"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// ScanTest 执行扫描测试任务并返回报告URL
// POST Path("/rick/openapi/{type}") -> quality.ScanTestCiController.ScanTest
func qualityScanTestCiControllerScanTestHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ScanTest",
		Patten:            "/rick/openapi/{type}",
		Desc:              "ScanTest 执行扫描测试任务并返回报告URL",
		ControllerPkgName: "quality",
		ControllerName:    "ScanTestCiController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "accessLog", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanTestCiController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		infoDto := dto.ScanTestInfoDTO{}
		if i, ok := decode.Implements(&infoDto); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &infoDto); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(infoDto); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := openapi.CiJobTypeInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ScanTest(infoDto, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}
