// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/controller/artifact"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// InstanceList 获取仓库实例列表
// GET Path("/neptune/v1/repo/instance/list") -> artifact.RepoController.InstanceList
func artifactRepoControllerInstanceListHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "InstanceList",
		Patten:            "/neptune/v1/repo/instance/list",
		Desc:              "InstanceList 获取仓库实例列表",
		ControllerPkgName: "artifact",
		ControllerName:    "RepoController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "制品扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := artifact.InitializeRepoController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.InstanceList()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// RepoInfos 获取仓库信息列表
// GET Path("/neptune/v1/repo/instance/{id}/repoInfos") -> artifact.RepoController.RepoInfos
func artifactRepoControllerRepoInfosHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RepoInfos",
		Patten:            "/neptune/v1/repo/instance/{id}/repoInfos",
		Desc:              "RepoInfos 获取仓库信息列表",
		ControllerPkgName: "artifact",
		ControllerName:    "RepoController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "制品扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := artifact.InitializeRepoController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := artifact.RepoIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		schema1 := artifact.RepoSchemaReq{}
		if i, ok := decode.Implements(&schema1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &schema1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(schema1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.RepoInfos(req1, schema1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// Products 获取制品列表
// GET Path("/neptune/v1/repo/instance/{id}/products/{repoId}/{repoName}") -> artifact.RepoController.Products
func artifactRepoControllerProductsHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Products",
		Patten:            "/neptune/v1/repo/instance/{id}/products/{repoId}/{repoName}",
		Desc:              "Products 获取制品列表",
		ControllerPkgName: "artifact",
		ControllerName:    "RepoController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "制品扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := artifact.InitializeRepoController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := artifact.RepoLatestProductsReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		schema1 := artifact.RepoSchemaReq{}
		if i, ok := decode.Implements(&schema1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &schema1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(schema1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Products(req1, schema1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// Versions 获取制品版本列表
// GET Path("/neptune/v1/repo/instance/{id}/versions/{repoName}/{prodId}/{prodName}") -> artifact.RepoController.Versions
func artifactRepoControllerVersionsHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Versions",
		Patten:            "/neptune/v1/repo/instance/{id}/versions/{repoName}/{prodId}/{prodName}",
		Desc:              "Versions 获取制品版本列表",
		ControllerPkgName: "artifact",
		ControllerName:    "RepoController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "制品扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := artifact.InitializeRepoController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := artifact.RepoVersionsReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		schema1 := artifact.RepoSchemaReq{}
		if i, ok := decode.Implements(&schema1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &schema1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(schema1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Versions(req1, schema1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// LatestProducts 获取最新制品列表
// GET Path("/neptune/v1/repo/instance/{id}/latest/{repoId}/{repoName}") -> artifact.RepoController.LatestProducts
func artifactRepoControllerLatestProductsHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "LatestProducts",
		Patten:            "/neptune/v1/repo/instance/{id}/latest/{repoId}/{repoName}",
		Desc:              "LatestProducts 获取最新制品列表",
		ControllerPkgName: "artifact",
		ControllerName:    "RepoController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "制品扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := artifact.InitializeRepoController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := artifact.RepoLatestProductsReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		schema1 := artifact.RepoSchemaReq{}
		if i, ok := decode.Implements(&schema1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &schema1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(schema1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.LatestProducts(req1, schema1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// VersionPath 获取版本路径
// GET Path("/neptune/v1/repo/instance/{id}/versionPath/{versionId}") -> artifact.RepoController.VersionPath
func artifactRepoControllerVersionPathHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "VersionPath",
		Patten:            "/neptune/v1/repo/instance/{id}/versionPath/{versionId}",
		Desc:              "VersionPath 获取版本路径",
		ControllerPkgName: "artifact",
		ControllerName:    "RepoController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "制品扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := artifact.InitializeRepoController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := artifact.RepoVersionPath{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		schema1 := artifact.RepoSchemaReq{}
		if i, ok := decode.Implements(&schema1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &schema1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(schema1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.VersionPath(req1, schema1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}
