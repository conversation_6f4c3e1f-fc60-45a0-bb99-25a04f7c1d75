// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality/help"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/controller/quality"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// SearchTemplateRules 搜索模板规则
// GET Path("/rick/api/sonarRule/search") -> quality.SonarRuleController.SearchTemplateRules
func qualitySonarRuleControllerSearchTemplateRulesHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "SearchTemplateRules",
		Patten:            "/rick/api/sonarRule/search",
		Desc:              "SearchTemplateRules 搜索模板规则",
		ControllerPkgName: "quality",
		ControllerName:    "SonarRuleController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeSonarRuleController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		search := help.SonarRuleSearchReq{}
		if i, ok := decode.Implements(&search); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &search); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(search); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.SearchTemplateRules(search)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// ShowRuleDetail 显示规则详情
// GET Path("/rick/api/sonarRule/detail/{key}") -> quality.SonarRuleController.ShowRuleDetail
func qualitySonarRuleControllerShowRuleDetailHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ShowRuleDetail",
		Patten:            "/rick/api/sonarRule/detail/{key}",
		Desc:              "ShowRuleDetail 显示规则详情",
		ControllerPkgName: "quality",
		ControllerName:    "SonarRuleController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeSonarRuleController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := quality.SonarRuleKeyInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ShowRuleDetail(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// RuleStatusList 获取规则状态列表
// GET Path("/rick/api/sonarRule/status/enums") -> quality.SonarRuleController.RuleStatusList
func qualitySonarRuleControllerRuleStatusListHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RuleStatusList",
		Patten:            "/rick/api/sonarRule/status/enums",
		Desc:              "RuleStatusList 获取规则状态列表",
		ControllerPkgName: "quality",
		ControllerName:    "SonarRuleController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeSonarRuleController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.RuleStatusList()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// RuleTypesList 获取规则类型列表
// GET Path("/rick/api/sonarRule/type/enums") -> quality.SonarRuleController.RuleTypesList
func qualitySonarRuleControllerRuleTypesListHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RuleTypesList",
		Patten:            "/rick/api/sonarRule/type/enums",
		Desc:              "RuleTypesList 获取规则类型列表",
		ControllerPkgName: "quality",
		ControllerName:    "SonarRuleController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeSonarRuleController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.RuleTypesList()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// ActiveOrDeActiveRule 激活或禁用规则
// POST Path("/rick/api/sonarRule/updateStatus") -> quality.SonarRuleController.ActiveOrDeActiveRule
func qualitySonarRuleControllerActiveOrDeActiveRuleHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ActiveOrDeActiveRule",
		Patten:            "/rick/api/sonarRule/updateStatus",
		Desc:              "ActiveOrDeActiveRule 激活或禁用规则",
		ControllerPkgName: "quality",
		ControllerName:    "SonarRuleController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeSonarRuleController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.ActiveDeActiveRuleReqDTO{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ActiveOrDeActiveRule(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}
