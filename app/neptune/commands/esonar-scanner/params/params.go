package params

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/uuid"
	"github.com/duke-git/lancet/v2/netutil"
	"github.com/spf13/pflag"
)

// 常量定义
const (
	TypeScan = "scan" // 扫描任务
	TypeUnit = "unit" // 单元测试任务

	ModeIncrement = "increment" // 增量扫描
	ModeTotal     = "total"     // 全量扫描

	AppTypeFront = "front" // 前端应用

	LanguageJava    = "java"                               // Java语言
	MavenConfigPath = "/usr/share/maven/conf/settings.xml" // Maven配置路径

	JavaVersion8  = "Java8"  // Java 8版本
	JavaVersion11 = "Java11" // Java 11版本
	JavaVersion17 = "Java17" // Java 17版本

	Jdk8Home  = "/tools/jdk8"                 // JDK 8路径
	Jdk11Home = "/usr/lib/jvm/java-11-oracle" // JDK 11路径
	Jdk17Home = "/tools/jdk17"                // JDK 17路径
)

// QualityScanBizParam 定义业务参数结构
type QualityScanBizParam struct {
	CallBack    string `json:"callBack"`    // 任务记录ID
	Type        string `json:"type"`        // 任务类型: unit(单测)/scan(扫描)
	Mode        string `json:"mode"`        // 模式: increment(增量)/total(全量)
	Language    string `json:"language"`    // 语言: java
	JavaVersion string `json:"javaVersion"` // java版本: Java8/Java11/Java17
	ModulePath  string `json:"modulePath"`  // 模块路径
	AppType     string `json:"appType"`     // 应用类型: 前端/后端 front/backend
}

// QualityScanCodeParam 定义代码相关参数结构
type QualityScanCodeParam struct {
	CodeUrl         string              `json:"codeUrl"`         // 仓库地址: http://xx.xx.git
	GitUser         string              `json:"gitUser"`         // 仓库用户名: root
	GitToken        string              `json:"gitToken"`        // 仓库密码: 123456
	Branch          string              `json:"branch"`          // 分支: master
	BaseCommitId    string              `json:"baseCommitId"`    // 基准commitId
	CompareCommitId string              `json:"compareCommitId"` // 比较commitId
	MvnParam        QualityScanMvnParam `json:"MvnParam"`        // Maven参数
	// 生成的参数
	ModulePath string `json:"modulePath"` // 模块路径
	JavaHome   string `json:"javaHome"`   // 语言对应的java home
}

// QualityScanSonarParam 定义Sonar相关参数结构
type QualityScanSonarParam struct {
	SonarUrl      string `json:"sonarUrl"`      // Sonar服务地址
	SonarToken    string `json:"sonarToken"`    // Sonar认证Token
	SonarPdf      bool   `json:"sonarPdf"`      // 是否生成PDF报告
	SonarProfile  string `json:"sonarProfile"`  // 规则集名称
	SonarCallback string `json:"sonarCallback"` // 回调地址
	ExclusionPath string `json:"exclusionPath"` // 排除的路径: src/test/java/*
	InclusionPath string `json:"inclusionPath"` // 包含的路径: src/test/java/*
	// 生成的参数
	SonarKey      string `json:"sonarKey"`      // Sonar项目唯一键
	ExecCommitId  string `json:"execCommitId"`  // 执行的commitId
	ExecDate      string `json:"execDate"`      // 执行的commitId对应的时间
	SonarJavaHome string `json:"sonarJavaHome"` // Sonar使用的Java Home
	HookUseful    bool   `json:"hookUseful"`    // 是否启用回调
}

// QualityScanMvnParam 定义Maven相关参数结构
type QualityScanMvnParam struct {
	MvnRepo string `json:"mvnRepo"` // Maven仓库地址
	MvnUser string `json:"mvnUser"` // Maven用户名
	MvnPw   string `json:"mvnPw"`   // Maven密码
}

// Params 包含所有参数的主结构体
type Params struct {
	BizParam   QualityScanBizParam   // 业务参数
	CodeParam  QualityScanCodeParam  // 代码参数
	SonarParam QualityScanSonarParam // Sonar参数
}

// paramInit 从命令行参数初始化参数结构
func paramInit(cf *pflag.FlagSet) (error, *Params) {
	params := &Params{}

	// 从命令行参数获取业务相关参数
	if err := initBizParams(cf, params); err != nil {
		return err, params
	}

	// 从命令行参数获取代码相关参数
	if err := initCodeParams(cf, params); err != nil {
		return err, params
	}

	// 从命令行参数获取Sonar相关参数
	if err := initSonarParams(cf, params); err != nil {
		return err, params
	}

	// 从命令行参数获取Maven相关参数
	if err := initMvnParams(cf, params); err != nil {
		return err, params
	}

	return nil, params
}

// initBizParams 初始化业务参数
func initBizParams(cf *pflag.FlagSet, params *Params) error {
	// 获取回调地址
	callBack, err := cf.GetString("call-back")
	if err != nil {
		return err
	}
	params.BizParam.CallBack = callBack

	// 获取任务类型
	taskType, err := cf.GetString("type")
	if err != nil {
		return err
	}
	params.BizParam.Type = taskType

	// 获取扫描模式
	mode, err := cf.GetString("mode")
	if err != nil {
		return err
	}
	params.BizParam.Mode = mode

	// 获取编程语言
	language, err := cf.GetString("language")
	if err != nil {
		return err
	}
	params.BizParam.Language = language

	// 获取Java版本
	javaVersion, err := cf.GetString("java-version")
	if err != nil {
		return err
	}
	params.BizParam.JavaVersion = javaVersion

	// 获取应用类型
	appType, err := cf.GetString("app-type")
	if err != nil {
		return err
	}
	params.BizParam.AppType = appType

	// 获取模块路径并规范化
	modulePath, err := cf.GetString("module-path")
	if err != nil {
		return err
	}
	// 移除模块路径前面的 "./" 和 "/"
	params.BizParam.ModulePath = strings.TrimLeft(strings.TrimLeft(modulePath, "./"), "/")
	params.CodeParam.ModulePath = params.BizParam.ModulePath

	return nil
}

// initCodeParams 初始化代码相关参数
func initCodeParams(cf *pflag.FlagSet, params *Params) error {
	// 获取代码仓库URL
	codeUrl, err := cf.GetString("code-url")
	if err != nil {
		return err
	}
	params.CodeParam.CodeUrl = codeUrl

	// 获取Git用户名
	gitUser, err := cf.GetString("git-user")
	if err != nil {
		return err
	}
	params.CodeParam.GitUser = gitUser

	// 获取Git令牌
	gitToken, err := cf.GetString("git-token")
	if err != nil {
		return err
	}
	params.CodeParam.GitToken = gitToken

	// 获取Git分支
	branch, err := cf.GetString("branch")
	if err != nil {
		return err
	}
	params.CodeParam.Branch = branch

	// 获取基准CommitID
	baseCommitId, err := cf.GetString("base-commit-id")
	if err != nil {
		return err
	}
	params.CodeParam.BaseCommitId = baseCommitId

	// 获取比较CommitID
	compareCommitId, err := cf.GetString("compare-commit-id")
	if err != nil {
		return err
	}
	params.CodeParam.CompareCommitId = compareCommitId

	return nil
}

// initSonarParams 初始化Sonar相关参数
func initSonarParams(cf *pflag.FlagSet, params *Params) error {
	// 获取Sonar URL
	sonarUrl, err := cf.GetString("sonar-url")
	if err != nil {
		return err
	}
	params.SonarParam.SonarUrl = sonarUrl

	// 获取Sonar令牌
	sonarToken, err := cf.GetString("sonar-token")
	if err != nil {
		return err
	}
	params.SonarParam.SonarToken = sonarToken

	// 获取是否生成PDF报告
	sonarPdf, err := cf.GetBool("sonar-pdf")
	if err != nil {
		return err
	}
	params.SonarParam.SonarPdf = sonarPdf

	// 获取规则集名称
	sonarProfile, err := cf.GetString("sonar-profile")
	if err != nil {
		return err
	}
	params.SonarParam.SonarProfile = sonarProfile

	// 获取回调地址
	sonarCallback, err := cf.GetString("sonar-callback")
	if err != nil {
		return err
	}
	params.SonarParam.SonarCallback = sonarCallback

	// 获取排除路径
	sonarExclusion, err := cf.GetString("sonar-exclusion")
	if err != nil {
		return err
	}
	params.SonarParam.ExclusionPath = sonarExclusion

	// 获取包含路径
	sonarInclusion, err := cf.GetString("sonar-inclusion")
	if err != nil {
		return err
	}
	params.SonarParam.InclusionPath = sonarInclusion

	return nil
}

// initMvnParams 初始化Maven相关参数
func initMvnParams(cf *pflag.FlagSet, params *Params) error {
	// 获取Maven仓库地址
	mvnRepo, err := cf.GetString("mvn-repo")
	if err != nil {
		return err
	}
	params.CodeParam.MvnParam.MvnRepo = mvnRepo

	// 获取Maven用户名
	mvnUser, err := cf.GetString("mvn-user")
	if err != nil {
		return err
	}
	params.CodeParam.MvnParam.MvnUser = mvnUser

	// 获取Maven密码
	mvnPw, err := cf.GetString("mvn-pw")
	if err != nil {
		return err
	}
	params.CodeParam.MvnParam.MvnPw = mvnPw

	return nil
}

// CheckAndInit 检查参数并初始化
func CheckAndInit(cf *pflag.FlagSet) (error, *Params) {
	// 初始化基本参数
	err, params := paramInit(cf)
	if err != nil {
		return fmt.Errorf("初始化参数报错! err=%s", err), params
	}

	// 根据任务类型进行不同处理
	switch params.BizParam.Type {
	case TypeUnit:
		// 单元测试任务检查和初始化
		if strings.ToLower(params.BizParam.Language) != LanguageJava {
			return fmt.Errorf("单元测试任务目前只支持java语言! language=%s", params.BizParam.Language), params
		}

		// 设置Sonar使用的Java Home
		params.SonarParam.SonarJavaHome = Jdk11Home

		// 根据指定的Java版本设置相应的Java Home
		switch params.BizParam.JavaVersion {
		case JavaVersion17:
			params.CodeParam.JavaHome = Jdk17Home
		case JavaVersion11:
			params.CodeParam.JavaHome = Jdk11Home
		default:
			params.CodeParam.JavaHome = Jdk8Home
		}

	case TypeScan:
		// 扫描任务不需要特殊处理

	default:
		return fmt.Errorf("任务类型 %s 不合法", params.BizParam.Type), params
	}

	// 生成唯一的SonarKey
	params.SonarParam.SonarKey = params.BizParam.Type + "-" + uuid.NewUUID()

	return nil, params
}

// NoCodeCheck 检查是否需要执行代码扫描
// 在增量模式下，如果基准CommitID和比较CommitID相同，则不需要执行代码扫描
func NoCodeCheck(code QualityScanCodeParam, mode, callbackUrl string) bool {
	// 全量模式下必须执行代码扫描
	if mode == ModeTotal {
		return false
	}

	log.Printf("增量模式, 正在检查commit id...")

	// 如果基准CommitID和比较CommitID不同，需要执行代码扫描
	if code.CompareCommitId != code.BaseCommitId {
		return false
	}

	log.Printf("base commit id (%s)和 compare commit id (%s)相同, 不需要扫描!", code.BaseCommitId, code.CompareCommitId)
	log.Println("流程结束! 不再执行扫描/单测任务!")

	// 准备回调数据
	callBackBody, _ := json.Marshal(map[string]any{
		"status": "SUCCESS",
		"qualityGate": map[string]any{
			"conditions": []map[string]any{
				{
					"metric": "new_line_coverage",
					"value":  "100",
				},
			},
		},
	})

	// 执行回调
	httpClient := netutil.NewHttpClient()
	createRequest := &netutil.HttpRequest{
		RawURL: callbackUrl,
		Method: http.MethodPost,
		Body:   callBackBody,
	}

	// 发送回调请求
	resp, err := httpClient.SendRequest(createRequest)
	if err != nil || resp.StatusCode != http.StatusOK {
		log.Printf("回调主服务失败！resp=%v, err=%v", resp, err)
	} else {
		log.Printf("回调主服务成功! %v", resp)
	}

	return true
}
