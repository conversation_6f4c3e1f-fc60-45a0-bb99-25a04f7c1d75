package scan

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/esonar-scanner/params"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/utils"
)

// IScanCodeScanner 定义代码扫描器结构体
type IScanCodeScanner struct{}

// DiffCode 计算两个提交之间的代码差异
// 返回增量文件列表，用于增量扫描
func (u *IScanCodeScanner) DiffCode(appPath string, bizParam params.QualityScanBizParam, codeParam params.QualityScanCodeParam) string {
	// 全量模式不需要计算差异
	if params.ModeTotal == bizParam.Mode {
		return ""
	}

	log.Printf("增量模式，计算文件差异")

	// 根据应用类型准备不同的Git命令
	var gitCmd *exec.Cmd
	if params.AppTypeFront == strings.ToLower(bizParam.AppType) {
		log.Println("前端增量逻辑！")
		// 前端应用只对特定前端文件进行过滤
		gitCmd = exec.Command("/bin/bash", "-c",
			fmt.Sprintf("cd %s && git diff --name-only %s %s | grep -E '*.js|*.jsx|*.ts|*.tsx|*.vue' > ./diff.diff",
				appPath, codeParam.BaseCommitId, codeParam.CompareCommitId))
	} else {
		log.Println("后端语言逻辑！")
		// 后端应用计算所有文件差异
		gitCmd = exec.Command("/bin/bash", "-c",
			fmt.Sprintf("cd %s && git diff --name-only %s %s > ./diff.diff",
				appPath, codeParam.BaseCommitId, codeParam.CompareCommitId))
	}

	// 执行Git命令并获取输出
	err := utils.SyncOutLog(gitCmd)
	if err != nil {
		log.Println("获取增量差异失败！执行增量非diff逻辑")
		return ""
	}

	// 读取差异文件
	file, err := os.ReadFile(path.Join(appPath, "diff.diff"))
	if err != nil {
		log.Printf("读取diff文件失败: %v", err)
		return ""
	}

	content := string(file)
	if content == "" {
		log.Println("没有差异文件")
		return ""
	}

	// 处理差异文件列表
	scanFile := ""
	fileList := strings.Split(content, "\n")

	// 文件数量超过100，不再进行筛选
	if len(fileList) > 100 {
		log.Printf("增量文件个数 %d > 100个，不再进行筛选！", len(fileList))
		return scanFile
	}

	// 筛选模块路径相关的文件
	for _, filePath := range fileList {
		if filePath != "" && strings.Contains(filePath, bizParam.ModulePath) {
			// 将模块路径替换为**，便于后续处理
			normalizedPath := strings.Replace(filePath, bizParam.ModulePath, "**", 1)
			log.Println("增量文件 ===> ", normalizedPath)
			scanFile += normalizedPath + ","
		}
	}

	return scanFile
}

// CodeCusDeal 切换代码到指定的Commit
func (u *IScanCodeScanner) CodeCusDeal(appPath string, codeParam params.QualityScanCodeParam) error {
	// 强制切换到目标提交
	checkCmd := exec.Command("/bin/bash", "-c",
		fmt.Sprintf("cd %s && git checkout -f %s",
			appPath, codeParam.CompareCommitId))

	return utils.SyncOutLog(checkCmd)
}

// ScannerCli 执行Sonar代码扫描
func (u *IScanCodeScanner) ScannerCli(appPath, language, modulePath, diffInclude string, sonarP params.QualityScanSonarParam) error {
	// 合并包含路径
	if sonarP.InclusionPath != "" {
		diffInclude += "," + sonarP.InclusionPath
	}

	// 确定是否跳过PDF生成
	pdfSkip := !sonarP.SonarPdf

	// 构建Sonar命令
	sonarCmdTemplate := strings.Join([]string{
		"sonar-scanner",
		"-Dsonar.host.url=%s",
		"-Dsonar.projectKey=%s",
		"-Dsonar.projectName=%s",
		"-Dsonar.projectDate=%s",
		"-Dsonar.projectVersion=%s",
		"-Dsonar.language=%s",
		"-Dsonar.sources=.",
		"-Dsonar.scm.disabled=false",
		"-Dsonar.scm.provider=git",
		"-Dsonar.analysis.isHookUseful=%s",
		"-Dsonar.exclusions=%s",
		"-Dsonar.inclusions=%s",
		"-Dsonar.pdf.skip=%v",
		"-Dsonar.login=%s",
		"-Dsonar.analysis.isHookUseful=%v",
	}, " ")

	sonarCmd := fmt.Sprintf(sonarCmdTemplate,
		sonarP.SonarUrl,
		sonarP.SonarKey,
		sonarP.SonarKey,
		sonarP.ExecDate,
		sonarP.ExecCommitId,
		language,
		sonarP.SonarCallback,
		sonarP.ExclusionPath,
		diffInclude,
		pdfSkip,
		sonarP.SonarToken,
		sonarP.HookUseful)

	// 确定执行路径
	execPath := path.Join(appPath, modulePath)

	// Java语言特殊处理
	if params.LanguageJava == strings.ToLower(language) {
		// 创建Java编译目录
		targetDir := path.Join(execPath, "target", "classes")
		err := os.MkdirAll(targetDir, 0o755)
		if err != nil {
			log.Printf("创建 target/classes 目录失败: %v", err)
		}

		// 添加Java特定参数
		sonarCmd += " -Dsonar.java.source=1.8 -Dsonar.java.binaries=target/classes"
	}

	log.Printf("sonar 命令: %s", sonarCmd)

	// 执行Sonar扫描命令
	command := exec.Command("/bin/sh", "-c",
		fmt.Sprintf("cd %s && export JAVA_HOME=%s && %s",
			execPath, sonarP.SonarJavaHome, sonarCmd))

	return utils.SyncOutLog(command)
}
