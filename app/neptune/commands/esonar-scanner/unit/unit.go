package unit

import (
	"fmt"
	"log"
	"os/exec"
	"path"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/esonar-scanner/params"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/utils"
)

// IUnitCodeScanner 单元测试代码扫描器结构体
type IUnitCodeScanner struct{}

// CodeCusDeal 执行代码切换和单元测试
// 该方法会切换到指定的代码版本，配置Maven，并执行单元测试
func (u *IUnitCodeScanner) CodeCusDeal(appPath string, codeParam params.QualityScanCodeParam) error {
	// 切换到指定的代码提交
	checkCmd := exec.Command("/bin/bash", "-c",
		fmt.Sprintf("cd %s && git checkout -f %s",
			appPath, codeParam.CompareCommitId))
	log.Printf("git check: %s", checkCmd.String())

	if err := utils.SyncOutLog(checkCmd); err != nil {
		return fmt.Errorf("check commit error: %v", err)
	}

	// 初始化Maven配置
	mp := codeParam.MvnParam
	log.Printf("初始化Maven配置: %+v", mp)

	if err := genMvnSetting(mp.MvnRepo, mp.MvnUser, mp.MvnPw, params.MavenConfigPath); err != nil {
		return fmt.Errorf("生成Maven配置文件报错: %v", err)
	}

	// 确定执行路径
	execPath := path.Join(appPath, codeParam.ModulePath)

	// 构建Maven测试命令
	testCmd := exec.Command("/bin/bash", "-c",
		fmt.Sprintf("cd %s && export JAVA_HOME=%s && "+
			"mvn -B -U org.jacoco:jacoco-maven-plugin:0.8.5:prepare-agent test "+
			"-Dmaven.test.failure.ignore=true '-Dtest=*Test' "+
			"-DfailIfNoTests=false -Dautoconfig.skip",
			execPath, codeParam.JavaHome))

	log.Printf("执行Maven测试命令: %s", testCmd.String())

	if err := utils.SyncOutLog(testCmd); err != nil {
		return fmt.Errorf("单元测试执行异常: %v", err)
	}

	// 生成测试覆盖率报告
	reportCmd := exec.Command("/bin/bash", "-c",
		fmt.Sprintf("cd %s && mvn org.jacoco:jacoco-maven-plugin:0.8.5:report",
			execPath))

	log.Printf("执行报告生成命令: %s", reportCmd.String())

	return utils.SyncOutLog(reportCmd)
}

// ScannerCli 执行Sonar扫描
// 该方法使用Maven插件运行Sonar扫描，分析单元测试结果
func (u *IUnitCodeScanner) ScannerCli(appPath, language, modulePath, diffInclude string, sonarP params.QualityScanSonarParam) error {
	// 确定是否跳过PDF生成
	pdfSkip := !sonarP.SonarPdf

	// 构建Sonar Maven命令
	sonarParams := []string{
		fmt.Sprintf("-Dsonar.host.url=%s", sonarP.SonarUrl),
		fmt.Sprintf("-Dsonar.projectKey=%s", sonarP.SonarKey),
		fmt.Sprintf("-Dsonar.projectName=%s", sonarP.SonarKey),
		"org.sonarsource.scanner.maven:sonar-maven-plugin:sonar",
		fmt.Sprintf("-Dsonar.projectDate=%s", sonarP.ExecDate),
		fmt.Sprintf("-Dsonar.projectVersion=%s", sonarP.ExecCommitId),
		fmt.Sprintf("-Dsonar.analysis.isHookUseful=%s", sonarP.SonarCallback),
		fmt.Sprintf("-Dsonar.coverage.exclusions=%s", sonarP.ExclusionPath),
		fmt.Sprintf("-Dsonar.pdf.skip=%v", pdfSkip),
		fmt.Sprintf("-Dsonar.login=%s", sonarP.SonarToken),
		fmt.Sprintf("-Dsonar.analysis.isHookUseful=%v", sonarP.HookUseful),
		"-Dsonar.scm.provider=git",
		"-Dsonar.java.source=1.8",
		"-Dsonar.java.binaries=target/classes",
		"-Dsonar.language=java",
		"-Dsonar.sources=src/",
		"-Dsonar.scm.disabled=false",
		"-Dsonar.coverage.jacoco.xmlReportPaths=target/site/jacoco/jacoco.xml",
		"-Duser.timezone=Asia/Shanghai",
	}

	sonarCmd := fmt.Sprintf("mvn %s",
		strings.Join(sonarParams, " "))

	// 确定执行路径
	execPath := path.Join(appPath, modulePath)

	// 构建完整命令
	command := exec.Command("/bin/sh", "-c",
		fmt.Sprintf("cd %s && export JAVA_HOME=%s && %s",
			execPath, sonarP.SonarJavaHome, sonarCmd))

	log.Printf("Sonar命令: %s", sonarCmd)

	return utils.SyncOutLog(command)
}

// DiffCode 实现接口方法，对于单元测试不需要计算差异
func (u *IUnitCodeScanner) DiffCode(appPath string, bizParam params.QualityScanBizParam, codeParam params.QualityScanCodeParam) string {
	return ""
}
