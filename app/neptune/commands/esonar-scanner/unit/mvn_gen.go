package unit

import (
	"fmt"
	"log"
	"os"
)

// Maven设置文件模板，用于生成settings.xml
// 该模板包含了镜像配置和认证信息
const mvnSettingTemplate = "<settings>\n" +
	"<mirrors>\n" +
	"<mirror>\n" +
	"<id>magic-mvn</id>\n" +
	"<name>magic-mvn</name>\n" +
	"<url>%s</url>\n" +
	"<mirrorOf>*</mirrorOf>\n" +
	"</mirror>\n" +
	"</mirrors>\n" +
	"<servers>\n" +
	"<server>\n" +
	"<id>magic-mvn</id>\n" +
	"<username>%s</username>\n" +
	"<password>%s</password>\n" +
	"</server>\n" +
	"</servers>\n" +
	"</settings>"

// genMvnSetting 生成Maven配置文件，如果仓库URL为空，则使用阿里云Maven仓库作为默认值
func genMvnSetting(url, user, pw, outputPath string) error {
	// 默认使用阿里云Maven仓库
	if url == "" {
		log.Println("Maven仓库URL为空，使用阿里云Maven仓库作为默认值")
		url = "http://maven.aliyun.com/nexus/content/groups/public"
	}

	// 使用模板生成配置文件内容
	content := fmt.Sprintf(mvnSettingTemplate, url, user, pw)

	// 写入配置文件
	if err := os.WriteFile(outputPath, []byte(content), 0o644); err != nil {
		log.Printf("写入Maven配置文件失败: %v", err)
		return err
	}

	log.Printf("成功生成Maven配置文件: %s", outputPath)
	return nil
}
