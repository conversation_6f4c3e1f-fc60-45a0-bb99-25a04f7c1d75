package utils

import (
	"errors"
	"io"
	"log"
	"os"
	"os/exec"
	"strings"
)

// SyncOutLog 同步执行命令并将命令输出记录到标准输出
func SyncOutLog(cmdInfo *exec.Cmd) error {
	// 获取命令的标准输出和标准错误管道
	cmdStdoutPipe, _ := cmdInfo.StdoutPipe()
	cmdStderrPipe, _ := cmdInfo.StderrPipe()

	// 启动命令但不等待其完成
	err := cmdInfo.Start()
	if err != nil {
		log.Printf("启动命令失败: %v", err)
		return err
	}

	// 创建一个新的日志记录器，输出到标准输出
	logger := log.New(os.Stdout, "", log.LstdFlags)
	oldFlags := logger.Flags()
	logger.SetFlags(0) // 临时禁用时间戳等前缀

	// 启动两个协程分别处理标准输出和标准错误
	go syncLog(logger, cmdStdoutPipe)
	go syncLog(logger, cmdStderrPipe)

	// 等待命令执行完成
	err = cmdInfo.Wait()
	logger.SetFlags(oldFlags) // 恢复原始日志格式

	if err != nil {
		log.Printf("命令执行失败: %v", err)
		return err
	}
	return nil
}

// syncLog 将输入流中的数据同步到日志记录器
func syncLog(logger *log.Logger, reader io.ReadCloser) {
	// 用于存储尚未处理的数据
	buffer := make([]byte, 0)
	// 用于存储行尾数据
	lineEndBuffer := make([]byte, 0)
	// 用于从reader读取数据的临时缓冲区
	readBuffer := make([]byte, 1024)

	for {
		// 从reader读取数据
		bytesRead, err := reader.Read(readBuffer)

		if bytesRead > 0 {
			// 将读取的数据追加到buffer
			buffer = append(buffer, readBuffer[:bytesRead]...)

			// 当累积的数据超过1024字节时处理
			if len(buffer) > 1024 {
				// 按换行符分割数据
				lines := strings.Split(string(buffer), "\n")

				// 拼接完整行并记录日志
				completeLines := append(lineEndBuffer, []byte(strings.Join(lines[:len(lines)-1], "\n"))...)
				logger.Printf("%s", completeLines)

				// 保存最后一行(可能是不完整的)
				lineEndBuffer = []byte(lines[len(lines)-1])
				buffer = make([]byte, 0)
			}
		}

		// 处理错误
		if err != nil {
			if errors.Is(err, io.EOF) {
				// 读取结束，输出剩余数据
				logger.Printf("%s", buffer)
				break
			}
			// 其他错误则中断处理
			break
		}
	}
}
