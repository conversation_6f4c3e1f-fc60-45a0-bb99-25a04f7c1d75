package docker

import (
	"fmt"
	"log"
	"os"
	"os/exec"

	trivyscanner "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/trivy-scanner"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/utils"
	"github.com/spf13/pflag"
)

// DockerScanner Docker镜像扫描器
// 用于扫描Docker镜像中的安全漏洞
type DockerScanner struct {
	*trivyscanner.BaseScanner        // 基础扫描器
	image                     string // Docker镜像名称
	addr                      string // Docker仓库地址
	user                      string // Docker仓库用户名
	password                  string // Docker仓库密码
	nonSsl                    string // 是否禁用SSL
}

// NewDockerScanner 创建Docker镜像扫描器实例
func NewDockerScanner(f *pflag.FlagSet) (*DockerScanner, error) {
	// 创建基础扫描器
	baseScanner, err := trivyscanner.NewBaseScanner(f)
	if err != nil {
		return nil, fmt.Errorf("创建基础扫描器失败: %v", err)
	}

	// 创建忽略文件
	if err := baseScanner.IgnoreFile(); err != nil {
		return nil, fmt.Errorf("创建忽略文件失败: %v", err)
	}

	// 获取Docker镜像名称
	image, err := f.GetString("image")
	if err != nil {
		return nil, fmt.Errorf("获取image参数失败: %v", err)
	}

	// 获取Docker仓库地址
	addr, err := f.GetString("docker-addr")
	if err != nil {
		return nil, fmt.Errorf("获取docker-addr参数失败: %v", err)
	}

	// 获取Docker仓库用户名
	user, err := f.GetString("docker-user")
	if err != nil {
		return nil, fmt.Errorf("获取docker-user参数失败: %v", err)
	}

	// 获取Docker仓库密码
	password, err := f.GetString("docker-password")
	if err != nil {
		return nil, fmt.Errorf("获取docker-password参数失败: %v", err)
	}

	// 获取是否禁用SSL
	nonSsl, err := f.GetString("non-ssl")
	if err != nil {
		return nil, fmt.Errorf("获取non-ssl参数失败: %v", err)
	}

	return &DockerScanner{
		BaseScanner: baseScanner,
		image:       image,
		addr:        addr,
		user:        user,
		password:    password,
		nonSsl:      nonSsl,
	}, nil
}

// Exec 实现Scanner接口的Exec方法
// 登录Docker仓库(如需)并执行Trivy扫描
func (s *DockerScanner) Exec() error {
	log.Println("扫描镜像:", s.image)

	// 构建Trivy扫描命令参数
	trivyArgs := []string{
		"image", s.image,
		"--username", s.user,
		"--password", s.password,
		"--ignorefile", trivyscanner.IgnoreFile,
		"--skip-db-update",
		"--skip-java-db-update",
		"--scanners", "vuln",
		"--timeout", "15m",
		"--format", "template",
		"--template", "@/root/gitlab.tpl",
		"--output", trivyscanner.ResultFilePath,
	}

	// 创建Trivy命令
	trivyCmd := exec.Command("trivy", trivyArgs...)

	// 设置环境变量
	trivyCmd.Env = append(os.Environ(), fmt.Sprintf("TRIVY_NON_SSL=%s", s.nonSsl))

	log.Println("执行扫描命令:", trivyCmd.String())

	// 执行扫描
	if err := utils.SyncOutLog(trivyCmd); err != nil {
		return fmt.Errorf("执行Trivy扫描失败: %v", err)
	}

	log.Println("扫描完成")
	return nil
}
