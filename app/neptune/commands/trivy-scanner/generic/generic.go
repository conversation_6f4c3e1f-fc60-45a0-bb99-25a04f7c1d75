package generic

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/exec"
	"path"
	"strings"

	trivyscanner "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/trivy-scanner"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/utils"

	"github.com/spf13/pflag"
)

// GenericScanner 通用制品扫描器
// 用于下载并扫描通用制品文件(如JAR、WAR、ZIP等)
type GenericScanner struct {
	*trivyscanner.BaseScanner            // 基础扫描器
	filename                      string // 制品文件名
	artifactDownloadUrl           string // 制品下载URL
	artifactDownloadToken         string // 下载认证Token
	artifactDownloadAuthorization string // 下载Basic认证信息
}

// NewGenericScanner 创建通用制品扫描器实例
func NewGenericScanner(f *pflag.FlagSet) (*GenericScanner, error) {
	// 创建基础扫描器
	baseScanner, err := trivyscanner.NewBaseScanner(f)
	if err != nil {
		return nil, fmt.Errorf("创建基础扫描器失败: %v", err)
	}

	// 创建忽略文件
	if err := baseScanner.IgnoreFile(); err != nil {
		return nil, fmt.Errorf("创建忽略文件失败: %v", err)
	}

	// 获取制品文件名
	filename, err := f.GetString("filename")
	if err != nil {
		return nil, fmt.Errorf("获取filename参数失败: %v", err)
	}

	// 获取制品下载URL
	artifactDownloadUrl, err := f.GetString("artifact-download-url")
	if err != nil {
		return nil, fmt.Errorf("获取artifact-download-url参数失败: %v", err)
	}

	// 获取下载认证Token
	artifactDownloadToken, err := f.GetString("artifact-download-token")
	if err != nil {
		return nil, fmt.Errorf("获取artifact-download-token参数失败: %v", err)
	}

	// 获取下载Basic认证信息
	artifactDownloadAuthorization, err := f.GetString("artifact-download-authorization")
	if err != nil {
		return nil, fmt.Errorf("获取artifact-download-authorization参数失败: %v", err)
	}

	return &GenericScanner{
		BaseScanner:                   baseScanner,
		filename:                      filename,
		artifactDownloadUrl:           artifactDownloadUrl,
		artifactDownloadToken:         artifactDownloadToken,
		artifactDownloadAuthorization: artifactDownloadAuthorization,
	}, nil
}

// Exec 实现Scanner接口的Exec方法
// 下载制品、解压(如需)并执行Trivy扫描
func (s *GenericScanner) Exec() error {
	log.Println("制品名称:", s.filename)
	log.Printf("制品下载地址: %s", s.artifactDownloadUrl)

	// 确定制品保存路径
	filePath := path.Join("/root/", s.filename)

	// 下载制品文件
	if err := downloadFile(s.artifactDownloadUrl, s.artifactDownloadToken, s.artifactDownloadAuthorization, filePath); err != nil {
		log.Println("下载制品失败:", err)
		return fmt.Errorf("下载制品失败: %v", err)
	}

	// 根据文件扩展名确定解压方法
	var shell string
	if strings.HasSuffix(s.filename, ".zip") {
		shell = fmt.Sprintf("unzip %s -d /root/scanning", filePath)
	} else if strings.HasSuffix(s.filename, ".tar.gz") || strings.HasSuffix(s.filename, ".tar") {
		shell = fmt.Sprintf("tar -xf %s -C /root/scanning", filePath)
	}

	// 确定扫描路径
	var scanPath string

	// 需要解压的情况
	if shell != "" {
		scanPath = "/root/scanning"

		// 创建解压目录
		if err := os.MkdirAll(scanPath, os.ModePerm); err != nil {
			return fmt.Errorf("创建解压目录失败: %v", err)
		}

		// 创建解压脚本
		extractScript := "/root/extract.sh"
		if err := os.WriteFile(extractScript, []byte(shell), os.ModePerm); err != nil {
			return fmt.Errorf("创建解压脚本失败: %v", err)
		}

		// 执行解压
		extractCmd := exec.Command("sh", "-x", extractScript)
		if err := utils.SyncOutLog(extractCmd); err != nil {
			return fmt.Errorf("解压制品失败: %v", err)
		}
	} else {
		// 不需要解压的情况
		scanPath = filePath
	}

	// 构建Trivy扫描命令参数
	trivyArgs := []string{
		"rootfs",
		scanPath,
		"--ignorefile", trivyscanner.IgnoreFile,
		"--skip-db-update",
		"--skip-java-db-update",
		"--scanners", "vuln",
		"--timeout", "15m",
		"--format", "template",
		"--template", "@/root/gitlab.tpl",
		"--output", trivyscanner.ResultFilePath,
	}

	// 执行Trivy扫描
	trivyCmd := exec.Command("trivy", trivyArgs...)
	log.Println("执行扫描命令:", trivyCmd.String())

	if err := utils.SyncOutLog(trivyCmd); err != nil {
		return fmt.Errorf("执行Trivy扫描失败: %v", err)
	}

	log.Println("扫描完成")
	return nil
}

// downloadFile 下载文件到指定路径
// 支持Token认证和Basic认证两种方式
func downloadFile(url, token, auth, filePath string) error {
	// 创建HTTP客户端
	client := &http.Client{}

	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return fmt.Errorf("创建下载请求失败: %v", err)
	}

	// 设置认证头
	if token != "" {
		req.Header.Set("Token", token)
	} else if auth != "" {
		req.Header.Set("Authorization", "Basic "+auth)
	}

	// 设置接受类型
	req.Header.Set("Accept", "application/octet-stream")
	log.Printf("请求头: %+v", req.Header)

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送下载请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载请求失败，HTTP状态码: %d", resp.StatusCode)
	}

	// 创建目标文件
	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("创建目标文件失败: %v", err)
	}
	defer file.Close()

	// 将响应内容写入文件
	if _, err := io.Copy(file, resp.Body); err != nil {
		return fmt.Errorf("写入文件内容失败: %v", err)
	}

	log.Printf("文件下载成功: %s", filePath)
	return nil
}
