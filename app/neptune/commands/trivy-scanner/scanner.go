package trivy_scanner

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"

	"github.com/spf13/pflag"
)

// 常量定义
const (
	ResultFilePath = "/root/output.json" // Trivy扫描结果文件路径
	IgnoreFile     = "/root/trivyignore" // Trivy忽略文件路径
)

// Scanner 定义扫描器接口
type Scanner interface {
	// Exec 执行扫描操作
	Exec() error

	// UploadResult 上传扫描结果
	UploadResult() error
}

// BaseScanner 基础扫描器实现
type BaseScanner struct {
	cubeMantistUrl  string // Mantis服务URL
	uploadResultUrl string // 结果上传接口路径
	whiteCVE        string // 白名单CVE列表
}

// NewBaseScanner 创建基础扫描器实例
func NewBaseScanner(f *pflag.FlagSet) (*BaseScanner, error) {
	// 获取Mantis服务URL
	cubeMantistUrl, err := f.GetString("cube-mantis-url")
	if err != nil {
		return nil, fmt.Errorf("获取cube-mantis-url参数失败: %v", err)
	}

	// 获取结果上传接口路径
	uploadResultUrl, err := f.GetString("upload-result-url")
	if err != nil {
		return nil, fmt.Errorf("获取upload-result-url参数失败: %v", err)
	}

	// 获取白名单CVE列表
	whiteCVE, err := f.GetString("white-cve")
	if err != nil {
		return nil, fmt.Errorf("获取white-cve参数失败: %v", err)
	}

	return &BaseScanner{
		cubeMantistUrl:  cubeMantistUrl,
		uploadResultUrl: uploadResultUrl,
		whiteCVE:        whiteCVE,
	}, nil
}

// Exec 实现Scanner接口的Exec方法
// 此基础实现不执行实际操作，需由具体实现类覆盖
func (s *BaseScanner) Exec() error {
	return nil
}

// IgnoreFile 创建Trivy忽略文件
// 将白名单CVE列表写入忽略文件
func (s *BaseScanner) IgnoreFile() error {
	// 创建忽略文件
	file, err := os.Create(IgnoreFile)
	if err != nil {
		return fmt.Errorf("创建忽略文件失败: %v", err)
	}
	defer func() {
		if closeErr := file.Close(); closeErr != nil {
			logger.Logger.Errorf("关闭忽略文件失败: %v", closeErr)
		}
	}()

	// 使用缓冲写入提升性能
	writer := bufio.NewWriter(file)

	// 按行写入白名单CVE
	for _, line := range strings.Split(s.whiteCVE, ",") {
		if line = strings.TrimSpace(line); line != "" {
			if _, err := writer.WriteString(line + "\n"); err != nil {
				return fmt.Errorf("写入CVE条目失败: %v", err)
			}
		}
	}

	// 确保所有数据写入文件
	if err := writer.Flush(); err != nil {
		return fmt.Errorf("刷新缓冲区失败: %v", err)
	}

	return nil
}

// UploadResult 实现Scanner接口的UploadResult方法
// 将扫描结果上传到指定的服务
func (s *BaseScanner) UploadResult() error {
	// 打开结果文件
	file, err := os.Open(ResultFilePath)
	if err != nil {
		return fmt.Errorf("打开结果文件失败: %v", err)
	}
	defer func() {
		if closeErr := file.Close(); closeErr != nil {
			logger.Logger.Errorf("关闭结果文件失败: %v", closeErr)
		}
	}()

	// 准备multipart表单数据
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// 创建文件表单字段
	part, err := writer.CreateFormFile("file", filepath.Base(file.Name()))
	if err != nil {
		return fmt.Errorf("创建表单文件字段失败: %v", err)
	}

	// 复制文件内容到表单
	if _, err = io.Copy(part, file); err != nil {
		return fmt.Errorf("复制文件内容失败: %v", err)
	}

	// 完成表单构建
	if err = writer.Close(); err != nil {
		return fmt.Errorf("关闭表单写入器失败: %v", err)
	}

	// 构建完整的上传URL
	apiUrl, err := url.JoinPath(s.cubeMantistUrl, s.uploadResultUrl)
	if err != nil {
		return fmt.Errorf("构建API URL失败 (mantis: %s, path: %s): %v",
			s.cubeMantistUrl, s.uploadResultUrl, err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", apiUrl, body)
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置正确的Content-Type
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer func() {
		if closeErr := resp.Body.Close(); closeErr != nil {
			logger.Logger.Errorf("关闭响应体失败: %v", closeErr)
		}
	}()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("上传失败，HTTP状态码: %s", resp.Status)
	}

	// 解析响应内容
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应内容失败: %v", err)
	}

	// 解析API响应
	var response controller.Response
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return fmt.Errorf("解析响应JSON失败: %v", err)
	}

	// 检查业务状态码
	if response.Code == constants.ResponseErrorCode {
		return fmt.Errorf("上传失败，业务错误: %s", response.Message)
	}

	log.Println("上传成功")
	return nil
}
