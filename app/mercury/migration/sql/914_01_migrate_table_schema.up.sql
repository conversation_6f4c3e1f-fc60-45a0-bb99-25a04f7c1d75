-- Create table api_data_model
CREATE TABLE api_data_model (
    name varchar,
    alias_name varchar,
    description varchar,
    content text,
    folder_id bigint,
    "key" varchar,
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    order_no bigint,
    pre_node varchar,
    post_node varchar,
    lib_id bigint,
    PRIMARY KEY (id)
);

CREATE INDEX model_key_idx ON api_data_model USING btree (key);

-- Create table api_doc_base
CREATE TABLE api_doc_base (
    "type" varchar DEFAULT 'http'::character varying,
    name varchar,
    method varchar,
    path varchar,
    status bigint,
    description varchar,
    request text,
    responsible_id varchar,
    folder_id bigint,
    bind_label_id varchar,
    jupiter_api_id bigint,
    jupiter_version_id bigint,
    "key" varchar,
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    order_no bigint,
    pre_node varchar,
    post_node varchar,
    lib_id bigint,
    PRIMARY KEY (id)
);

CREATE INDEX api_key_idx ON api_doc_base USING btree (key);

-- Create table api_doc_base_his
CREATE TABLE api_doc_base_his (
    api_doc_base_id bigint,
    "type" varchar DEFAULT 'http'::character varying,
    name varchar,
    method varchar,
    path varchar,
    status bigint,
    description varchar,
    request text,
    responsible_id varchar,
    folder_id bigint,
    bind_label_id varchar,
    jupiter_api_id bigint,
    jupiter_version_id bigint,
    "key" varchar,
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    order_no bigint,
    pre_node varchar,
    post_node varchar,
    lib_id bigint,
    response_list text,
    response_case_list text,
    PRIMARY KEY (id)
);

CREATE INDEX api_doc_base_his_api_doc_base_id_idx ON api_doc_base_his USING btree (api_doc_base_id);

-- Create table api_doc_quick_request
CREATE TABLE api_doc_quick_request (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    order_no bigint,
    pre_node varchar,
    post_node varchar,
    lib_id bigint,
    "type" varchar DEFAULT 'http'::character varying,
    name varchar,
    method varchar,
    path varchar,
    status bigint,
    description varchar,
    request text,
    folder_id bigint,
    setting text,
    pre_operation text,
    post_operation text,
    "key" varchar,
    PRIMARY KEY (id)
);

CREATE INDEX case_key_idx ON api_doc_quick_request USING btree (key);

-- Create table api_doc_runtime
CREATE TABLE api_doc_runtime (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    "type" varchar DEFAULT 'http'::character varying,
    name varchar,
    method varchar,
    path varchar,
    status bigint,
    description varchar,
    request text,
    lib_id bigint,
    api_doc_base_id bigint,
    setting text,
    pre_operation text,
    post_operation text,
    PRIMARY KEY (id)
);

-- Create table api_env
CREATE TABLE api_env (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    name varchar,
    code varchar,
    pre_url varchar,
    lib_id bigint,
    order_no bigint,
    PRIMARY KEY (id)
);

CREATE INDEX api_env_lib_id ON api_env USING btree (lib_id);

-- Create table api_folder
CREATE TABLE api_folder (
    name varchar,
    "type" varchar,
    parent_id bigint,
    path text,
    "key" varchar,
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    order_no bigint,
    pre_node varchar,
    post_node varchar,
    lib_id bigint,
    PRIMARY KEY (id)
);

CREATE INDEX folder_key_idx ON api_folder USING btree (key);

CREATE INDEX idx_path ON api_folder USING btree (path);

-- Create table api_import_task
CREATE TABLE api_import_task (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    name varchar,
    lib_id bigint,
    doc_type varchar,
    url varchar,
    username varchar,
    password varchar,
    auth boolean,
    start_time timestamp without time zone,
    end_time timestamp without time zone,
    cron varchar,
    content text,
    trigger_time timestamp without time zone,
    running boolean,
    PRIMARY KEY (id)
);

-- Create table api_import_task_records
CREATE TABLE api_import_task_records (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    task_name varchar,
    task_id bigint,
    run_time timestamp without time zone,
    success boolean,
    message text,
    operator varchar,
    PRIMARY KEY (id)
);

-- Create table api_library
CREATE TABLE api_library (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    name varchar,
    app_id text,
    description varchar,
    bind_api_status varchar,
    space_id varchar,
    api_count bigint,
    PRIMARY KEY (id)
);

-- Create table api_mock
CREATE TABLE api_mock (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    name varchar,
    "condition" text,
    api_doc_base_id bigint,
    enabled boolean,
    sort bigint,
    expectation_body text,
    expectation_content_type varchar,
    expectation_header text,
    expectation_code integer,
    PRIMARY KEY (id)
);

-- Create table api_response
CREATE TABLE api_response (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    name varchar,
    resp_code integer,
    content_format varchar,
    content text,
    api_id bigint,
    lib_id bigint,
    order_no bigint,
    PRIMARY KEY (id)
);

CREATE INDEX api_response_lib_id ON api_response USING btree (lib_id);

CREATE INDEX api_response_api_id ON api_response USING btree (api_id);

-- Create table api_response_case
CREATE TABLE api_response_case (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    name varchar,
    content text,
    resp_id bigint,
    order_no bigint,
    PRIMARY KEY (id)
);

CREATE INDEX api_response_case_resp_id ON api_response_case USING btree (resp_id);

-- Create table api_run_record
CREATE TABLE api_run_record (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    path varchar,
    lib_id bigint,
    api_doc_id bigint,
    name varchar,
    request text,
    request_method varchar,
    success boolean,
    resp_code integer,
    content_type varchar,
    body text,
    return_file boolean,
    file_body bytea,
    header text,
    cookie text,
    use_time bigint,
    post_operation_response text,
    content_length bigint,
    is_failed boolean,
    error_message varchar,
    real_request text,
    PRIMARY KEY (id)
);

-- Create table api_share
CREATE TABLE api_share (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    name varchar,
    lib_id bigint,
    env_id bigint,
    choose_type varchar,
    choose_ids text,
    choose_label_ids text,
    exclude_label_ids text,
    PRIMARY KEY (id)
);

-- Create table api_status
CREATE TABLE api_status (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    name varchar,
    lib_id bigint,
    PRIMARY KEY (id)
);

-- Create table api_variable
CREATE TABLE api_variable (
    id SERIAL NOT NULL,
    creator varchar(100),
    modifier varchar(100),
    gmt_created timestamp without time zone,
    gmt_modified timestamp without time zone,
    is_deleted varchar(1) DEFAULT 'N'::character varying,
    lib_id bigint,
    "type" bigint,
    env_id bigint,
    var_key varchar,
    var_value text,
    description varchar,
    order_no bigint,
    PRIMARY KEY (id)
);