DROP TABLE IF EXISTS api_data_model;

DROP TABLE IF EXISTS api_doc_base;

DROP TABLE IF EXISTS api_doc_base_his;

DROP TABLE IF EXISTS api_doc_quick_request;

DROP TABLE IF EXISTS api_doc_runtime;

DROP TABLE IF EXISTS api_env;

DROP TABLE IF EXISTS api_folder;

DROP TABLE IF EXISTS api_import_task;

DROP TABLE IF EXISTS api_import_task_records;

DROP TABLE IF EXISTS api_library;

DROP TABLE IF EXISTS api_mock;

DROP TABLE IF EXISTS api_response;

DROP TABLE IF EXISTS api_response_case;

DROP TABLE IF EXISTS api_run_record;

DROP TABLE IF EXISTS api_share;

DROP TABLE IF EXISTS api_status;

DROP TABLE IF EXISTS api_variable;