// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// POST Path("/mercury/v1/model") -> controller.ApiDataModelController.AddDataModelHandler
func controllerApiDataModelControllerAddDataModelHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "AddDataModelHandler",
		Patten:            "/mercury/v1/model",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDataModelController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DATAMODEL-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDataModelController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.ApiDataModelDTO{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.AddDataModelHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/mercury/v1/model/{id}") -> controller.ApiDataModelController.UpdateDataModelHandler
func controllerApiDataModelControllerUpdateDataModelHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateDataModelHandler",
		Patten:            "/mercury/v1/model/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDataModelController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DATAMODEL-EDIT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDataModelController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.UpdateDataModelReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		data := dto.ApiDataModelDTO{}
		if i, ok := decode.Implements(&data); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &data); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(data); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateDataModelHandler(req1, data)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/mercury/v1/model/{id}") -> controller.ApiDataModelController.RemoveDataModelHandler
func controllerApiDataModelControllerRemoveDataModelHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RemoveDataModelHandler",
		Patten:            "/mercury/v1/model/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDataModelController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DATAMODEL-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDataModelController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.RemoveDataModelReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.RemoveDataModelHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mercury/v1/model/{id}/{outId}") -> controller.ApiDataModelController.GetDataModelDetailByIdHandler
func controllerApiDataModelControllerGetDataModelDetailByIdHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetDataModelDetailByIdHandler",
		Patten:            "/mercury/v1/model/{id}/{outId}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDataModelController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "接口管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDataModelController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.GetDataModelDetailReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetDataModelDetailByIdHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}
