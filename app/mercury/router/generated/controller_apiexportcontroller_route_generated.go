// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// GET Path("/mercury/v1/export/{shareId}/{docType}") -> controller.ApiExportController.ExportByShareId
func controllerApiExportControllerExportByShareIdHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ExportByShareId",
		Patten:            "/mercury/v1/export/{shareId}/{docType}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiExportController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog"},
		Label: map[string]string{
			"auditlog.resource": "接口管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiExportController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiExportReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ExportByShareId(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/export") -> controller.ApiExportController.Export
func controllerApiExportControllerExportHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Export",
		Patten:            "/mercury/v1/export",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiExportController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-EXPORT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiExportController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		exportDTO := dto.ApiExportDTO{}
		if i, ok := decode.Implements(&exportDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &exportDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(exportDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Export(exportDTO)

		}
	}
	return chain.ThenFunc(HandleFunc)
}
