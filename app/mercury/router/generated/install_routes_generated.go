// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
	"github.com/gorilla/mux"
	"github.com/justinas/alice"
)

func InstallRoutes(mux *mux.Router, opt types.Option) {
	//generate from Pkg: controller

	//
	mux.Methods("GET").Path("/mercury/v1/common/apiStatusList").Handler(controllerApiCommonControllerApiStatusListHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/common/dataTypeList").Handler(controllerApiCommonControllerDataTypeListHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/common/apiTypeList").Handler(controllerApiCommonControllerApiTypeListHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/common/transferJson2Schema").Handler(controllerApiCommonControllerTransferJson2SchemaHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/mercury/v1/model").Handler(controllerApiDataModelControllerAddDataModelHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mercury/v1/model/{id}").Handler(controllerApiDataModelControllerUpdateDataModelHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mercury/v1/model/{id}").Handler(controllerApiDataModelControllerRemoveDataModelHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/model/{id}/{outId}").Handler(controllerApiDataModelControllerGetDataModelDetailByIdHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/mercury/v1/doc/save").Handler(controllerApiDocControllerSaveDocHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mercury/v1/doc/save").Handler(controllerApiDocControllerUpdateDocHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mercury/v1/doc/{id}").Handler(controllerApiDocControllerUpdateStatusHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/doc/updateResponsibleId").Handler(controllerApiDocControllerUpdateResponseIdHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/doc/updateBatchStatus").Handler(controllerApiDocControllerUpdateBatchStatusHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mercury/v1/doc/{id}").Handler(controllerApiDocControllerRemoveDocByIdHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mercury/v1/doc/ids").Handler(controllerApiDocControllerRemoveDocByIdsHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/doc/save2Jupiter/{id}").Handler(controllerApiDocControllerSave2JupiterHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/doc/saveBatch2Jupiter/{appId}").Handler(controllerApiDocControllerSaveBatch2JupiterHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/doc/sync2Jupiter/{id}").Handler(controllerApiDocControllerSync2JupiterHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/doc/syncBatch2Jupiter").Handler(controllerApiDocControllerSyncBatch2JupiterHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/doc/unbindJupiter/{id}").Handler(controllerApiDocControllerUnbindJupiterHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/doc/list").Handler(controllerApiDocControllerSearchDocPageHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/doc/convert2Jupiter/{id}").Handler(controllerApiDocControllerConvert2JupiterHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/doc/{id}").Handler(controllerApiDocControllerGetDocDetailByIdHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/mercury/v1/dir/apiGroups").Handler(controllerApiDocDirControllerGetApiGroupsHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/mercury/v1/history/list/{docId}").Handler(controllerApiDocHistoryControllerGetHistoryListByDocIdHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/history/{id}").Handler(controllerApiDocHistoryControllerGetHistoryDetailByIdHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/mercury/v1/case").Handler(controllerApiDocQuickControllerSaveQuickRequestHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mercury/v1/case").Handler(controllerApiDocQuickControllerUpdateQuickRequestHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mercury/v1/case/{id}").Handler(controllerApiDocQuickControllerRemoveByIdHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/case/saveToApi/{folderId}/{name}").Handler(controllerApiDocQuickControllerSaveToApiHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/case/{id}").Handler(controllerApiDocQuickControllerGetByIdHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("PUT").Path("/mercury/v1/variable/saveEnv").Handler(controllerApiEnvControllerUpdateEnvHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/variable/saveEnv").Handler(controllerApiEnvControllerSaveEnvHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mercury/v1/variable/deleteEnv").Handler(controllerApiEnvControllerDeleteEnvHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/variable/saveGlobalVars").Handler(controllerApiEnvControllerSaveGlobalVarsHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/variable/getLibraryEnvs").Handler(controllerApiEnvControllerGetLibraryEnvsHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/variable/getEnvVars").Handler(controllerApiEnvControllerGetEnvVarsHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/variable/getGlobalVars").Handler(controllerApiEnvControllerGetGlobalVarsHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/variable/getCurrentVars/{libId}/{envId}").Handler(controllerApiEnvControllerGetVarsHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/mercury/v1/export/{shareId}/{docType}").Handler(controllerApiExportControllerExportByShareIdHandleFunc(alice.New(_recovery, _accessLog), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/export").Handler(controllerApiExportControllerExportHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))

	//
	mux.Methods("POST").Path("/mercury/v1/import/importAndSave").Handler(controllerApiImportControllerImportDocHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/import/registerImportTask").Handler(controllerApiImportControllerImportDocTaskHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mercury/v1/import/registerImportTask").Handler(controllerApiImportControllerUpdateImportTaskHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mercury/v1/import/task/{id}").Handler(controllerApiImportControllerDeleteTaskByIdHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mercury/v1/import/task/{id}/{running}").Handler(controllerApiImportControllerUpdateTaskStatusHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/import/task/run/{id}").Handler(controllerApiImportControllerRunImmediatelyHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/import/task/page/{libId}").Handler(controllerApiImportControllerGetTasksPageHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/import/task/{id}").Handler(controllerApiImportControllerGetTaskByIdHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/import/record").Handler(controllerApiImportControllerGetTaskRecordsByTaskIdHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/mercury/v1/library").Handler(controllerApiLibraryControllerAddLibraryHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mercury/v1/library/{id}").Handler(controllerApiLibraryControllerModifyLibraryHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mercury/v1/library/{id}").Handler(controllerApiLibraryControllerRemoveLibraryHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/library/list").Handler(controllerApiLibraryControllerSearchLibraryHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/library/{id}/doc/status").Handler(controllerApiLibraryControllerGetDocStatusHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/mercury/v1/mock").Handler(controllerApiMockControllerSaveMockHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/mock/copy/{id}").Handler(controllerApiMockControllerCopyMockByIdHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/mock/drag").Handler(controllerApiMockControllerDragMockHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/mock/status/{id}/{status}").Handler(controllerApiMockControllerUpdateStatusHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/mock/getMockPreview").Handler(controllerApiMockControllerGetMockPreviewHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/mock/getMocks/{docId}").Handler(controllerApiMockControllerGetMocksByDocIdHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/mock/getMockUrl/{docId}").Handler(controllerApiMockControllerGetMockUrlHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/mock/getTypes").Handler(controllerApiMockControllerGetMockParamDataTreeHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/mock/{id}").Handler(controllerApiMockControllerGetMockByIdHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mercury/v1/mock/{id}").Handler(controllerApiMockControllerDeleteMockByIdHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.PathPrefix("/mercury/v1/mock/mockApi/{docId}").Handler(controllerApiMockControllerMockHandleFunc(alice.New(_recovery, _accessLog), opt))

	//
	mux.Methods("GET").Path("/mercury/v1/overview/{libId}").Handler(controllerApiOverviewControllerGetDataHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/mercury/v1/run").Handler(controllerApiRunControllerRunApiHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/run/record/deleteBatch").Handler(controllerApiRunControllerDeleteRecordsHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/run/doc").Handler(controllerApiRunControllerSaveRunDocHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/run/doc/sync/{docId}").Handler(controllerApiRunControllerSyncRunDocHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/run/record/saveToApiDoc/{folderId}/{name}").Handler(controllerApiRunControllerSaveToApiDocHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/run/doc/{docId}").Handler(controllerApiRunControllerGetRunByDocIdHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/run/assertType").Handler(controllerApiRunControllerGetAssertTypeHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/run/records").Handler(controllerApiRunControllerGetRecordsListHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/run/record/{id}").Handler(controllerApiRunControllerGetRecordHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/mercury/v1/share/data/getTree/{id}").Handler(controllerApiShareControllerGetShareDataHandleFunc(alice.New(_recovery, _accessLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/share/data/doc/{id}").Handler(controllerApiShareControllerGetDocDetailByIdHandlerHandleFunc(alice.New(_recovery, _accessLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/share/data/getTreeInHub/{appId}").Handler(controllerApiShareControllerGetDataInHubHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/share/list/{libId}").Handler(controllerApiShareControllerGetSharesHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/share/{id}").Handler(controllerApiShareControllerGetShareByIdHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/share").Handler(controllerApiShareControllerSaveShareHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mercury/v1/share").Handler(controllerApiShareControllerUpdateShareHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mercury/v1/share/{id}").Handler(controllerApiShareControllerRemoveShareHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))

	//
	mux.Methods("PUT").Path("/mercury/v1/tree/updateFolder/api").Handler(controllerApiTreeControllerUpdateApiFolderHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mercury/v1/tree/updateFolder/model").Handler(controllerApiTreeControllerUpdateModelFolderHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/mercury/v1/tree/updateFolder/case").Handler(controllerApiTreeControllerUpdateCaseFolderHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/tree/{libId}/addFolder/api").Handler(controllerApiTreeControllerAddApiFolderHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/tree/{libId}/addFolder/model").Handler(controllerApiTreeControllerAddModelFolderHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/tree/{libId}/addFolder/case").Handler(controllerApiTreeControllerAddCaseFolderHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/tree/ordering/api").Handler(controllerApiTreeControllerApiTreeOrderingHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/tree/ordering/model").Handler(controllerApiTreeControllerModelTreeOrderingHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/tree/ordering/case").Handler(controllerApiTreeControllerCaseTreeOrderingHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/tree/copy/apiNode/{id}/{libId}").Handler(controllerApiTreeControllerApiNodeCopyHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/tree/copy/folderNode/api/{id}/{libId}").Handler(controllerApiTreeControllerApiFolderNodeCopyHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/tree/copy/modelNode/{id}/{libId}").Handler(controllerApiTreeControllerModelNodeCopyHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/tree/copy/folderNode/model/{id}/{libId}").Handler(controllerApiTreeControllerModelFolderNodeCopyHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/tree/copy/caseNode/{id}/{libId}").Handler(controllerApiTreeControllerCaseNodeCopyHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/mercury/v1/tree/copy/folderNode/case/{id}/{libId}").Handler(controllerApiTreeControllerCaseFolderNodeCopyHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mercury/v1/tree/{libId}/apiNode/{id}").Handler(controllerApiTreeControllerRemoveApiNodeHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mercury/v1/tree/{libId}/folderNode/api/{id}").Handler(controllerApiTreeControllerRemoveApiFolderNodeHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mercury/v1/tree/{libId}/modelNode/{id}").Handler(controllerApiTreeControllerRemoveModelNodeHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mercury/v1/tree/{libId}/folderNode/model/{id}").Handler(controllerApiTreeControllerRemoveModelFolderNodeHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mercury/v1/tree/{libId}/caseNode/{id}").Handler(controllerApiTreeControllerRemoveCaseNodeHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/mercury/v1/tree/{libId}/folderNode/case/{id}").Handler(controllerApiTreeControllerRemoveCaseFolderNodeHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/mercury/v1/tree/{type}/{libId}").Handler(controllerApiTreeControllerViewTreeHandlerHandleFunc(alice.New(_recovery, _accessLog, _authentication, _auditLog), opt))

}
