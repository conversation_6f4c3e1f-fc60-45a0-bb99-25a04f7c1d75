// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/controller"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/log"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/terrors"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// GET Path("/mercury/v1/common/apiStatusList") -> controller.ApiCommonController.ApiStatusListHandler
func controllerApiCommonControllerApiStatusListHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ApiStatusListHandler",
		Patten:            "/mercury/v1/common/apiStatusList",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiCommonController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "接口管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiCommonController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ApiStatusListHandler()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mercury/v1/common/dataTypeList") -> controller.ApiCommonController.DataTypeListHandler
func controllerApiCommonControllerDataTypeListHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "DataTypeListHandler",
		Patten:            "/mercury/v1/common/dataTypeList",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiCommonController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "接口管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiCommonController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.DataTypeListHandler()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mercury/v1/common/apiTypeList") -> controller.ApiCommonController.ApiTypeListHandler
func controllerApiCommonControllerApiTypeListHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ApiTypeListHandler",
		Patten:            "/mercury/v1/common/apiTypeList",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiCommonController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "接口管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiCommonController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ApiTypeListHandler()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/common/transferJson2Schema") -> controller.ApiCommonController.TransferJson2SchemaHandler
func controllerApiCommonControllerTransferJson2SchemaHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "TransferJson2SchemaHandler",
		Patten:            "/mercury/v1/common/transferJson2Schema",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiCommonController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "接口管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiCommonController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.TransferJson2SchemaHandler()
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call TransferJson2SchemaHandler failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}
