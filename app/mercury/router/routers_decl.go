package router

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/controller"
	. "git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codegen/router/decl"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/auditlog"
)

const (
	accessLog       = "accessLog"
	recovery        = "recovery"
	authentication  = "authentication"
	authorization   = "authorization"
	auditLog        = "auditLog"
	authenticationx = "authentication-"
	authorizationx  = "authorization-"
	auditLogx       = "auditLog-"
)

const (
	MercuryAuditResourceLabelKey = auditlog.AuditResourceLabelKey + "=" + "接口管理"
	MUSTGET                      = auditlog.AuditOpTypeMustGETLabelKey
	MercuryAuditLogId            = "auditlog.id=$resp.traceId"
)

var _ = Path("/mercury/v1",
	Middlewares([]string{recovery, accessLog, authentication, auditLog},
		Labels([]string{MercuryAuditResourceLabelKey},
			Constructor(controller.InitializeApiCommonController,
				Path("/common",
					Path("/apiStatusList", GET(controller.DefaultApiCommonController.ApiStatusListHandler)),
					Path("/dataTypeList", GET(controller.DefaultApiCommonController.DataTypeListHandler)),
					Path("/apiTypeList", GET(controller.DefaultApiCommonController.ApiTypeListHandler)),
					Path("/transferJson2Schema", POST(controller.DefaultApiCommonController.TransferJson2SchemaHandler)),
				),
			),
			Constructor(controller.InitializeApiDataModelController,
				Path("/model",
					Middleware(authorization,
						POST(controller.DefaultApiDataModelController.AddDataModelHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DATAMODEL-CREATE"),
						Path("/{id}", PUT(controller.DefaultApiDataModelController.UpdateDataModelHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DATAMODEL-EDIT")),
						Path("/{id}", DELETE(controller.DefaultApiDataModelController.RemoveDataModelHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DATAMODEL-DELETE")),
					),
					Path("/{id}/{outId}", GET(controller.DefaultApiDataModelController.GetDataModelDetailByIdHandler)),
				),
			),
			Constructor(controller.InitializeApiDocController,
				Path("/doc",
					Middleware(authorization,
						Path("/save", POST(controller.DefaultApiDocController.SaveDocHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-CREATE")),
						Path("/save", PUT(controller.DefaultApiDocController.UpdateDocHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-EDIT")),
						Path("/{id}", PUT(controller.DefaultApiDocController.UpdateStatus, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-EDIT")),
						Path("/updateResponsibleId", POST(controller.DefaultApiDocController.UpdateResponseId, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-EDIT")),
						Path("/updateBatchStatus", POST(controller.DefaultApiDocController.UpdateBatchStatus, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-EDIT")),
						Path("/{id}", DELETE(controller.DefaultApiDocController.RemoveDocByIdHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-DELETE")),
						Path("/ids", DELETE(controller.DefaultApiDocController.RemoveDocByIds, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-DELETE")),
						Path("/save2Jupiter/{id}", POST(controller.DefaultApiDocController.Save2Jupiter, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-CREATE-TEMPLATE")),
						Path("/saveBatch2Jupiter/{appId}", POST(controller.DefaultApiDocController.SaveBatch2Jupiter, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-CREATE-TEMPLATE")),
						Path("/sync2Jupiter/{id}", POST(controller.DefaultApiDocController.Sync2Jupiter, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-SYNC")),
						Path("/syncBatch2Jupiter", POST(controller.DefaultApiDocController.SyncBatch2Jupiter, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-SYNC")),
						Path("/unbindJupiter/{id}", POST(controller.DefaultApiDocController.UnbindJupiter, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-DELETE-RELATE")),
					),
					Path("/list", GET(controller.DefaultApiDocController.SearchDocPage)),
					Path("/convert2Jupiter/{id}", GET(controller.DefaultApiDocController.Convert2Jupiter)),
					Path("/{id}", GET(controller.DefaultApiDocController.GetDocDetailByIdHandler)),
				),
			),
			Constructor(controller.InitializeApiDocDirController,
				Path("/dir",
					Path("/apiGroups", GET(controller.DefaultApiDocDirController.GetApiGroupsHandler)),
				),
			),
			Constructor(controller.InitializeApiDocHistoryController,
				Path("/history",
					Path("/list/{docId}", GET(controller.DefaultApiDocHistoryController.GetHistoryListByDocId)),
					Path("/{id}", GET(controller.DefaultApiDocHistoryController.GetHistoryDetailById)),
				),
			),
			Constructor(controller.InitializeApiDocQuickController,
				Path("/case",
					Middleware(authorization,
						POST(controller.DefaultApiDocQuickController.SaveQuickRequestHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-FASTREQUEST-CREATE"),
						PUT(controller.DefaultApiDocQuickController.UpdateQuickRequestHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-FASTREQUEST-EDIT"),
						Path("/{id}", DELETE(controller.DefaultApiDocQuickController.RemoveById, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-FASTREQUEST-DELETE")),
						Path("/saveToApi/{folderId}/{name}", POST(controller.DefaultApiDocQuickController.SaveToApi, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-CREATE")),
					),
					Path("/{id}", GET(controller.DefaultApiDocQuickController.GetById)),
				),
			),
			Constructor(controller.InitializeApiEnvController,
				Path("/variable",
					Middleware(authorization,
						Path("/saveEnv", PUT(controller.DefaultApiEnvController.UpdateEnv, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-ENV-CREATE")),
						Path("/saveEnv", POST(controller.DefaultApiEnvController.SaveEnv, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-ENV-EDIT")),
						Path("/deleteEnv", DELETE(controller.DefaultApiEnvController.DeleteEnvHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-ENV-DELETE")),
						Path("/saveGlobalVars", POST(controller.DefaultApiEnvController.SaveGlobalVars, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-GLOBALVAR-SAVE")),
					),
					Path("/getLibraryEnvs", GET(controller.DefaultApiEnvController.GetLibraryEnvsHandler)),
					Path("/getEnvVars", GET(controller.DefaultApiEnvController.GetEnvVarsHandler)),
					Path("/getGlobalVars", GET(controller.DefaultApiEnvController.GetGlobalVars)),
					Path("/getCurrentVars/{libId}/{envId}", GET(controller.DefaultApiEnvController.GetVars)),
				),
			),
			Constructor(controller.InitializeApiExportController,
				Middlewares([]string{authenticationx, auditLogx},
					Path("/export/{shareId}/{docType}", GET(controller.DefaultApiExportController.ExportByShareId)),
				),
				Middleware(authorization,
					Path("/export", POST(controller.DefaultApiExportController.Export, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-EXPORT")),
				),
			),
			Constructor(controller.InitializeApiImportController,
				Path("/import",
					Middleware(authorization,
						Label("auth.code=MAGIC-MANTIS-APILIBRARY-DOC-IMPORT",
							Path("/importAndSave", POST(controller.DefaultApiImportController.ImportDocHandler, MercuryAuditLogId)),
							Path("/registerImportTask", POST(controller.DefaultApiImportController.ImportDocTaskHandler, MercuryAuditLogId)),
							Path("/registerImportTask", PUT(controller.DefaultApiImportController.UpdateImportTask, MercuryAuditLogId)),
							Path("/task/{id}", DELETE(controller.DefaultApiImportController.DeleteTaskById, MercuryAuditLogId)),
							Path("/task/{id}/{running}", PUT(controller.DefaultApiImportController.UpdateTaskStatus, MercuryAuditLogId)),
							Path("/task/run/{id}", POST(controller.DefaultApiImportController.RunImmediately, MercuryAuditLogId)),
						),
					),
					Path("/task/page/{libId}", GET(controller.DefaultApiImportController.GetTasksPage)),
					Path("/task/{id}", GET(controller.DefaultApiImportController.GetTaskById)),
					Path("/record", GET(controller.DefaultApiImportController.GetTaskRecordsByTaskId)),
				),
			),
			Constructor(controller.InitializeApiLibraryController,
				Middleware(authorization,
					Path("/library", POST(controller.DefaultApiLibraryController.AddLibrary, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-CREATE"),
						Path("/{id}", PUT(controller.DefaultApiLibraryController.ModifyLibrary, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-EDIT")),
						Path("/{id}", DELETE(controller.DefaultApiLibraryController.RemoveLibrary, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DELETE")),
						Middleware(authorizationx,
							Path("/list", GET(controller.DefaultApiLibraryController.SearchLibrary)),
							Path("/{id}/doc/status", GET(controller.DefaultApiLibraryController.GetDocStatus)),
						),
					),
				),
			),
			Constructor(controller.InitializeApiMockController,
				Path("/mock",
					Middleware(authorization,
						Label("auth.code=MAGIC-MANTIS-APILIBRARY-DOC-MOCK",
							POST(controller.DefaultApiMockController.SaveMock, MercuryAuditLogId),
							Path("/copy/{id}", POST(controller.DefaultApiMockController.CopyMockById, MercuryAuditLogId)),
							Path("/drag", POST(controller.DefaultApiMockController.DragMock, MercuryAuditLogId)),
							Path("/status/{id}/{status}", POST(controller.DefaultApiMockController.UpdateStatus, MercuryAuditLogId)),
							Path("/getMockPreview", POST(controller.DefaultApiMockController.GetMockPreview, MercuryAuditLogId)),
							Path("/getMocks/{docId}", GET(controller.DefaultApiMockController.GetMocksByDocId)),
							Path("/getMockUrl/{docId}", GET(controller.DefaultApiMockController.GetMockUrl)),
							Path("/getTypes", GET(controller.DefaultApiMockController.GetMockParamDataTree)),
							Path("/{id}", GET(controller.DefaultApiMockController.GetMockById)),
							Path("/{id}", DELETE(controller.DefaultApiMockController.DeleteMockById, MercuryAuditLogId)),
						),
					),
					Middlewares([]string{authenticationx, auditLogx},
						PathPrefix("/mockApi/{docId}", ALL(controller.DefaultApiMockController.Mock)),
					),
				),
			),
			Constructor(controller.InitializeApiOverviewController,
				Path("/overview/{libId}", GET(controller.DefaultApiOverviewController.GetData)),
			),
			Constructor(controller.InitializeApiRunController,
				Path("/run",
					Middleware(authorization,
						POST(controller.DefaultApiRunController.RunApi, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-EXECUTE"),
						Path("/record/deleteBatch", POST(controller.DefaultApiRunController.DeleteRecords, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-HISTORY-DELETE")),
						Path("/doc", POST(controller.DefaultApiRunController.SaveRunDocHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-EDIT")),
						Path("/doc/sync/{docId}", POST(controller.DefaultApiRunController.SyncRunDocHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-EDIT")),
						Path("/record/saveToApiDoc/{folderId}/{name}", POST(controller.DefaultApiRunController.SaveToApiDoc, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-CREATE")),
					),
					Path("/doc/{docId}", GET(controller.DefaultApiRunController.GetRunByDocIdHandler)),
					Path("/assertType", GET(controller.DefaultApiRunController.GetAssertTypeHandler)),
					Path("/records", GET(controller.DefaultApiRunController.GetRecordsList)),
					Path("/record/{id}", GET(controller.DefaultApiRunController.GetRecord)),
				),
			),
			Constructor(controller.InitializeApiShareController,
				Path("/share",
					Middlewares([]string{authenticationx, auditLogx},
						Path("/data/getTree/{id}", GET(controller.DefaultApiShareController.GetShareData)),
						Path("/data/doc/{id}", GET(controller.DefaultApiShareController.GetDocDetailByIdHandler)),
					),
					Path("/data/getTreeInHub/{appId}", GET(controller.DefaultApiShareController.GetDataInHub)),
					Path("/list/{libId}", GET(controller.DefaultApiShareController.GetShares)),
					Path("/{id}", GET(controller.DefaultApiShareController.GetShareById)),
					Middleware(authorization,
						POST(controller.DefaultApiShareController.SaveShare, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-SHARE-CREATE"),
						PUT(controller.DefaultApiShareController.UpdateShare, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-SHARE-EDIT"),
						Path("/{id}", DELETE(controller.DefaultApiShareController.RemoveShare, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-SHARE-DELETE")),
					),
				),
			),
			Constructor(controller.InitializeApiTreeController,
				Path("/tree",
					Middleware(authorization,
						Path("/updateFolder/api", PUT(controller.DefaultApiTreeController.UpdateApiFolderHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-EDIT")),
						Path("/updateFolder/model", PUT(controller.DefaultApiTreeController.UpdateModelFolderHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DATAMODEL-EDIT")),
						Path("/updateFolder/case", PUT(controller.DefaultApiTreeController.UpdateCaseFolderHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-FASTREQUEST-EDIT")),
						Path("/{libId}/addFolder/api", POST(controller.DefaultApiTreeController.AddApiFolderHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-CREATE")),
						Path("/{libId}/addFolder/model", POST(controller.DefaultApiTreeController.AddModelFolderHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DATAMODEL-CREATE")),
						Path("/{libId}/addFolder/case", POST(controller.DefaultApiTreeController.AddCaseFolderHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-FASTREQUEST-CREATE")),
						Path("/ordering/api", POST(controller.DefaultApiTreeController.ApiTreeOrderingHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-SORT")),
						Path("/ordering/model", POST(controller.DefaultApiTreeController.ModelTreeOrderingHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DATAMODEL-SORT")),
						Path("/ordering/case", POST(controller.DefaultApiTreeController.CaseTreeOrderingHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-FASTREQUEST-SORT")),
						Path("/copy/apiNode/{id}/{libId}", POST(controller.DefaultApiTreeController.ApiNodeCopyHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-COPY")),
						Path("/copy/folderNode/api/{id}/{libId}", POST(controller.DefaultApiTreeController.ApiFolderNodeCopyHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-COPY")),
						Path("/copy/modelNode/{id}/{libId}", POST(controller.DefaultApiTreeController.ModelNodeCopyHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DATAMODEL-COPY")),
						Path("/copy/folderNode/model/{id}/{libId}", POST(controller.DefaultApiTreeController.ModelFolderNodeCopyHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DATAMODEL-COPY")),
						Path("/copy/caseNode/{id}/{libId}", POST(controller.DefaultApiTreeController.CaseNodeCopyHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-FASTREQUEST-COPY")),
						Path("/copy/folderNode/case/{id}/{libId}", POST(controller.DefaultApiTreeController.CaseFolderNodeCopyHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-FASTREQUEST-COPY")),
						Path("/{libId}/apiNode/{id}", DELETE(controller.DefaultApiTreeController.RemoveApiNodeHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-DELETE")),
						Path("/{libId}/folderNode/api/{id}", DELETE(controller.DefaultApiTreeController.RemoveApiFolderNodeHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DOC-DELETE")),
						Path("/{libId}/modelNode/{id}", DELETE(controller.DefaultApiTreeController.RemoveModelNodeHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DATAMODEL-DELETE")),
						Path("/{libId}/folderNode/model/{id}", DELETE(controller.DefaultApiTreeController.RemoveModelFolderNodeHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-DATAMODEL-DELETE")),
						Path("/{libId}/caseNode/{id}", DELETE(controller.DefaultApiTreeController.RemoveCaseNodeHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-FASTREQUEST-DELETE")),
						Path("/{libId}/folderNode/case/{id}", DELETE(controller.DefaultApiTreeController.RemoveCaseFolderNodeHandler, MercuryAuditLogId, "auth.code=MAGIC-MANTIS-APILIBRARY-FASTREQUEST-DELETE")),
					),
					Path("/{type}/{libId}", GET(controller.DefaultApiTreeController.ViewTreeHandler)),
				),
			),
		),
	))
