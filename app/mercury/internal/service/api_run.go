package service

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"math"
	"mime/multipart"
	"net"
	"net/http"
	urllib "net/url"
	"slices"
	"strconv"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/s3store"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dao"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/enums/assert"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/enums/data_type"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/enums/response_body_mode"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	. "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	set "github.com/duke-git/lancet/v2/datastructure/set"
	"github.com/duke-git/lancet/v2/xerror"
)

var apiRuntimeRecordDao dao.ApiRuntimeRecordDao

type ApiRunService struct{}

func (a ApiRunService) GetApiDocRuntimeByDocId(ctx *commoncontext.MantisContext, apiDocBaseId int64) dto.ApiRuntimeDTO {
	docRuntime := models.ApiDocRuntime{}
	docRuntime.ApiDocBaseId = apiDocBaseId
	docRuntime.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &docRuntime)
	if docRuntime.Id == 0 {
		// 不存在运行时记录，进行同步
		docRuntime = a.SyncFromDocBase(ctx, apiDocBaseId, commondto.UserInfo{})
	}
	docRequestDTO := dto.ApiDocRequestDTO{}
	if docRuntime.Request != "" {
		jsonx.UnMarshal([]byte(docRuntime.Request), &docRequestDTO)
	}
	setting := dto.ApiSetting{}
	jsonx.UnMarshal([]byte(docRuntime.SettingStr), &setting)
	preOperation := dto.ApiPreOperation{}
	jsonx.UnMarshal([]byte(docRuntime.PreOperationStr), &preOperation)
	postOperation := dto.ApiPostOperation{}
	jsonx.UnMarshal([]byte(docRuntime.PostOperationStr), &postOperation)
	res := dto.ApiRuntimeDTO{
		ApiDocRunTime: &docRuntime,
		Setting:       &setting,
		PreOperation:  &preOperation,
		PostOperation: &postOperation,
		DocRequestDTO: &docRequestDTO,
	}
	return res
}

func (a ApiRunService) SaveApiDocRuntime(ctx *commoncontext.MantisContext, runtimeDTO dto.ApiRuntimeDTO, info commondto.UserInfo) {
	runtime := runtimeDTO.ApiDocRunTime
	runtime.Modifier = info.AdAccount
	runtime.GmtModified = times.Now()
	runtime.Request = string(jsonx.Marshal(runtimeDTO.DocRequestDTO))
	runtime.SettingStr = string(jsonx.Marshal(runtimeDTO.Setting))
	runtime.PreOperationStr = string(jsonx.Marshal(runtimeDTO.PreOperation))
	runtime.PostOperationStr = string(jsonx.Marshal(runtimeDTO.PostOperation))
	gormx.InsertUpdateOneX(ctx, &runtime)
}

func (a ApiRunService) SyncFromDocBase(ctx *commoncontext.MantisContext, apiDocBaseId int64, info commondto.UserInfo) models.ApiDocRuntime {
	// 查询是否存在运行记录
	docRuntimeOld := models.ApiDocRuntime{
		ApiDocBaseId: apiDocBaseId,
	}
	docRuntimeOld.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &docRuntimeOld)
	base := models.ApiDocBase{}
	base.Id = apiDocBaseId
	base.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &base)
	req := dto.ApiDocRequestDTO{}
	jsonx.UnMarshal([]byte(base.Request), &req)
	// 将parameter的example同步到value
	if req.Parameters != nil {
		for i, parameter := range req.Parameters.Path {
			req.Parameters.Path[i].Value = parameter.Example
		}
		for i, parameter := range req.Parameters.Query {
			req.Parameters.Query[i].Value = parameter.Example
		}
		for i, parameter := range req.Parameters.Header {
			req.Parameters.Header[i].Value = parameter.Example
		}
		for i, parameter := range req.Parameters.Cookie {
			req.Parameters.Cookie[i].Value = parameter.Example
		}
	}
	// 将body的form的example同步到value
	if req.RequestBody.Parameters != nil {
		for i, parameter := range req.RequestBody.Parameters {
			req.RequestBody.Parameters[i].Value = parameter.Example
		}
	}
	req.RequestBody.Content = req.RequestBody.Example
	types := []string{commonconstants.JsonContentType, commonconstants.XmlContentType}
	if slices.Contains(types, req.RequestBody.Type) && req.RequestBody.Content == "" && req.RequestBody.JsonSchema != nil {
		req.RequestBody.Content = convertSchema2Json(ctx, *(req.RequestBody.JsonSchema), set.New[int64]())
	}
	req.RequestBody.JsonSchema = nil
	setting := dto.ApiSetting{
		AutoRedirect:       true,
		TimeoutMilliSecond: 0,
	}
	preOperation := dto.ApiPreOperation{}
	postOperation := dto.ApiPostOperation{}
	if docRuntimeOld.Id != 0 {
		jsonx.UnMarshal([]byte(docRuntimeOld.SettingStr), &setting)
		jsonx.UnMarshal([]byte(docRuntimeOld.PreOperationStr), &preOperation)
		jsonx.UnMarshal([]byte(docRuntimeOld.PostOperationStr), &postOperation)
	}
	docRuntime := models.ApiDocRuntime{
		Addons: Addons{
			GmtCreated:  times.Now(),
			GmtModified: times.Now(),
			IsDeleted:   commonconstants.DeleteNo,
			Id:          docRuntimeOld.Id,
		},
		Type:             base.Type,
		Name:             base.Name,
		Method:           base.Method,
		Path:             base.Path,
		Status:           base.Status,
		Description:      base.Description,
		Request:          string(jsonx.Marshal(&req)),
		LibId:            base.LibId,
		ApiDocBaseId:     base.Id,
		SettingStr:       string(jsonx.Marshal(&setting)),
		PreOperationStr:  string(jsonx.Marshal(&preOperation)),
		PostOperationStr: string(jsonx.Marshal(&postOperation)),
	}
	if info.AdAccount == "" {
		docRuntime.Creator = base.Creator
		docRuntime.Modifier = base.Modifier
	} else {
		docRuntime.Creator = info.AdAccount
		docRuntime.Modifier = info.AdAccount
	}
	gormx.InsertUpdateOneX(ctx, &docRuntime)
	return docRuntime
}

func (a ApiRunService) GetAssertTypeList() []assert.AssertType {
	return assert.GetAssertTypeSlice()
}

func (a ApiRunService) RunApi(ctx *commoncontext.MantisContext, runtimeDTO dto.ApiRuntimeDTO, user commondto.UserInfo) *dto.ApiRuntimeRecordDTO {
	// 获取最新的文档名，用于记录显示
	if runtimeDTO.Type == "api" && runtimeDTO.ApiDocRunTime.ApiDocBaseId != 0 {
		base := models.ApiDocBase{}
		base.Id = runtimeDTO.ApiDocRunTime.ApiDocBaseId
		base.IsDeleted = commonconstants.DeleteNo
		gormx.SelectOneByConditionX(ctx, &base)
		runtimeDTO.ApiDocRunTime.Name = base.Name
	}
	if runtimeDTO.ApiDocRunTime.Name == "" || runtimeDTO.Type == "quick" {
		runtimeDTO.ApiDocRunTime.Name = runtimeDTO.ApiDocRunTime.Path
	}
	// 处理参数
	url, header, cookie, data, parameterList := a.dealWithParam(ctx, runtimeDTO)
	// 处理auth
	a.dealWithAuth(runtimeDTO.DocRequestDTO.Auth, header)
	// 将处理完的参数记录
	realRequestDTO := dto.RealRequestDTO{
		Url:    url,
		Method: runtimeDTO.ApiDocRunTime.Method,
		Body: struct {
			Type       string              `json:"type"`
			Parameters []dto.BaseParameter `json:"parameters"`
			Content    string              `json:"content"`
		}{
			Type:       runtimeDTO.DocRequestDTO.RequestBody.Type,
			Parameters: parameterList,
			Content:    data.String(),
		},
	}
	if runtimeDTO.DocRequestDTO.RequestBody.Content == "" {
		realRequestDTO.Body.Content = ""
	}
	realRequestDTO.Header = make([]*dto.Header, 0)
	if header != nil && len(header) != 0 {
		for k, v := range header {
			realRequestDTO.Header = append(realRequestDTO.Header, &dto.Header{Name: k, Value: v})
		}
	}
	if cookie != nil && len(cookie) != 0 {
		h := dto.Header{Name: "Cookie"}
		hVal := ""
		for k, v := range cookie {
			hVal += k + "=" + v + ";"
		}
		hVal = hVal[:len(hVal)-1]
		h.Value = []string{hVal}
		realRequestDTO.Header = append(realRequestDTO.Header, &h)
	}
	// 设置超时时间
	timeoutMilli := runtimeDTO.Setting.TimeoutMilliSecond
	if timeoutMilli <= 0 {
		timeoutMilli = math.MaxInt32
	}
	// 开始读秒
	start := time.Now().UnixMilli()
	// 发送http请求
	response, err := a.sendRequest(runtimeDTO.ApiDocRunTime.Method, url, header, cookie, data, timeoutMilli, runtimeDTO.Setting.AutoRedirect)
	// 结束读秒
	end := time.Now().UnixMilli()
	// 组建记录对象
	res := dto.ApiRuntimeRecordDTO{}
	runRecord := models.ApiRunRecord{
		Addons: Addons{
			Creator:     user.AdAccount,
			Modifier:    user.AdAccount,
			GmtCreated:  times.Now(),
			GmtModified: times.Now(),
			IsDeleted:   commonconstants.DeleteNo,
		},
		Path:          runtimeDTO.PreUrl + runtimeDTO.ApiDocRunTime.Path,
		LibId:         runtimeDTO.ApiDocRunTime.LibId,
		ApiDocId:      runtimeDTO.ApiDocRunTime.ApiDocBaseId,
		Name:          runtimeDTO.ApiDocRunTime.Name,
		RequestMethod: runtimeDTO.ApiDocRunTime.Method,
		UseTime:       end - start,
	}
	res.ApiRunDTO = &runtimeDTO
	runRecord.Request = string(jsonx.Marshal(&runtimeDTO))
	// 如果err不是空，则意味着请求不正常且或者超时
	if err != nil || response == nil {
		runRecord.IsFailed = true
		var netErr net.Error
		if errors.As(err, &netErr) && netErr.Timeout() {
			runRecord.ErrorMessage = "请求超时"
		} else {
			runRecord.ErrorMessage = "错误：无法请求地址 " + url
		}
		res.RunRecord = &runRecord
		runRecord.Success = false
	} else {
		success := response.StatusCode == 200
		runRecord.RespCode = int32(response.StatusCode)
		runRecord.ContentType = response.Header.Get("Content-Type")
		content, contentLength := a.readContentToString(response)
		runRecord.Body = content
		runRecord.ContentLength = contentLength
		// 是否返回了文件
		returnFile := false
		// 处理header
		respHeader := make([]*models.Header, 0)
		for k, v := range response.Header {
			if strings.ToLower(k) == "content-type" {
				if len(v) > 0 && !strings.Contains(strings.ToLower(v[0]), "application/json") {
					returnFile = true
				}
			}
			respHeader = append(respHeader, &models.Header{
				Name:  k,
				Value: v,
			})
		}
		if returnFile {
			runRecord.Body = ""
			runRecord.FileBody = []byte(content)
		}
		runRecord.ReturnFile = returnFile
		runRecord.Header = respHeader
		runRecord.HeaderStr = string(jsonx.Marshal(&respHeader))
		// 处理cookie
		runRecord.Cookie = make([]*models.Cookie, 0)
		for _, c := range response.Cookies() {
			e := &models.Cookie{
				Name:     c.Name,
				Value:    c.Value,
				Domain:   c.Domain,
				Path:     c.Path,
				Expires:  times.ToTimeUtilTime(c.Expires),
				MaxAge:   c.MaxAge,
				Secure:   c.Secure,
				HttpOnly: c.HttpOnly,
			}
			runRecord.Cookie = append(runRecord.Cookie, e)
		}
		runRecord.CookieStr = string(jsonx.Marshal(&(runRecord.Cookie)))
		// 处理后置操作
		if runRecord.RespCode < 300 {
			postOperationResponsePointer := a.dealWithPostOperation(runRecord, runtimeDTO.PostOperation)
			for _, resp := range postOperationResponsePointer.AssertResponses {
				success = success && resp.Success
			}
			runRecord.PostOperationResponse = string(jsonx.Marshal(postOperationResponsePointer))
			res.PostOperationResp = postOperationResponsePointer
		}
		runRecord.IsFailed = false
		runRecord.Success = success
		res.RunRecord = &runRecord
	}
	runRecord.RealRequest = string(jsonx.Marshal(&realRequestDTO))
	res.RealRequest = &realRequestDTO
	// 保存进数据库
	gormx.InsertUpdateOneX(ctx, &runRecord)
	return &res
}

func (a ApiRunService) dealWithAuth(auth *dto.Auth, header map[string][]string) {
	if auth.Type == constants.AuthTypeBearerToken && auth.AuthObj["token"] != nil {
		header["Authorization"] = []string{"Bearer " + auth.AuthObj["token"].(string)}
	} else if auth.Type == constants.AuthTypeBasicAuth && auth.AuthObj["userName"] != nil && auth.AuthObj["password"] != nil {
		header["Authorization"] = []string{"Basic " + base64.StdEncoding.EncodeToString([]byte(auth.AuthObj["userName"].(string)+":"+auth.AuthObj["password"].(string)))}
	}
}

func (a ApiRunService) readContentToString(resp *http.Response) (string, int64) {
	defer resp.Body.Close()
	reader := resp.Body
	if resp.Header.Get("Content-Encoding") == "gzip" {
		reader1, err := gzip.NewReader(resp.Body)
		if err != nil {
			logger.Logger.Panicf("error in reading response body")
		}
		reader = reader1
	}
	content, err := io.ReadAll(reader)
	if err != nil {
		logger.Logger.Panicf("error in reading response body")
	}
	return string(content), int64(len(content))
}

func (a ApiRunService) dealWithParam(ctx *commoncontext.MantisContext, runtimeDTO dto.ApiRuntimeDTO) (string, map[string][]string, map[string]string, *bytes.Buffer, []dto.BaseParameter) {
	url := ""
	header := make(map[string][]string)
	cookie := make(map[string]string)
	requestDTO := runtimeDTO.DocRequestDTO
	parameters := requestDTO.Parameters
	requestBody := requestDTO.RequestBody
	// 判断url是否是mock请求，如果是则添加
	preUrl := runtimeDTO.PreUrl
	if strings.HasSuffix(preUrl, "/mercury/v1/mock/mockApi/") {
		preUrl = preUrl + strconv.FormatInt(runtimeDTO.ApiDocRunTime.ApiDocBaseId, 10) + "/"
	}
	// 获取全局变量以及环境变量
	varMap := apiVariableDao.GetVarMap(ctx, runtimeDTO.ApiDocRunTime.LibId, runtimeDTO.EnvId)
	// 拼接请求url
	if runtimeDTO.Type != "history" {
		url = preUrl + runtimeDTO.ApiDocRunTime.Path
	} else {
		url = runtimeDTO.ApiDocRunTime.Path
	}
	split := strings.SplitN(url, "?", 2)
	if len(split) > 1 {
		params := strings.Split(split[1], "&")
		values := urllib.Values{}
		for _, param := range params {
			keyVal := strings.Split(param, "=")
			if len(keyVal) == 2 {
				values.Add(keyVal[0], keyVal[1])
			}
		}
		url = split[0] + "?" + values.Encode()
	}
	// 拼接path参数
	if parameters.Path != nil && len(parameters.Path) != 0 {
		for _, path := range parameters.Path {
			name := "{" + path.Name + "}"
			value := urllib.QueryEscape(a.dealWithHtmlAndVariableAndMock(path.Value, varMap))
			url = strings.ReplaceAll(url, name, value)
		}
	}
	// 拼接query参数
	if parameters.Query != nil && len(parameters.Query) != 0 {
		url += "?"
		values := urllib.Values{}
		for _, query := range parameters.Query {
			if query.Type == data_type.ARRAY {
				arr := query.Value.([]any)
				for i, val := range arr {
					d := a.dealWithHtmlAndVariableAndMock(val, varMap)
					arr[i] = d
					values.Add(query.Name, d)
				}
			} else {
				v := a.dealWithHtmlAndVariableAndMock(query.Value, varMap)
				values.Add(query.Name, v)
			}
		}
		url = url + values.Encode()
	}
	// 创建cookie
	if parameters.Cookie != nil && len(parameters.Cookie) != 0 {
		for _, c := range parameters.Cookie {
			v := a.dealWithHtmlAndVariableAndMock(c.Value, varMap)
			cookie[c.Name] = v
		}
	}
	// 创建header
	if parameters.Header != nil && len(parameters.Header) != 0 {
		for _, h := range parameters.Header {
			if h.Type == data_type.ARRAY {
				arr := make([]string, 0)
				vals := h.Value.([]any)
				if vals != nil && len(vals) != 0 {
					for i, val := range vals {
						v := a.dealWithHtmlAndVariableAndMock(val, varMap)
						arr = append(arr, v)
						vals[i] = v
					}
				}
				header[h.Name] = arr
			} else {
				v := a.dealWithHtmlAndVariableAndMock(h.Value, varMap)
				header[h.Name] = []string{v}
			}
		}
	}
	// 拼接请求体
	data := &bytes.Buffer{}
	// 拼接form类型的请求体
	parameterList := make([]dto.BaseParameter, 0)
	if requestBody.Parameters != nil {
		if requestBody.Type == commonconstants.UrlencodedContentType {
			header["Content-Type"] = []string{requestBody.Type}
			postData := urllib.Values{}
			if len(requestBody.Parameters) != 0 {
				for _, p := range requestBody.Parameters {
					value := ""
					if p.Type == data_type.ARRAY {
						pList, ok := p.Value.([]any)
						if !ok || pList == nil || len(pList) == 0 {
							parameterList = append(parameterList, dto.BaseParameter{
								Name:  p.Name,
								Type:  p.Type,
								Value: "",
							})
						} else {
							newList := make([]string, 0, len(pList))
							for j := range pList {
								newList = append(newList, a.dealWithHtmlAndVariableAndMock(pList[j], varMap))
							}
							parameterList = append(parameterList, dto.BaseParameter{
								Name:  p.Name,
								Type:  p.Type,
								Value: newList,
							})
							value = string(jsonx.Marshal(newList))
						}
					} else {
						value = a.dealWithHtmlAndVariableAndMock(p.Value, varMap)
						parameterList = append(parameterList, dto.BaseParameter{
							Name:  p.Name,
							Type:  p.Type,
							Value: value,
						})
					}
					postData.Add(p.Name, value)
				}
			}
			data = bytes.NewBufferString(postData.Encode())
		} else if requestBody.Type == commonconstants.FormContentType {
			writer := multipart.NewWriter(data)
			header["Content-Type"] = []string{writer.FormDataContentType()}
			if len(requestBody.Parameters) != 0 {
				for i, p := range requestBody.Parameters {
					if p.Type == response_body_mode.FILE {
						parameterList = append(parameterList, requestBody.Parameters[i])
						// 从url获取文件流
						val := make([]dto.BaseParameterFileValue, 0)
						jsonx.UnMarshal(jsonx.Marshal(&(p.Value)), &val)
						if len(val) == 0 {
							continue
						}
						if val[0].Name == "" {
							continue
						}
						part, err := writer.CreateFormFile(p.Name, val[0].Name)
						if err != nil {
							logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in write to form-data"))
						}
						fileUrl := val[0].Url
						stream := s3store.GetStreamFromUrl(fileUrl)
						_, err = io.Copy(part, stream)
						if err != nil {
							logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in write to form-data"))
						}
						err = stream.Close()
						if err != nil {
							logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in write to form-data"))
						}
					} else {
						value := ""
						if p.Type == data_type.ARRAY {
							pList, ok := p.Value.([]any)
							if !ok || pList == nil || len(pList) == 0 {
								parameterList = append(parameterList, dto.BaseParameter{
									Name:  p.Name,
									Type:  p.Type,
									Value: "",
								})
							} else {
								newList := make([]string, 0, len(pList))
								for j := range pList {
									newList = append(newList, a.dealWithHtmlAndVariableAndMock(pList[j], varMap))
								}
								parameterList = append(parameterList, dto.BaseParameter{
									Name:  p.Name,
									Type:  p.Type,
									Value: newList,
								})
								value = string(jsonx.Marshal(newList))
							}
						} else {
							value = a.dealWithHtmlAndVariableAndMock(p.Value, varMap)
							parameterList = append(parameterList, dto.BaseParameter{
								Name:  p.Name,
								Type:  p.Type,
								Value: value,
							})
						}
						err := writer.WriteField(p.Name, value)
						if err != nil {
							logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in write to form-data"))
						}
					}
				}
			}
			_ = writer.Close()
		}
	} else if requestBody.Content != "" {
		header["Content-Type"] = []string{requestBody.Type}
		mock := a.dealWithVariableAndMock(requestBody.Content, varMap)
		data = bytes.NewBufferString(mock)
	}
	return url, header, cookie, data, parameterList
}

// dealWithHtmlAndVariableAndMock 解析前端传入的json
func (a ApiRunService) dealWithHtmlAndVariableAndMock(value any, varMap map[string]string) string {
	if value == nil {
		return ""
	}
	str := fmt.Sprintf("%v", value)
	if str != "" {
		mockDataHtmlDTO := dto.MockDataHtmlDTO{}
		jsonx.UnMarshal([]byte(str), &mockDataHtmlDTO)
		resStr := ""
		children := mockDataHtmlDTO.NodeList[0].Children
		if children != nil && len(children) != 0 {
			for _, node := range children {
				if node.NodeType == "var" {
					resStr += varMap[node.Value]
				} else if node.NodeType == "func" {
					resStr += node.Value
				} else {
					resStr += node.Text
				}
			}
		}
		return a.dealWithMock(resStr)
	}
	return ""
}

func (a ApiRunService) dealWithMock(value string) string {
	data := apiMockService.GetMockData(value)
	return data
}

// 处理值中的变量和mock
func (a ApiRunService) dealWithVariableAndMock(value string, varMap map[string]string) string {
	for k, v := range varMap {
		key := "{{" + k + "}}"
		value = strings.ReplaceAll(value, key, v)
	}
	data := apiMockService.GetMockData(value)
	return data
}

func (a ApiRunService) dealWithPostOperation(runRecord models.ApiRunRecord, operation *dto.ApiPostOperation) *dto.PostOperationResponse {
	// 处理断言
	response := dto.PostOperationResponse{
		AssertResponses: make([]dto.AssertResponse, 0),
	}
	for _, assertor := range operation.AssertSlice {
		asserter := assert.GetAssertDataFormatEnumByName(assertor.DataFormat)
		success, message := asserter.Assert(assertor.AssertType, runRecord, assertor.Path, assertor.Assertion)
		response.AssertResponses = append(response.AssertResponses, dto.AssertResponse{
			Name:    assertor.Name,
			Success: success,
			Message: message,
		})
	}
	return &response
}

func (a ApiRunService) sendRequest(method string, url string, header map[string][]string, cookie map[string]string, requestBodyReader *bytes.Buffer, timeoutMilli int64, openRedirect bool) (*http.Response, error) {
	request, err := http.NewRequest(method, url, requestBodyReader)
	if err != nil {
		return nil, err
	}
	for k, v := range header {
		if v != nil && len(v) != 0 {
			for _, val := range v {
				request.Header.Add(k, val)
			}
		}
	}
	for k, v := range cookie {
		request.AddCookie(&http.Cookie{
			Name:  k,
			Value: v,
		})
	}
	client := http.Client{
		Timeout: time.Duration(int64(time.Millisecond) * timeoutMilli),
	}
	if openRedirect {
		// 限制重定向5次
		client.CheckRedirect = func(req *http.Request, via []*http.Request) error {
			if len(via) > 5 {
				return errors.New("redirect too times")
			}
			return nil
		}
	} else {
		// 禁止重定向
		client.CheckRedirect = func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		}
	}
	response, err := client.Do(request)
	return response, err
}

func (a ApiRunService) GetRunRecords(ctx *commoncontext.MantisContext, libId int64) dto.ApiFolderDTO {
	list := apiRuntimeRecordDao.SelectNodesByLibId(ctx, libId)
	resChildren := make([]*dto.ApiFolderDTO, 0)
	// 现在目录的子节点
	var currentChildren []dto.ApiRuntimeRecordListDTO
	// 现在的目录指针
	var currentFolder *dto.ApiFolderDTO
	// 目录count，用于order
	var folderNodeCount int64 = 1
	// 现在的目录name
	timeCurrent := ""
	for _, item := range list {
		if item.Name == "" {
			item.Name = item.Path
		}
		timeThis := item.GmtCreated.Format("2006-01-02")
		// 如果目录已经变了
		if timeCurrent == "" || timeThis != timeCurrent {
			if currentChildren != nil {
				newChildren := make([]dto.ApiRuntimeRecordListDTO, len(currentChildren))
				copy(newChildren, currentChildren)
				currentFolder.Children = newChildren
			}
			timeCurrent = timeThis
			currentChildren = make([]dto.ApiRuntimeRecordListDTO, 0)
			parse, _ := time.Parse("2006-01-02", timeThis)
			currentFolder = &dto.ApiFolderDTO{
				Id:       parse.UnixMilli(),
				Name:     timeCurrent,
				Type:     "record",
				LibId:    libId,
				NodeType: "folderNode",
				Key:      fmt.Sprintf("folerNode.%d", folderNodeCount),
			}
			resChildren = append(resChildren, currentFolder)
			folderNodeCount += 1
		}
		currentChildren = append(currentChildren, item)
		currentFolder.LeafCount += 1
	}
	if currentChildren != nil {
		currentFolder.Children = currentChildren
	}
	res := dto.ApiFolderDTO{
		Name:     "运行记录",
		Type:     "record",
		ParentId: 0,
		LibId:    libId,
		NodeType: "folderNode",
		Key:      "folderNode.0",
		Children: resChildren,
	}
	return res
}

func (a ApiRunService) GetRunRecord(ctx *commoncontext.MantisContext, id int64) dto.ApiRuntimeRecordDTO {
	record := models.ApiRunRecord{}
	record.Id = id
	gormx.SelectOneByConditionX(ctx, &record)
	runtimeDTO := &dto.ApiRuntimeDTO{}
	if record.Request == "" {
		runtimeDTO = nil
	} else {
		jsonx.UnMarshal([]byte(record.Request), runtimeDTO)
	}
	postOperationResponse := &dto.PostOperationResponse{}
	if record.PostOperationResponse == "" {
		postOperationResponse = nil
	} else {
		jsonx.UnMarshal([]byte(record.PostOperationResponse), &postOperationResponse)
	}
	creator, err := cubeRemoteApi.GetUserInfo(record.Creator, utils.IDString(ctx.User.CompanyID))
	if err != nil {
		logger.Logger.Panic(err)
	}
	record.CreatorName = creator.Name
	header := make([]*models.Header, 0)
	if (record.HeaderStr) != "" {
		jsonx.UnMarshal([]byte(record.HeaderStr), &header)
	}
	record.Header = header
	cookie := make([]*models.Cookie, 0)
	if (record.CookieStr) != "" {
		jsonx.UnMarshal([]byte(record.CookieStr), &cookie)
	}
	record.Cookie = cookie
	res := dto.ApiRuntimeRecordDTO{
		ApiRunDTO:         runtimeDTO,
		RunRecord:         &record,
		PostOperationResp: postOperationResponse,
	}
	if record.RealRequest != "" {
		realRequestDTO := dto.RealRequestDTO{}
		jsonx.UnMarshal([]byte(record.RealRequest), &realRequestDTO)
		res.RealRequest = &realRequestDTO
	}
	return res
}

func (a ApiRunService) DeleteByIds(ctx *commoncontext.MantisContext, ids []int64, user commondto.UserInfo) {
	gormx.UpdateBatchByParamBuilderAndMapX(ctx,
		gormx.NewParamBuilder().Model(&models.ApiRunRecord{}).In("id", ids).Eq("is_deleted", commonconstants.DeleteNo),
		map[string]any{
			"is_deleted":   commonconstants.DeleteYes,
			"gmt_modified": times.Now(),
			"modifier":     user.AdAccount,
		})
}

func (a ApiRunService) SaveToApiDoc(ctx *commoncontext.MantisContext, runtimeDTO dto.ApiRuntimeDTO, user commondto.UserInfo, folderId int64, name string) {
	rname, err := urllib.QueryUnescape(name)
	if err != nil {
		logger.Logger.Panicf("error in decode url, err=%s", err.Error())
	}
	runTimeModel := runtimeDTO.ApiDocRunTime
	apiDocBase := models.ApiDocBase{
		Type:        runTimeModel.Type,
		Name:        rname,
		Method:      runTimeModel.Method,
		Path:        runTimeModel.Path,
		Status:      runTimeModel.Status,
		Description: runTimeModel.Description,
		Request:     string(jsonx.Marshal(runtimeDTO.DocRequestDTO)),
		FolderId:    folderId,
		Key:         fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants.NodeTypeApi),
		ApiTreeInfo: models.ApiTreeInfo{
			Addons: Addons{
				Creator:     user.AdAccount,
				Modifier:    user.AdAccount,
				GmtCreated:  times.Now(),
				GmtModified: times.Now(),
				IsDeleted:   commonconstants.DeleteNo,
			},
			NodeType: constants.NodeTypeApi,
			LibId:    runTimeModel.LibId,
		},
	}
	if apiDocBase.Status == 0 {
		apiDocBase.Status = 6
	}
	if apiDocBase.Name == "" {
		apiDocBase.Name = apiDocBase.Path
	}
	treeOperation.AddOneNodeToTail(ctx, folderId, &apiDocBase, constants.FolderTypeApi)
}

func (a ApiRunService) DeleteByDocId(ctx *commoncontext.MantisContext, docId int64, user commondto.UserInfo) {
	gormx.UpdateBatchByParamBuilderAndMapX(ctx,
		gormx.NewParamBuilder().Model(&models.ApiRunRecord{}).Eq("api_doc_id", docId).Eq("is_deleted", commonconstants.DeleteNo),
		map[string]any{
			"is_deleted":   commonconstants.DeleteYes,
			"gmt_modified": times.Now(),
			"modifier":     user.AdAccount,
		})
}
