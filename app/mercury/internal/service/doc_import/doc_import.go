package doc_import

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/cron"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/web"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/notification"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service/tree_node/tree_operate"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto/doc_import_export"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/cache"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	. "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/remote"
	"github.com/duke-git/lancet/v2/netutil"
	"github.com/duke-git/lancet/v2/xerror"
	"github.com/mitchellh/mapstructure"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

var (
	apiFolderDao     dao.ApiFolderDao
	apiImportTaskDao dao.ApiImportTaskDao
	treeOperation    tree_operate.TreeOperation
	cubeRemoteApi    remote.CubeBaseRemoteApi
)

type LoadDocService interface {
	AnalyseAndLoadDoc(ctx *commoncontext.MantisContext, loadDocDto doc_import_export.LoadDocDTO)
	GetLoadTypeEnum() string
}

const defaultApiDocStatus int64 = 6

func GetDocLoadHandler(docType string) LoadDocService {
	switch docType {
	case constants.SWAGGER:
		return SwaggerLoadDocService{}
	case constants.Postman:
		return PostmanLoadDocService{}
	case constants.OPENAPI_3:
		return OpenApi3LoadDocService{}
	default:
		return LoadDocService(nil)
	}
}

type ApiDocImportService struct{}

func (a ApiDocImportService) Analyse(ctx *commoncontext.MantisContext, doc doc_import_export.LoadDocDTO, docType string) {
	key := constants.MercuryImportJob + strconv.FormatInt(doc.GetLibId(), 10)
	_, err := cache.GetObject(key)
	if err == nil {
		logger.Logger.Panicf("%+v", xerror.New("存在运行中的定时任务，请等待定时任务运行完成"))
	}
	lock, err := cache.TryLock(30*time.Second, key, 3000)
	if err != nil {
		logger.Logger.Panicf("error obtain lock, err=%s", err.Error())
	}
	// 最终解锁
	defer cache.Unlock(lock)
	a.analyseWithoutLock(ctx, doc, docType)
}

func (a ApiDocImportService) analyseWithoutLock(ctx *commoncontext.MantisContext, doc doc_import_export.LoadDocDTO, docType string) {
	dataFolder := doc.GetDataModelFolderId()
	if dataFolder != 0 {
		dFolder := models.ApiFolder{}
		dFolder.Id = dataFolder
		dFolder.IsDeleted = commonconstants.DeleteNo
		dFolder.Type = constants.FolderTypeModel
		err := gormx.SelectOneByCondition(ctx, &dFolder)
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.Panicf("%+v", xerror.New("数据模型目录不存在"))
		}
	}
	apiFolder := doc.GetApiDocFolderId()
	if apiFolder != 0 {
		aFolder := models.ApiFolder{}
		aFolder.Id = apiFolder
		aFolder.IsDeleted = commonconstants.DeleteNo
		aFolder.Type = constants.FolderTypeApi
		err := gormx.SelectOneByCondition(ctx, &aFolder)
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.Panicf("%+v", xerror.New("接口目录不存在"))
		}
	}
	GetDocLoadHandler(docType).AnalyseAndLoadDoc(ctx, doc)
}

func (a ApiDocImportService) ImportTask(ctx *commoncontext.MantisContext, scheduleTime string, startTime time.Time, endTime time.Time, task models.ApiImportTask,
	content doc_import_export.LoadAndExportDocDTOContent,
) {
	// 判断是否唯一
	// 新建任务，并且开启任务唯一
	if task.Id == 0 && configs.Config.Modules.Mercury.SingleTask {
		taskOld := models.ApiImportTask{
			LibId: task.LibId,
			Addons: Addons{
				IsDeleted: commonconstants.DeleteNo,
			},
		}
		gormx.SelectOneByConditionX(ctx, &taskOld)
		if taskOld.Id != 0 {
			// 存在重复任务
			logger.Logger.Panicf("接口库中存在任务，无法新建！")
		}
	}
	// 写任务表
	task.Running = true
	if content.Type == 3 {
		task.Running = false
	}
	task.IsDeleted = commonconstants.DeleteNo
	// 处理scheduleTime,只保留6位
	split := strings.Split(scheduleTime, " ")
	if len(split) > 5 {
		scheduleTimeNew := ""
		for i := 0; i < 5; i++ {
			scheduleTimeNew += split[i] + " "
		}
		if split[5] != "*" && split[5] != "?" {
			// 处理周
			dows := strings.Split(split[5], ",")
			// gocron 周是0-6(周日-周六)，进行处理
			for _, dow := range dows {
				d, _ := strconv.ParseInt(dow, 10, 64)
				scheduleTimeNew += strconv.FormatInt(d-1, 10) + ","
			}
		} else {
			scheduleTimeNew += split[5]
		}
		scheduleTime = scheduleTimeNew
	}
	task.ScheduleTime = scheduleTime
	task.StartTime = &startTime
	task.EndTime = &endTime
	contentStr := string(jsonx.Marshal(&content))
	task.Content = contentStr
	if task.Id != 0 {
		oldTask := models.ApiImportTask{}
		oldTask.Id = task.Id
		oldTask.IsDeleted = commonconstants.DeleteNo
		gormx.SelectOneByConditionX(ctx, &oldTask)
		task.TriggerTime = oldTask.TriggerTime
		task.Creator = oldTask.Creator
		task.GmtCreated = oldTask.GmtCreated
		task.Modifier = content.UserAccount
		task.GmtModified = times.Now()
	} else {
		task.Creator = content.UserAccount
		task.Modifier = content.UserAccount
		task.GmtCreated = times.Now()
		task.GmtModified = times.Now()
	}
	gormx.InsertUpdateOneX(ctx, &task)
	// 向scheduler注册任务
	jsonx.UnMarshal([]byte(content.CronContent), &(content.CronContentStruct))
	if content.TriggerType == commonconstants.TaskTypeScheduled { // 如果是定时任务，判断其执行时间是否在现在之前，若是，则不重载
		if content.CronContentStruct.CurrentDate.Before(time.Now()) {
			return
		}
	} else if content.TriggerType == commonconstants.TaskTypeManual { // 如果是手动任务，不注册
		return
	}
	job := &cron.Task{
		StartTime:   &startTime,
		EndTime:     &endTime,
		Cron:        scheduleTime,
		JobName:     constants.MercuryImportJob,
		Key:         constants.MercuryImportJob + strconv.FormatInt(task.Id, 10),
		ExecuteTime: content.CronContentStruct.CurrentDate,
		Type:        content.TriggerType,
		Job:         NewMercuryImportJob(strconv.FormatInt(task.Id, 10)),
	}
	err := job.RegisterJob()
	if err != nil {
		logger.Logger.Panicf("error register job, err=%s", err.Error())
	}
}

func (a ApiDocImportService) importDoc(ctx *commoncontext.MantisContext, task models.ApiImportTask, user *commondto.UserInfo, withLock bool) {
	content := doc_import_export.LoadAndExportDocDTOContent{}
	jsonx.UnMarshal([]byte(task.Content), &content)
	defer func() {
		errorAnalyse := recover()
		taskRecords := models.ApiImportTaskRecords{
			TaskName: task.Name,
			TaskId:   task.Id,
			RunTime:  times.Now(),
			Success:  true,
			Message:  "success",
			Operator: task.Creator,
			Addons: Addons{
				Creator:     task.Creator,
				Modifier:    task.Modifier,
				GmtCreated:  times.Now(),
				GmtModified: times.Now(),
				IsDeleted:   commonconstants.DeleteNo,
			},
		}
		if user != nil {
			taskRecords.Operator = user.AdAccount
		}
		if errorAnalyse != nil {
			// 出错，如果不是不通知，则发送错误通知
			if content.NoticeType != constants.ImportTaskNoticeTypeNever {
				apiLibrary := models.ApiLibrary{}
				apiLibrary.Id = content.LibId
				gormx.SelectOneByConditionX(ctx, &apiLibrary)
				project, _ := cubeRemoteApi.GetProjectInfo(apiLibrary.SpaceId)
				// 发送邮件通知
				notification.Send(content.NoticeUsers, "接口管理执行定时导入失败",
					fmt.Sprintf("接口管理执行定时导入失败！项目： %s, 接口库：%s, 数据源：%s, 导入url： %s, 错误信息： %v。",
						project.Name, apiLibrary.Name, task.Name, task.Url, errorAnalyse),
					content.NoticeTemplates, "", user.CompanyID)
			}
			taskRecords.Success = false
			taskRecords.Message = fmt.Sprintf("%+v", errorAnalyse)
		} else if content.NoticeType == constants.ImportTaskNoticeTypeAlways {
			// 未出错，如果是总是通知，则发送成功通知
			apiLibrary := models.ApiLibrary{}
			apiLibrary.Id = content.LibId
			gormx.SelectOneByConditionX(ctx, &apiLibrary)
			project, _ := cubeRemoteApi.GetProjectInfo(apiLibrary.SpaceId)
			// 发送邮件通知
			notification.Send(content.NoticeUsers, "接口管理执行定时导入成功",
				fmt.Sprintf("接口管理执行定时导入成功！，项目： %s, 接口库：%s, 数据源：%s。",
					project.Name, apiLibrary.Name, task.Name),
				content.NoticeTemplates, "", user.CompanyID)
		}
		// 写任务记录表
		gormx.InsertUpdateOneX(ctx, &taskRecords)
		// 写任务表
		gormx.UpdateBatchByParamBuilderAndMapX(ctx,
			gormx.NewParamBuilder().Model(&models.ApiImportTask{}).Eq("id", task.Id),
			map[string]any{"trigger_time": time.Now()})
		if errorAnalyse != nil {
			logger.Logger.Panicf("%v", xerror.New("任务执行失败"))
		}
	}()
	docJson := GetSwaggerByUrl(task.Url, task.Auth, task.UserName, task.Password)
	loadDocDTO := doc_import_export.GetLoadDocDTO(task.DocType)
	loadDocDTOMap := make(map[string]any)
	err := json.Unmarshal([]byte(docJson), &loadDocDTOMap)
	if err != nil {
		logger.Logger.Panicf("%v", xerror.Wrap(err, "文档格式错误或者url错误"))
	}
	err = mapstructure.Decode(loadDocDTOMap, &loadDocDTO)
	if err != nil {
		logger.Logger.Panicf("%v", xerror.Wrap(err, "文档格式错误"))
	}
	loadDocDTO.SetDataModelFolderId(content.DataModelFolderId)
	loadDocDTO.SetApiDocFolderId(content.ApiDocFolderId)
	loadDocDTO.SetLibId(content.LibId)
	loadDocDTO.SetUserAccount(content.UserAccount)
	loadDocDTO.SetSpaceId(content.SpaceId)
	loadDocDTO.SetXServiceName(content.XServiceName)
	loadDocDTO.SetXUserCenterSession(content.XUserCenterSession)
	loadDocDTO.SetStrategy(content.Strategy)
	if withLock {
		a.Analyse(ctx, loadDocDTO, task.DocType)
	} else {
		a.analyseWithoutLock(ctx, loadDocDTO, task.DocType)
	}
}

func (a ApiDocImportService) ImportDocByTaskId(ctx *commoncontext.MantisContext, taskId string) {
	task := models.ApiImportTask{}
	id, _ := strconv.ParseInt(taskId, 10, 64)
	task.Id = id
	gormx.SelectOneByConditionX(ctx, &task)
	// 同一个接口库执行上锁
	lock, err := cache.TryLock(30*time.Second, constants.MercuryImportJob+strconv.FormatInt(task.LibId, 10), 30000)
	if err != nil {
		logger.Logger.Panicf("error obtain lock, err=%s", err.Error())
	}
	defer cache.Unlock(lock)
	a.importDoc(ctx, task, nil, false)
}

func (a ApiDocImportService) GetTaskPages(ctx *commoncontext.MantisContext, libId int64, requestDTO doc_import_export.TaskRequestDTO, pageRequest gormx.PageRequest) gormx.PageResult {
	params := gormx.NewParamBuilder().Model(&models.ApiImportTask{}).Eq("lib_id", libId).Eq("is_deleted", commonconstants.DeleteNo)
	if requestDTO.Name != "" {
		params.Like("name", "%"+requestDTO.Name+"%")
	}
	if requestDTO.DataFormat != "" {
		params.Eq("doc_type", requestDTO.DataFormat)
	}
	if requestDTO.Creator != "" {
		params.Eq("creator", requestDTO.Creator)
	}
	if requestDTO.GmtCreated != "" {
		params.Gt("gmt_created", requestDTO.GetCreatTimeStart())
		params.Lt("gmt_created", requestDTO.GetCreatTimeEnd())
	}
	tasks := make([]models.ApiImportTask, 0)
	pageResult := gormx.PageSelectByParamBuilderX(ctx, params, &tasks, pageRequest)
	res := make([]doc_import_export.LoadDocDTOResponse, len(tasks), len(tasks))
	for i, task := range tasks {
		task.TimeLimit = []int64{task.StartTime.UnixMilli(), task.EndTime.UnixMilli()}
		content := doc_import_export.LoadAndExportDocDTOContent{}
		jsonx.UnMarshal([]byte(task.Content), &content)
		res[i] = doc_import_export.LoadDocDTOResponse{
			Id:                task.Id,
			Name:              task.Name,
			LibId:             task.LibId,
			DocType:           task.DocType,
			Url:               task.Url,
			UserName:          task.UserName,
			Password:          task.Password,
			Auth:              task.Auth,
			ScheduleTime:      task.ScheduleTime,
			DataModelFolderId: content.DataModelFolderIdStr,
			ApiDocFolderId:    content.ApiDocFolderIdStr,
			Strategy:          content.Strategy,
			NoticeType:        content.NoticeType,
			NoticeUsers:       strings.Join(content.NoticeUsers, ","),
			NoticeTemplates:   strings.Join(content.NoticeTemplates, ","),
			Type:              content.Type,
			CronContent:       content.CronContent,
			TimeLimit:         task.TimeLimit,
			TriggerTime:       task.TriggerTime,
			GmtCreated:        task.GmtCreated,
			GmtModified:       task.GmtModified,
			Creator:           task.Creator,
			Modifier:          task.Modifier,
			Running:           task.Running,
			TriggerType:       content.TriggerType,
		}
	}
	pageResult.List = res
	return *pageResult
}

func (a ApiDocImportService) GetTaskById(ctx *commoncontext.MantisContext, id int64) doc_import_export.LoadDocDTOResponse {
	task := models.ApiImportTask{}
	task.Id = id
	gormx.SelectOneByConditionX(ctx, &task)
	task.TimeLimit = []int64{task.StartTime.UnixMilli(), task.EndTime.UnixMilli()}
	content := doc_import_export.LoadAndExportDocDTOContent{}
	jsonx.UnMarshal([]byte(task.Content), &content)
	return doc_import_export.LoadDocDTOResponse{
		Id:                task.Id,
		Name:              task.Name,
		LibId:             task.LibId,
		DocType:           task.DocType,
		Url:               task.Url,
		UserName:          task.UserName,
		Password:          task.Password,
		Auth:              task.Auth,
		ScheduleTime:      task.ScheduleTime,
		DataModelFolderId: content.DataModelFolderIdStr,
		ApiDocFolderId:    content.ApiDocFolderIdStr,
		Strategy:          content.Strategy,
		NoticeUsers:       strings.Join(content.NoticeUsers, ","),
		NoticeTemplates:   strings.Join(content.NoticeTemplates, ","),
		Type:              content.Type,
		CronContent:       content.CronContent,
		TimeLimit:         task.TimeLimit,
		TriggerTime:       task.TriggerTime,
		GmtCreated:        task.GmtCreated,
		GmtModified:       task.GmtModified,
		Creator:           task.Creator,
		Modifier:          task.Modifier,
		Running:           task.Running,
		TriggerType:       content.TriggerType,
	}
}

func (a ApiDocImportService) DeleteTaskById(ctx *commoncontext.MantisContext, id int64, info commondto.UserInfo) {
	// 删除任务
	err := cron.RemoveJob(constants.MercuryImportJob + strconv.FormatInt(id, 10))
	if err != nil {
		logger.Logger.Panicf("error remove job, err=%s", err.Error())
	}
	task := models.ApiImportTask{
		Addons: Addons{
			Id:          id,
			Modifier:    info.AdAccount,
			GmtModified: times.Now(),
			IsDeleted:   commonconstants.DeleteYes,
		},
	}
	gormx.UpdateOneByConditionX(ctx, &task)
}

func (a ApiDocImportService) DeleteTaskByLibId(ctx *commoncontext.MantisContext, libId int64, user commondto.UserInfo) {
	tasks := make([]models.ApiImportTask, 0)
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(&models.ApiImportTask{}).Eq("lib_id", libId).Eq("is_deleted", commonconstants.DeleteYes),
		&tasks)
	for _, task := range tasks {
		a.DeleteTaskById(ctx, task.Id, user)
	}
}

func (a ApiDocImportService) GetRecordsByTaskId(ctx *commoncontext.MantisContext, taskId int64, request gormx.PageRequest) *gormx.PageResult {
	res := make([]models.ApiImportTaskRecords, 0)
	result := gormx.PageSelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(&models.ApiImportTaskRecords{}).Eq("task_id", taskId).
			Eq("is_deleted", commonconstants.DeleteNo).OrderByDesc("run_time"),
		&res, request)
	userMap := make(map[string]string)
	for _, record := range res {
		userMap[record.Operator] = ""
	}
	err := cubeRemoteApi.GetUserAdAccountNameMap(userMap, utils.IDString(ctx.User.CompanyID))
	if err != nil {
		logger.Logger.Panic(err)
	}
	for i, record := range res {
		record.OperatorName = userMap[record.Operator]
		res[i] = record
	}
	result.List = res
	return result
}

func (a ApiDocImportService) UpdateTaskStatusById(ctx *commoncontext.MantisContext, taskId int64, info commondto.UserInfo, running bool) {
	task := models.ApiImportTask{}
	task.Id = taskId
	gormx.SelectOneByConditionX(ctx, &task)
	if task.Id == 0 {
		logger.Logger.Panicf("no such task, taskId=%d", taskId)
	}
	content := doc_import_export.LoadAndExportDocDTOContent{}
	jsonx.UnMarshal([]byte(task.Content), &content)
	jsonx.UnMarshal([]byte(content.CronContent), &(content.CronContentStruct))
	if content.TriggerType == commonconstants.TaskTypeScheduled { // 如果是定时任务，判断其执行时间是否在现在之前，若是，则不重载
		if content.CronContentStruct.CurrentDate.Before(time.Now()) {
			return
		}
	} else if content.TriggerType == commonconstants.TaskTypeManual { // 如果是手动任务，不注册
		return
	}
	if running {
		job := &cron.Task{
			StartTime:   task.StartTime,
			EndTime:     task.EndTime,
			Cron:        task.ScheduleTime,
			JobName:     constants.MercuryImportJob,
			Key:         constants.MercuryImportJob + strconv.FormatInt(task.Id, 10),
			ExecuteTime: content.CronContentStruct.CurrentDate,
			Type:        content.TriggerType,
			Job:         NewMercuryImportJob(strconv.FormatInt(task.Id, 10)),
		}
		err := job.RegisterJob()
		if err != nil {
			logger.Logger.Panicf("error register job, err=%s", err.Error())
		}
	} else {
		err := cron.RemoveJob(constants.MercuryImportJob + strconv.FormatInt(task.Id, 10))
		if err != nil {
			logger.Logger.Panicf("error remove job, err=%s", err.Error())
		}
	}
	// 修改表
	gormx.UpdateBatchByParamBuilderAndMapX(ctx, gormx.NewParamBuilder().Model(&models.ApiImportTask{}).Eq("id", taskId),
		map[string]any{
			"running":      running,
			"modifier":     info.AdAccount,
			"gmt_modified": times.Now(),
		})
}

func (a ApiDocImportService) RunImmediately(ctx *commoncontext.MantisContext, taskId int64, user commondto.UserInfo) {
	task := models.ApiImportTask{}
	task.Id = taskId
	gormx.SelectOneByConditionX(ctx, &task)
	if task.Id == 0 {
		logger.Logger.Panicf("no such task, taskId=%d", taskId)
	}
	a.importDoc(ctx, task, &user, true)
}

func GetSwaggerByUrl(uri string, auth bool, username string, password string) string {
	header := http.Header{}
	if auth {
		header.Add("Authorization", "Basic "+base64.StdEncoding.EncodeToString([]byte(username+":"+password)))
	}
	encodedURL := web.EncodeUrl(uri)
	request := &netutil.HttpRequest{
		RawURL:  encodedURL,
		Method:  "GET",
		Headers: header,
	}
	httpClient := netutil.NewHttpClient()
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != 200 {
		logger.Logger.Panicf("%v", xerror.Wrap(err, "文档url错误"))
	}
	if b, err := io.ReadAll(resp.Body); err == nil {
		r := string(b)
		return r
	}
	return ""
}
