package service

import (
	"fmt"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	. "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
)

var apiDocQuickDao dao.ApiDocQuickDao

type ApiDocQuickRequestService struct{}

func (a ApiDocQuickRequestService) SaveDocQuickRequest(ctx *commoncontext.MantisContext, apiQuickDocDTO dto.ApiQuickDocDTO, user commondto.UserInfo) {
	doc := apiQuickDocDTO.ApiDocQuick
	doc.Name = doc.Path
	doc.IsDeleted = commonconstants.DeleteNo
	doc.Modifier = user.AdAccount
	doc.GmtModified = times.Now()
	AnalyseSchemaRefPath(ctx, apiQuickDocDTO.DocRequestDTO.RequestBody.JsonSchema)
	doc.Request = string(jsonx.Marshal(&(apiQuickDocDTO.DocRequestDTO)))
	doc.SettingStr = string(jsonx.Marshal(&(apiQuickDocDTO.Setting)))
	doc.PreOperationStr = string(jsonx.Marshal(&(apiQuickDocDTO.PreOperation)))
	doc.PostOperationStr = string(jsonx.Marshal(&(apiQuickDocDTO.PostOperation)))
	if doc.Id == 0 {
		doc.Creator = user.AdAccount
		doc.GmtCreated = times.Now()
		// 在树上新增
		doc.Key = fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants.NodeTypeCase)
		treeOperation.AddOneNodeToTail(ctx, doc.FolderId, doc, constants.FolderTypeCase)
	} else {
		old := models.ApiDocQuickRequest{}
		old.Id = doc.Id
		gormx.SelectOneByConditionX(ctx, &old)
		doc.PreNode = old.PreNode
		doc.PostNode = old.PostNode
		doc.Key = old.Key
		gormx.InsertUpdateOneX(ctx, &doc)
	}
}

func (a ApiDocQuickRequestService) RemoveBatchByFolder(ctx *commoncontext.MantisContext, folder []int64, info commondto.UserInfo) {
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiDocQuickRequest{}).In("folder_id", folder)
	gormx.UpdateBatchByParamBuilderAndMapX(ctx, paramBuilder, map[string]any{
		"is_deleted":   commonconstants.DeleteYes,
		"modifier":     info.AdAccount,
		"gmt_modified": times.Now(),
	})
}

func (a ApiDocQuickRequestService) RemoveById(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	docQuickRequest := models.ApiDocQuickRequest{}
	docQuickRequest.Id = id
	// 从树上移除
	gormx.SelectOneByConditionX(ctx, &docQuickRequest)
	treeOperation.RemoveOneNodeOnTree(ctx, &docQuickRequest, constants.FolderTypeCase)
	docQuickRequest.IsDeleted = commonconstants.DeleteYes
	docQuickRequest.Modifier = user.AdAccount
	docQuickRequest.GmtModified = times.Now()
	gormx.UpdateOneByConditionX(ctx, &docQuickRequest)
}

func (a ApiDocQuickRequestService) GetDetailById(ctx *commoncontext.MantisContext, id int64) dto.ApiQuickDocDTO {
	doc := models.ApiDocQuickRequest{}
	doc.Id = id
	gormx.SelectOneByConditionX(ctx, &doc)
	setting := dto.ApiSetting{}
	jsonx.UnMarshal([]byte(doc.SettingStr), &setting)
	preOperation := dto.ApiPreOperation{}
	jsonx.UnMarshal([]byte(doc.PreOperationStr), &preOperation)
	postOperation := dto.ApiPostOperation{}
	jsonx.UnMarshal([]byte(doc.PostOperationStr), &postOperation)
	docRequest := dto.ApiDocRequestDTO{}
	jsonx.UnMarshal([]byte(doc.Request), &docRequest)
	AnalyseSchemaRef(ctx, docRequest.RequestBody.JsonSchema, -1)
	res := dto.ApiQuickDocDTO{
		ApiDocQuick:   &doc,
		Setting:       &setting,
		PreOperation:  &preOperation,
		PostOperation: &postOperation,
		DocRequestDTO: &docRequest,
	}
	return res
}

func (a ApiDocQuickRequestService) Copy(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) models.ApiDocQuickRequest {
	doc := models.ApiDocQuickRequest{}
	doc.Id = id
	gormx.SelectOneByConditionX(ctx, &doc)
	doc.Id = 0
	if len(doc.Name) <= 250 {
		doc.Name = doc.Name + constants.CopyNameSuffix
	}
	doc.Creator = user.AdAccount
	doc.Modifier = user.AdAccount
	doc.GmtCreated = times.Now()
	doc.GmtModified = times.Now()
	doc.Key = fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants.NodeTypeApi)
	// 加进树里，加到原节点的后面
	oriApi := models.ApiDocQuickRequest{}
	oriApi.Id = id
	gormx.SelectOneByConditionX(ctx, &oriApi)
	treeOperation.AddOneNodeToTree(ctx, &doc, &oriApi, constants.TreePositionPost, constants.FolderTypeCase)
	return doc
}

func (a ApiDocQuickRequestService) GetByLibId(ctx *commoncontext.MantisContext, libId int64) []models.ApiDocQuickRequest {
	return apiDocQuickDao.SelectNodeByLibId(ctx, libId)
}

func (a ApiDocQuickRequestService) SaveToApi(ctx *commoncontext.MantisContext, quickDocDTO dto.ApiQuickDocDTO, user commondto.UserInfo, folderId int64, name string) {
	runTimeModel := quickDocDTO.ApiDocQuick
	request := string(jsonx.Marshal(&(quickDocDTO.DocRequestDTO)))
	gormx.TransactionX(ctx, func() error {
		apiDocBase := models.ApiDocBase{
			Type:          runTimeModel.Type,
			Name:          name,
			Method:        runTimeModel.Method,
			Path:          runTimeModel.Path,
			Status:        runTimeModel.Status,
			Description:   runTimeModel.Description,
			Request:       request,
			FolderId:      folderId,
			ResponsibleId: user.AdAccount,
			Key:           fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants.NodeTypeApi),
			ApiTreeInfo: models.ApiTreeInfo{
				Addons: Addons{
					Creator:     user.AdAccount,
					Modifier:    user.AdAccount,
					GmtCreated:  times.Now(),
					GmtModified: times.Now(),
					IsDeleted:   commonconstants.DeleteNo,
				},
				NodeType: constants.NodeTypeApi,
				LibId:    runTimeModel.LibId,
			},
		}
		if apiDocBase.Status == 0 {
			apiDocBase.Status = 6
		}
		// 在树上新增
		treeOperation.AddOneNodeToTail(ctx, folderId, &apiDocBase, constants.FolderTypeApi)
		apiDocRuntime := models.ApiDocRuntime{
			Addons: Addons{
				Creator:     user.AdAccount,
				Modifier:    user.AdAccount,
				GmtCreated:  times.Now(),
				GmtModified: times.Now(),
				IsDeleted:   commonconstants.DeleteNo,
			},
			Type:             apiDocBase.Type,
			Name:             apiDocBase.Name,
			Method:           apiDocBase.Method,
			Path:             apiDocBase.Path,
			Status:           apiDocBase.Status,
			Description:      apiDocBase.Description,
			Request:          request,
			LibId:            apiDocBase.LibId,
			ApiDocBaseId:     apiDocBase.Id,
			SettingStr:       string(jsonx.Marshal(&(quickDocDTO.Setting))),
			PreOperationStr:  string(jsonx.Marshal(&(quickDocDTO.PreOperation))),
			PostOperationStr: string(jsonx.Marshal(&(quickDocDTO.PostOperation))),
		}
		gormx.InsertUpdateOneX(ctx, &apiDocRuntime)
		// 新增历史
		history := models.ApiDocBaseHistory{
			ApiDocBaseId:     apiDocBase.Id,
			ApiDocBase:       apiDocBase,
			ResponseList:     "",
			ResponseCaseList: "",
		}
		history.Id = 0
		gormx.InsertUpdateOneX(ctx, &history)
		return nil
	})
}

func (a ApiDocQuickRequestService) CopyByFolderId(ctx *commoncontext.MantisContext, srcFolderId int64, tarFolderId int64, oldNewKeyMap map[string]string, user commondto.UserInfo) {
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiDocQuickRequest{}).Eq("folder_id", srcFolderId).Eq("is_deleted", commonconstants.DeleteNo)
	apiDocList := make([]models.ApiDocQuickRequest, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &apiDocList)
	if apiDocList != nil && len(apiDocList) != 0 {
		for i, doc := range apiDocList {
			doc.FolderId = tarFolderId
			doc.Creator = user.AdAccount
			doc.Modifier = user.AdAccount
			doc.Id = 0
			doc.Key = oldNewKeyMap[doc.Key]
			// 修正顺序
			doc.SetPreNode(oldNewKeyMap[doc.GetPreNode()])
			doc.SetPostNode(oldNewKeyMap[doc.GetPostNode()])
			apiDocList[i] = doc
		}
		gormx.InsertBatchX(ctx, &apiDocList)
	}
}
