package main

import (
	"log"
	"os"

	uiagent "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"github.com/spf13/cobra"
)

func main() {
	// 定义根命令
	rootCmd := &cobra.Command{
		Use:   "ui-executor",
		Short: "ui-executor command-line interface",
		Long:  `ui-executor command-line interface`,
		RunE:  run,
	}

	// 添加命令行参数
	fs := rootCmd.Flags()
	fs.StringP("input", "", "", "用例json")
	fs.StringP("callback", "", "", "步骤执行结果回调地址")
	fs.StringP("reportid", "", "", "当前执行的报告id")
	fs.StringP("filedownloadurl", "", "", "下载文件url")
	fs.StringP("variableurl", "", "", "请求变量地址")
	fs.StringP("env", "", "", "环境")

	if err := rootCmd.Execute(); err != nil {
		log.Printf("执行失败: %v", err)
		os.Exit(1)
	}
}

// 命令执行入口
func run(cmd *cobra.Command, args []string) error {
	log.Println("开始执行UI测试")
	cf := cmd.Flags()

	// 获取参数
	caseJSON, err := cf.GetString("input")
	if err != nil {
		panic(err)
	}

	callback, err := cf.GetString("callback")
	if err != nil {
		panic(err)
	}

	reportId, err := cf.GetString("reportid")
	if err != nil {
		panic(err)
	}

	fileDownloadURL, err := cf.GetString("filedownloadurl")
	if err != nil {
		panic(err)
	}

	variableURL, err := cf.GetString("variableurl")
	if err != nil {
		panic(err)
	}

	env, err := cf.GetString("env")
	if err != nil {
		panic(err)
	}

	// 初始化ID生成器
	snowflake.Init()

	// 执行UI测试
	err = uiagent.Run(caseJSON, callback, reportId, fileDownloadURL, variableURL, env)
	if err != nil {
		return err
	}

	log.Println("UI测试执行完成")
	return nil
}
