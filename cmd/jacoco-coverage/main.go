package main

import (
	"log"
	"os"

	jacococoverage "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/jacoco-coverage"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/jacoco-coverage/params"
	"github.com/spf13/cobra"
)

func main() {
	// 定义根命令
	rootCmd := &cobra.Command{
		Use:   "jacoco-coverage",
		Short: "jacoco-coverage command-line interface",
		Long:  `jacoco-coverage command-line interface`,
		RunE:  run,
	}

	// 添加命令行参数
	fs := rootCmd.Flags()

	// 基本参数
	fs.StringP("mode", "", "", "模式: increment(增量)/total(全量)")
	fs.StringP("app-name", "", "", "应用名")
	fs.StringP("ip", "", "", "ip")
	fs.StringP("port", "", "", "port")
	fs.StringP("packages", "", "", "comma separated packages, such as com.zhongan.a.*,com.zhongan.b.*")
	fs.StringP("module-name", "", "", "module name")
	fs.StringP("exclude", "", "", "excluding some class for Coverage")

	// Git 参数
	fs.StringP("git-url", "", "", "svn branch urls for diff lists")
	fs.StringP("git-user", "", "", "git user")
	fs.StringP("git-token", "", "", "git token")
	fs.StringP("branch", "", "", "branch")
	fs.StringP("base-commit-id", "", "", "base commit id")
	fs.StringP("compare-commit-id", "", "", "compare commit id")

	// OSS 存储参数
	fs.StringP("oss-path", "", "", "oss-path")
	fs.StringP("oss-endpoint", "", "", "oss endpoint")
	fs.StringP("oss-bucket-name", "", "", "oss bucket name")
	fs.StringP("oss-access-id", "", "", "oss access id")
	fs.StringP("oss-access-key", "", "", "oss access key")
	fs.BoolP("oss-path-style", "", false, "是否启用反响代理")

	// 任务相关参数
	fs.StringP("task-no", "", "", "task no")
	fs.StringP("mantis-call-back", "", "", "mantis 回调地址")
	fs.StringP("old-exec-path", "", "", "上一次执行的exec文件在oss中的路径")

	if err := rootCmd.Execute(); err != nil {
		log.Printf("执行失败: %v", err)
		os.Exit(1)
	}
}

// 命令执行入口
func run(cmd *cobra.Command, args []string) error {
	log.Println("开始执行覆盖率统计任务")

	// 初始化参数
	cf := cmd.Flags()
	p, err := params.InitParams(cf)
	if err != nil {
		return err
	}

	log.Printf("初始化参数完成: %+v", p)

	// 执行覆盖率统计
	if err = jacococoverage.Exec(p); err != nil {
		return err
	}

	log.Println("覆盖率统计完成")
	return nil
}
