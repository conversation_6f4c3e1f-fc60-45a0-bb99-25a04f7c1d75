package main

import (
	"log"
	"os"

	esonarscanner "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/esonar-scanner"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/esonar-scanner/params"
	"github.com/spf13/cobra"
)

func main() {
	// 定义根命令
	rootCmd := &cobra.Command{
		Use:   "esonar-scanner",
		Short: "esonar-scanner command-line interface",
		Long:  `esonar-scanner command-line interface`,
		RunE:  run,
	}

	// 添加命令行参数
	fs := rootCmd.Flags()
	// 业务参数
	fs.StringP("type", "", "", "任务类型: unit(单测)/scan(扫描)")
	fs.StringP("mode", "", "", "模式: increment(增量)/total(全量)")
	fs.StringP("language", "", "", "语言: java/go/...")
	fs.StringP("java-version", "", "", "java版本: Java8/Java11/Java17")
	fs.StringP("module-path", "", "", "模块路径: cube-magic-base")
	fs.StringP("app-type", "", "", "应用类型: front/backend")
	fs.StringP("call-back", "", "", "主服务callback地址: http://svc-xxx")

	// 代码参数
	fs.StringP("code-url", "", "", "仓库地址: http://xx.xx.git")
	fs.StringP("git-user", "", "", "仓库用户名: root")
	fs.StringP("git-token", "", "", "仓库用token: xxx")
	fs.StringP("branch", "", "", "分支: master")
	fs.StringP("base-commit-id", "", "", "基础提交id: xx")
	fs.StringP("compare-commit-id", "", "", "比较提交id: xx")

	// Sonar参数
	fs.StringP("sonar-url", "", "", "sonar地址: https://sonar.com.cn")
	fs.StringP("sonar-token", "", "", "sonar-token: xxx")
	fs.BoolP("sonar-pdf", "", false, "是否生成pds报告: true/false")
	fs.StringP("sonar-profile", "", "", "sonar规则集名称: default")
	fs.StringP("sonar-callback", "", "", "sonar回调地址: https://cube.com.cn/api/magic/xxx")
	fs.StringP("sonar-exclusion", "", "", "排除的路径: **/x.java")
	fs.StringP("sonar-inclusion", "", "", "包含的路径: **/x.java")

	// Maven参数
	fs.StringP("mvn-repo", "", "", "maven仓库地址: https://maven.aliyun.com/repository/public")
	fs.StringP("mvn-user", "", "", "maven用户名: ")
	fs.StringP("mvn-pw", "", "", "maven密码: ")

	if err := rootCmd.Execute(); err != nil {
		log.Printf("执行失败: %v", err)
		os.Exit(1)
	}
}

// 命令执行入口
func run(cmd *cobra.Command, args []string) error {
	log.Println("当前主机IP:", os.Getenv("TASK_HOST_IP"))

	// 检查并初始化参数
	cf := cmd.Flags()
	err, p := params.CheckAndInit(cf)
	if err != nil {
		return err
	}

	// 检查是否需要跳过代码扫描
	if params.NoCodeCheck(p.CodeParam, p.BizParam.Mode, p.BizParam.CallBack) {
		log.Println("无需执行代码扫描，任务完成")
		return nil
	}

	log.Printf("初始化参数完成: %+v", p)

	// 执行扫描任务
	return esonarscanner.Execute(p)
}
