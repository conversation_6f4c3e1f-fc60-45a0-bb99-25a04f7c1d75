package main

import (
	"fmt"
	"log"
	"os"
	"strings"

	trivyscanner "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/trivy-scanner"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/trivy-scanner/docker"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/trivy-scanner/generic"
	"github.com/spf13/cobra"
)

// 扫描器类型常量
const (
	scannerTypeGeneric = "generic"
	scannerTypeDocker  = "docker"
)

func main() {
	// 定义根命令
	rootCmd := &cobra.Command{
		Use:   "trivy-scanner",
		Short: "trivy-scanner command-line interface",
		Long:  `trivy-scanner command-line interface`,
		RunE:  run,
	}

	// 添加命令行参数
	fs := rootCmd.Flags()
	fs.StringP("type", "", "", "制品类型可选: Generic、Docker")
	fs.StringP("image", "", "", "镜像名称")
	fs.StringP("docker-addr", "", "", "镜像仓库地址")
	fs.StringP("docker-user", "", "", "镜像仓库用户")
	fs.StringP("docker-password", "", "", "镜像仓库密码")
	fs.StringP("upload-result-url", "", "", "上传扫描报告接口URL")
	fs.StringP("cube-mantis-url", "", "", "cube-mantis服务地址")
	fs.StringP("filename", "", "", "制品文件名称")
	fs.StringP("artifact-download-url", "", "", "制品下载URL")
	fs.StringP("artifact-download-token", "", "", "制品下载Token")
	fs.StringP("artifact-download-authorization", "", "", "制品下载Authorization")
	fs.StringP("white-cve", "", "", "CVE白名单")
	fs.StringP("non-ssl", "", "", "镜像仓库是否为http")

	if err := rootCmd.Execute(); err != nil {
		log.Printf("执行失败: %v", err)
		os.Exit(1)
	}
}

// 命令执行入口
func run(cmd *cobra.Command, args []string) error {
	log.Println("当前主机IP:", os.Getenv("TASK_HOST_IP"))

	// 获取制品类型
	cf := cmd.Flags()
	artifactType, err := cf.GetString("type")
	if err != nil {
		return fmt.Errorf("获取制品类型失败: %w", err)
	}
	log.Println("制品扫描类型:", artifactType)

	// 根据制品类型创建扫描器
	var scanner trivyscanner.Scanner
	switch strings.ToLower(artifactType) {
	case scannerTypeGeneric:
		scanner, err = generic.NewGenericScanner(cf)
	case scannerTypeDocker:
		scanner, err = docker.NewDockerScanner(cf)
	default:
		return fmt.Errorf("不支持的制品类型: %s", artifactType)
	}
	if err != nil {
		return err
	}

	// 执行安全扫描
	log.Println("执行安全扫描")
	if err = scanner.Exec(); err != nil {
		return fmt.Errorf("扫描失败: %w", err)
	}

	// 上传扫描报告
	log.Println("上传扫描报告")
	if err = scanner.UploadResult(); err != nil {
		return fmt.Errorf("上传报告失败: %w", err)
	}

	log.Println("扫描完成")
	return nil
}
